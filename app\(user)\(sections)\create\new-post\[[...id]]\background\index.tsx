import domToImage from "dom-to-image";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

import { userDataActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

const colors = [
  "#ffffff",
  "#000000",
  "#AFB1B3",
  "#7A29CC",
  "#AC1991",
  "#27B1FF",
  "#29A81E",
  "#FFB800",
  "#FF7816",
  "#F44B41",
];

interface Bg {
  removeBg?: Function;
  setBg?: Function;
  backgroundColor?: string;
  backgroundImage?: Function;
  textColor?: string;
  bgCaption?: string;
  postSettings?: boolean;
}

const Background = (bgProps: Bg) => {
  const background = {
    backgroundColor: useAppSelector(
      (state) => state?.userData?.post?.backgroundColor
    ),
    textColor: useAppSelector((state) => state?.userData?.post?.textColor),
  };

  const bgInitState = {
    backgroundColor: "#AFB1B3",
    textColor: "#ffffff",
    PostImageData: undefined,
  };
  const bgCaption = useAppSelector((state) => state.userData?.post?.caption);
  const dispatchAction = useAppDispatch();

  const [backgroundColor, setBackgroundColor] = useState(
    background.backgroundColor || bgProps.backgroundColor || "#AFB1B3"
  );
  const [active, setActive] = useState(false);
  const [PostImageData, setPostImgData] = useState<File>();
  const [textColor, setTextColor] = useState(
    background.textColor || bgProps.textColor || "#000000"
  );
  const [activeIndex, setActiveIndex] = useState(-1);
  const [activeTextIndex, setActiveTextIndex] = useState(-1);
  const [text, setText] = useState(bgCaption || bgProps.bgCaption);
  const [bg, setBg] = useState({
    backgroundColor,
    textColor,
    PostImageData,
  });

  const textareaRef = useRef(null);
  const bgImageRef = useRef(null);

  useEffect(() => {
    if (text?.length) {
      setActive(true);
    } else {
      setActive(false);
    }
  }, [text]);

  const handleTextareaChange = (e: any) => {
    setText(e.target.value);
    dispatchAction(userDataActions.setPostCaption(e.target.value));
    resizeTextarea();
  };

  useEffect(() => {
    resizeTextarea();
  }, [bgCaption, bgProps.bgCaption]);

  const resizeTextarea = () => {
    const textarea = textareaRef.current as any;
    textarea.style.height = "auto";

    const newHeight = textarea.scrollHeight + 1;
    const maxHeight = window.innerHeight * 0.5;

    if (newHeight > maxHeight) {
      const currentFontSize = window
        .getComputedStyle(textarea, null)
        .getPropertyValue("font-size");
      const newFontSize = parseFloat(currentFontSize) - 1;
      textarea.style.fontSize = newFontSize + "px";
    }

    textarea.style.height = `${newHeight}px`;
  };

  const [_textStoryImage, setTextImageStoryImage] = useState("");

  const captureImage = async () => {
    const textAreaBox = bgImageRef.current;

    // Check if textAreaBox is null or undefined before proceeding
    if (!textAreaBox) {
      console.error("textAreaBox is null or undefined");
      return;
    }

    try {
      const domNode = textAreaBox as any;
      const scale = 1;
      const dataUrl = await domToImage.toPng(textAreaBox, {
        quality: 0.99,
        width: domNode.clientWidth * scale,
        height: domNode.clientHeight * scale,
        style: {
          transform: "scale(" + scale + ")",
          textAlign: "center",
          verticalAlign: "middle",
          border: "none",
          outline: "none",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        },
      });

      const blob = await fetch(dataUrl).then((res) => res.blob());
      const file = new File([blob], "text_area_image.png", { type: blob.type });
      setTextImageStoryImage(URL.createObjectURL(file));
      setPostImgData(file);

      if (bgProps.backgroundImage) {
        bgProps.backgroundImage(file);
      }
    } catch (error) {
      console.error("Error capturing image:", error);
    }
  };

  return (
    <div
      className="container-lg position-relative "
      style={{ height: "maxContent" }}
    >
      {!(bgProps.backgroundColor || bgProps.textColor) && (
        <>
          {!bgProps.postSettings && (
            <Image
              onClick={() => {
                bgProps.removeBg && bgProps.removeBg();
                dispatchAction(userDataActions.setPostCaption(""));
                setBg(bgInitState);
                dispatchAction(userDataActions.setPostBackground(bg));
              }}
              src={"/images/svg/nav-close.svg"}
              width={25}
              height={25}
              className="invert me-3 mt-1 position-absolute end-0 top-0 bg-cream-light rounded-pill svg-icon"
              alt="remove-question"
            />
          )}
        </>
      )}
      <div
        ref={bgImageRef}
        id="post-bg"
        className="d-flex justify-content-center align-items-center rounded-2 shadow-dark"
        style={{
          backgroundColor,
          height: "25rem",
        }}
      >
        <textarea
          ref={textareaRef}
          name="canvas"
          id="bg-canvas"
          maxLength={200}
          className="background-area border-0 fw-semibold"
          style={{
            color: textColor,
            backgroundColor,
            maxHeight: "25rem",
            height: "maxContent",
            fontSize: "3em",
            resize: "none",
            minWidth: "1px",
            width: "90%",
            minHeight: "maxContent",
            border: "none",
            overflow: "hidden",
            padding: "8px",
            outline: "none",
            textAlign: "center",
            verticalAlign: "middle",
          }}
          defaultValue={bgCaption || bgProps.bgCaption}
          onChange={handleTextareaChange}
          // onFocus={() => setActive(true)}
          onBlur={(e) =>
            dispatchAction(userDataActions.setPostCaption(e.target.value))
          }
          rows={1}
          spellCheck={false}
          disabled={
            bgProps.backgroundColor || bgProps.textColor || bgProps.postSettings
              ? true
              : false
          }
        />
      </div>
      {!bgProps.postSettings && (
        <>
          {!(bgProps.backgroundColor || bgProps.textColor) && (
            <div>
              <div className="d-flex w-100 justify-content-center p-2 align-items-center flex-wrap">
                <p
                  className="fs-6 fw-semibold mb-2 mb-md-0 mb-lg-0 me-2 text-center text-lg-end"
                  style={{ width: "10rem" }}
                >
                  Background color:
                </p>
                <div className="d-flex gap-2">
                  {colors.map((color, index) => (
                    <button
                      key={index}
                      className={`rounded-pill border ${
                        activeIndex === index ? "border-danger" : ""
                      }`}
                      style={{
                        backgroundColor: color,
                        width: "20px",
                        height: "20px",
                      }}
                      onClick={() => {
                        setActiveIndex(index);
                        setBackgroundColor(color);
                        setBg({ ...bg, backgroundColor: color });
                        setActive(true);
                      }}
                    />
                  ))}
                </div>
              </div>

              <div className="d-flex w-100 justify-content-center p-2 align-items-center flex-wrap">
                <p
                  className="fs-6 fw-semibold mb-2 mb-md-0 mb-lg-0 me-2 text-center text-lg-end"
                  style={{ width: "10rem" }}
                >
                  Text color:
                </p>
                <div className="d-flex gap-2">
                  {colors.map((color, index) => (
                    <button
                      key={index}
                      className={`rounded-pill border ${
                        activeTextIndex === index ? "border-danger" : ""
                      }`}
                      style={{
                        backgroundColor: color,
                        width: "20px",
                        height: "20px",
                      }}
                      onClick={() => {
                        setActiveTextIndex(index);
                        setTextColor(color);
                        setBg({ ...bg, textColor: color });
                        setActive(true);
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          )}
        </>
      )}
      {active && (
        <>
          {!(
            bgProps.backgroundColor ||
            bgProps.textColor ||
            bgProps.postSettings
          ) && (
            <div className="d-flex done-btn align-items-center justify-content-center mt-2">
              <button
                className="btn btn-purple"
                onClick={() => {
                  captureImage();
                  dispatchAction(userDataActions.setPostBackground(bg));
                  dispatchAction(userDataActions.setPostCaption(text || ""));
                  bgProps.setBg?.(bgCaption, bg);
                  setActive(false);
                }}
              >
                Done
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default Background;
