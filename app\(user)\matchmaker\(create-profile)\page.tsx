"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { GetUserMatchProfile } from "@/api/matchmaker/matchmaker.api";
import CreateProfile from "@/components/matchmaker/create-profile";
import { matchMakerActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

export default function MatchmakerPage() {
  const dispatch = useAppDispatch();

  const router = useRouter();
  const user = useAppSelector((state) => state.user);
  const [profileExists, setProfileExists] = useState<boolean>(false);

  const mode = useSearchParams().get("mode");

  useEffect(() => {
    GetUserMatchProfile()
      .then((user: any) => {
        if (mode === "edit") return;

        let data;

        dispatch(
          matchMakerActions.setIsProfileActivated(user?.data?.[0]?.is_activated)
        );

        if (user?.data?.[0]?._id) {
          data = true;
          setProfileExists(true);
        } else {
          data = false;
          setProfileExists(false);
        }

        if (window.location.pathname === "/matchmaker" && data) {
          router.push("/matchmaker/match");
        }
      })
      .catch((error) => {
        console.error("Error fetching user match profile:", error);
      });
  }, []);

  useEffect(() => {
    if (user.role === "guest") {
      router.push("/");
    }
  }, [user]);

  return (
    <div className="d-flex flex-column vh-100 overflow-y-auto position-relative">
      {user.role !== "guest" && !profileExists && <CreateProfile />}
    </div>
  );
}
