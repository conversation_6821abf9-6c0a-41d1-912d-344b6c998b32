import "./index.scss";
import React, { useEffect, useState } from "react";
import Image from "next/image";

import { type ModalRef } from "@/components/common/modal";
import WishListModal from "@/components/common/wishlist";
import { getWishList, getWishListUsers } from "@/api/user";
import { getAssetUrl } from "@/utils/assets";
import { useAppSelector } from "@/redux-store/hooks";

const WishList = () => {
  const wishListModal = { method: {} as ModalRef };
  const [wishListData, setWishListData] = useState([]);
  const user = useAppSelector((state) => state.defaults.creator_profile);
  const actualUser = useAppSelector((state) => state.user);
  const wishListItem = useAppSelector((state) => state.shopSlice);
  const [selectedItemData, setSelectedItemData] = useState<any>(null);

  const handelModal = () => {
    wishListModal.method.open();
    setSelectedItemData(null);
  };

  useEffect(() => {
    if (user.id === actualUser.id) {
      getWishList().then((res: any) => {
        setWishListData(res.data);
      });
    } else {
      getWishListUsers(user.id).then((res) => {
        setWishListData(res.data);
      });
    }
  }, [wishListItem.WishListItemAdded]);

  const handleModalOpen = (item: any) => {
    setSelectedItemData(item);
    wishListModal.method.open();
  };

  return (
    <>
      <div className="wishlist-container">
        <div className="d-flex justify-content-end  mb-3 gap-3">
          {user.id === actualUser.id && (
            <div className="button-wrapper" onClick={() => handelModal()}>
              <div className="d-flex gap-2 align-items-center me-2">
                <Image
                  src={"/images/post/line-plus.svg"}
                  alt=""
                  width={14}
                  height={14}
                />
                <span className="fs-6 fw-semibold">Add new</span>
              </div>
            </div>
          )}
        </div>
        <div>
          {wishListData.length === 0 ? (
            <>
              <div className="d-flex justify-content-center">
                No Wishlist Data
              </div>
            </>
          ) : (
            <>
              <div className="wishlistData-wrapper">
                {wishListData.map((item: any) => (
                  <div className="wishlistInfo-wrapper" key={item._id + 1}>
                    <div
                      className="img-background"
                      onClick={() => handleModalOpen(item)}
                    >
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={getAssetUrl({
                          media: item?.media && item?.media[0],
                        })}
                        height={200}
                        width={265}
                        alt="image"
                      />
                    </div>
                    <div className="mt-3">
                      <p>{item.title}</p>
                    </div>
                    <div>
                      <p>${item.amount}</p>
                    </div>
                    <div className="d-flex justify-content-between">
                      {user.id === actualUser.id && (
                        <div className="d-flex  gap-2">
                          <div>
                            <input
                              type="checkbox"
                              className="wishlist-input"
                              checked={item.is_public}
                              // disabled
                              name=""
                              id=""
                            />
                          </div>
                          <div>
                            <p className="fw-normal m-0 ">Show/Hide</p>
                          </div>
                        </div>
                      )}
                      <div className="underline">Options</div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
      <WishListModal
        setChildRef={wishListModal}
        iteminitialValues={selectedItemData}
      />
    </>
  );
};

export default WishList;
