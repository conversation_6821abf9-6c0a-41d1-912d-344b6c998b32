import { useCopyToClipboard } from "@uidotdev/usehooks";
import classNames from "classnames";
import { memo, useState } from "react";
import Swal from "sweetalert2";

import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";
import { nativeShare } from "@/utils/native-share";
import { sleep } from "@/utils/sleep";

import { PostActionBtnClass } from "../../class";

const Toast = Swal.mixin({
  toast: true,
  position: "top-end",
  iconColor: "white",
  customClass: {
    popup: "colored-toast",
  },
  showConfirmButton: false,
  timer: 1500,
  timerProgressBar: true,
});

interface Props {
  postId: string;
  cls?: string;
  style?: React.CSSProperties;
  iconWhite?: boolean;
  shareTitle?: string;
  shareText?: string;
  from?: string;
}

function PostActionShareBase(props: Props) {
  const role = useAppSelector((state) => state.user.role);
  const isShared = useAppSelector((s) => s.connections.shared?.[props.postId]);
  const [_copiedText, copyToClipboard] = useCopyToClipboard();
  const [shared, setShared] = useState(false);

  const onClick = async () => {
    if (role === "guest") return ModalService.open("SIGN_IN");
    const url = window.location.origin + "/post/" + props.postId;
    let wasNativeShare = true;

    try {
      setShared(true);
      await nativeShare({
        title: props.shareTitle || "",
        text: props.shareText || "",
        url,
      });
    } catch (error: any) {
      console.error(error);
      if (error.name === "AbortError") return;

      wasNativeShare = false;
      await copyToClipboard(url);
      Toast.fire({
        icon: "success",
        title: "Link copied",
      });
    } finally {
      await sleep(wasNativeShare ? 300 : 1500);
      setShared(false);
    }
  };

  return (
    <div
      onClick={onClick}
      className={classNames(PostActionBtnClass, props.cls, {
        active: !shared,
        "pe-none": shared,
      })}
      style={{
        filter: props.iconWhite && !shared ? "brightness(99)" : "unset",
        ...(props.style || {}),
      }}
    >
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        width={32}
        height={32}
        className=""
        alt=""
        src={`/images/post/${shared ? "new-fill-share" : "new-share"}.svg`}
        style={{
          filter: props.from === "reel" && !shared ? "brightness(5)" : "",
        }}
      />
      {props.from !== "reel" && (
        <div
          className={classNames("d-md-block d-none", {
            "color-primary": shared,
          })}
        >
          Share
        </div>
      )}
    </div>
  );
}

const PostActionShare = memo(PostActionShareBase);
export default PostActionShare;
