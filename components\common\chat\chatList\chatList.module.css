.chat-tab-active {
  border-bottom: 1px solid #ac1991;
  color: #ac1991;
  transition: color 0.3s ease-in-out;
}

.chatItem {
  position: relative;
  overflow: hidden;
  height: fit-content;
  transition: transform 0.3s ease-out;
  will-change: transform;
}

.deleteButton {
  position: absolute;
  right: -100px;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: right 0.3s ease;
  color: white;
  will-change: right;
}

.swiped {
  transform: translateX(-40px);
  transition: transform 0.3s ease-out;
}

.swiped .deleteButton {
  right: -10px;
}

.badgeButton {
  transition: right 0.3s ease;
}
