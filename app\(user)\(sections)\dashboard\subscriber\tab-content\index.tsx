import { useEffect, useState } from "react";

import { subsciberAnalysis, subsciberOverview } from "@/api/dashboard";
import { useAppSelector } from "@/redux-store/hooks";
import { getDateCategoryInfo, getWeekInfo } from "@/utils/creator-dashboard";

import Bar<PERSON>hart from "../../utils/bar-chart";

interface Subscriber {
  type: "channel" | "group";
}

const channelColors = ["#751363", "#AC1991", "#A24F93"];

export default function SubscriberTab(subTabProps: Subscriber) {
  const dateFilter = useAppSelector(
    (state) => state.user.creatorDashboardDateFilter
  );
  const [totalSub, setTotalSub] = useState(0);
  const [gainedSub, setGainedSub] = useState(0);
  const [lostSub, setLostSub] = useState(0);
  const [TraficSeries, setTrafficSeries] = useState([]) as any[];
  const [categories, setCategories] = useState([]) as any[];
  const [showSubs, setShowSubs] = useState("gained");

  useEffect(() => {
    subsciberOverview(subTabProps.type, dateFilter).then((res: any) => {
      setTotalSub(res.data.total_subs);
      setGainedSub(res.data.gained_subs);
      setLostSub(res.data.dropped_subs);
    });

    if (dateFilter) {
      const from = dateFilter.split("=")[1].split("&")[0];
      const to = dateFilter.split("=")[2];
      const { categoryArray, groupCategory, filter } = getDateCategoryInfo(
        from,
        to
      );

      subsciberAnalysis(subTabProps.type, groupCategory, dateFilter).then(
        (res) => {
          const categories = categoryArray;

          const trafficSeries = getTrafficSeries(filter);
          setCategories(categories);
          setTrafficSeries(trafficSeries);
          updateTrafficSeries(res.data, filter ? filter : "year");
        }
      );
    }
  }, [subTabProps.type, dateFilter, showSubs]);

  const getTrafficSeries = (filter: string) => {
    const lengthsMap: any = {
      today: 24,
      week: 7,
      month: 31,
      year: 12,
    };
    const length = lengthsMap[filter] || 0;
    return [
      { name: "MONTHLY", data: Array.from({ length }, () => 0) },
      { name: "QUARTERLY", data: Array.from({ length }, () => 0) },
      { name: "HALF_YEARLY", data: Array.from({ length }, () => 0) },
      { name: "YEARLY", data: Array.from({ length }, () => 0) },
    ];
  };

  const transformData = (data: any[]) => {
    const transformedData: any[] = [];

    data.forEach((entry) => {
      ["monthly", "quarterly", "half_yearly", "yearly"].forEach((type) => {
        if (entry[type] && entry[type].subscribers) {
          transformedData.push({
            type: type.toUpperCase(),
            count: entry[type].subscribers[showSubs],
            hour: entry.hour,
            day: entry.day,
            month: entry.month,
            year: entry.year,
            timestamp: entry.timestamp,
          });
        }
      });
    });

    return transformedData;
  };

  const updateTrafficSeries = (data: any[], filter: string) => {
    const hourlyData = transformData(data);
    const updatedSeries = [...getTrafficSeries(filter)];

    hourlyData.forEach((data: any) => {
      const index =
        filter === "today"
          ? data.hour
          : filter === "week"
          ? getWeekInfo(data.timestamp).findIndex(
              (day) => parseInt(day.date) === data.day
            )
          : filter === "month"
          ? data.day - 1
          : filter === "year"
          ? data.month - 1
          : -1;

      const seriesIndex =
        data.type === "MONTHLY"
          ? 0
          : data.type === "QUARTERLY"
          ? 1
          : data.type === "HALF_YEARLY"
          ? 2
          : data.type === "YEARLY"
          ? 3
          : -1;

      if (index !== -1 && seriesIndex !== -1) {
        updatedSeries[seriesIndex].data[index] += data.count;
      }
    });

    setTrafficSeries(updatedSeries);
  };

  return (
    <div className="subscriber-content-wrapper">
      <ul
        className="nav nav-pills subscription-box-border  w-100 flex-nowrap text-nowrap scrollable"
        id="pills-tab"
        role="tablist"
      >
        <li className="flex-grow-1" role="presentation">
          <button
            disabled={true}
            className="rounded-0 border-0 border-bottom bg-body color-dark h-100 w-100 fs-6 fw-medium px-3 py-2 d-flex flex-column"
          >
            <span>Total</span>
            <h4 className="mt-2">{totalSub}</h4>
          </button>
        </li>
        <li className="nav-item flex-grow-1" role="presentation">
          <button
            className={`nav-link active rounded-0 border-0 bg-grey border-bottom color-dark h-100 w-100 fs-6 fw-medium px-3 py-2 d-flex flex-column`}
            id="pills-total-tab"
            data-bs-toggle="pill"
            data-bs-target={`${
              subTabProps.type === "channel"
                ? "#pills-today-adult"
                : subTabProps.type === "group"
                ? "#pills-today-design"
                : "#pills-today"
            }`}
            type="button"
            role="tab"
            aria-controls={`pills-total`}
            aria-selected="false"
            onClick={() => setShowSubs("gained")}
          >
            <span>Gained</span>
            <h4 className="mt-2">{gainedSub}</h4>
          </button>
        </li>
        <li className="nav-item flex-grow-1" role="presentation">
          <button
            className="nav-line rounded-0 border-0 border-bottom color-dark h-100 w-100 fs-6 fw-medium px-3 py-2 d-flex flex-column"
            id="pills-droppedsub-tab"
            data-bs-toggle="pill"
            data-bs-target={`${
              subTabProps.type === "channel"
                ? "#pills-today-adult"
                : subTabProps.type === "group"
                ? "#pills-today-design"
                : "#pills-today"
            }`}
            type="button"
            role="tab"
            aria-controls={"pills-droppedsub"}
            aria-selected="false"
            onClick={() => setShowSubs("lost")}
          >
            <span>Dropped</span>
            <h4 className="mt-2">{lostSub}</h4>
          </button>
        </li>
      </ul>

      <div className="tab-content" id="pills-tabContent">
        <div
          className="tab-pane fade show active"
          data-bs-target={`${
            subTabProps.type === "channel"
              ? "#pills-today-adult"
              : subTabProps.type === "group"
              ? "#pills-today-design"
              : "#pills-today"
          }`}
          role="tabpanel"
          aria-labelledby="pills-today-tab"
        >
          <div className="subscriber-options-wrapper w-100 d-flex flex-column">
            <div className="subscription-box-border">
              <BarChart
                series={TraficSeries}
                minTick={0}
                maxTick={totalSub}
                categories={categories}
                colors={channelColors}
                barWidth="50%"
              />
            </div>
            <div className="d-flex gap-3 flex-wrap gap-lg-3 flex-column mt-2 flex-lg-row align-items-start">
              <p className="fs-6 fw-medium mb-0">Subscription package :</p>
              <div className="options-wrapper d-flex flex-column flex-lg-row gap-lg-4 gap-2">
                <div className="options-wrapper d-flex align-items-center gap-2">
                  <div
                    className="box p-2 rounded-2"
                    style={{ backgroundColor: "#7e0b6b" }}
                  />
                  <p className="fs-6 fw-medium mb-0">Monthly</p>
                </div>
                <div className="options-wrapper d-flex align-items-center gap-2">
                  <div className="box p-2 bg-purple rounded-2" />
                  <p className="fs-6 fw-medium mb-0">Quarterly</p>
                </div>
                <div className="options-wrapper d-flex align-items-center gap-2">
                  <div
                    className="box p-2 rounded-2"
                    style={{ backgroundColor: "#A24F93" }}
                  />
                  <p className="fs-6 fw-medium mb-0">Half Yearly</p>
                </div>
                <div className="options-wrapper d-flex align-items-center gap-2">
                  <div
                    className="box p-2 rounded-2"
                    style={{ backgroundColor: "#7e0b6b" }}
                  />
                  <p className="fs-6 fw-medium mb-0">Yearly</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
