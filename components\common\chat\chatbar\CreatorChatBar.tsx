"use client";

import classNames from "classnames";
import debounce from "lodash/debounce";
import throttle from "lodash/throttle";
import Image from "next/image";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import { ChatFeeType } from "@/api/chat";
import { ChatMedia } from "@/api/user";
import { ModalService } from "@/components/modals";
import { MediaFeeChat } from "@/global/constants";
import {
  ATTACHMENT_MAX_DURATION,
  IMAGE_ATTACHMENT_MAX_SIZE,
  VIDEO_ATTACHMENT_MAX_SIZE,
} from "@/global/limits/chat";
import useBrowser from "@/hooks/useBrowser";
import useFileUploader from "@/hooks/useFileUploader";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/redux-store/slices/chat.slice";
import type { Media } from "@/types/media";
import { getAssetUrl } from "@/utils/assets";
import socketChannel from "@/utils/chatSocket";
import { formatCurrency } from "@/utils/formatter";

import SharePaidFeaturesModal from "../../modal/chat/share-paid-features";
import {
  MicIconChatbar,
  RemoveFileSelected,
} from "../chat-box/utils/svg-utils";
import "./chatbar.scss";
import { AudioRecordModal } from "./helpers/AudioRecordModal";
import { EditMediaModal } from "./helpers/EditMediaModal";
import { MediaFeeModal } from "./helpers/MediaFeeModal";
import ServicesBtn from "./helpers/ServicesBtn";
import { OptionPlusIcon, PaidFeaturesIcon, PromoteOptionsIcon } from "./svg";

export function fetchChatFeeDetails(props: {
  chatList: Chat[];
  channelId: string;
  targetUser: string;
}) {
  const foundChat = props.chatList.find(
    (chat) => chat.converse_channel_id === props.channelId
  );

  const chat_fee_services =
    foundChat?.initiator._id === props.targetUser
      ? foundChat?.initiator.chat_fee_services || []
      : foundChat?.target.chat_fee_services || [];

  const consumables = foundChat?.converse_consumable || [];

  return { chat_fee_services, consumables };
}

const CreatorChatBar = ({ chattingFeeRef }: any) => {
  const { uploadFile } = useFileUploader();
  const user = useAppSelector((state) => state.user);
  const browser = useBrowser();
  const chatData = useAppSelector((state) => state.chat);
  const chatList = useAppSelector((state) => state.chat.chatList);
  const dispatch = useAppDispatch();
  const [_optionsUi, setOptionsUi] = useState<boolean>(false);
  const [message, setMessage] = useState<string>("");
  const [file, setFile] = useState<File | undefined>(undefined);
  const [isVideo, setIsVideo] = useState<boolean>(false);
  const [receiverUserType, setReceiverUserType] = useState<string>("USER");
  const [image, setImage] = useState<string>("");
  const [channelData, setChannelData] = useState<Chat>();
  const targetUser = useAppSelector((s) => s.chat.targetUser);
  const [vaultMedia, setVaultMedia] = useState<any[]>([]);
  const [_chatEnabledLocal, setChatEnabledLocal] = useState<boolean>(false);
  const [_dontAsk, setDontAsk] = useState(false);
  const [_targetData, setTargetData] = useState<ChatPerson>();

  const messageInputRef = useRef<any>(null);

  const hasStartedTyping = useRef(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [mediaFee, setMediaFee] = useState(5);
  const [hasFeeSet, setHasFeeSet] = useState(false);
  const isLoading = useAppSelector((s) => s.chat.isLoaded);
  const replyMessage = useAppSelector((s) => s.chat.replyMessage);

  useEffect(() => {
    const targetId = chatData.targetUser;
    const data = chatData.chatList.filter((chat: any) => {
      return (
        chat?.target?._id === targetId || chat?.initiator?._id === targetId
      );
    })?.[0];

    if (!data) return;
    const { target, initiator } = data;
    const isTargetUser = target._id === targetUser;
    const targetData = isTargetUser ? target : initiator;

    setTargetData(targetData);

    setDontAsk(data?.payment_reminder ?? false);

    setChatEnabledLocal(
      data.buyers.some((b) => b.buyer === targetUser || b.buyer === user.id) ||
        !targetData?.latest_chat_fee?.is_active
    );

    if (user.id === data?.target?._id) {
      setReceiverUserType(data?.initiator?.user_type);
      if (data?.initiator?.user_type === "USER") setChatEnabledLocal(true);
    } else if (user.id === data?.initiator?._id) {
      if (data?.target?.user_type === "USER") setChatEnabledLocal(true);
      setReceiverUserType(data?.target?.user_type);
    }

    setChannelData(data);

    return () => {
      setFile(undefined);
      dispatch(chatActions.setHasMedia(false));

      setImage("");
      setMediaFee(0);
      setHasFeeSet(false);
      setMessage("");
    };
  }, [chatData.targetUser, chatList]);

  const uploadPicture = (file: any) => {
    const type = file.target.files[0].type;
    const size = file.target.files[0].size;

    if (
      (type.startsWith("image") && size > IMAGE_ATTACHMENT_MAX_SIZE) ||
      (type.startsWith("video") && size > VIDEO_ATTACHMENT_MAX_SIZE)
    ) {
      return Swal.fire({
        title: "File too large",
        icon: "error",
        text: `The maximum file size allowed for ${
          type.startsWith("video") ? "video" : "image"
        } attachments is ${type.startsWith("video") ? "1 GB" : "40 MB"}.`,
        confirmButtonText: "Okay",
        showCancelButton: false,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        allowOutsideClick: false,
      });
    }

    setIsVideo(type.startsWith("video"));

    const url = URL.createObjectURL(file.target.files[0]);
    const videoElement = document.createElement("video");

    videoElement.src = url;

    videoElement.onloadedmetadata = () => {
      if (videoElement.duration > ATTACHMENT_MAX_DURATION) {
        URL.revokeObjectURL(url);
        return Swal.fire({
          title: "File length should not be greater than 10 minutes",
          icon: "error",
          confirmButtonText: "Okay",
          showCancelButton: false,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          allowOutsideClick: false,
        });
      }
    };

    setFile(file.target.files[0]);
    setImage(url);
    dispatch(chatActions.setHasMedia(true));
  };

  useEffect(() => {
    setOptionsUi(false);
  }, [chatData.channelId]);

  useEffect(() => {
    setMessage("");
    socketChannel?.channel?.stopTyping();
  }, [chatData.targetUser]);

  const throttledChatMedia = throttle(ChatMedia, 3000);

  const notifyAndSendMessage = (type: "media" | "text" = "text") => {
    if (type === "media") return;
    const msgMeta = {
      chat_list_message: message,
      converseId: chatData.channelId,
      avatar: user.profile.avatar,
      name: user.profile.display_name,
      type: "message",
      id: user.id,
      ...(replyMessage?.message ? { replyMessage } : {}),
    };

    dispatch(chatActions.updateLoadingState(true));
    dispatch(
      chatActions.updateLastMessage({
        channelId: chatData.channelId,
        message: message || (!isVideo ? "Sent an image" : "Sent a video"),
      })
    );

    socketChannel.channel?.sendMessage({
      message: message || "Attachment",
      meta: msgMeta,
    });
  };

  useEffect(() => {
    const element = document.getElementById("dropdownMenuButton");

    if (!element) return;

    const onShow = () => {
      element.classList.add("rotate");
      element.classList.remove("reset-rotate");
    };

    const onHide = () => {
      element.classList.remove("rotate");
      element.classList.add("reset-rotate");
    };

    element.addEventListener("shown.bs.dropdown", onShow);
    element.addEventListener("hidden.bs.dropdown", onHide);

    return () => {
      element.removeEventListener("shown.bs.dropdown", onShow);
      element.removeEventListener("hidden.bs.dropdown", onHide);
    };
  }, []);

  function handleDeduct() {
    if (!chatList.length) return chatList;

    const updatedChatList = chatList.map((chat) => {
      if (chat.converse_channel_id === chatData.channelId) {
        return {
          ...chat,
          converse_consumable: chat.converse_consumable?.map((c) =>
            c.buyer === user.id
              ? {
                  ...c,
                  available_message: Math.max(c.available_message - 1, 0),
                }
              : c
          ),
        };
      }

      return chat;
    });

    return updatedChatList;
  }

  const sendMessage = async () => {
    if (
      !chatData.targetUser ||
      message.length > 2000 ||
      (!message.trim() && !file && vaultMedia.length === 0)
    ) {
      if (message.length > 2000) {
        Swal.fire({
          title: "Message too long",
          icon: "error",
          text: "The maximum message length is 2,000 characters.",
          confirmButtonText: "Okay",
          showCancelButton: false,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          allowOutsideClick: false,
        });
      }

      return;
    }

    const { chat_fee_services } = fetchChatFeeDetails({
      chatList,
      channelId: chatData.channelId,
      targetUser,
    });

    if (
      !channelData?.buyers.some(
        (b) => b.buyer === user.id || b.buyer === targetUser
      ) &&
      chat_fee_services.length > 0 &&
      chat_fee_services.some((c) => c.chat_fee_type !== ChatFeeType.FREE) &&
      (!channelData?.converse_consumable?.some((c) => c.buyer === user.id) ||
        channelData?.converse_consumable?.find((c) => c.buyer === user.id)
          ?.available_message === 0)
    ) {
      ModalService.open("SHOW_SERVICES", {
        authorName:
          channelData?.target?._id === targetUser
            ? channelData?.target?.display_name || ""
            : channelData?.initiator?.display_name! || "",
        targetUserId: targetUser,
        filter: {
          type: "CHAT-FEE",
        },
        directMessage: {
          showBtn: true,
          file: file || null,
          message,
          vault_media_ids: vaultMedia.length > 0 ? [vaultMedia?.[0]?._id] : [],
          channel_id: chatData.channelId,
          dmCb: () => {
            setFile(undefined);
            dispatch(chatActions.setHasMedia(false));

            setImage("");
            setMediaFee(0);
            setHasFeeSet(false);
            dispatch(chatActions.setNewMessage(message));
            setVaultMedia([]);
            if (messageInputRef.current) messageInputRef.current.rows = 1;
            setMessage("");
            dispatch(chatActions.setReplyMessage({}));
          },
        },
        chattingFeeRef,
      });
      return;
    }

    if (messageInputRef.current) messageInputRef.current.focus();

    const targetUserId = chatData.targetUser;
    const index = chatList.findIndex(
      (chat: any) =>
        chat?.initiator?._id === targetUserId ||
        chat?.target?._id === targetUserId
    );

    if (index !== -1) {
      const updatedChatList = [
        chatList[index],
        ...chatList.slice(0, index),
        ...chatList.slice(index + 1),
      ];
      dispatch(chatActions.setChatUserList(updatedChatList));
    }

    dispatch(chatActions.updateChatActivity(chatData.channelId));

    const shouldDeduct =
      !channelData?.buyers.some(
        (b) => b.buyer === user.id || b.buyer === targetUser
      ) &&
      channelData?.converse_consumable &&
      (channelData?.converse_consumable?.find((c) => c.buyer === user.id)
        ?.available_message ?? 0) > 0;

    if (
      file ||
      vaultMedia.length > 0 ||
      channelData?.buyers.some(
        (b) => b.buyer === user.id || b.buyer === targetUser
      ) ||
      chat_fee_services.some((c) => c.chat_fee_type !== ChatFeeType.FREE) ||
      (channelData?.converse_consumable?.find((c) => c.buyer === user.id)
        ?.available_message ?? 0) > 0
    ) {
      try {
        dispatch(
          chatActions.setMediaLoading({
            channel_id: chatData.channelId,
            loading: true,
          })
        );

        setFile(undefined);
        setMessage("");
        setVaultMedia([]);

        let mediaResponse;
        if (file) mediaResponse = await uploadFile(file, "Chat");
        const filePath =
          mediaResponse?.[0]?.uploadURL?.split("/")[
            mediaResponse?.[0]?.uploadURL?.split("/").length - 1
          ];

        const res: any = await throttledChatMedia({
          ...(file &&
            filePath &&
            mediaResponse?.[0]?.type && {
              media: [
                {
                  path: (mediaResponse as any)?.[0]?.s3Multipart?.key,
                  type: mediaResponse[0].type?.split("/")[0] as
                    | "image"
                    | "video",
                },
              ],
            }),
          ...(vaultMedia.length > 0 && {
            vault_media_ids: [vaultMedia?.[0]?._id],
          }),
          channel_id: chatData.channelId,
          message_text: message || "Attachment",
          meta: {
            chat_list_message:
              message || (!isVideo ? "Sent an image" : "Sent a video"),
            converseId: chatData.channelId,
            id: user.id,
            type:
              file || vaultMedia.length > 0 ? "message-attachment" : "message",
            ...(hasFeeSet && mediaFee >= MediaFeeChat
              ? { media_fee: mediaFee, is_unlocked: false }
              : { is_unlocked: true }),
            ...(replyMessage?.message ? { replyMessage } : {}),
          },
          browser: {
            name: browser.browser.name || "",
            os: browser.os.name || "",
          },
          ...(shouldDeduct ? { deduct_count: true } : {}),
        });

        if (shouldDeduct) {
          const updated = handleDeduct();
          dispatch(chatActions.setChatUserList([...updated]));
        }

        const media = res?.data?.media;
        dispatch(chatActions.setMediaId(media?._id));

        dispatch(chatActions.updateLoadingState(true));
        dispatch(
          chatActions.updateLastMessage({
            channelId: chatData.channelId,
            message: message || (!isVideo ? "Sent an image" : "Sent a video"),
          })
        );
      } catch (err: any) {
        console.error(err);
        Swal.fire({
          icon: "error",
          text: err?.response?.data?.message || "Something went wrong",
          confirmButtonText: "Close",
          confirmButtonColor: "#AC1991",
          customClass: {
            confirmButton: "custom-btn",
          },
        });
      } finally {
        dispatch(
          chatActions.setMediaLoading({
            channel_id: chatData.channelId,
            loading: false,
          })
        );
        // if (image) URL.revokeObjectURL(image);
      }
    } else {
      notifyAndSendMessage();
    }

    // Reset form and state
    setFile(undefined);
    dispatch(chatActions.setHasMedia(false));

    setImage("");
    setMediaFee(0);
    setHasFeeSet(false);
    dispatch(chatActions.setNewMessage(message));
    setVaultMedia([]);
    if (messageInputRef.current) messageInputRef.current.rows = 1;
    setMessage("");
    dispatch(chatActions.setReplyMessage({}));
  };

  const [cropMedia, setCropMedia] = useState(false);

  const debouncedStopTyping = useCallback(
    debounce(() => {
      socketChannel?.channel?.stopTyping();
    }, 200),
    [socketChannel?.channel]
  );

  const isImageAsset = () => {
    if (Array.isArray(replyMessage?.meta?.media)) {
      return replyMessage?.meta?.media[0]?.type?.includes("image");
    } else {
      return replyMessage?.meta?.media?.type?.includes("image");
    }
  };

  return (
    <>
      <div className="position-relative">
        {replyMessage?.message && (
          <div
            className="position-absolute rounded p-3"
            style={{
              background: "rgba(243, 233, 242, 1)",
              width: "100%",
              bottom: messageInputRef?.current?.rows > 1 ? 100 : 72,
            }}
          >
            <div
              className="rounded p-1 position-relative"
              style={{
                background: "rgba(243, 233, 242)",
              }}
            >
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <div className="fw-bold ">{replyMessage?.display_name}</div>
                  <div
                    style={{
                      color: "black",
                    }}
                  >
                    {replyMessage?.message}
                  </div>
                </div>
                {replyMessage?.meta?.media &&
                  "is_unlocked" in replyMessage?.meta &&
                  replyMessage?.meta?.is_unlocked && (
                    <div className="me-4">
                      {isImageAsset() ? (
                        // eslint-disable-next-line @next/next/no-img-element
                        <img
                          src={getAssetUrl({
                            media: Array.isArray(replyMessage?.meta?.media)
                              ? replyMessage?.meta?.media[0]
                              : replyMessage?.meta?.media,
                            defaultType: "avatar",
                            variation:
                              "is_unlocked" in replyMessage?.meta &&
                              !replyMessage?.meta?.is_unlocked
                                ? "blur"
                                : "compressed",
                          })}
                          className="rounded object-fit-cover pointer"
                          width={50}
                          height={50}
                          onClick={() => {
                            if (
                              "is_unlocked" in replyMessage?.meta &&
                              !replyMessage?.meta?.is_unlocked
                            )
                              return;
                            window.KNKY.showFullscreenMedia(
                              "image",
                              getAssetUrl({
                                media: Array.isArray(replyMessage?.meta?.media)
                                  ? replyMessage?.meta?.media[0]
                                  : replyMessage?.meta?.media,
                                defaultType: "avatar",
                                variation: "compressed",
                              })
                            );
                          }}
                          alt=""
                        />
                      ) : (
                        <video
                          src={getAssetUrl({
                            media: Array.isArray(replyMessage?.meta?.media)
                              ? replyMessage?.meta?.media[0]
                              : replyMessage?.meta?.media,
                            variation: "compressed",
                          })}
                          className="rounded object-fit-cover pointer"
                          width={50}
                          height={50}
                          controlsList="nodownload"
                          controls={false}
                          autoPlay
                          loop
                          onClick={() => {
                            window.KNKY.showFullscreenMedia(
                              "video",
                              getAssetUrl({
                                media: Array.isArray(replyMessage?.meta?.media)
                                  ? replyMessage?.meta?.media[0]
                                  : replyMessage?.meta?.media,
                                defaultType: "avatar",
                                variation: "compressed",
                              })
                            );
                          }}
                        />
                      )}
                    </div>
                  )}
              </div>
              <span className="rounded fs-8 position-absolute top-0 end-0 d-flex justify-content-center w-fit">
                <button
                  className="btn-close"
                  onClick={() => {
                    dispatch(chatActions.setReplyMessage({}));
                  }}
                ></button>
              </span>
            </div>
          </div>
        )}
        {((image && file) || vaultMedia?.length > 0) && (
          <div
            className="rounded attachment-box position-absolute d-flex align-items-center z-2"
            style={{
              bottom: messageInputRef?.current?.rows > 1 ? 100 : 72,
            }}
          >
            <div
              className="position-absolute d-flex justify-content-center align-items-center"
              style={{
                height: 20,
                width: 20,
                top: 1,
                left: 100,
                right: "",
                cursor: "pointer",
              }}
              onClick={() => {
                setImage("");
                setFile(undefined);
                dispatch(chatActions.setHasMedia(false));
                setVaultMedia([]);
                setCropMedia(false);
              }}
            >
              <RemoveFileSelected />
            </div>
            {(image && file) || vaultMedia.length > 0 ? (
              <div>
                {file?.type.includes("image") ||
                vaultMedia?.[0]?.type === "image" ? (
                  <Image
                    src={
                      vaultMedia?.[0]?._id
                        ? getAssetUrl({ media: vaultMedia?.[0] as Media })
                        : image || ""
                    }
                    alt="Attachment"
                    height={100}
                    width={100}
                    style={{ objectFit: "contain" }}
                  />
                ) : (
                  <video
                    src={
                      vaultMedia?.[0]?._id
                        ? getAssetUrl({ media: vaultMedia?.[0] as Media })
                        : image || ""
                    }
                    height={100}
                    width={100}
                    playsInline
                    autoPlay
                    muted
                    controlsList="nodownload"
                  ></video>
                )}
              </div>
            ) : null}
            {((image && file) || vaultMedia?.length > 0) && (
              <div className="position-absolute top-0 end-0 p-3 fw-bold color-medium d-flex flex-column align-items-end justify-content-between h-100">
                <span
                  style={{
                    color:
                      hasFeeSet && mediaFee < MediaFeeChat ? "red" : "#ac1991",
                  }}
                  className="underline pointer"
                  data-bs-toggle="modal"
                  data-bs-target="#mediaFeeModal"
                >
                  {!hasFeeSet ? "Send with a fee" : formatCurrency(mediaFee)}
                </span>
                {!isVideo && vaultMedia.length === 0 && (
                  <span
                    className="pointer"
                    data-bs-toggle="modal"
                    onClick={() => {
                      setTimeout(() => {
                        setCropMedia(true);
                      }, 200);
                    }}
                    data-bs-target="#editMediaModal"
                  >
                    Crop
                  </span>
                )}
              </div>
            )}
          </div>
        )}
        <div
          className={`chat-bar d-flex align-items-center gap-2 p-3 ${
            window.innerWidth > 576 ? "p-3" : "p-3 pb-4 px-3"
          }  position-relative`}
        >
          <div
            className="pointer"
            id="dropdownMenuButton"
            data-bs-toggle="dropdown"
            aria-expanded="false"
            data-bs-auto-close="outside"
          >
            <OptionPlusIcon />
          </div>
          <input
            onChange={(e) => uploadPicture(e)}
            disabled={
              user.loggedIn === false || !chatData?.targetUser || !isLoading
                ? true
                : false
            }
            onClick={(e) => {
              (e.target as HTMLInputElement).value = "";
            }}
            type="file"
            name="upload-picture"
            id="upload-picture"
            accept="image/*, video/*"
            hidden
          />
          <ul
            className={classNames("dropdown-menu pointer w-fit translate", {})}
          >
            <label htmlFor="upload-picture" className="dropdown-item pointer">
              <Image
                width={"28"}
                height={"25"}
                src="/images/svg/gallery.svg"
                alt="close-feeling"
                className="svg-icon"
              />
              <span className="ms-2">Media</span>
            </label>
            <li
              className="dropdown-item pointer"
              onClick={async () => {
                try {
                  const { medias } = await window.KNKY.openVault({
                    readonly: true,
                  });
                  setVaultMedia(medias);
                } catch (error) {
                  setVaultMedia([]);
                }
              }}
            >
              <Image
                width={"28"}
                height={"25"}
                src="/images/svg/vault.svg"
                alt="close-feeling"
                className="svg-icon"
              />
              <span className="ms-2">Choose From Vault</span>
            </li>
            <li
              className="dropdown-item my-1"
              data-bs-toggle="modal"
              data-bs-target="#audioRecordModal"
              title="Record a voice message"
            >
              <span className="pointer svg-icon">
                <MicIconChatbar />
              </span>
              <span className="ms-2">Voice message</span>
            </li>
            <li
              className="dropdown-item my-1"
              data-bs-toggle="modal"
              data-bs-target="#promoteYourOptions"
              title="Promote your services"
            >
              <PromoteOptionsIcon />
              <span className="ms-2">Promote your services</span>
            </li>
            {process.env.environment !== "live" && (
              <li
                className="dropdown-item my-1"
                data-bs-toggle="modal"
                data-bs-target="#sharePaidFeaturesModal"
                title="Paid Features"
              >
                <PaidFeaturesIcon />
                <span className="ms-2">Paid Features</span>
              </li>
            )}
          </ul>

          {receiverUserType === "CREATOR" && (
            <ServicesBtn
              target={{
                target_user_display_name:
                  channelData?.target?._id === targetUser
                    ? channelData?.target?.display_name!
                    : channelData?.initiator?.display_name!,
                target_user_id: targetUser,
              }}
            />
          )}
          <textarea
            className="form-control"
            value={message}
            rows={1}
            onChange={(e: any) => {
              setMessage(e.target.value);
              const textarea = e.target;
              textarea.rows = 1;
              const lineHeight = 20;
              const currentRows = Math.min(
                Math.floor(textarea.scrollHeight / lineHeight),
                2
              );
              textarea.rows = currentRows;

              if (!hasStartedTyping.current) {
                socketChannel?.channel?.startTyping();
                hasStartedTyping.current = true;
              }

              if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
              }

              typingTimeoutRef.current = setTimeout(() => {
                debouncedStopTyping();
                hasStartedTyping.current = false;
              }, 200);
            }}
            style={{ resize: "none", overflowY: "hidden" }}
            ref={messageInputRef}
            onKeyDown={(e: any) => {
              if (e.key === "Enter") {
                if (e.shiftKey || /Android|iPhone/i.test(navigator.userAgent)) {
                  e.preventDefault();
                  const textarea = e.target;
                  const cursorPosition = textarea.selectionStart;
                  const value = textarea.value;
                  textarea.value =
                    value.substring(0, cursorPosition) +
                    "\n" +
                    value.substring(cursorPosition);
                  textarea.selectionStart = textarea.selectionEnd =
                    cursorPosition + 1;
                  setMessage(textarea.value);

                  setTimeout(() => {
                    textarea.scrollTop = textarea.scrollHeight;
                  }, 0);
                } else {
                  e.preventDefault();
                  sendMessage();
                }
              }
            }}
            placeholder={
              !user.loggedIn ? "Login to send a message" : "Send a message"
            }
            disabled={!user.loggedIn || isLoading ? false : true}
          />

          <Image
            width={window.innerWidth > 576 ? "30" : "26"}
            height={window.innerWidth > 576 ? "30" : "26"}
            onClick={() => {
              messageInputRef.current?.focus();
              sendMessage();
            }}
            className="invert ms-2 pointer"
            src="/images/svg/send-chat.svg"
            alt="close-feeling"
          />
        </div>
      </div>
      <AudioRecordModal />
      <SharePaidFeaturesModal />
      <MediaFeeModal
        setMediaFee={setMediaFee}
        hasMediaFee={hasFeeSet}
        mediaFee={mediaFee}
        setHasMediaFee={setHasFeeSet}
      />

      <EditMediaModal
        cropMedia={cropMedia}
        currentUrl={image}
        setCroppedPostUrlFunc={(data) => {
          const file = new File([data], "file", { type: data.type });
          setFile(file as any);
          setImage(URL.createObjectURL(file));
        }}
        resetCropper={setCropMedia}
      />
    </>
  );
};

export default memo(CreatorChatBar);
