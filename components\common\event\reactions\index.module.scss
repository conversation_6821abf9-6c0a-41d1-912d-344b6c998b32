.reactionBtn::after {
  display: none !important;
}

.mw-fit {
  min-width: fit-content;
}

.mb-2nh {
  margin-bottom: 0.75rem !important;
}

.inActive {
  filter: grayscale(100%);
}

.inActiveMobile {
  filter: brightness(0) invert(1);
}

.icon {
  cursor: pointer;

  img {
    height: 1.5em;
  }
}

.icon:hover {
  filter: brightness(80%);
}

.flyingReaction {
  position: fixed;
  bottom: -32px;
  animation: floatUp 5s linear forwards;
  width: 2em;
  z-index: 9999;
  pointer-events: none;
}

@keyframes floatUp {
  0% {
    bottom: -32px;
    opacity: 1;
  }
  75% {
    bottom: 60%;
    opacity: 1;
  }
  100% {
    bottom: 85%;
    opacity: 0;
  }
}

@keyframes floatUpMobile {
  0% {
    bottom: 34px;
    opacity: 1;
  }
  75% {
    bottom: 45%;
    opacity: 1;
  }
  100% {
    bottom: 65%;
    opacity: 0;
  }
}

@media screen and (max-width: 576px) {
  .flyingReaction {
    position: absolute;
    animation: floatUpMobile 4s linear forwards;
  }
}
