import type {
  GlobalNotification,
  NotificationSettings,
} from "@/app/(user)/(sections)/notifications/page";
import {
  blockActions,
  connectionsActions,
  defaultActions,
  notificationActions,
  userActions,
  userDataActions,
} from "@/redux-store/actions";
import type { BlockedUser } from "@/redux-store/slices/block.slice";
import { KycStatus } from "@/redux-store/slices/user.slice";
import { store } from "@/redux-store/store";
import type { Media, UsedAs, Variation } from "@/types/media";
import type { UserProfile, UserProfileResponse } from "@/types/user";
import { UserRole } from "@/types/user";

import API from ".";

interface UserRegisterBody {
  f_name: string;
  l_name: string;
  username: string;
  dob: string;
  email: string;
  password: string;
  phone_number?: string;
  display_name: string;
  user_type: string;
  device_info: {
    device: string;
    os: string;
    browser: string;
    country: string;
    city: string;
  };
  email_submission_id: string;
}

export interface RequestOtpBody {
  email: string;
  f_name: string;
  l_name: string;
  display_name: string;
}
export interface VerifyOtpBody {
  otp: string;
  otp_id: string;
  email: string;
}
export interface ExpoLoginBody {
  ot: string;
  kyc_id?: string;
}
interface RequestOtpResponse {
  data: {
    otp_id: string;
  };
}
interface UserRegisterResponse {
  data: {
    token: string;
    refresh_token: string;
    otp_required: boolean;
    otp_id: string;
    display_name: string;
  };
}
interface VerificationBody {
  otp: string;
  otp_id: string;
}

export const UserRegister = async (body: UserRegisterBody) =>
  API.post(API.USER_REGISTER, body) as Promise<UserRegisterResponse>;

export const VerifyLogin = async (body: VerificationBody) =>
  API.post(
    API.USERS + "/verify-login-otp",
    body
  ) as Promise<UserRegisterResponse>;

export const RequestOtp = async (body: RequestOtpBody) =>
  API.post(
    `${API.USERS}/request-signup-otp`,
    body
  ) as Promise<RequestOtpResponse>;

export const VerifyOtp = async (body: VerifyOtpBody) =>
  API.post(`${API.USERS}/verify-otp`, body) as Promise<any>;

export const UsernameAvailability = async (body: any, username: string) =>
  API.get(
    `${API.USERS}/username-availability?username=${username}`,
    (body = {})
  ) as Promise<any>;

export const ExpoLogin = async (body: ExpoLoginBody) =>
  API.post(`${API.USERS}/expoonetimelogin`, body) as Promise<any>;

export interface UserLoginBody {
  username: string;
  password: string;
  device_info: {
    device: string;
    os: string;
    browser: string;
    country: string;
    device_id: string;
    city: string;
  };
  otp?: number | string;
  otp_id?: string;
}

export const UserLogin = async (body: UserLoginBody) =>
  API.post(API.USER_LOGIN, body) as Promise<UserRegisterResponse>;

export const GetUserProfile = async () => {
  try {
    const res = await GetBlockedUsers();

    const { blocked_profiles, blockers_ids } = res.data;
    store.dispatch(blockActions.setBlockedUsers(blocked_profiles));
    store.dispatch(blockActions.setHasBlockedMe(blockers_ids));
  } catch (error) {
    console.log({ error });
  }

  const switchAccount = store.getState().defaults.switch_account;
  return new Promise((resolve, reject) => {
    API.get(API.USER_PROFILE)
      .then((res: UserProfileResponse) => {
        const currentUserState = store.getState().user;
        const profile = res.data[0];

        if (
          typeof profile.used_once === "boolean" &&
          profile.used_once === false
        ) {
          UpdateUsedOnce();
        }

        // store.dispatch(userActions.setMassPayData(profile.masspay_data));

        store.dispatch(userActions.setKycRecord(profile?.kyc));
        store.dispatch(
          userActions.setUserTwoFactorAuth(profile.two_factor_auth)
        );

        store.dispatch(userActions.setWalkthrough(profile.walkthrough));

        if (profile?.kyc?.latest_session?.kyc_status === "Rejected") {
          store.dispatch(userActions.setUserIsVerified(KycStatus.REJECTED));
        } else if (
          (profile?.kyc?.latest_session?.kyc_status === "Awaiting" &&
            profile?.kyc?.latest_session.idv_status === "InProgress") ||
          (profile?.kyc?.latest_session?.idv_status === "Completed" &&
            !("kyc_status" in profile?.kyc?.latest_session))
        ) {
          store.dispatch(userActions.setUserIsVerified(KycStatus.SUBMITTED));
        } else if (
          profile?.kyc &&
          store.getState().user.role === "user" &&
          profile?.kyc?.age_verification_completed === true
        ) {
          store.dispatch(userActions.setUserIsVerified(KycStatus.ACCEPTED));
        } else if (
          profile?.kyc &&
          store.getState().user.role === "creator" &&
          profile?.kyc?.full_kyc_completed === true
        ) {
          store.dispatch(userActions.setUserIsVerified(KycStatus.ACCEPTED));
        } else {
          store.dispatch(userActions.setUserIsVerified(undefined));
        }

        if (profile.prime_limit) {
          store.dispatch(userActions.setPrimeLimit(profile.prime_limit));
        }

        if (profile.masspay_data) {
          store.dispatch(userActions.setMassPayData(profile.masspay_data));
          store.dispatch(
            userActions.setMassPayIsCreated(profile.masspay_data.is_activated)
          );
        }

        store.dispatch(userActions.setConsumables(profile.consumables));

        store.dispatch(
          notificationActions.toggleNotificationBanner(
            profile?.notification_setting?.notification_banner
          )
        );
        store.dispatch(
          notificationActions.toggleNotificationSound(
            profile?.notification_setting?.notification_sound
          )
        );

        const initialAccount = {
          name: profile.display_name,
          pic: profile.avatar[0],
          id: profile?.username,
          type: "self",
          role: profile.user_type.toLowerCase(),
        };
        store.dispatch(userActions.setUserProfile(profile));

        store.dispatch(userActions.setFetched(true));

        !switchAccount?.name &&
          store.dispatch(defaultActions.setSwitchAccount(initialAccount));

        // profile.recent_followings.forEach((uid) => {
        //   store.dispatch(connectionsActions.addFollowing(uid));
        // });

        profile?.subscribed?.forEach((uid) => {
          store.dispatch(connectionsActions.addSubscription(uid));
        });

        if (
          profile.user_type.toLowerCase() === UserRole.CREATOR &&
          currentUserState?.role !== UserRole.CREATOR
        ) {
          GetCreatorToken().then((creatorRes) => {
            if (currentUserState?.token == creatorRes?.data?.token) return;

            store.dispatch(
              userDataActions.setCreatorStatus({ creatorStatus: false } as any)
            );

            store.dispatch(
              userDataActions.setUserMeta({ token: creatorRes?.data?.token })
            );
            store.dispatch(userActions.setUserToken(creatorRes.data?.token));

            // Swal.fire({
            //   icon: "success",
            //   title: "You are now creator!",
            //   text: "We will review and send a mail to you soon. Thanks!",
            //   confirmButtonText: "Finish",
            //   confirmButtonColor: "#AC1991",
            //
            //   showCloseButton: true,
            // });
          });
        }

        GetGlobalNotifications().then((res) => {
          store.dispatch(notificationActions.setNotificationSettings(res.data));
        });

        resolve(profile);
      })
      .catch(reject);
  }) as Promise<UserProfile>;
};

export const GetKYCSessionLink = async () => {
  return API.get(`${API.PAYOUTS}/kyc`) as Promise<Record<string, any>>;
};

interface KYCSessionResponse {
  status: "string";
  message: string;
  data: {
    session_id: string;
  };
}

export const GetKYCSessionId = async () => {
  return API.post(`${API.USERS}/kyc`, {}) as Promise<KYCSessionResponse>;
};

export const GetMassPayActivationLink = async () => {
  return API.get(`${API.PAYOUTS}/activation_link`);
};

interface UserProfileByUsernameResponse {
  data: UserProfile[];
}

export const GetUserProfileByUsername = async (username: string) =>
  API.get(
    `${API.USER_PROFILE}/${username}`
  ) as Promise<UserProfileByUsernameResponse>;

export const GetUserProfileById = async (id: string) =>
  API.get(
    `${API.USER_PROFILE}/${id}?type=id`
  ) as Promise<UserProfileByUsernameResponse>;

// interface GetCreatorTokenResponse {
//   data: UserProfile;
// }

export const GetCreatorToken = async () => {
  // const headers = {
  //   Authorization: `Bearer ${token}`,
  // };
  // const response = await API.get(API.RENEW_TOKEN);
  let response;

  try {
    response = await fetch(`${process.env.backend}/v1/users/renew-token`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${store.getState().user.refreshToken}`,
      },
    });
    const data = await response.json();

    if ([200, 201].includes(data.status)) {
      // Swal.fire({
      //   icon: "success",
      //   title: "You are now creator!",
      //   text: "We will review and send a mail to you soon. Thanks!",
      //   confirmButtonText: "Finish",
      //   confirmButtonColor: "#AC1991",
      //
      //   showCloseButton: true,
      // }).then((result: { isConfirmed: boolean }) => {
      //   if (result.isConfirmed) {
      //     window.location.reload();
      //   }
      // });
    }

    // localStorage.setItem("token", data.data.token);
    store.dispatch(userActions.setUserToken(data.data.token));
    store.dispatch(userActions.setUserRefreshToken(data.data.refresh_token));
    // localStorage.setItem("refreshToken", data.data.refresh_token);

    return data;
  } catch (error) {
    console.error("Error fetching data:", error);
  }
};

export interface UserCompleteProfileBody {
  country: string;
  preferences: {
    gender: string;
    kinks: string[];
    lifestyle_topics: string[];
  };
  gender: string;
  avatar: File | string;
  background: File | string;
}

export const Getcountries = async () => {
  return API.get(`${API.SYSTEM_DEFAULTS}/countries?limit=500`);
};

export const UserCompleteProfile = async (body: UserCompleteProfileBody) => {
  const form = new FormData();

  form.append("country", body.country);
  form.append("preferences", JSON.stringify(body.preferences));
  form.append("gender", body.gender);
  body.avatar && form.append("avatar", body.avatar);
  body.background && form.append("background", body.background);

  return API.patch(API.USER_COMPLETE_PROFILE, form);
};

export interface UserBecomeCreatorBody {
  type: string;
  body: {
    country: string;
    tax_number: string;
    insurance_number: string;
    social_handles: { platform: string; handle: string }[];
    reason: string;
  };

  tnc_accepted: boolean;
}

export const UserBecomeCreator = async (body: Record<string, any>) =>
  API.post(API.BECOME_A_CREATOR, body) as Promise<any>;

export interface GetUserFollowersBody {
  username: string;
}

interface GetUserFollowersResponse {
  data: any;
}

export const GetUserFollowers = async (uid?: string) =>
  API.get(
    `${API.USER_FOLLOWERS}?target_user_id=${uid}`
  ) as Promise<GetUserFollowersResponse>;

export interface GetUserFollowingBody {
  username: string;
}

interface UserFollowing {
  _id: string;
  f_name: string;
  l_name: string;
  username: string;
  avatar: Media[];
  role: string;
  display_name: string;
}

interface GetUserFollowingResponse {
  data: UserFollowing[];
}

export const GetUserFollowing = async (uid?: string) =>
  API.get(
    `${API.USER_FOLLOWING}${uid ? `?target_user_id=${uid}` : ""}`
  ) as Promise<GetUserFollowingResponse>;

export interface UserFollowResponse {
  message: "success" | "Already following";
  status: 200 | 400;
}

export const UserFollow = async (uid: string) =>
  API.post(API.USER_FOLLOW, {
    target_user: uid,
  }) as Promise<UserFollowResponse>;

export interface UserUnFollowResponse {
  message: "success" | string;
  status: 200 | 400;
}

export const UserUnFollow = async (uid: string) =>
  API.delete(API.USER_UN_FOLLOW, {
    target_user: uid,
  }) as Promise<UserUnFollowResponse>;

export interface GetFeaturedUserProfileResponse {
  data: UserProfile[];
}

export const GetFeaturedUserProfiles = async (page: number) =>
  API.get(
    `${API.USER_FEATURED_PROFILE}?limit=18&page=${page}`
  ) as Promise<GetFeaturedUserProfileResponse>;

export interface EditProfile {
  avatar?: File;
  background?: File;
  delete_background?: boolean;
  delete_avatar?: boolean;
}

export interface GetEditProfileResponse {
  data: EditProfile;
}

export const EditAvatarAndBackground = async (body: EditProfile) => {
  const form = new FormData();
  body.avatar && form.append("avatar", body.avatar);
  body.background && form.append("background", body.background);
  body.delete_avatar &&
    form.append("delete_avatar", JSON.stringify(body.delete_avatar));
  body.delete_background &&
    form.append("delete_background", JSON.stringify(body.delete_background));
  return API.patch(
    API.USER_EDIT_PROFILE,
    form
  ) as Promise<GetEditProfileResponse>;
};

export const getUserProfile = async (
  username: string,
  limit: number = 20,
  page: number = 1
) =>
  API.get(
    `${API.SEARCH_USER}?key=${username}&limit=${limit}&page=${page}`
  ) as Promise<any>;

export const postWishList = async (body: any) =>
  API.post(API.WISHLIST, body) as Promise<any>;

export const getWishListUsers = async (id: string) =>
  API.get(
    API.WISHLIST + `?limit=100&page=1&target_user_id=${id}`
  ) as Promise<any>;

export const getWishList = async () =>
  API.get(API.WISHLIST + "?limit=100&page=1");

export const getSavedPostsData = async (page: any) =>
  API.get(API.USERS + `/save-post?limit=20&page=${page}`);

interface ChatFee {
  type: "FEE" | "FREE";
  price: number;
  is_active: boolean;
  _id: string;
}

interface SpecialOption {
  type: "VOICE" | "VIDEO" | "RATING";
  price: number;
  is_active: boolean;
  _id: string;
}

enum AutoMessageType {
  NEW_SUBSCRIBER = "NEW-SUBSCRIBER",
  NEW_TIP = "NEW-TIP",
  NEW_PREMIUM_UNLOCK = "NEW-PREMIUM-UNLOCK",
  PRODUCT_SELL = "PRODUCT-SELL",
  TICKET_BOOKING = "TICKET-BOOKING",
  REQUEST = "REQUEST",
}

interface AutoMessage {
  type: AutoMessageType;
  message: string;
  is_active: boolean;
  media: any[];
  _id: string;
}

interface UserProfileSettingsResponse {
  chat_fee: ChatFee[];
  special_option: SpecialOption[];
  auto_message: AutoMessage[];
}

export const getUserProfileSettings = async (userId: string) =>
  API.get(API.PROFILE_SETTING + `?target_user=${userId}`) as Promise<{
    data: UserProfileSettingsResponse;
  }>;

export interface DeviceInfo {
  device_id?: string;
  device: string;
  os: string;
  browser: string;
  country: string;
  city: string;
  jti: string;
  issued_on: string;
  expires_on: string;
  _id: string;
}

export const loggedInDevices = async () =>
  API.get(API.USERS + `/logged-in-devices`) as Promise<any>;

export interface DirectMessageBody {
  channel_id: string;
  message: string;
  media: { path: string; type: "image" | "video" | "audio" }[];
  vault_media_ids?: string[];
}

export const SendDirectMessage = async (messageBody: DirectMessageBody) => {
  const jsonData: Record<string, any> = {
    channel_id: messageBody.channel_id,
    message: messageBody.message,
  };

  if (messageBody.media) {
    jsonData.media = messageBody.media;
  }

  if (
    Array.isArray(messageBody.vault_media_ids) &&
    messageBody.vault_media_ids.length > 0
  ) {
    jsonData.vault_media_ids = messageBody.vault_media_ids;
  }

  return API.post(API.USERS + `/direct-message`, jsonData) as Promise<any>;
};

export const ChatMedia = async ({
  media,
  message_text,
  meta,
  channel_id,
  vault_media_ids,
  chat_fee_id,
  payment_reminder = false,
  deduct_count = false,
}: {
  media?: { path: string; type: "image" | "video" | "audio" }[];
  message_text: string;
  meta: Record<string, any>;
  channel_id: string;
  setProgress?: (progress: number) => void;
  vault_media_ids?: string[];
  chat_fee_id?: string;
  browser?: {
    name: string;
    os: string;
  };
  deduct_count?: boolean;
  payment_reminder?: boolean;
}) => {
  const jsonData: {
    message_text: string;
    meta: Record<string, any>;
    channel_id: string;
    media?: { path: string; type: "image" | "video" | "audio" }[];
    vault_media_ids?: string[];
    chat_fee_id?: string;
    deduct_count?: boolean;
    payment_reminder?: string;
  } = {
    message_text,
    meta,
    channel_id,
  };

  if (media) jsonData.media = media;
  if (vault_media_ids) jsonData.vault_media_ids = vault_media_ids;
  if (chat_fee_id) jsonData.chat_fee_id = chat_fee_id;
  if (deduct_count) jsonData.deduct_count = deduct_count;
  if (payment_reminder !== undefined)
    jsonData.payment_reminder = JSON.stringify(payment_reminder ?? false);

  return API.post(`${API.USERS}/chat-media`, jsonData) as Promise<any>;
};

export async function createMediaPreview(file: File): Promise<string> {
  try {
    if (file.type.startsWith("image/")) {
      return URL.createObjectURL(file);
    } else if (file.type.startsWith("video/")) {
      return await captureVideoThumbnail(file);
    }

    throw new Error("Unsupported media type");
  } catch (error) {
    console.error("Error creating media preview:", error);
    return "/images/common/defaultBack.svg";
  }
}

async function captureVideoThumbnail(file: File): Promise<string> {
  return new Promise((resolve) => {
    const video = document.createElement("video");
    video.src = URL.createObjectURL(file);
    video.currentTime = 2;
    video.muted = true;
    video.playsInline = true;

    video.onloadeddata = () => {
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const context = canvas.getContext("2d");

      if (context) {
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(URL.createObjectURL(blob));
          } else {
            console.error("Failed to create thumbnail blob");
            resolve("/images/common/defaultBack.svg");
          }
        });
      } else {
        console.error("Failed to capture video frame");
        resolve("/images/common/defaultBack.svg");
      }
    };

    video.onerror = () => {
      console.error("Error loading video for thumbnail");
      resolve("/images/common/defaultBack.svg");
    };
  });
}

export const UpdateMediaMessage = async ({
  media_id,
  channel_id,
  creation_time,
  message_id,
  meta,
}: {
  media_id: string;
  channel_id: string;
  creation_time: string;
  message_id: string;
  meta: Record<string, any>;
}) => {
  return API.patch(API.USERS + `/chat-media/${media_id}`, {
    channel_id,
    creation_time,
    message_id,
    meta,
  }) as Promise<any>;
};

export const BlockUser = async (target_user: string) => {
  return API.post(API.USERS + "/block-user", {
    target_user,
  }) as Promise<{ data: BlockedUser }>;
};

export const UnblockUser = async (target_user: string) => {
  return API.post(API.USERS + "/unblock-user", {
    target_user,
  }) as Promise<any>;
};

export const GetBlockedUsers = async () => {
  return API.get(API.USERS + "/blocklist") as Promise<{
    data: { blocked_profiles: BlockedUser[]; blockers_ids: string[] };
  }>;
};

export const ForgotPassword = async ({ email }: { email: string }) => {
  return API.post(API.USERS + "/forgot-password", {
    email,
  }) as Promise<any>;
};

export const VerifyResetPassword = async ({
  new_password,
  confirm_new_password,
  token,
}: {
  new_password: string;
  confirm_new_password: string;
  token: string;
}) =>
  API.post(API.USERS + `/verify-forgot-password?token=${token}`, {
    new_password,
    confirm_new_password,
  }) as Promise<any>;

export const GetUserLocation = async () => {
  return API.get(API.USERS + `/location-info`) as Promise<any>;
};

export const GetReferrals = async () =>
  API.get(API.USERS + "/my-referrals") as Promise<any>;

export const GetUserTags = async () =>
  API.get(API.USERS + "/tags?limit=20") as Promise<any>;

export const SoundAndBannerSettings = async ({
  notification_sound,
  notification_banner,
}: {
  notification_sound: string | boolean;
  notification_banner: string | boolean;
}) => {
  return API.patch(API.USERS + "/notification-setting", {
    notification_sound,
    notification_banner,
    alert_type: "All",
  }) as Promise<any>;
};

export const DeleteAccount = (body: any) => {
  return API.post(API.USERS + "/delete-profile", body) as Promise<any>;
};

export interface BillingAddressInterface {
  full_address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  is_default?: boolean;
  country_code?: number | string;
}

export const AddBillingAddress = (body: BillingAddressInterface) => {
  return API.post(`${API.USERS}/address`, {
    ...body,
    type: "Billing",
  }) as Promise<{
    data: {
      address_id: string;
    };
  }>;
};

export const GetTaxInformation = async () => {
  return API.get(`${API.USERS}/get-tax-info`) as Promise<any>;
};

export const GetCreatorsWithKinks = async (hashtags: string[]) =>
  API.get(
    API.USERS + `/creators-cards?hashtags=${JSON.stringify(hashtags)}&limit=20`
  ) as Promise<GetFeaturedUserProfileResponse>;

export const UpdateUsedOnce = async () =>
  API.post(API.USERS + "/used-once", {}) as Promise<any>;

export const ForceChangePassword = async (new_password: string) =>
  API.post(API.USERS + "/force-change-password", {
    new_password,
  });

export const UpdateDOB = (date: Date) =>
  API.post(API.USERS + "/update-dob", {
    dob: date.toUTCString(),
  }) as Promise<any>;

export const GetGlobalNotifications = () => {
  return API.get(API.USERS + "/global-notification") as Promise<{
    data: {
      new_likes_and_interaction: GlobalNotification;
      new_comments_and_replies: GlobalNotification;
      new_mentions: GlobalNotification;
      new_matches: GlobalNotification;
      new_content_from_my_subscriptions: GlobalNotification;
      new_content_from_following: GlobalNotification;
    };
  }>;
};

export const UpdateGlobalNotifications = (body: NotificationSettings) => {
  return API.patch(API.USERS + "/global-notification", body) as Promise<any>;
};

interface AcceptOfferBody {
  claim_id: string;
  action: "Accepted";
  payment_mode: string;
  tokenised_card_id?: string;
}

export const AcceptOffer = (body: AcceptOfferBody) => {
  return API.post(API.USERS + "/respond-to-offer", body) as Promise<any>;
};

export const MarkOfferSeen = (offer_id: string) => {
  return API.patch(
    API.USERS + `/acknowledge-offer-invitation/${offer_id}`,
    {}
  ) as Promise<any>;
};

export const CalculateRefundAmount = (body: any) => {
  return API.post(API.USERS + "/calculate-refund", body) as Promise<any>;
};

export const GetUserOffers = () => {
  return API.get(API.USERS + "/get-claimed-offers") as Promise<any>;
};

export const GetUserOffersById = (id: any) => {
  return API.get(API.ADMIN + `/offers/${id}`) as Promise<any>;
};

interface SocialSignInUser {
  firstName: string;
  lastName: string;
  email: string;
  federatedId: string;
  provider: "google" | "facebook" | "twitter";
  idToken: string;
  email_verified: boolean;
  device_info: {
    device_id: string;
    device: string;
    os: string;
    browser: string;
    country: string;
    city: string;
  };
  otp_id?: string;
  otp?: string;
}

export const SocialLogin = (body: SocialSignInUser) =>
  API.post(`${API.USERS}/social-login`, body) as Promise<UserRegisterResponse>;

export const SocialSignUp = (
  body: SocialSignInUser & {
    user_type: "CREATOR" | "USER";
    access_token: string;
    access_token_expires_at: string;
    refresh_token: string;
    refresh_token_expires_at: string;
  }
) =>
  API.post(`${API.USERS}/social-signup`, body) as Promise<UserRegisterResponse>;

export interface SocialsInterface {
  _id: string;
  user: string;
  email: string;
  email_verified: boolean;
  f_name: string;
  l_name: string;
  provider: string;
  federatedid: string;
  is_primary?: boolean;
  access_token: string;
  access_token_expires_at: string; // Consider changing to `Date` if you parse it
  refresh_token: string;
  refresh_token_expires_at: string; // Consider changing to `Date` if you parse it
  is_deleted: boolean;
  created_at: string; // Consider changing to `Date` if you parse it
  updated_at: string; // Consider changing to `Date` if you parse it
  __v: number;
}

export const GetSocialAccounts = () =>
  API.get(API.USERS + "/social-linked-accounts") as Promise<{
    data: SocialsInterface[];
  }>;

interface ConnectSocialAccountBody {
  federatedid: string;
  provider: "google" | "facebook" | "twitter" | "linkedin";
  email: string;
  email_verified: boolean;
  f_name: string;
  l_name: string;
  idToken: string;
  access_token: string;
  access_token_expires_at: string;
  refresh_token: string;
  refresh_token_expires_at: string;
  otp?: number | string;
  otp_id?: string;
}

export const ConnectSocialAccount = (body: ConnectSocialAccountBody) =>
  API.post(API.USERS + "/social-linked-accounts", body) as Promise<any>;

export const DeleteSocialAccount = (account_id: string) =>
  API.delete(
    API.USERS + `/social-linked-accounts/${account_id}`
  ) as Promise<any>;

export const GetReelProfile = async (
  inputs: Record<string, any>
): Promise<any> => {
  const { user_id, channel_id, group_id } = inputs;
  return API.get(
    API.USERS +
      `/reels?page=1&limit=10${user_id ? `&user_id=${user_id}` : ""}${
        channel_id ? `&channel_id=${channel_id}` : ""
      }${group_id ? `&group_id=${group_id}` : ""}`
  );
};

export const GetPreviousUsername = async () => {
  return API.get(API.USERS + "/unique-username") as Promise<{
    data: {
      _id: string;
      username: string;
      status: "Active" | "Reserved" | "Realeased";
      user: {
        _id: string;
        change_username?: { change_to_new: string; revert_back: string };
      };
    }[];
  }>;
};

export const ModifyUsername = (body: {
  username?: string;
  action: "change" | "revert";
  user: string;
}) => {
  return API.post(API.USERS + "/change-username", body) as Promise<any>;
};

export const SetOrRemoveFCMToken = (body: {
  fcm_token: string;
  device_id: string;
  action: "add" | "remove";
}) => API.patch(API.USERS + "/fcm-token", body) as Promise<any>;

export const GetFCMToken = (device_id: string) =>
  API.get(API.USERS + `/fcm-token?device_id=${device_id}`) as Promise<{
    FcmTokenDeviceDetails: {
      _id: string;
      device_id: string;
      device: string;
      os: string;
      browser: string;
      country: string;
      city: string;
      jti: string;
      issued_on: string;
      expires_on: string;
      fcm_token?: string;
    };
  }>;

export const GetSignedUrl = (props: {
  assets_path: string[];
  type?: "signedPath" | "signature" | "signedFullUrl";
  skipAuth?: boolean;
  customHeader?: Record<string, string>;
}) => {
  return API.post(
    API.USERS + "/generate-signed-urls",
    {
      assets_path: props.assets_path,
      type: props.type,
    },
    { ...props.customHeader },
    props.skipAuth ? true : false
  ) as Promise<{
    result: Record<string, string>;
  }>;
};

interface VerifyTagAndRealeaseBody {
  email: string;
  otp_id?: string;
  otp?: string;
}

export const VerifyTagAndRealease = (body: VerifyTagAndRealeaseBody) => {
  return API.post(
    API.USERS + "/release-form-validate",
    body,
    {},
    true
  ) as Promise<any>;
};

export const GetTagReleaseKYCSessionId = (email: string, id: string) => {
  return API.post(
    API.USERS + "/taguser-kyc",
    { email, id },
    {},
    true
  ) as Promise<any>;
};

export const GetTagAndReleaseInfo = (id: string) => {
  return API.get(
    API.USERS + `/release-form-creator-details?id=${id}`,
    {},
    true
  ) as Promise<any>;
};

export const GenerateReleaseForm = (body: { username: string[] }) => {
  return API.post(API.USERS + "/release-form", body) as Promise<{
    data: ReleaseFormsResponse[];
  }>;
};

export interface ReleaseFormsResponse {
  _id: string;
  friendly_name: string;
  email_verified: boolean;
  form_id: string;
  kyc_status: string;
  status: "success" | "pending" | "rejected" | "cancelled";
  attempt: number;
  expires_on: string;
  exist_user_id?: {
    _id: string;
    user_type: "CREATOR" | "USER";
    username: string;
    display_name: string;
    email: string;
  };
}

export const GetReleaseForms = (type?: string) => {
  return API.get(
    API.USERS + `${`/release-form${type ? `?type=${type}` : ""}`}`
  ) as Promise<ReleaseFormsResponse[]>;
};

export const PreVerifiedSignUp = ({ kyc_id }: { kyc_id: string }) => {
  return API.post(
    API.USERS + "/sign-up/preverified",
    { kyc_id },
    {},
    true
  ) as Promise<{
    message: string;
    ot: string;
    status: number;
  }>;
};

interface Internaltagconsent {
  user: UserProfile;
  status: string;
}
export interface GetContentListResponse {
  _id: string;
  created_at: string;
  media: {
    _id: string;
    type: "image" | "video";

    path: string;
    name: string;
    status: string;
    consent: {
      _id: string;
      internal_tag_consent: Internaltagconsent[];
      external_tag_consent: any[];
    };
    variations: Variation[];
    signature?: Partial<Record<Variation, string>>;
    original_path: string;
    signed_urls?: {
      [key in Variation]: string;
    };
    poster?: {
      _id: string;
      path: string;
      signature?: Partial<Record<Variation, string>>;
      variations: Variation[];
      resolution?: { width: number; height: number };
      moderation_flags: string[];
      is_autogenerated?: boolean;
      signed_urls?: {
        [key in Variation]: string;
      };
    };
    resolution?: { width: number; height: number };
    duration?: number;
    moderation_flags?: string[];
    used_as: UsedAs;
    post?: string;
    is_author_present?: boolean;
    people_count?: number;
  }[];
  author: UserProfile;
  post?: string;
}

export const GetTagContent = (
  page: number,
  isMe: "All owners" | string,
  status: "Pending" | "Accepted" | "Rejected" | "All status" | string,
  source: "vault" | "post" | "All" | string,
  contentType: "deleted" | "available" | "All types" | string,
  dateFilter: string,
  id?: string
) => {
  return API.get(
    API.USERS +
      `/media-consent?page=${page}${
        isMe !== "All owners" ? "&owner=self" : ""
      }${status !== "All status" ? `&status=${status}` : ""}${
        source !== "All" ? `&source=${source}` : ""
      }${contentType !== "All types" ? `&content_type =${contentType}` : ""}${
        dateFilter ? `&${dateFilter}` : ""
      }${id ? `&collaborator_id=${id}` : ""}`
  ) as Promise<GetContentListResponse[]>;
};

export interface GetTagCollaboratorsResponse {
  _id: string;
  approved: number;
  pending: number;
  rejected: number;
  sender: string;
  receiver: string;
  unique_id: string;
  created_at: string;
  collaborator: UserProfile;
}

export const GetTagCollaborators = (page: number, search?: string) => {
  return API.get(
    API.USERS +
      `/media-consent-relation?page=${page}${search ? `&search=${search}` : ""}`
  ) as Promise<GetTagCollaboratorsResponse[]>;
};

export const GetExternalTagInformation = (body: {
  email: string;
  otp_id?: string;
  otp?: string;
}) =>
  API.post(
    API.USERS + "/external-user-validation",
    body,
    {},
    true
  ) as Promise<any>;
