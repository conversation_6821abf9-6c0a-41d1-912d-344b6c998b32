"use client";

import Image from "next/image";
import { memo, useState } from "react";

import { UserFollow, UserUnFollow } from "@/api/user";
import ActionButton from "@/components/common/action-button";
import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";

interface Follow {
  visible?: boolean;
  cls?: string;
  author: string;
  followed_by_you?: boolean;
  white_variant?: boolean;
  setFollowersCount?: any;
  from?: string;
  onlyIcon?: boolean;
}

const Follow = ({
  cls,
  visible,
  author,
  followed_by_you,
  white_variant,
  setFollowersCount,
  from = "post",
  onlyIcon,
}: Follow) => {
  const [isFollowing, setFollowing] = useState<boolean | undefined>(
    followed_by_you
  );
  const [loading, setLoading] = useState(false);
  const isMobile = window.innerWidth < 768;
  const userId = useAppSelector((state) => state.user.id);
  const role = useAppSelector((state) => state.user.role);

  const onClick = () => {
    if (role === "guest" || loading) {
      ModalService.open("SIGN_IN");
      return;
    }

    setLoading(true);

    if (isFollowing) {
      UserUnFollow(author)
        .then(() => {
          setLoading(false);
          setFollowing(false);
          setFollowersCount((count: number) => count - 1);
        })
        .catch((error) => {
          console.error(error);
          setLoading(false);
        });
    } else {
      UserFollow(author)
        .then(() => {
          setLoading(false);
          setFollowing(true);
          setFollowersCount((count: number) => count + 1);
        })
        .catch((err) => {
          setLoading(false);
          console.error(err);
        });
    }
  };

  if (author === userId || !visible) return <></>;

  return onlyIcon ? (
    <Image
      alt=""
      src={"/images/common/follow-icon.svg"}
      width={isMobile ? 18 : 40}
      height={isMobile ? 18 : 40}
      style={{ filter: isFollowing ? "grayscale(1)" : "none" }}
      onClick={onClick}
      className="pointer"
    />
  ) : (
    <ActionButton
      className={`w-100 ${cls}`}
      onClick={onClick}
      type={white_variant ? (isFollowing ? "outline" : "solid") : "outline"}
      variant={isFollowing ? (from === "reel" ? "white" : "dark") : "info"}
    >
      {isFollowing ? "Following" : "Follow"}
    </ActionButton>
  );
};

export default memo(Follow);
