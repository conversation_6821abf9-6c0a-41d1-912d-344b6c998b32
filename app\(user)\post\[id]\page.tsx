"use client";

import Image from "next/image";
import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useInView } from "react-intersection-observer";
import Swal from "sweetalert2";
import "./index.scss";

import { SeatReservation } from "@/api/event";
import {
  type BadgesType,
  type GetPost,
  GetPostComments,
  GetSinglePost,
} from "@/api/post";
import Background from "@/app/(user)/(sections)/create/new-post/[[...id]]/background";
import { PollBox } from "@/app/(user)/(sections)/create/new-post/[[...id]]/poll";
import ActionButton from "@/components/common/action-button";
import Badges from "@/components/common/badges";
import Follow from "@/components/common/buttons/follow";
import type { ModalRef } from "@/components/common/modal";
import LiveEvent from "@/components/common/post/live-event";
import BuyTicketModal from "@/components/common/post/modal/buy-ticket";
import PremiumPurchase from "@/components/common/post/modal/premium";
import PostHead from "@/components/common/post/post-head";
import PostInteractions from "@/components/common/post/post-interactions";
import SellShopItem from "@/components/common/sell-item";
import Shimmer from "@/components/common/shimmer";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import usePauseOnVisibilityChange from "@/hooks/usePauseOnVisibilityChange";
import {
  chatActions,
  configActions,
  defaultActions,
  liveEventActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { store } from "@/redux-store/store";
import { type Media } from "@/types/media";
import type {
  Author,
  Post,
  PostActions,
  PostComment,
  PostPoll,
  PostType,
  TaggedChannel,
  TaggedCollab,
} from "@/types/post";
import type { SellItem } from "@/types/sell-item";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";
import { formatDurationToMinSec } from "@/utils/number";
import "swiper/css";
import "swiper/css/navigation";

import NotFound from "@/app/not-found";
import type { PlyrRef } from "@/components/common/plyr";
import { PlyrPlayer } from "@/components/common/plyr";
import { Resolution } from "@/components/common/post";
import ContentWarning from "@/components/common/post/content-warning";
import PostDetails from "@/components/common/post/post-details";
import Error500 from "@/components/misc/500";
import { ModalService } from "@/components/modals";
import { checkVerification } from "@/utils/check-verification";

import { MultipleMedia } from "./multipleMedia";

interface sendTicketBody {
  postId: string;
  eventId: string;
  author: Author;
  price: number;
}

const PostComponent = () => {
  const router = useRouter();

  const id = useParams()?.id as string;
  const swiper_id = `swiper-${id}`;
  const [details, setDetails] = useState<Post>();
  const [details2, setDetails2] = useState<GetPost>();
  const plyrRef = useRef<PlyrRef>(null);
  const [actions, setActions] = useState<PostActions>();
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const buyTicketModalRef = { method: {} as ModalRef };
  const addCardModalRef = { method: {} as ModalRef };
  const [comments, setComments] = useState<PostComment[]>([]);
  const UserId = useAppSelector((state) => state.user.id);

  const videoRef = useRef<HTMLVideoElement>(null);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [localPurchased, setLocalPurchased] = useState(false);
  const [showShimmer, setShowShimmer] = useState(true);
  const [error, setError] = useState<500 | 404 | null>(null);

  useEffect(() => {
    dispatchAction(configActions.toggleShowPositionWidget());

    return () => {
      dispatchAction(configActions.toggleShowPositionWidget());
    };
  }, []);

  useEffect(() => {
    GetSinglePost(id)
      .then((res) => {
        setShowShimmer(false);
        setDetails(res.data[0]);
        setDetails2(res.data[0]);
        setSoldCount(res?.data[0]?.event?.sold_ticket_count);

        const actionsObj = {
          loved: {
            by: "",
            count: res?.data[0]?.likes?.length || 0,
            active:
              res?.data[0]?.likes?.includes(store?.getState()?.user?.id) ||
              false,
          },
          save: 0,
          comments: res?.data[0]?.counter?.comments,
          shares: 0,
        };

        setActions(actionsObj);
      })
      .catch((err) => {
        setShowShimmer(false);
        setError(err.statusCode);
      });
  }, []);

  const handleShowMore = () => {
    setPage((prevPage) => prevPage + 1);
  };

  useEffect(() => {
    fetchComments(page);
  }, [page]);

  const fetchComments = (page: number) => {
    setLoading(true);
    GetPostComments(id, page)
      .then((res) => {
        setLoading(false);
        console.log("Comments response: ", res.data);
        const comments = res.data.map((comment: any) => ({
          _id: comment?._id,
          author: {
            _id: comment?.author?._id,

            username: comment.author.username,
            pic: getAssetUrl({
              media: comment.author.avatar?.[0],
              defaultType: "avatar",
            }),
            display_name: comment?.author?.display_name,
            role: comment.author.user_type.toLowerCase(),
            badges: comment.author?.badges,
          },
          hearts: comment?.counter?.reactions || 0,
          text: comment.text,
          ts: new Date(comment.created_at).getTime(),
          is_liked: comment?.likes?.length != 0,
          replies: comment?.replies?.map((reply: any) => ({
            _id: reply._id,
            author: {
              _id: reply?.author?._id,
              username: reply?.author?.username,
              pic: getAssetUrl({
                media: reply?.author?.avatar?.[0],
                defaultType: "avatar",
              }),
              display_name: reply?.author?.display_name,
              role: reply?.author?.user_type?.toLowerCase(),
              badges: reply?.author?.badges,
            },
            text: reply.text,
            hearts: reply?.counter?.reactions || 0,
            ts: new Date(reply.created_at).getTime(),
            is_liked: reply?.likes?.length != 0,
          })),
        }));

        if (comments.length < 10) {
          setHasMore(false);
        }

        setComments((prevComments) => [...prevComments, ...comments]);
      })

      .catch(() => setLoading(false));
  };

  const [showBuyTicketModal, setBuyTicketModal] = useState(false);
  const profile = useAppSelector((state) => state?.user?.profile);
  const [seatReserved, setSeatReserved] = useState<boolean>(
    details?.seat_reserved!
  );

  const [soldCount, setSoldCount] = useState<number>(
    details?.event?.sold_ticket_count!
  );

  const dispatchAction = useAppDispatch();
  useEffect(() => {
    const ticketDetails: sendTicketBody = {
      postId: details2?._id!,
      eventId: details2?.event?._id!,
      author: details?.author!,
      price: details2?.event?.price!,
    };

    showBuyTicketModal &&
      dispatchAction(defaultActions.setTicketDetails(ticketDetails));
    showBuyTicketModal && buyTicketModalRef.method.open();
    showAddCardModal && addCardModalRef.method.open();

    // currentPostId = postId!;
  }, [showBuyTicketModal || showAddCardModal]);

  const notMyPost = profile._id !== details?.author._id;

  const [isLivePost, setIsLivePost] = useState<boolean>(
    details2?.type === "Event"
  );
  const postDetails = {
    _id: details2?._id!,
    author: {
      _id: details?.author?._id!,
      badges: details?.author?.badges as BadgesType,
    },
  };

  useEffect(() => {
    setIsLivePost(details2?.type === "Event");
    console.log("whats the TYP: ", details2?.type);
  }, [details2?.type]);

  const postSource = {
    postFrom: details?.channels?.length
      ? "Channel"
      : details?.groups?.length
      ? "Group"
      : "Normal",
    id: details?.channels?.length
      ? details?.channels[0]?.channel_tagname
      : details?.groups?.length
      ? details?.groups[0]?.tag_name
      : details?.author?.username,
  };

  const showSubscribe: boolean = details?.channels?.length
    ? true
    : details?.groups?.length
    ? true
    : details?.min_subscription?._id
    ? true
    : false;

  const PostCaption = ({
    desc,
    backgroundColor,
    is_purchased,
    type,
  }: {
    desc: string;
    backgroundColor: string;
    post_type?: string;
    is_purchased: boolean;
    type: string;
  }) => {
    const handleHashtagClick = (hashtag: string) => {
      window.location.href = `/interested?hashtag=${encodeURIComponent(
        hashtag
      )}`;
    };

    const handleTaggedClick = (tag: string) => {
      router.push(`/creator/${tag}`);
    };

    const handleChannelClick = (channel: string) => {
      router.push(`/channel/${channel}`);
    };

    const handleCollabClick = (collab: string) => {
      router.push(`/collab/${collab}`);
    };

    const renderDescription = (desc: string) => {
      if (!desc) return null;

      const hashtagRegex = /#\w+/g;
      const taggedRegex = /\u200D@\w+/g;
      const channelRegex = /\u200B@[\w.-]+/g;
      const collabRegex = /\u200C@[\w.-]+/g;

      // Split the description into normal text, hashtags, and tagged users
      const parts = desc
        .split(
          new RegExp(
            `(${hashtagRegex.source}|${taggedRegex.source}|${channelRegex.source}|${collabRegex.source})`,
            "g"
          )
        )
        .filter(Boolean) // Remove any empty strings
        .map((part, index) => {
          if (hashtagRegex.test(part)) {
            return (
              <span
                key={`hashtag-${index}`}
                className="hashtag color-primary pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  handleHashtagClick(part.substring(1));
                }}
              >
                &nbsp;{part}&nbsp;
              </span>
            );
          } else if (taggedRegex.test(part)) {
            return (
              <span
                key={`tagged-${index}`}
                className="hashtag color-primary pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  handleTaggedClick(part.substring(2));
                }}
              >
                &nbsp;{part}&nbsp;
              </span>
            );
          } else if (channelRegex.test(part)) {
            return (
              <span
                key={`channel-${index}`}
                className="hashtag color-primary pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  handleChannelClick(part.replace(/\u200B@/g, ""));
                }}
              >
                &nbsp;{part}&nbsp;
              </span>
            );
          } else if (collabRegex.test(part)) {
            return (
              <span
                key={`collab-${index}`}
                className="hashtag color-primary pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCollabClick(part.replace(/\u200C@/g, ""));
                }}
              >
                &nbsp;{part}&nbsp;
              </span>
            );
          } else {
            return part;
          }
        });

      return parts;
    };

    return (
      <div>
        {isLivePost && (
          <h5 className="mt-2 px-3 mb-0">{details?.event?.name}</h5>
        )}

        {!backgroundColor && (
          <div
            id="description"
            className={`post-desc px-3 fs-6 ${type}`}
            style={{ wordBreak: "break-word", fontWeight: "500" }}
          >
            {renderDescription(desc?.trim())}
          </div>
        )}
      </div>
    );
  };

  const LiveVideoPlayer = ({
    src,
    data,
  }: {
    thumbnail: string;
    src: string;

    sold?: number;
    total?: number;
    data?: Post;
  }) => {
    const [play, setPlay] = useState(false);
    const router = useRouter();

    const role = useAppSelector((state) => state?.user?.role);
    const isCardExist = useAppSelector(
      (state) => state?.userData?.cardDetails[0]
    );

    if (play) {
      return (
        <video
          className="post-video-detail w-100 h-100  "
          controls
          src={src}
          onError={() => setPlay((state) => !state)}
        />
      );
    }

    const registerFree = (id: string, body: any) => {
      SeatReservation(id, body)
        .then(() => {
          setTimeout(() => {
            GetSinglePost(details?._id).then((res) => {
              setSeatReserved(res?.data[0]?.seat_reserved);
              setSoldCount(res?.data[0]?.event?.sold_ticket_count);
            });
          }, 100);
          Swal.fire({
            icon: "success",
            title: `You have successfully register for ${data?.author?.display_name}'s event`,
            confirmButtonText: "Finish",
            confirmButtonColor: "#AC1991",

            showCloseButton: true,
          });
        })
        .catch((error) => {
          Swal.fire({
            icon: "error",
            title: error.message,
            confirmButtonText: "Finish",
            confirmButtonColor: "#AC1991",
            showCloseButton: true,
          });
        });
    };

    const goWatch = () => {
      dispatchAction(chatActions.clearStreamChat());
      dispatchAction(liveEventActions.resetLiveEvent());
      router.push(`/events/${data?.event?._id}/live`);
    };

    // return (
    //   <>
    //     <div
    //       className="position-relative topMargin"
    //       style={{ marginTop: "10%" }}
    //     >
    //       <div
    //         className={
    //           data?.event?.is_live_stream
    //             ? `nonLiveStream position-relative`
    //             : "position-relative"
    //         }
    //       >
    //         {/* <div className="position-absolute top-50 start-50 translate-middle p-2 rounded-pill border border-1 bg-dark pointer ">
    //         <Image
    //           width={56}
    //           height={56}
    //           alt="play_icon"
    //           src="/images/common/play.svg"
    //           onClick={() => setPlay((state) => !state)}
    //         />
    //       </div> */}
    //         {!data?.event?.is_live_stream &&
    //           isVideoAsset[
    //             src.substring(
    //               src.lastIndexOf(".") + 1,
    //               src.indexOf("?") === -1 ? src.length : src.indexOf("?")
    //             ) || ""
    //           ] && (
    //             <video
    //               src={src}
    //               controls
    //               autoPlay
    //               muted
    //               playsInline
    //               ref={videoRef}
    //               id="video-post"
    //               className="post-img-detail w-100 h-100  "
    //             />
    //           )}
    //       </div>
    //     </div>
    //   </>
    // );
  };

  const PostContent = ({
    userDetails,
    desc,
    hashtags,

    media,
    preview,
    post_type,
    type,
    shop_item,
    poll,
    backgroundColor,
    textColor,
    pay_and_watch_rate,
    post_id,
    is_purchased,
    is_subscribed,

    shared_on_profile,
    media_categories,
  }: {
    userDetails: { _id: string; author: { _id: string; badges: BadgesType } };
    desc: string;
    hashtags: string[];
    media: Media[];
    preview: Media;
    poll: PostPoll;
    post_type: string;
    type: PostType;
    shop_item?: SellItem;
    backgroundColor: string;
    textColor: string;
    post_id: string;
    pay_and_watch_rate: number;
    is_purchased: boolean;
    is_subscribed: boolean;

    // is_channel_post is the boolean to check if it is channel post or not, using it, if it is channel post then only we are checking for if user subscribed it or not. (To hide posts if channel is not subscribed by the user)

    shared_on_profile: boolean;
    media_categories: string[];
  }) => {
    console.log({ media });
    const [ref, inView] = useInView({
      triggerOnce: true,
    });
    const [showModal, setShowModal] = useState(false);
    const purchaseModalRef = { method: {} as ModalRef };
    const role = useAppSelector((state) => state.user.role);
    const [imagesrc, setImagesrc] = useState(
      getAssetUrl({ media: media?.[0] })
    );
    const [purchased, setPurchased] = useState(is_purchased);

    const router = useRouter();

    const isKycCompleted: boolean = useAppSelector(
      (state) => state.user.profile.kyc.full_kyc_completed
    );
    const isAgeVerified: boolean = useAppSelector(
      (state) => state.user.profile.kyc.age_verification_completed
    );

    const isVerified =
      role === "creator"
        ? isKycCompleted
        : role === "user"
        ? isAgeVerified
        : false;

    const [showWarning, setShowWarning] = useState<boolean>(
      media_categories?.includes("NSFW")
    );

    useEffect(() => {
      role !== "guest" && showModal && purchaseModalRef.method.open();
    }, [showModal]);
    const dispatchAction = useAppDispatch();
    useEffect(() => {
      if (inView && videoRef.current) {
        videoRef.current.play().catch((error) => {
          console.error("Error playing video:", error);
        });
      } else if (videoRef.current) {
        videoRef.current.pause();
      }
    }, [inView, videoRef]);
    usePauseOnVisibilityChange(videoRef);

    const [multiMedia, setMultiMedia] = useState<Media[]>(media);
    const [showCarousel, setShowCarousel] = useState<boolean>(
      multiMedia?.length > 1 &&
        (purchased ||
          details?.has_prime ||
          is_subscribed ||
          (!notMyPost && details?.status === "Completed"))
    );
    const [imageCount, setImageCount] = useState(0);
    const [videoCount, setVideoCount] = useState(0);
    const [totalDuration, setTotalDuration] = useState(0);

    useEffect(() => {
      let duration = 0;

      if (multiMedia) {
        const { imageCount, videoCount } = multiMedia?.reduce(
          (counts, item) => {
            if (item.type === "image") {
              counts.imageCount++;
            } else if (item.type === "video") {
              counts.videoCount++;

              duration += item.duration!;
            }

            return counts;
          },
          { imageCount: 0, videoCount: 0 }
        );
        setTotalDuration(duration);

        setImageCount(imageCount);
        setVideoCount(videoCount);
      }
    }, [multiMedia]);

    const handleHashtagClick = (hashtag: string) => {
      dispatchAction(
        defaultActions.setSelectedTag(encodeURIComponent(hashtag))
      );

      router.push(
        `/interested?hashtag=${encodeURIComponent(hashtag)}&redirect`
      );
    };

    const postPurchased = (data: any) => {
      setImagesrc(`${getAssetUrl({ media: data[0] })}`);
      setPurchased(true);
      setMultiMedia(data);

      if (data.length > 1) {
        setShowCarousel(true);
      }

      // TODO: reloading page because postPurchased not working properly. Need to figure out later.
      setTimeout(() => {
        window.location.reload();
      }, 1200);
    };

    useEffect(() => {
      console.log({ purchased });
    }, [purchased]);

    const handleRedirection = () => {
      if (role === "guest") {
        return;
      }

      if (
        ((type === "Premium" && !purchased) ||
          (type === "Prime" && details && !details.has_prime) ||
          (!shared_on_profile && !is_subscribed)) &&
        notMyPost
      ) {
        return;
      }

      return router.push(`/post/${details?._id.replace(" ", "")}`);
    };

    const paidPost: boolean =
      ((type === "Premium" && !purchased) ||
        (type === "Prime" && !details?.has_prime) ||
        (!shared_on_profile && !is_subscribed)) &&
      notMyPost;

    const hasPrime = useAppSelector((state) =>
      state.user.profile.badges.subscription_badge.includes("Prime")
    );

    // if (details?.author?.account_status === "Banned") {
    //   return (
    //     <div className="position-relative">
    //       <MediaProcessing isBanned={true} />
    //     </div>
    //   );
    // }
    const _id = `swiper-${details?._id}`;

    const options = {
      controls: [
        "rewind",
        "play",
        "fast-forward",
        "progress",
        "current-time",
        "mute",
        "volume",
        "fullscreen",
      ],
      settings: ["quality", "speed"],
      disableContextMenu: true,
      listeners: {
        click: (e: any) => {
          // e.stopPropagation();
          e.preventDefault();

          window.KNKY.showFullscreenMedia("video", imagesrc);

          e.target.controls = !e.target.controls;
        },
      },
    };

    const purchasePremiumPost = async () => {
      const verified = await checkVerification("premium");

      if (verified) {
        if (showModal) {
          setShowModal(false);
          setTimeout(() => {
            setShowModal(true);
          }, 0);
        } else {
          setShowModal(true);
        }
      }
    };

    const buyPrimePost = async () => {
      if (role === "guest") return;
      const verified = await checkVerification("prime");

      if (verified) {
        ModalService.open("PURCHASE_PLANS", { planData: "KnkyPrime" });
      }
    };

    return (
      <>
        <div className={!paidPost ? `pointer` : ""}>
          <div
            ref={ref}
            className="post-content py-1 d-flex flex-column gap-2"
            onClick={() => handleRedirection()}
          >
            {/* <LiveStatusLabel /> */}

            <div className="overflow-hidden position-relative">
              {paidPost && (
                <div className="premium-post-locked " style={{ zIndex: "2" }}>
                  <div className="d-flex flex-column justify-content-center align-items-center gap-4">
                    {!shared_on_profile ? (
                      <Image
                        src="/images/post/fill-unlock.svg"
                        alt="subscription-icon"
                        width={50}
                        height={50}
                      />
                    ) : type === "Prime" ? (
                      <Image
                        src="/images/svg/Prime.svg"
                        alt="prime-icon"
                        width={50}
                        height={50}
                      />
                    ) : (
                      <>
                        <Image
                          src="/images/svg/Premium.svg"
                          alt="premium-icon"
                          width={50}
                          height={50}
                        />
                      </>
                    )}
                    {type === "Premium" ? (
                      <button
                        className={`btn d-block w-100 ${"btn-premium"}`}
                        style={{ zIndex: "3" }}
                        onClick={() => purchasePremiumPost()}
                      >
                        {`Unlock for ${formatCurrency(pay_and_watch_rate)}`}
                      </button>
                    ) : type === "Prime" ? (
                      <ActionButton
                        type="solid"
                        variant="prime"
                        className={`z-3`}
                        onClick={() => buyPrimePost()}
                      >
                        {`Buy Prime`}
                      </ActionButton>
                    ) : (
                      <></>
                    )}
                    <div className="d-flex flex-column justify-content-center gap-1">
                      <div
                        className="text-white  d-flex align-items-center gap-2 justify-content-center"
                        style={{ textWrap: "nowrap" }}
                      >
                        {imageCount != 0 && (
                          <div className="d-flex align-items-center gap-2 justify-content-center">
                            <Image
                              alt=""
                              src={"/images/home/<USER>"}
                              height={24}
                              width={24}
                              style={{ filter: "brightness(2)" }}
                            />

                            {imageCount}
                            {imageCount > 1 ? " images" : " image"}
                          </div>
                        )}

                        {!!videoCount && (
                          <>
                            {!!imageCount && " | "}
                            <div className=" d-flex align-items-center gap-2 justify-content-center ">
                              <Image
                                alt=""
                                src={"/images/home/<USER>"}
                                height={24}
                                width={24}
                                style={{ filter: "brightness(2)" }}
                              />
                              {videoCount}
                              {`${videoCount > 1 ? " videos" : " video"}`}
                            </div>
                          </>
                        )}
                      </div>
                      <div className=" d-flex text-white mt-2 align-items-center gap-2 justify-content-center ">
                        {!!totalDuration &&
                          ` Total: ${formatDurationToMinSec(totalDuration)}`}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {showWarning && details?.media_categories && (
                <ContentWarning category={details?.media_categories} />
              )}
              <div
                className={`post-media   ${
                  showWarning && !isVerified ? "warning-effect" : ""
                }  `}
              >
                {shop_item &&
                shop_item?.media?.length !== 0 &&
                post_type === "Product" ? (
                  <div
                    className="container p-4"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <SellShopItem
                      data={details?.shop_item! || details2?.products}
                      type={"profile"}
                      post_type={post_type}
                      authorId={details?.author?._id}
                    />
                  </div>
                ) : (
                  <>
                    {isLivePost ? (
                      <div
                        onClick={(e) => e.stopPropagation()}
                        className="mb-1"
                      >
                        <LiveEvent data={details!} src={imagesrc} />
                      </div>
                    ) : paidPost && media?.[0] ? (
                      <div className="position-relative">
                        {multiMedia.length === 1 && (
                          <Resolution
                            resolution={media[0].resolution}
                            authorHasPro={details?.author.badges?.subscription_badge.includes(
                              "CreatorPro"
                            )}
                            type={media[0].type}
                            visibility={details2?.visibility!}
                            isPaidSubscription={
                              details?.channels?.[0]?.is_paid ||
                              details?.groups?.[0]?.is_paid
                            }
                          />
                        )}
                        <div
                          className="background-blur"
                          style={{
                            backgroundImage: `url(${getAssetUrl({
                              media: preview || media[0],
                              variation:
                                media?.[0]?.type !== "video" && !preview
                                  ? "blur"
                                  : "compressed",
                              poster:
                                !preview &&
                                media?.[0]?.type === "video" &&
                                media?.[0]?.poster
                                  ? true
                                  : null,
                            })})`,
                            position: "absolute",
                            top: 0,
                            left: 0,
                            width: "100%",
                            height: "100%",
                            backgroundSize: "cover",
                            backgroundPosition: "center",
                            filter: "blur(10px)",
                          }}
                        />
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          width={1280}
                          height={720}
                          className={`post-img-detail position-relative w-100 h-100 ${
                            media[0].type === "video"
                              ? `${
                                  media[0]?.poster &&
                                  (media[0]?.poster.resolution &&
                                  media[0]?.poster.resolution?.width /
                                    media[0]?.poster.resolution?.height <
                                    1.35
                                    ? "post-aspect-ratio"
                                    : details?.preview[0] &&
                                      details?.preview[0]?.resolution &&
                                      details?.preview[0]?.resolution?.width /
                                        details?.preview[0]?.resolution
                                          ?.height <
                                        1.35
                                    ? "post-aspect-ratio"
                                    : "")
                                }`
                              : `${
                                  media[0].resolution &&
                                  media[0].resolution?.width /
                                    media[0].resolution?.height <
                                    1.35
                                    ? "post-aspect-ratio"
                                    : ""
                                }`
                          } `}
                          alt=""
                          src={getAssetUrl({
                            media: preview || media[0],
                            variation:
                              !preview &&
                              (media?.[0]?.type === "image" ||
                                (media?.[0].type === "video" &&
                                  media?.[0]?.poster?.is_autogenerated))
                                ? "blur"
                                : "compressed",
                            poster:
                              !preview &&
                              media?.[0]?.type === "video" &&
                              media?.[0]?.poster
                                ? true
                                : false,
                          })}
                          onContextMenu={(e) => e.preventDefault()}
                        />
                      </div>
                    ) : media?.[0]?.type === "image" &&
                      !showCarousel &&
                      details?.status === "Completed" ? (
                      <div className="position-relative">
                        <Resolution
                          resolution={media[0].resolution}
                          visibility={details2?.visibility!}
                          isPaidSubscription={
                            details?.channels?.[0]?.is_paid ||
                            details?.groups?.[0]?.is_paid
                          }
                        />
                        <div
                          className="background-blur"
                          style={{
                            backgroundImage: `url(${imagesrc})`,
                            filter: "blur(16px) brightness(1.1)",
                            position: "absolute",
                            top: 0,
                            left: 0,
                            width: "100%",
                            height: "100%",
                            backgroundSize: "cover",
                            backgroundPosition: "center",
                            zIndex: 1,
                          }}
                        />
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          width={1280}
                          height={720}
                          className={`post-img-detail w-100 h-100  z-1 position-relative  ${
                            media[0].resolution &&
                            media[0].resolution?.width /
                              media[0].resolution?.height <
                              1.3
                              ? "post-aspect-ratio"
                              : ""
                          }`}
                          alt=""
                          src={imagesrc}
                          onClick={() => {
                            window.KNKY.showFullscreenMedia("image", imagesrc);
                          }}
                          onContextMenu={(e) => e.preventDefault()}
                        />
                      </div>
                    ) : media?.[0]?.type === "video" &&
                      !showCarousel &&
                      details?.status === "Completed" ? (
                      <div
                        className="position-relative"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <div
                          className="position-absolute top-0 start-0 w-100 h-100"
                          style={{
                            background: `url(${getAssetUrl({
                              media: media[0],
                              poster: media[0].poster ? true : false,
                            })})`,
                            backgroundSize: "cover",
                            backgroundPosition: "center",
                            filter: "blur(10px)",
                          }}
                        ></div>
                        {/* <video
                          src={imagesrc}
                          autoPlay
                          muted
                          ref={videoRef}
                          controlsList="nodownload"
                          id="video-post"
                          playsInline
                          onContextMenu={(e) => e.preventDefault()}
                          onClick={(e) => {
                            e.stopPropagation();

                            window.KNKY.showFullscreenMedia("video", imagesrc);

                            //  @ts-expect-error - Property 'controls' does not exist on type 'EventTarget'.
                            e.target.controls = !e.target.controls;
                          }}
                          // @ts-expect-error - Property 'controls' does not exist on type 'EventTarget'.
                          onPause={(e) => (e.target.controls = false)}
                          //  @ts-expect-error - Property 'controls' does not exist on type 'EventTarget'.
                          onEnded={(e) => (e.target.controls = true)}
                          className={`post-img-detail w-100 h-100 z-5 position-relative ${
                            media[0].resolution &&
                            media[0].resolution?.width /
                              media[0].resolution?.height <
                              1.3
                              ? "post-aspect-ratio"
                              : ""
                          }`}
                        /> */}

                        <PlyrPlayer
                          source={imagesrc}
                          options={options}
                          classes={`post-img-detail w-100 h-100 z-5 position-relative ${
                            media[0].resolution &&
                            media[0].resolution?.width /
                              media[0].resolution?.height <
                              1.3
                              ? "post-aspect-ratio"
                              : ""
                          }`}
                          hasPrime={hasPrime}
                          plyrRef={plyrRef}
                          mediaResolution={media[0].resolution}
                          visibility={details2?.visibility}
                          isPaidSubscription={
                            details?.channels?.[0]?.is_paid ||
                            details?.groups?.[0]?.is_paid
                          }
                          authorHasPro={userDetails.author.badges.subscription_badge.includes(
                            "CreatorPro"
                          )}
                        />
                      </div>
                    ) : showCarousel ? (
                      <>
                        <MultipleMedia
                          mediaList={multiMedia}
                          id={_id}
                          type={details?.type!}
                          authorHasPro={userDetails.author.badges.subscription_badge.includes(
                            "CreatorPro"
                          )}
                          isPaidSubscription={
                            details?.channels?.[0]?.is_paid ||
                            details?.groups?.[0]?.is_paid ||
                            false
                          }
                        />
                      </>
                    ) : backgroundColor ? (
                      <div className="post-background-wrapper py-2">
                        <Background
                          backgroundColor={backgroundColor}
                          textColor={textColor}
                          bgCaption={desc}
                        />
                      </div>
                    ) : (
                      type === "Premium" && notMyPost && <></>
                    )}

                    {poll && (
                      <div
                        className="post-poll-wrapper py-2 d-flex justify-content-center "
                        onClick={(e) => e.stopPropagation()}
                      >
                        <PollBox postPoll={poll} details={userDetails} />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {role !== "guest" && showModal && (
          <PremiumPurchase
            type="POST"
            setChildRef={purchaseModalRef}
            selectedItem={pay_and_watch_rate}
            post_id={post_id}
            purchaseSuccess={(data) => postPurchased(data)}
          />
        )}
      </>
    );
  };

  const TaggedUsers = ({
    tagged_users,
    tagged_channels,
    tagged_collabs,
  }: {
    tagged_users: Author[];
    tagged_channels: TaggedChannel[];
    tagged_collabs: TaggedCollab[];
  }) => {
    const router = useRouter();

    const mainDivRef = useRef<HTMLDivElement>(null);
    const [width, setWidth] = useState(0);
    const scrollableRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (mainDivRef && mainDivRef.current)
        setWidth(mainDivRef.current.clientWidth);
    }, [mainDivRef.current]);

    useEffect(() => {
      const isDesktop = window.innerWidth >= 992;
      if (!isDesktop) return;

      let mouseDown = false;
      let startX: any, scrollLeft: any;
      const slider = scrollableRef.current;

      if (!slider) return;

      const startDragging = (e: any) => {
        mouseDown = true;
        startX = e.pageX - slider.offsetLeft;
        scrollLeft = slider.scrollLeft;
      };

      const stopDragging = () => {
        scrollableRef.current?.style.setProperty("cursor", "");
        mouseDown = false;
      };

      const move = (e: any) => {
        if (!mouseDown) return;
        e.preventDefault();
        scrollableRef.current?.style.setProperty("cursor", "grabbing");
        const x = e.pageX - slider.offsetLeft;
        const scroll = x - startX;
        slider.scrollLeft = scrollLeft - scroll;
      };

      slider.addEventListener("mousedown", startDragging);
      slider.addEventListener("mouseup", stopDragging);
      slider.addEventListener("mouseleave", stopDragging);
      slider.addEventListener("mousemove", move);

      slider.addEventListener("mouseover", startDragging);
      slider.addEventListener("mouseleave", stopDragging);

      return () => {
        slider.removeEventListener("mousedown", startDragging);
        slider.removeEventListener("mouseup", stopDragging);
        slider.removeEventListener("mouseleave", stopDragging);
        slider.removeEventListener("mousemove", move);
        slider.removeEventListener("mouseover", startDragging);
        slider.removeEventListener("mouseleave", stopDragging);
      };
    }, []);

    const canFollow = profile._id !== details?._id;

    return (
      <div className={`pb-0`}>
        <div
          className="d-flex justify-content-between align-items-center px-3 pt-3"
          ref={mainDivRef}
        >
          <h6>Tagged in this post</h6>
        </div>

        <div className="d-flex gap-2 overflow-scroll px-3" ref={scrollableRef}>
          {tagged_users?.map((user) => (
            <div key={user?._id}>
              <div
                className=" rounded-3  p-3 "
                style={{
                  backgroundImage: `linear-gradient(90deg,rgba(26, 26, 26, 1) 0%,rgba(52, 51, 51, 0.86) 43%,rgba(131, 129, 129, 0.08) 100%),url(
                    ${getAssetUrl({
                      media: user?.background?.[0],
                      defaultType: "background",
                      variation: "thumb",
                    })})`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  width: tagged_users.length === 1 ? width : width - 100,
                }}
              >
                <div className="d-flex justify-content-between align-items-center">
                  <Link
                    href={`/${user?.user_type?.toLowerCase()}/${
                      user?.username
                    }`}
                    className="d-flex gap-2 align-items-center"
                  >
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={getAssetUrl({
                        media: user?.avatar?.[0],
                        defaultType: "avatar",
                      })}
                      width={56}
                      height={56}
                      alt=""
                      className="rounded-circle object-fit-cover d-none d-md-block"
                    />
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={getAssetUrl({
                        media: user?.avatar?.[0],
                        defaultType: "avatar",
                        variation: "thumb",
                      })}
                      width={36}
                      height={36}
                      alt=""
                      className="rounded-circle object-fit-cover d-block d-md-none"
                    />

                    <div className="d-flex flex-column justify-content-between">
                      <div className="d-flex align-items-center">
                        <span className="fs-6 text-white fs-md-5 ">
                          <span className="d-none d-md-inline">
                            {user?.display_name}
                          </span>
                          <span className="d-inline d-md-none fw-light">
                            {user?.display_name}
                          </span>
                        </span>{" "}
                        &nbsp;
                        <span className="fs-6 text-white-50 fw-light d-none d-md-inline-block">
                          @{user?.username}
                        </span>
                        <span className="ms-1">
                          <Badges array={user?.badges!} />
                        </span>
                      </div>
                      <div className="d-flex align-items-center gap-2">
                        <span className="fs-7 text-white fw-light">
                          {user?.counter?.post_count || 0} posts{" "}
                        </span>

                        {user?.min_subscription && (
                          <>
                            <div
                              className="rounded-circle "
                              style={{
                                width: "0.2rem",
                                height: "0.2rem",
                                background: "#868484",
                                marginTop: "0.16rem",
                              }}
                            ></div>
                            <span className="fs-7 text-white fw-light">
                              {formatCurrency(user?.min_subscription?.price) ||
                                0}{" "}
                              <span className="d-inline d-md-none">/</span>{" "}
                              <span className="d-none d-md-inline">per</span>{" "}
                              month
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </Link>
                  <div className="d-flex gap-3 align-items-center">
                    <Follow
                      white_variant
                      visible={canFollow}
                      cls="d-md-block d-sm-none d-none btn-light"
                      author={user?._id}
                      followed_by_you={user.followed_by_you!}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
          {tagged_channels?.map((user) => {
            return (
              <div key={user?._id}>
                <div
                  className=" rounded-3  p-3 "
                  style={{
                    backgroundImage: `linear-gradient(90deg,rgba(26, 26, 26, 1) 0%,rgba(52, 51, 51, 0.86) 43%,rgba(131, 129, 129, 0.08) 100%),url(
                      ${getAssetUrl({
                        media: user?.background?.[0],
                        defaultType: "background",
                        variation: "thumb",
                      })})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    width: tagged_channels.length === 1 ? width : width - 100,
                  }}
                >
                  <div className="d-flex justify-content-between align-items-center">
                    <Link
                      href={`/channel/${user?.channel_tagname}`}
                      className="d-flex gap-2 align-items-center"
                    >
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={getAssetUrl({
                          media: user?.avatar?.[0],
                          defaultType: "avatar",
                        })}
                        width={56}
                        height={56}
                        alt=""
                        className="rounded-circle object-fit-cover d-none d-md-block"
                      />
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={getAssetUrl({
                          media: user?.avatar?.[0],
                          defaultType: "avatar",
                          variation: "thumb",
                        })}
                        width={36}
                        height={36}
                        alt=""
                        className="rounded-circle object-fit-cover d-block d-md-none"
                      />

                      <div className="d-flex flex-column justify-content-between">
                        <div className="d-flex align-items-center">
                          <span className="fs-6 text-white fs-md-5 ">
                            <span className="d-none d-md-inline">
                              {user?.name}
                            </span>
                            <span className="d-inline d-md-none fw-light">
                              {user?.name}
                            </span>
                          </span>{" "}
                          &nbsp;
                          <span className="fs-6 text-white-50 fw-light d-none d-md-inline-block">
                            @{user?.channel_tagname}
                          </span>
                          <span className="fw-medium px-2 py-1 bg-green2 text-white rounded-2 fs-8 ms-2">
                            Channel
                          </span>
                        </div>
                        <div className="d-flex align-items-center gap-2">
                          <span className="fs-7 text-white fw-light">
                            {user?.counter?.post_count || 0} posts{" "}
                          </span>

                          {user?.min_subscription && (
                            <>
                              <div
                                className="rounded-circle "
                                style={{
                                  width: "0.2rem",
                                  height: "0.2rem",
                                  background: "#868484",
                                  marginTop: "0.16rem",
                                }}
                              ></div>
                              <span className="fs-7 text-white fw-light">
                                {formatCurrency(
                                  user?.min_subscription?.price
                                ) || 0}{" "}
                                <span className="d-inline d-md-none">/</span>{" "}
                                <span className="d-none d-md-inline">per</span>{" "}
                                month
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </Link>
                    {!user.is_owned && (
                      <div className="d-flex gap-3 align-items-center">
                        <ActionButton
                          variant={user.is_subscribed ? "white" : "primary"}
                          type={user.is_subscribed ? "outline" : "solid"}
                          disabled={user.is_subscribed}
                          onClick={() =>
                            router.push(`/channel/${user?.channel_tagname}`)
                          }
                        >
                          {user.is_subscribed ? "Subscribed" : "Subscribe"}
                        </ActionButton>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
          {tagged_collabs?.map((user) => {
            return (
              <div key={user?._id}>
                <div
                  className=" rounded-3  p-3 "
                  style={{
                    backgroundImage: `linear-gradient(90deg,rgba(26, 26, 26, 1) 0%,rgba(52, 51, 51, 0.86) 43%,rgba(131, 129, 129, 0.08) 100%),url(
                      ${getAssetUrl({
                        media: user?.background?.[0],
                        defaultType: "background",
                        variation: "thumb",
                      })})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    width:
                      tagged_collabs.length === 1 ? width - 30 : width - 100,
                  }}
                >
                  <div className="d-flex justify-content-between align-items-center">
                    <Link
                      href={`/collab/${user?.tag_name}`}
                      className="d-flex gap-2 align-items-center"
                    >
                      {user?.members
                        ?.slice(0, 3)
                        ?.map(({ member_id }: any, index: number) => (
                          <div
                            key={member_id.username}
                            style={{
                              marginLeft: index > 0 ? "-30px" : "",
                            }}
                          >
                            <>
                              {/* eslint-disable-next-line @next/next/no-img-element */}
                              <img
                                className="rounded-circle object-fit-cover d-none d-md-block"
                                alt=""
                                width={56}
                                height={56}
                                src={getAssetUrl({
                                  media: member_id.avatar[0],
                                  defaultType: "avatar",
                                })}
                              />
                              {/* eslint-disable-next-line @next/next/no-img-element */}
                              <img
                                src={getAssetUrl({
                                  media: member_id.avatar[0],
                                  defaultType: "avatar",
                                  variation: "thumb",
                                })}
                                width={36}
                                height={36}
                                alt=""
                                className="rounded-circle object-fit-cover d-block d-md-none"
                              />
                            </>
                          </div>
                        ))}

                      <div className="d-flex flex-column justify-content-between">
                        <div className="d-flex align-items-center">
                          <span className="fs-6 text-white fs-md-5 ">
                            <span className="d-none d-md-inline">
                              {user?.name}
                            </span>
                            <span className="d-inline d-md-none fw-light">
                              {user?.name}
                            </span>
                          </span>{" "}
                          &nbsp;
                          <span className="fs-6 text-white-50 fw-light d-none d-md-inline-block">
                            @{user?.tag_name}
                          </span>
                          <span className="fw-medium px-2 py-1 bg-blue text-white rounded-2 fs-8 ms-2">
                            Collab
                          </span>
                        </div>
                        <div className="d-flex align-items-center gap-2">
                          <span className="fs-7 text-white fw-light">
                            {user?.counter?.post_count || 0} posts{" "}
                          </span>

                          {user?.min_subscription && (
                            <>
                              <div
                                className="rounded-circle "
                                style={{
                                  width: "0.2rem",
                                  height: "0.2rem",
                                  background: "#868484",
                                  marginTop: "0.16rem",
                                }}
                              ></div>
                              <span className="fs-7 text-white fw-light">
                                {formatCurrency(
                                  user?.min_subscription?.price
                                ) || 0}{" "}
                                <span className="d-inline d-md-none">/</span>{" "}
                                <span className="d-none d-md-inline">per</span>{" "}
                                month
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </Link>
                    {!user.is_group_member && (
                      <div className="d-flex gap-3 align-items-center">
                        <ActionButton
                          variant={user.is_subscribed ? "white" : "primary"}
                          type={user.is_subscribed ? "outline" : "solid"}
                          disabled={user.is_subscribed}
                          onClick={() =>
                            router.push(`/collab/${user?.tag_name}`)
                          }
                        >
                          {user.is_subscribed ? "Subscribed" : "Subscribe"}
                        </ActionButton>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const isMobile = window.innerWidth <= 767;
  console.log({ details }, { details2 });
  const navigateBack = useNavigateBack();

  if (error === 404) {
    return <NotFound />;
  }

  if (error === 500) {
    return <Error500 />;
  }

  const isExpired = new Date(details?.expired_on!).getTime() < Date.now();

  return (
    <div className="container-xxl px-0">
      <div className=" post bg-body mb-4 position-relative">
        {showShimmer ? (
          <Shimmer type="postDetail"></Shimmer>
        ) : (
          <>
            <div
              onClick={() => navigateBack()}
              className={`pointer z-2 m-2 ${
                details2?.media?.[0] ? "position-absolute" : ""
              }`}
            >
              <Image
                src={"/images/svg/back.svg"}
                className="back-button-drop-shadow"
                width={isMobile ? 36 : 54}
                height={isMobile ? 36 : 54}
                alt="back"
              />
            </div>
            <PostContent
              userDetails={postDetails}
              desc={details2?.caption!}
              media={details?.media! || details2?.media}
              preview={details2?.preview[0]!}
              hashtags={details?.hashtags!}
              shop_item={details2?.products! || details?.shop_item}
              poll={details?.poll!}
              backgroundColor={details2?.backgroundColor!}
              textColor={details2?.textColor!}
              type={details2?.visibility!}
              post_type={details2?.type!}
              pay_and_watch_rate={details?.pay_and_watch_rate || 0}
              post_id={details2?._id!}
              is_purchased={details?.is_purchased!}
              is_subscribed={details?.is_subscribed!}
              shared_on_profile={
                details?.shared_on_profile! ||
                details2?.shared_on_profile! ||
                false
              }
              media_categories={details?.media_categories!}
            />
            <PostHead
              author={details?.author!}
              channelAuthor={details?.channels?.[0]!}
              shared_on_profile={details?.shared_on_profile!}
              ts={new Date(details2?.created_at || "").getTime()}
              type={details2?.visibility!}
              feelingValue={details?.feeling || ""}
              group={details2?.groups?.[0]!}
              details={details!}
              postType={details?.post_type || ""}
              route={details2?.type}
              collaborators={details2?.collaborators!}
              showSubscribe={showSubscribe}
              postSource={postSource}
              isExpired={isExpired}
            />
            <PostCaption
              backgroundColor={details?.backgroundColor!}
              desc={details?.desc || details2?.caption!}
              post_type={details2?.type!}
              type={details2?.visibility!}
              is_purchased={details?.is_purchased!}
            />
            {UserId === details?.author?._id && <div className="pb-2"></div>}
            {isLivePost && (
              <LiveVideoPlayer
                thumbnail="/images/common/back.jpg"
                data={details}
                src={getAssetUrl({ media: details2?.media?.[0] })}
              />
            )}
            {details?.tagged_users.length ||
            details?.tagged_channels?.length ||
            details?.tagged_groups?.length ? (
              <div className="overflow-hidden">
                <TaggedUsers
                  tagged_users={details.tagged_users}
                  tagged_channels={details.tagged_channels || []}
                  tagged_collabs={details.tagged_groups || []}
                />
              </div>
            ) : (
              <></>
            )}
            {details && details2 && UserId !== details?.author?._id && (
              <>
                <PostInteractions
                  comments={comments!}
                  details={details}
                  type={details2.visibility}
                  postType={details2.type!}
                  showSubscribe={showSubscribe}
                  postSource={postSource}
                  actions={actions!}
                  postDetail={true}
                />
                {hasMore && (
                  <span
                    onClick={handleShowMore}
                    className="pointer mx-md-3 fw-medium mb-2"
                  >
                    {loading ? "Loading..." : "See more comments"}
                  </span>
                )}
              </>
            )}
            {showBuyTicketModal && (
              <BuyTicketModal
                setChildRef={buyTicketModalRef}
                misc={{ setSeatReserved, setSoldCount }}
              />
            )}
          </>
        )}
      </div>
      {(UserId === details?.author?._id ||
        details?.collaborators.some((res) => res.user._id === UserId)) && (
        <PostDetails
          postId={details2?._id!}
          postType={details2?.visibility!}
          details={details!}
          comments={comments!}
          actions={actions!}
          type={details2?.type!}
          hasMore={hasMore}
          handleShowMore={handleShowMore}
          loading={loading}
        />
      )}
    </div>
  );
};

export default PostComponent;
