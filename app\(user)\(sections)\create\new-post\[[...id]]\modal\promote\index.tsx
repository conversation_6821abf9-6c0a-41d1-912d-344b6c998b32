import Image from "next/image";
import "./index.scss";
import { useEffect, useState } from "react";
import Link from "next/link";

import { ProductListing } from "@/api/shop";
import { getAssetUrl } from "@/utils/assets";
import Modal, { type ModalImportProps } from "@/components/common/modal";
import { useAppSelector } from "@/redux-store/hooks";

// interface ItemsBody{

// }

export default function Promote({
  setChildRef,
  chosenItems,
  onclose,
}: ModalImportProps & { chosenItems: Function } & { onclose: () => void }) {
  const uid = useAppSelector((state) => state.user.id);

  const [shopItem, setshopItem] = useState<any>({});
  const [productsData, setProductsData] = useState<any>([]);
  const [disbleBtn, setDisabledBtn] = useState(true);

  const selectItem = (item: any) => {
    setshopItem(item);
    setDisabledBtn(false);
  };

  useEffect(() => {
    ProductListing(uid)
      .then((response) => {
        setProductsData(response.data);

        // dispatchAction(setOptions(response.data));
      })
      .catch((err) => {
        console.error(err);
      });
  }, []);

  return (
    <Modal
      title="Add shop items in post"
      subtitle={[""]}
      setChildRef={setChildRef}
      render={"direct"}
      onClose={() => onclose()}
    >
      <div className="container ">
        <div className="d-flex flex-column gap-2 p-2 h-50">
          <div className="input-group mb-3">
            <span
              className="input-group-text border-0 bg-cream"
              id="basic-addon1"
            >
              <Image
                width={25}
                height={25}
                src="/images/svg/search.svg"
                alt="search-icon"
              />
            </span>
            <input
              type="text"
              className="form-control border-0 bg-cream color-dark shadow-none "
              placeholder="Search"
              aria-label="Search"
              aria-describedby="basic-addon1"
            />
          </div>
          <div
            className="d-flex flex-column overflow-y-scroll"
            style={{ maxHeight: "25rem" }}
          >
            <div className="d-flex gap-5  p-2 align-items-center justify-content-center">
              {/* <div className="form-check">
                <input
                  className="form-check-input bg-cream btn-purple"
                  type="radio"
                  value=""
                  name="flexRadioDefault"
                  id="flexRadioDefault"
                />
              </div>
              <div className="shop-image-wrapper">
                <Image
                  src="/images/svg/bag-image.svg"
                  width={50}
                  className="invert"
                  height={50}
                  alt="shop-bag"
                />
              </div> */}
              {/* <div className="d-flex flex-column align-content-between w-25">
                <p className="fs-6 mb-1">My Shoes</p>
                <p className="fs-8 mb-0">
                  <Image
                    src="/images/svg/auction-gavel.svg"
                    width={25}
                    height={25}
                    alt="auction-gavel"
                  />{" "}
                  Auction in 3d
                </p>
              </div> */}
            </div>
            {productsData.map((item: any, index: any) => (
              <div
                key={index}
                className="d-flex gap-5 p-2 align-items-center justify-content-center"
              >
                <div className="form-check">
                  <input
                    className="form-check-input bg-cream btn-purple"
                    type="radio"
                    value=""
                    id="flexRadioDefault"
                    onChange={() => selectItem(item)}
                    checked={shopItem && shopItem?._id === item._id}
                  />
                </div>
                <div className="shop-image-wrapper">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={getAssetUrl({ media: item?.media?.[0] })}
                    width={50}
                    className="invert"
                    height={50}
                    alt="shop-bag"
                    style={{ objectFit: "cover" }}
                  />
                </div>
                <div className="d-flex flex-column align-content-between w-25">
                  <p className="fs-6 mb-1">{item?.name}</p>
                  <p className="fs-8 color-grey mb-0">${item?.price}</p>
                </div>
              </div>
            ))}
            {!productsData.length && (
              <h4 className="d-flex flex-column align-items-center text-center">
                No Shop Items Available <br />{" "}
                <Link
                  href="/create/shop-item"
                  className="color-primary pointer"
                >
                  Create New
                </Link>
              </h4>
            )}
          </div>

          <div className="d-flex justify-content-center mt-2">
            {/* <p className="fs-6 fw-medium">Selected : {shopItem.name} </p> */}
          </div>

          <div className="d-flex w-100 p-2 justify-content-center ">
            <button
              className={`btn w-75 ${
                disbleBtn ? "btn-disabled" : "btn-purple"
              }`}
              disabled={disbleBtn}
              onClick={() => {
                chosenItems(shopItem);
                setChildRef.method.close();
              }}
            >
              Complete
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
