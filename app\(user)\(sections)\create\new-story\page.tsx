"use client";

import "./index.scss";

import classNames from "classnames";
import Hls from "hls.js";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

import { StorySubmit } from "@/api/story";
import PostNavbar from "@/app/(user)/(sections)/navbar";
import KYCwarning from "@/components/common/KYC-warning";
import Loader from "@/components/common/loader/loader";
import { type ModalRef } from "@/components/common/modal";
import PromoteModal from "@/components/common/stories/modal";
import { PostVisibility } from "@/global/constants";
import {
  STORY_IMAGE_MAX_SIZE,
  STORY_VIDEO_MAX_LENGTH,
  STORY_VIDEO_MAX_SIZE,
} from "@/global/limits/story";
import { configActions, shopActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";
import "cropperjs/dist/cropper.css";

import useFileUploader from "@/hooks/useFileUploader";

import { MediaPost } from "../new-post/[[...id]]/media";
import { Thumbnail } from "../new-post/[[...id]]/video-thumbnail";
import FabricCanvas from "./fabric-canvas";

interface Story {
  media: boolean;
  promote: boolean;
  text: boolean;
  vault: boolean;
}

const TextStoryBackgroundColor = [
  "#FFFFFF",
  "#131416",
  "#AFB1B3",
  "#7A29CC",
  "#AC1991",
  "#27B1FF",
  "#29A81E",
  "#FFB800",
  "#FF7816",
  "#F44B41",
];
const TextStoryFontColor = [
  "#FFFFFF",
  "#131416",
  "#AFB1B3",
  "#7A29CC",
  "#AC1991",
  "#27B1FF",
  "#29A81E",
  "#FFB800",
  "#FF7816",
  "#F44B41",
];
const SvgMap = {
  media: "image-story",
  text: "text-story",
  promote: "promote-story",
  vault: "vault",
};

function getVideoDuration(file: File) {
  return new Promise((resolve, reject) => {
    if (file && file.type.startsWith("video/")) {
      const url = URL.createObjectURL(file);
      const videoElement = document.createElement("video");

      videoElement.src = url;
      videoElement.addEventListener("loadedmetadata", () => {
        const duration = videoElement.duration;
        URL.revokeObjectURL(url);
        resolve(duration);
      });

      videoElement.addEventListener("error", (err) => {
        URL.revokeObjectURL(url);
        reject(err);
      });
    } else {
      resolve(0);
    }
  });
}

const getMediaLimits = async ({
  file,
  type,
  size,
  duration,
}: {
  type: string;
  size: number;
  file?: File;
  duration?: number;
}) => {
  if (type.startsWith("image/") && size > STORY_IMAGE_MAX_SIZE) {
    toast.error("The maximum allowed image file size is 40MB.");
    return false;
  } else if (type.startsWith("video/") && size > STORY_VIDEO_MAX_SIZE) {
    toast.error("The maximum allowed video file size is 500MB.");

    return false;
  }

  let time: any;

  if (duration) {
    time = duration;
  } else {
    time = await getVideoDuration(file!);
  }

  if (time > STORY_VIDEO_MAX_LENGTH) {
    toast.dismiss();
    toast.error("The maximum allowed video file length is 1 minute.");

    return false;
  }

  return true;
};

export default function CreateStory() {
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  const [vaultMedia, setVaultMedia] = useState<any>();
  const user = useAppSelector((state) => state.user);
  const [promoteData, setPromoteData] = useState([]) as any[];
  const shopData = useAppSelector((state) => state.shopSlice.shopData);

  const promoteModalRef = { method: {} as ModalRef };
  const { uploadFile } = useFileUploader();
  const [story, setStory] = useState({
    media: false,
    text: false,
    promote: false,
    vault: false,
    xyz: false,
  });
  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );

  useEffect(() => {
    if (shopData && Object.keys(shopData).length) {
      setStory({
        ...story,
        media: false,
        text: false,
        promote: true,
        vault: false,
      });
      setPromoteData(shopData);
      setVaultMedia(null);
      setDoneButton(false);
      setShowThumbnail(false);
      setType("Product");
      setVisibility(PostVisibility[2]);
      setVisibilityIndex(2);
    }

    return () => {
      dispatchAction(shopActions.resetsingleShopItem());
    };
  }, [shopData]);

  useEffect(() => {
    dispatchAction(configActions.toggleShowPositionWidget());

    return () => {
      dispatchAction(configActions.toggleShowPositionWidget());
    };
  }, []);

  const [croppedPostUrl, setCroppedPostUrl] = useState("");
  const [postImg, setPostImg] = useState("") as any;
  const [postVid, setPostVid] = useState("");
  const [postData, setPostData] = useState<File>();
  const [fileName, setFileName] = useState("file");
  const [loading, setLoading] = useState(false);
  const [postSubmited, setPostSubmited] = useState(false);
  const [visibility, setVisibility] = useState(PostVisibility[0]);
  const [visibilityIndex, setVisibilityIndex] = useState(0);
  const [videoThumbnail, setVideoThumbnail] = useState<File>();
  const [selectedThumbnail, setSelectedThumbnail] = useState<string>();
  const [thumbnailUrl, setThumbnailUrl] = useState("") as any;
  const [selectedThumbailID, setSelectedThumbailID] = useState("") as any;
  const [showThumbnail, setShowThumbnail] = useState(false);
  const [TextBackgroundColor, setTextBackgroundColor] = useState(
    TextStoryBackgroundColor[1]
  );
  const [TextFontColor, setTextFontColor] = useState(TextStoryFontColor[0]);

  const [activeColorIndex, setActiveColorIndex] = useState(0);
  const [showError, setShowError] = useState(false);
  const [activeFontColorIndex, setActiveFontColorIndex] = useState(1);
  const [type, setType] = useState("");

  const [premiumValue, setPremiumValue] = useState(5);

  const handlePremium = (e: any) => {
    let value = e.target.value;

    if (value.length > 6) {
      return;
    }

    if (/[^0-9.]/.test(value) || value.split(".").length > 2) {
      return;
    }

    if (value !== "0" && value !== "0." && !value.startsWith("0.")) {
      value = value.replace(/^0+/, "");
    }

    const numericValue = parseFloat(value);
    const isValid = numericValue >= 5;

    setPremiumValue(value);

    setShowError(!isValid);
  };

  const handleValultPosterSelection = async () => {
    const { medias } = await window.KNKY.openVault({
      readonly: true,
      constraints: { type: "image" },
    });

    if (medias[0].type === "image") {
      //@ts-expect-error ts error
      setSelectedThumbnail(getAssetUrl({ media: medias[0] }));
      setSelectedThumbailID(medias[0]._id);
    }
  };

  async function fileUpload(e: any) {
    const file = e.target.files[0];
    const isValid = await getMediaLimits({
      file: file,
      size: file.size,
      type: file.type,
    });

    if (file && isValid) {
      setFileName(file?.name?.replace(/\s+/g, ""));
      setPostImg("");
      setType("Normal");
      const blobUrl = URL.createObjectURL(file);
      setDoneButton(true);

      if (file.type.startsWith("image/")) {
        setPostImg(blobUrl);
        setCroppedPostUrl("");
        setStory({
          ...story,
          media: true,
          text: false,
          promote: false,
          vault: false,
        });
        setShowThumbnail(false);
      } else if (file.type.startsWith("video/")) {
        const blob = await fetch(blobUrl).then((res) => res.blob());
        const fName = file.name.replace(/\s+/g, "");
        const files = new File([blob], fName, { type: blob.type });
        setPostData(files);
        setShowThumbnail(true);
        setPostVid(blobUrl);
        setStory({
          ...story,
          media: true,
          text: false,
          promote: false,
          vault: false,
        });
        setDoneButton(false);
      } else {
        console.error("Selected file is neither an image nor a video.");
      }
    }
  }

  const setCroppedPostUrlFunc = (dataUrl: any) => {
    const blob = dataUrl;
    const file = new File([blob], fileName, { type: blob.type });
    setPostData(file);
    setCroppedPostUrl(URL.createObjectURL(blob));
    setDoneButton(false);
  };

  function removePost() {
    setPostImg("");
    setPostVid("");
    setCroppedPostUrl("");
    setType("");
    setPostData(undefined);
    setShowThumbnail(false);
    setDoneButton(true);
    dispatchAction(shopActions.resetsingleShopItem());
    setStory({ ...story, media: false, text: false, promote: false });
  }

  const [doneButton, setDoneButton] = useState(true);

  const textStroyRef = useRef(null) as any;
  const cropperRef = useRef(null) as any;
  const imageRef = useRef(null) as any;
  const videoRef = useRef(null) as any;
  useEffect(() => {
    if (!videoRef.current) return;
    const source = postVid || getAssetUrl({ media: vaultMedia[0] });

    const video = videoRef.current;

    const isHls = /\.m3u8/.test(source);
    const hlsSource = isHls ? source : "";

    if (isHls && Hls.isSupported() && hlsSource) {
      const hls = new Hls();
      hls.loadSource(hlsSource);
      hls.attachMedia(video);
    } else if (isHls && video.canPlayType("application/vnd.apple.mpegurl")) {
      video.src = hlsSource;
    } else {
      video.src = source;
    }
  }, [videoRef.current, postVid, vaultMedia]);

  const captureImage = async () => {
    if (textStroyRef.current) {
      textStroyRef.current.exportImage();
    }
  };

  return (
    <>
      <PostNavbar
        text={"Create new story"}
        icon="/images/svg/nav-close.svg"
        btnClass={""}
        btnIsValid={Object.values(story).some((value) => value === true)}
        btnIsDirty={Object.values(story).some((value) => value === true)}
        btnIsSubmitting={false}
        buttonText={
          (story.media && !showThumbnail) || story.text
            ? doneButton
              ? "Done"
              : "Submit"
            : "Submit"
        }
        showBtn={true}
        loader={loading}
        submitPost={async () => {
          if (!doneButton) {
            router.push("/fresh");
            setLoading(true);
            setPostSubmited(true);
            const modifiedInfo = [];
            const PosterInfo = [];

            try {
              if (!vaultMedia && !selectedThumbailID) {
                if (videoThumbnail) {
                  const [res1, res2] = await Promise.all([
                    uploadFile(postData!, "Story"),
                    uploadFile(videoThumbnail!, "Story"),
                  ]);
                  modifiedInfo.push({
                    path: (res2 as any)?.[0]?.s3Multipart?.key,
                    type: res2?.[0]?.type.split("/")[0] as "image" | "video",
                  });

                  PosterInfo.push({
                    path: (res2 as any)?.[1]?.s3Multipart?.key,
                    type: res2?.[1]?.type.split("/")[0] as "image" | "video",
                  });
                } else {
                  const res = await uploadFile(postData!, "Story");

                  modifiedInfo.push({
                    path: (res as any)?.[0]?.s3Multipart?.key,
                    type: res?.[0]?.type.split("/")[0] as "image" | "video",
                  });
                }
              }
            } catch (error: any) {
              setLoading(false);
              toast.error(error.message);
            }

            const storyResponse = await StorySubmit({
              caption: "Noraml",
              media: modifiedInfo,
              type: type,
              poster: PosterInfo,
              product_id: promoteData?._id,
              pay_and_watch_rate: premiumValue,
              vault_poster_ids: selectedThumbailID ? [selectedThumbailID!] : [],
              visibility: visibility.value,
              vault_media_ids: vaultMedia ? [vaultMedia[0]?._id]! : [],
              poster_generation:
                (postData?.type.includes("video") && !videoThumbnail) ||
                (vaultMedia &&
                  vaultMedia[0]?.type === "video" &&
                  !selectedThumbailID)
                  ? "true"
                  : false,
            });

            if (storyResponse) {
              toast.success("Story uploaded successfully!");
            }
          } else {
            if (story.media && cropperRef.current) {
              cropperRef.current.triggerCrop();
            } else {
              setLoading(true);
              captureImage();
            }
          }
        }}
      />
      <div
        className={`container-xxl py-3 d-flex gap-3 pb-0 pb-lg-3 flex-column flex-lg-row ${
          postSubmited ? "pe-none" : ""
        }`}
      >
        <div className="story-type-wrapper col-12 col-lg-4 d-flex flex-column gap-3 flex-grow-1">
          <div className="post-type-selection d-flex flex-column gap-3 bg-body p-3 rounded-3 ">
            <div className="post-selection-title h5 fw-semibold">
              Add to your story
            </div>
            <div className="post-type-options d-flex flex-wrap gap-3">
              <StoryButton
                name="media"
                label="Photo or Video"
                disable={story.vault}
                setStory={() =>
                  setStory({
                    ...story,
                    media: true,
                    text: false,
                    promote: false,
                    vault: false,
                  })
                }
                story={story}
                content={
                  <input
                    disabled={story.vault}
                    type="file"
                    id="myFileInput"
                    className={classNames(
                      "media-input position-absolute top-0 bottom-0 start-0 end-0 opacity-0 z-3  pointer",
                      { "pe-none": story.vault }
                    )}
                    accept="image/*,video/*"
                    onChange={fileUpload}
                  />
                }
              />
              <StoryButton
                name="text"
                label="Text story"
                disable={false}
                onClick={() => {
                  const fileInput: any = document.getElementById("myFileInput");
                  if (fileInput) fileInput.value = "";

                  setType("Normal");
                  setDoneButton(true);
                  setStory({
                    ...story,
                    text: true,
                    media: false,
                    promote: false,
                    vault: false,
                  });
                }}
                setStory={() =>
                  setStory({
                    ...story,
                    text: true,
                  })
                }
                story={story}
              />
              <StoryButton
                name="promote"
                label="Promote"
                disable={false}
                setStory={() =>
                  setStory({
                    ...story,
                    promote: true,
                  })
                }
                story={story}
                onClick={() => {
                  const fileInput: any = document.getElementById("myFileInput");
                  if (fileInput) fileInput.value = "";
                  promoteModalRef.method.open();
                }}
              />
              <StoryButton
                name="vault"
                label="Vault"
                disable={story.media}
                setStory={() =>
                  setStory({
                    ...story,
                    vault: true,
                  })
                }
                story={story}
                onClick={async () => {
                  const fileInput: any = document.getElementById("myFileInput");
                  if (fileInput) fileInput.value = "";

                  try {
                    const { medias } = await window.KNKY.openVault({
                      readonly: true,
                    });
                    const isValid = await getMediaLimits({
                      //@ts-expect-error ts error
                      size: medias[0].size,
                      type: medias[0].type,
                      //@ts-expect-error ts error
                      duration: medias[0].duration,
                    });
                    console.log({ medias });

                    if (isValid) {
                      setVaultMedia(medias);

                      if (medias[0].type === "video") {
                        setShowThumbnail(true);
                      }

                      setDoneButton(false);
                      setStory({
                        ...story,
                        vault: true,
                        media: false,
                        text: false,
                        promote: false,
                      });
                      setType("Normal");
                    }
                  } catch (error) {
                    setShowThumbnail(false);
                    setVaultMedia(null);
                    setStory({ ...story, vault: false });
                  }
                }}
              />
            </div>
          </div>
          {user.role === "creator" && !isKycCompleted && (
            <KYCwarning type="story" />
          )}
          {showThumbnail && (
            <Thumbnail
              isVault={vaultMedia ? true : false}
              cb={() => handleValultPosterSelection()}
              setValue1={setVideoThumbnail}
              setValue2={setThumbnailUrl}
              image={selectedThumbnail!}
              onClose={() => {
                setVideoThumbnail(undefined);
                setSelectedThumbailID("");
              }}
            />
          )}
        </div>
        {postSubmited ? (
          <div
            className="story-content d-flex justify-content-center align-items-center flex-grow-1 bg-body rounded-3 px-2 py-2 col-12 col-lg-7"
            style={{ height: "60vh" }}
          >
            <Loader />
          </div>
        ) : (
          <div className="story-content flex-grow-1 bg-body rounded-3 px-3 py-2 col-12 col-lg-7">
            <div className="post-content-display d-flex  flex-column gap-3 bg-body rounded-3">
              <div className="story-content-user d-flex gap-2 py-1 align-items-center flex-nowrap">
                <div className="rounded-pill story-wrapper">
                  <Image
                    width={60}
                    height={60}
                    className="img-fluid rounded-pill object-fit-cover"
                    src={getAssetUrl({ media: user.profile.avatar[0] })}
                    alt="profile-img"
                  />
                </div>
                <div className="d-flex flex-column flex-nowrap">
                  <p className="fs-5 text-overflow-ellipsis fw-medium mb-0">
                    {user.profile.username}
                  </p>
                  <div className="dropdown w-100 mt-2">
                    <button
                      className={
                        "btn  btn-secondary w-100 d-flex align-items-center justify-content-between bg-cream ps-2 pe-2 "
                      }
                      type="button"
                      data-bs-toggle="dropdown"
                      disabled={user.role === "user" || story.promote === true}
                      aria-expanded="false"
                    >
                      <div className="d-flex gap-1 w-100  align-items-center">
                        <Image
                          src={`${visibility.icon}`}
                          width={25}
                          height={25}
                          alt="visibility"
                        />
                        <span className="fs-6 fw-medium color-dark">
                          {visibility.text}
                        </span>
                      </div>
                      <Image
                        src={"/images/svg/chevron-down.svg"}
                        width={22}
                        height={22}
                        className="svg-icon"
                        alt="dropdown"
                      />
                    </button>
                    <ul
                      className="dropdown-menu w-100"
                      ref={(el) => {
                        if (el)
                          el.style.setProperty(
                            "min-width",
                            "fit-content",
                            "important"
                          );
                      }}
                    >
                      {PostVisibility.filter(
                        (visibility) =>
                          visibility.text !== "Draft" &&
                          visibility.text !== "Subscription"
                      ).map((visibility, index) => (
                        <li
                          key={index}
                          className="user-group-wrapper d-flex pe-2 ps-2 gap-2 align-items-center mb-2 pointer"
                          onClick={() => {
                            setVisibility(PostVisibility[index]);
                            setVisibilityIndex(index);
                            setShowError(false);
                          }}
                          style={{ whiteSpace: "nowrap" }}
                        >
                          <div className="form-check d-flex align-items-center">
                            <input
                              className="form-check-input rounded-pill shadow-dark fs-8"
                              type="radio"
                              value=""
                              name="visibility-radio"
                              id={`flexCheckDefault-${index}`}
                              onChange={() => {
                                setVisibility(PostVisibility[index]);
                                setVisibilityIndex(index);
                              }}
                              checked={index === visibilityIndex}
                            />
                          </div>
                          <Image
                            src={`${visibility.icon}`}
                            width={25}
                            height={25}
                            alt="visibility"
                          />
                          <p className="fs-6 mb-0 fw-medium">
                            {visibility.text}
                          </p>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
              <div className="position-relative">
                <label
                  htmlFor="Price to unlock"
                  className={
                    visibility.value !== "Premium"
                      ? "d-none"
                      : " align-self-start color-medium fs-7 "
                  }
                >
                  Price to unlock
                  {/*  */}
                  <span className="color-red"> * </span>
                </label>
                <div
                  className={
                    visibility.value !== "Premium"
                      ? "d-none"
                      : "input-group w-lg-50 w-100 align-items-center bg-cream rounded-3 ps-2 pe-2"
                  }
                >
                  <span>$</span>

                  <input
                    name="Price to unlock"
                    type="text"
                    placeholder="Price to unlock"
                    className={
                      " color-dark bg-transparent border-0 py-2 px-2 shadow-none flex-grow-1"
                    }
                    value={premiumValue}
                    onChange={handlePremium}
                    // style={{ width: "90%" }}
                    // disabled={visibility.text !== "Premium"}
                  />
                </div>
                {showError && (
                  <span className="color-red fs-8 ms-2 position-absolute">
                    A minimum of $5 is required.
                  </span>
                )}
              </div>

              {story.media && (
                <div className="media-content d-flex position-relative justify-content-center px-1 mt-3  w-100 flex-column align-items-center">
                  {!croppedPostUrl && postImg && (
                    <MediaPost
                      postImg={postImg}
                      onCropComplete={setCroppedPostUrlFunc}
                      removePost={removePost}
                      ref={cropperRef}
                      showdDoneButton={false}
                    />
                  )}
                  {croppedPostUrl && (
                    <div
                      className="d-flex justify-content-center align-items-center ratio-1x1"
                      ref={imageRef}
                    >
                      <div
                        onClick={() => removePost()}
                        className="media-remove-btn rounded-2 p-1 position-absolute pointer"
                      >
                        <Image
                          src={"/images/svg/nav-close-white.svg"}
                          width={25}
                          height={25}
                          alt=""
                        />
                      </div>
                      <Image
                        src={croppedPostUrl}
                        alt=""
                        layout="fill"
                        style={{
                          maxHeight: "30rem",
                          objectFit: "contain",
                        }}
                        className="img-fluid position-relative"
                        // onClick={handleImageClick}
                      />
                      {/* {clickPosition.left !== 0 && (
                  <TextBox
                    left={clickPosition.left}
                    top={clickPosition.top}
                    onChange={(value: string) => handleTextChange(value)}
                    // onBlur={() => storyCaption(caption)}
                    value={caption}
                    onRemove={removeTextBox}
                  />
                )} */}
                    </div>
                  )}
                  {postVid.length != 0 && (
                    <>
                      <div className=" d-flex z-3 justify-content-end">
                        <Image
                          onClick={removePost}
                          src={"/images/svg/nav-close.svg"}
                          width={25}
                          height={25}
                          className="invert mt-lg-0 svg-icon"
                          alt="remove-media"
                        />
                      </div>
                      <video
                        ref={videoRef}
                        controls
                        playsInline
                        autoPlay
                        className="img-fluid ratio ratio-16x9"
                        style={{
                          maxHeight: "30rem",
                        }}
                      />
                    </>
                  )}
                </div>
              )}
              {story.text && (
                <div className="text-stroy-container">
                  <div className="d-flex justify-content-end">
                    <Image
                      onClick={removePost}
                      src={"/images/svg/nav-close.svg"}
                      width={25}
                      height={25}
                      className="invert svg-icon"
                      alt="remove-media"
                    />
                  </div>
                  <div className={doneButton ? "" : "pe-none"}>
                    <div className="d-flex justify-content-center">
                      <FabricCanvas
                        textColor={TextFontColor}
                        backgroundColor={TextBackgroundColor}
                        ref={textStroyRef}
                        onExportComplete={(file) => {
                          setPostData(file);
                          setTimeout(() => {
                            setDoneButton(false);
                            setLoading(false);
                          }, 500);
                        }}
                      />
                    </div>
                    <div className="d-flex flex-column gap-3 mt-3">
                      <div className="text-story-background">
                        <p className="mb-0 flex-grow">Background Color : </p>
                        <div className="d-flex gap-2 gap-lg-3  ">
                          {TextStoryBackgroundColor.map((color, index) => (
                            <>
                              <div
                                className={`color-box ${
                                  index === activeColorIndex ? "active" : ""
                                }`}
                                key={color}
                                style={{ background: color }}
                                onClick={() => {
                                  setTextBackgroundColor(
                                    TextStoryBackgroundColor[index]
                                  );
                                  setActiveColorIndex(index);
                                }}
                              ></div>
                            </>
                          ))}
                        </div>
                      </div>
                      <div className="text-story-background">
                        <p className="mb-0">Text Color : </p>
                        <div className="d-flex gap-2 gap-lg-3 ">
                          {TextStoryBackgroundColor.map((color, index) => (
                            <>
                              <div
                                className={`color-box ${
                                  index === activeFontColorIndex ? "active" : ""
                                }`}
                                key={color}
                                style={{ background: color }}
                                onClick={() => {
                                  setTextFontColor(TextStoryFontColor[index]);
                                  setActiveFontColorIndex(index);
                                }}
                              ></div>
                            </>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {story.promote && (
                <>
                  <div className="text-story-container">
                    <Image
                      onClick={removePost}
                      src={"/images/svg/nav-close.svg"}
                      width={25}
                      height={25}
                      className="invert svg-icon"
                      alt="remove-media"
                    />

                    <div className="promote-wrapper bg-body">
                      <div>
                        {/* eslint-disable-next-line @next/next/no-img-element  */}
                        <img
                          src={getAssetUrl({
                            media:
                              promoteData &&
                              promoteData?.media &&
                              promoteData?.media[0],
                          })}
                          width={350}
                          height={250}
                          alt="promote"
                          className="object-fit-cover w-100"
                        />
                      </div>
                      <div>
                        <h5 className="m-0">{promoteData?.name}</h5>
                      </div>
                      <div>Category: {promoteData?.category}</div>
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="btn btn-green">
                          {" "}
                          <p className="m-0">
                            {" "}
                            {formatCurrency(+promoteData?.price)} -Buy Now!
                          </p>{" "}
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
              {story.vault && vaultMedia && (
                <div className="d-flex justify-content-center align-items-center ratio-1x1 position-relative">
                  <div className="media-remove-btn rounded-2 p-1 position-absolute pointer">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      alt=""
                      loading="lazy"
                      width="25"
                      height="25"
                      decoding="async"
                      data-nimg="1"
                      src="/images/svg/nav-close-white.svg"
                      onClick={() => {
                        setStory({ ...story, vault: false });
                        setVaultMedia(null);
                        setShowThumbnail(false);
                      }}
                    />
                  </div>
                  {/* eslint-disable-next-line @next/next/no-img-element */}

                  {vaultMedia[0].type === "video" ? (
                    <video
                      controls
                      ref={videoRef}
                      playsInline
                      autoPlay
                      className="img-fluid ratio ratio-16x9"
                      style={{
                        maxHeight: "30rem",
                      }}
                    >
                      <source
                        src={getAssetUrl({ media: vaultMedia[0] })}
                        type="video/mp4"
                      />
                    </video>
                  ) : (
                    /* eslint-disable-next-line @next/next/no-img-element */
                    <img
                      src={getAssetUrl({ media: vaultMedia && vaultMedia[0] })}
                      style={{
                        maxHeight: "30rem",
                        objectFit: "cover",
                        width: "100%",
                      }}
                      alt="valut-media img-fluid"
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <PromoteModal setChildRef={promoteModalRef} />
    </>
  );
}

const StoryButton = ({
  name,
  label,
  content,
  onClick,
  id,
  disable,
  setStory,
  story,
}: {
  name: keyof Story;
  label: string;
  content?: React.ReactNode;
  onClick?: Function;
  id?: string;
  disable: boolean;
  setStory: React.Dispatch<React.SetStateAction<Story>>;
  story: Story;
}) => {
  const setPostType = (value: object) => {
    Object.entries(value).forEach(([key, value]) => {
      if (key !== "promote") {
        setStory({
          ...story,
          [key]: value,
        });
      }
    });
  };

  return (
    <div className="post-type-btn-container position-relative col-lg-5 p-0 flex-grow-0 flex-md-grow-1 flex-lg-grow-1">
      <button
        className={`btn position-relative z-3 d-flex align-items-center gap-2 post-type-btn rounded-3 shadow-dark hover-active-hollow w-100 color-bold py-2 ${
          disable && "disabled grayscale opacity-50 pe-none"
        } ${story[name] == true && "active-post-btn"}`}
        onClick={(evt) => {
          setPostType({
            [name]: true,
          });
          onClick?.(evt);
        }}
        id={id}
      >
        <Image
          width={28}
          height={28}
          src={`/images/svg/${SvgMap[name]}.svg`}
          className="svg-icon"
          alt={name}
        />
        <span className="text-overflow-ellipsis d-lg-block d-md-block">
          {label}
        </span>
      </button>
      {content}
    </div>
  );
};
