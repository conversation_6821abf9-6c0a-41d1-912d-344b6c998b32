"use client";

import classNames from "classnames";
import { Formik } from "formik";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Swal from "sweetalert2";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import * as Yup from "yup";
import "./index.scss";

import type { MediaInfo } from "@/api/post";
import { PostSubmit } from "@/api/post";
import type { Product } from "@/api/shop";
import { CreateProduct, ShopItemCategory } from "@/api/shop";
import KYCwarning from "@/components/common/KYC-warning";
import Button from "@/components/common/button";
import ReadMoreContent from "@/components/common/chat/chat-box/read-more-content";
import FileInput from "@/components/common/file";
import FileHandler from "@/components/common/file/handler";
import Form, { FormHeader } from "@/components/common/form";
import Input from "@/components/common/input";
import InputArray from "@/components/common/input-array";
import InputGroup from "@/components/common/list";
import Select from "@/components/common/select";
import TextArea from "@/components/common/textarea";
import Wrapper, { Divider } from "@/components/common/wrapper";
import useFileUploader from "@/hooks/useFileUploader";
import useIsMobile from "@/hooks/useIsMobile";
import { configActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { formatNumber } from "@/utils/number";
import "swiper/css";
import "swiper/css/navigation";

// import { CreateProduct } from "@/api/shop";

export default function CreateShopItem() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const userName = useAppSelector((state) => state.user.profile.username);
  const isMobile = useIsMobile();
  const [isEnable, setIsEnable] = useState(false);
  const initialValues = {
    from: "",
    saved: false,
    nextBtnText: "",
    name: "",
    brand: "",
    media: [],
    category: "",
    custom_category: "",
    quantity: 1,
    description: "",
    variations: [],
    price: 5,
    end_date: "",
    sizes: [],
    sell: {
      type: "Normal",
      normal: { price: 0 },
      auction: { reserved_price: 0, end_date: "", end_time: "" },
    },
  };
  const [formValues, setFormValues] = useState(initialValues);
  const [preview, setPreview] = useState(false);
  const [shopItem, setShopItem] = useState<Product>({
    name: initialValues.name,
    brand: initialValues.brand,
    media: initialValues.media,
    category: initialValues.category,
    custom_category: initialValues.custom_category,
    quantity: initialValues.quantity,
    description: initialValues.description,
    variations: {
      colors: initialValues.variations,
      sizes: initialValues.sizes,
    },
    sell_type: initialValues.sell.type,
    price: initialValues.price,
    end_date: initialValues.sell.auction.end_date,
    is_public: true,
  });

  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .min(3, "First name must be at least 3 characters")
      .required("Name is required"),
    brand: Yup.string()
      .required("Brand name is required")
      .min(3, "Brand name must be at least 3 characters"),
    media: Yup.array()
      .of(Yup.string())
      .min(2, "At least two item photos are required.")
      .max(10, "At max 10 item photos are allowed.")
      .required("Media is required"),
    category: Yup.string().min(1).required("Category is required"),
    custom_category: Yup.string().when("category", {
      is: "Others",
      then: () =>
        Yup.string().required(
          'Custom category is required when category is "Others"'
        ),
      otherwise: () => Yup.string().notRequired(),
    }),
    price: Yup.number()
      .min(5, "Price must be greater than or equal to $5.00")
      .max(300000, "Price must be less than or equal to $3,00,000.00")
      .required("Price is required"),
    description: Yup.string().max(400).required("Description is required"),
    variations: Yup.array().max(10, "At max 10 item variation are allowed"),
    sizes: Yup.array().max(10, "At max 10 item size are allowed"),
    sell: Yup.object().shape({
      type: Yup.string()
        .oneOf(["Normal", "Auction"], "Invalid sell type")
        .required("Sell type is required"),
      normal: Yup.object().when("type", {
        is: "normal",
        then: () =>
          Yup.object().shape({
            price: Yup.number()
              .min(5, "Price must be greater than or equal to 5")
              .required("Price is required"),
          }),
      }),
      auction: Yup.object().when("type", {
        is: "auction",
        then: () =>
          Yup.object().shape({
            reserved_price: Yup.number()
              .min(5, "Reserved price must be greater than or equal to 5")
              .required("Reserved price is required"),
            end_date: Yup.date()
              .min(new Date(), "Auction end date must be in the future")
              .typeError("Invalid end date"),
            end_time: Yup.date()
              .min(new Date(), "Auction end time must be in the future")
              .typeError("Invalid end time"),
          }),
      }),
    }),
    quantity: Yup.number()
      .min(1, "Item available quantity must be greater than or equal to 1")

      .required("Item available quantity is required"),
  });

  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );

  useEffect(() => {
    dispatch(configActions.toggleShowPositionWidget());

    return () => {
      dispatch(configActions.toggleShowPositionWidget());
    };
  }, []);

  const Preview = ({
    setPreview,
  }: {
    setPreview: (value: boolean) => void;
  }) => {
    const [post, setPost] = useState(true);
    const { uploadFile } = useFileUploader();
    const _id = useAppSelector((state) => state.user.id);
    const id = `swiper-sell-item-${_id}`;
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [mainSwiper, setMainSwiper] = useState(null);

    const handleNext = () => {
      //@ts-expect-error "swiper typescript error"
      if (mainSwiper) mainSwiper.slideNext();
    };

    const handlePrev = () => {
      //@ts-expect-error "swiper typescript error"
      if (mainSwiper) mainSwiper.slidePrev();
    };

    const [viewImageIndex, setViewImageIndex] = useState(1);

    return (
      <>
        <FormHeader
          title="Create new item"
          closeIcon="back"
          onClick={() => setPreview(false)}
        >
          <div className="d-flex">
            <Button
              isValid={true}
              dirty={true}
              isSubmitting={isSubmitting}
              loader={isSubmitting}
              text="Submit"
              onClick={
                post
                  ? async () => {
                      router.push(`/creator/${userName}/shop-item`);

                      try {
                        const mediaInfo: MediaInfo[] | null = [];

                        const mediaArray: any = [...shopItem.media];

                        if (mediaArray.length) {
                          for (let i = 0; i < mediaArray.length; i++) {
                            const res = await uploadFile(mediaArray[i], "Post"); 
                            mediaInfo.push({
                              path: (res as any)?.[0]?.s3Multipart?.key,
                              type: res?.[0]?.type.split("/")[0] as
                                | "image"
                                | "video",
                            });
                          }
                        }

                        shopItem.media = mediaInfo;

                        await CreateProduct(shopItem)
                          .then(() => {
                            Swal.fire({
                              icon: "success",
                              title: "Created successfully",
                              text: "Your fans will be able to buy your product soon",
                              confirmButtonText: "Finish",
                              confirmButtonColor: "#AC1991",
                              showCloseButton: true,
                            }).then((res) => {
                              if (res.isConfirmed) {
                                window.location.reload();
                              }
                            });
                          })
                          .catch((error) => {
                            setIsSubmitting(false);

                            if (error.errorCode === 1000) {
                              Swal.fire({
                                icon: "warning",
                                title: "Oops! Some words aren’t allowed.",
                                text: `The word "${error.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
                              });
                            } else {
                              Swal.fire({
                                icon: "error",
                                title: error.message,
                              });
                            }
                          });
                      } catch (error) {
                        console.error(error);
                        setIsSubmitting(false);
                        Swal.fire({
                          icon: "error",
                          title: "Error",
                          text: "Failed to upload media",
                        });
                      }
                    }
                  : () => {
                      router.push(`/creator/${userName}/shop-item`);
                      setIsSubmitting(true);
                      CreateProduct(shopItem)
                        .then((res: any) => {
                          PostSubmit({
                            visibility: "Public",
                            type: "Product",
                            product_id: res?.data?._id,
                          }).then(() => {
                            setIsSubmitting(false);
                            Swal.fire({
                              icon: "success",
                              title: "Created successfully",
                              text: "Your Fans will be able to buy your item soon",
                              confirmButtonText: "Finish",
                              confirmButtonColor: "#AC1991",
                              customClass: {
                                confirmButton: "custom-btn",
                              },
                              showCloseButton: true,
                            }).then((res) => {
                              if (res.isConfirmed) {
                                router.push(`/creator/${userName}/shop-item`);
                              }
                            });
                          });
                        })

                        .catch((err) => {
                          console.error(err);
                          setIsSubmitting(false);

                          if (err.errorCode === 1000) {
                            Swal.fire({
                              icon: "warning",
                              title: "Oops! Some words aren’t allowed.",
                              text: `The word "${err.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
                            });
                          } else {
                            Swal.fire({
                              icon: "error",
                              title: err.message,
                            });
                          }
                        });
                    }
              }
            />
          </div>
        </FormHeader>

        <section className="container-xxl g-lg-3 g-0 py-3 d-flex gap-3 bg-body mt-4 rounded-3  flex-lg-row flex-column">
          <div
            className={classNames(
              "overflow-hidden position-relative",
              isMobile ? "w-100 p-3" : "w-50"
            )}
          >
            <Swiper
              key={id} // Unique key to re-render on `id` change
              modules={[Navigation]}
              // navigation={{
              //   nextEl: `.swiper-button-next`,
              //   prevEl: `.swiper-button-prev`,
              // }}
              direction="horizontal"
              effect="slide"
              //@ts-expect-error "swiper typescript error"
              onSwiper={setMainSwiper}
              slidesPerView={1}
              loop={false}
              followFinger={true}
              allowTouchMove={true}
              onSlideChange={(swiper) =>
                setViewImageIndex(swiper.activeIndex + 1)
              }
              className={`sell-item-images`} 
            >
              {shopItem?.media?.map((image: any, index) => (
                <SwiperSlide
                  key={index}
                  className="overflow-hidden w-100 h-100"
                >
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={URL.createObjectURL(image)}
                    alt=""
                    width={480}
                    height={400}
                    className="object-fit-cover w-100 "
                    style={{ borderRadius: "8px 8px 0 0" }}
                  />
                </SwiperSlide>
              ))}
              <div
                className={`swiper-button-prev shop-prev-btn  ${
                  viewImageIndex === 1 ? "d-none" : ""
                }`}
                onClick={handlePrev}
              ></div>
              <div
                className={`swiper-button-next shop-next-btn ${
                  viewImageIndex === shopItem?.media?.length ? "d-none" : ""
                }`}
                onClick={handleNext}
              ></div>
              <div className="sell-item-images-count position-absolute left-0 bottom-0 ms-2 mb-2 px-2 py-1 bg-body rounded-3 z-1">
                {formatNumber(viewImageIndex)}/
                {formatNumber(shopItem?.media?.length)}
              </div>
            </Swiper>
          </div>

          <div className={classNames(isMobile ? "w-100 p-3" : "w-50")}>
            <div className="pb-3 border-bottom">
              <span className="fs-4 fw-semibold d-block mb-3">
                {shopItem.name}
              </span>
              <p className=" fs-6 fw-medium">
                <ReadMoreContent
                  text={shopItem.description}
                  characterCount={50}
                />
              </p>
            </div>

            <div className="mt-3 fs-6 fw-medium border-bottom">
              <div className="row mb-3">
                <div className="col-4">Brand:</div>
                <div className="col-8">{shopItem.brand}</div>
              </div>
              <div className="row mb-3">
                <div className="col-4">Category:</div>
                <div className="col-8">{shopItem.category}</div>
              </div>
              {shopItem.category === "Others" && (
                <div className="row mb-3">
                  <div className="col-4">Custom Category:</div>
                  <div className="col-8">{shopItem.custom_category}</div>
                </div>
              )}

              <div className="row mb-3">
                <div className="col-4">Item Quantity:</div>
                <div className="col-8">{shopItem.quantity}</div>
              </div>

              <div className="row mb-3 align-items-center w-100">
                <div className="col-4">Variations:</div>
                <div className="col-8 ps-0 d-flex  gap-3  scrollable-container mw-50">
                  {shopItem?.variations?.colors?.map((variation) => (
                    <div
                      key={variation}
                      className="bg-cream rounded-3 py-2 px-3 text-nowrap pill pointer"
                    >
                      {variation}
                    </div>
                  ))}
                  {shopItem?.variations?.colors.length === 0 && (
                    <p className="m-0">NA</p>
                  )}
                </div>
              </div>

              <div className="row mb-3 align-items-center w-100">
                <div className="col-4">Size:</div>
                <div className="col-8 ps-0 d-flex gap-3 scrollable-container mw-50">
                  {shopItem?.variations?.sizes?.map((size) => (
                    <div
                      key={size}
                      className="bg-cream rounded-3 text-nowrap py-2 px-3 pill pointer text-uppercase"
                    >
                      {size}
                    </div>
                  ))}
                  {shopItem?.variations?.sizes.length === 0 && (
                    <p className="m-0">NA</p>
                  )}
                </div>
              </div>
            </div>

            <div className="mt-3 d-flex justify-content-between align-items-center">
              {/* <div className="d-flex gap-3 align-items-center fs-6 fw-medium">
                <div className="form-check form-switch">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    id="darkModeToggle"
                    onChange={() => {
                      post ? setPost(false) : setPost(true);
                    }}
                    checked={!post}
                  />
                </div>

                <span>Show/hide</span>
              </div> */}

              {/* <div className="fs-7">
                <a href="">Options</a>
              </div> */}
            </div>
          </div>
        </section>
      </>
    );
  };

  useEffect(() => {
    console.log(shopItem);
  }, [shopItem]);

  return (
    <>
      {preview ? (
        <Preview setPreview={setPreview} />
      ) : (
        <Formik
          initialValues={formValues}
          validationSchema={validationSchema}
          validateOnChange={true}
          validateOnBlur={true}
          onSubmit={async (data) => {
            setFormValues(data);

            setShopItem({
              name: data.name,
              brand: data.brand,
              media: data?.media?.map(
                (media) => media && FileHandler.get(media)
              ),
              category: data.category,
              custom_category: data.custom_category,
              quantity: data.quantity,
              price: data.price,
              description: data.description,
              variations: {
                colors: data.variations,
                sizes: data.sizes,
              },
              sell_type: data.sell.type.toUpperCase(),
              end_date: data?.end_date,
              is_public: true,
            });
            setPreview(true);
            setIsEnable(true);
          }}
        >
          {({ values, setFieldValue, ...rest }) => (
            <Form
              title="Create new item"
              nextBtnText="Create my item"
              formikValues={{ values, setFieldValue, ...rest }}
              dirty={isEnable}
            >
              <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1 ">
                <Wrapper title="" className="w-lg-75">
                  <Input
                    name="name"
                    label="Item name"
                    placeholder="Eg. Bondage, Dildo..."
                    className="fs-6"
                  />
                  <Input
                    name="brand"
                    label="Brand name"
                    required={false}
                    placeholder="Example: Nike, Adidas,..."
                  />
                  <Select
                    name="category"
                    label="Category"
                    array={Object.values(ShopItemCategory)
                      .filter((category) => category !== "All Items")
                      .map((category) => ({
                        text: category,
                        value: category,
                      }))}
                    selected={values.category}
                    setFieldValue={setFieldValue}
                    childClass="max-h-50"
                  />
                  {values.category === "Others" && (
                    <Input
                      customErrorClassname="position-absolue top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                      name="custom_category"
                      type="text"
                      label="Custom Category"
                      placeholder="Create your own category"
                    />
                  )}
                  <Input
                    name="quantity"
                    type="number"
                    label="Item quantity"
                    placeholder="1"
                  />
                </Wrapper>
                <Wrapper
                  title="Description"
                  counter={{ value: values.description, max: 400 }}
                >
                  <TextArea
                    name="description"
                    type="text"
                    required={false}
                    maxlength={400}
                    placeholder="Say something about your item here..."
                    customErrorClassname="position-absolut top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                  />
                </Wrapper>
                <Wrapper title="Details">
                  <InputArray
                    name="variations"
                    label="Variations (at max. 10 variants)"
                    required={false}
                    values={values.variations}
                    placeholder="Colours etc..."
                    showSearchList={false}
                    disable={values.variations.length >= 10}
                  />
                  <InputArray
                    name="sizes"
                    label="Size (at max. 10 variants)"
                    required={false}
                    values={values.sizes}
                    placeholder="New Size"
                    showSearchList={false}
                    disable={values.sizes.length >= 10}
                  />
                </Wrapper>
              </div>
              <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
                <Wrapper className="mb-2" title="Price & Sell Type">
                  <InputGroup
                    label=""
                    type="radio"
                    required={false}
                    theme="circle-purple"
                    name="sell.type"
                    className="align-items-md-start"
                    childClass="ms-1"
                    array={[
                      {
                        text: "Normal",
                        value: "Normal",
                      },
                      // {
                      //   text: "Auction",
                      //   value: "Auction",
                      // },
                    ]}
                    selected={values?.sell?.type}
                  />
                  <Divider />
                  {values?.sell?.type === "Auction" ? (
                    <>
                      <Input
                        customErrorClassname="position-absolue top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                        name="price"
                        type="number"
                        label="Reserver Price"
                        placeholder="0"
                        priceInput={true}
                        className="w-lg-75"
                      >
                        <span>Minimum $5.00 USD</span>
                      </Input>
                      <Input
                        customErrorClassname="position-absolue top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                        name="end_date"
                        type="date"
                        label="Auction Ends on"
                        placeholder="12/02/2001"
                        className="w-lg-75"
                      />
                    </>
                  ) : (
                    <>
                      <div className="position-relative">
                        <Input
                          customErrorClassname="position-absolue top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                          name="price"
                          type="number"
                          priceInput={true}
                          label="Price"
                          placeholder="0"
                          className="w-lg-75"
                        />
                      </div>
                      <span className="color-medium fs-7">
                        Minimum $5.00 USD
                      </span>
                    </>
                  )}
                </Wrapper>
                <Wrapper
                  className="mt-3"
                  titleClass="text-wrap"
                  title="Item photos (Add a minimum of 2 photos and a maximum of 10 photos.)"
                >
                  <FileInput
                    name="media"
                    accept="image"
                    required={false}
                    multiple={true}
                    maxFiles={10}
                    values={values}
                    setFieldValue={setFieldValue}
                  >
                    <div className="d-flex flex-column align-items-center justify-content-center text-center p-2 px-3 border border-style-dashed border-opacity-50">
                      <Image
                        src="/images/post/line-plus.svg"
                        width={25}
                        height={25}
                        alt="plus_icon"
                      />
                      <h6>Upload</h6>
                    </div>
                  </FileInput>
                </Wrapper>
                {!isKycCompleted && <KYCwarning type="shop item" />}
              </div>
            </Form>
          )}
        </Formik>
      )}
    </>
  );
}
