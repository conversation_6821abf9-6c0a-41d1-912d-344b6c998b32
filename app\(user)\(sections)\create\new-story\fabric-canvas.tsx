import domToImage from "dom-to-image";
import { Canvas, Textbox } from "fabric";
import { forwardRef, useEffect, useImperativeHandle, useRef } from "react";

import useIsMobile from "@/hooks/useIsMobile";

const FabricCanvas = forwardRef(
  (
    {
      textColor,
      backgroundColor,
      onExportComplete,
    }: {
      textColor: string;
      backgroundColor: string;
      onExportComplete?: (file: File) => void;
    },
    ref
  ) => {
    const canvasRef = useRef<HTMLCanvasElement | null>(null);
    const canvasInstanceRef = useRef<Canvas | null>(null);
    const isMobile = useIsMobile();

    useEffect(() => {
      const canvas = new Canvas(canvasRef.current!);
      canvasInstanceRef.current = canvas;

      // Reference to the single textbox
      let singleTextbox: Textbox | null = null;

      canvas.on("mouse:down", (options) => {
        if (options.target == null) {
          // If a textbox already exists, do not create a new one
          if (singleTextbox) {
            return;
          }

          // Get mouse position
          const pointer = canvas.getPointer(options.e);

          // Create a new textbox
          singleTextbox = new Textbox("Click to edit", {
            left: pointer.x,
            top: pointer.y,
            fill: textColor,
            selectionBackgroundColor: "#0000",
            fontWeight: 500,
            fontSize: isMobile ? 18 : 24,
            width: 150,
            splitByGrapheme: true,
          });

          canvas.add(singleTextbox);
          canvas.setActiveObject(singleTextbox);
          canvas.renderAll();
        }
      });

      return () => {
        canvas.dispose();
      };
    }, []);

    useEffect(() => {
      const canvas = canvasInstanceRef.current;

      if (canvas) {
        canvas.backgroundColor = backgroundColor;
        canvas.renderAll();
      }
    }, [backgroundColor]);

    useEffect(() => {
      const canvas = canvasInstanceRef.current;

      if (canvas) {
        const textbox = canvas.getObjects("textbox")[0] as Textbox;

        if (textbox) {
          textbox.set({ fill: textColor });
          canvas.renderAll();
        }
      }
    }, [textColor]);

    const handleExportImage = async () => {
      const node = canvasRef.current;
      const canvas = canvasInstanceRef.current;

      if (canvas && node) {
        try {
          canvas.discardActiveObject();
          canvas.renderAll();
          const scale = 2;
          const highResCanvas = document.createElement("canvas");
          const context = highResCanvas.getContext("2d")!;
          highResCanvas.width = node.width * scale;
          highResCanvas.height = node.height * scale;
          context.scale(scale, scale);
          context.drawImage(node, 0, 0);

          const blob = await domToImage.toBlob(highResCanvas, {
            quality: 1,
            width: highResCanvas.width,
            height: highResCanvas.height,
          });

          const file = new File([blob], "canvas-image.jpeg", {
            type: "image/jpeg",
          });

          if (onExportComplete) {
            onExportComplete(file);
          }
        } catch (error) {
          console.error("Failed to export image:", error);
        }
      }
    };

    useImperativeHandle(ref, () => ({
      exportImage: handleExportImage,
    }));

    return (
      <canvas
        ref={canvasRef}
        width={isMobile ? "auto" : "350"}
        height="500"
        id="c"
      />
    );
  }
);

FabricCanvas.displayName = "FabricCanvas";

export default FabricCanvas;
