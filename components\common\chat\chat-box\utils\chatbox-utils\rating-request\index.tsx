import classNames from "classnames";
import Image from "next/image";
import { memo, useState } from "react";

import ActionButton from "@/components/common/action-button";
import DateFormatter from "@/components/common/date";
import { InformationIcon } from "@/components/matchmaker/match-cards/utils/svg";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import { getAssetUrl } from "@/utils/assets";
import socketChannel from "@/utils/chatSocket";
import { formatCurrency } from "@/utils/formatter";

import ReadMoreContent from "../../../read-more-content";
import { handleReq } from "../../apiUtils";
import { ProcessingLoader } from "../../svg-utils";

import "swiper/css";
import "swiper/css/navigation";

const RatingRequestWrapper = ({
  message,
  rateRef,
  setCallReqId,
  imageModalRef,
  setImageMessage,
  usedAs = "sender",
  dateRequired = true,
}: {
  message: MessageInterface;
  rateRef: any;
  setCallReqId: (value: any) => void;
  imageModalRef: any;
  setImageMessage: (value: any) => void;
  usedAs?: "sender" | "receiver";
  dateRequired?: boolean;
}) => {
  const dispatch = useAppDispatch();

  const targetUser = useAppSelector((s) => s.chat.targetUser);
  const chatList = useAppSelector((s) => s.chat.chatList);
  const userId = useAppSelector((s) => s.user.id);

  const handleAccept = async () => {
    dispatch(chatActions.setFocusedReq(message));
  };

  const handleReject = async () => {
    await handleReq(message?.meta.reqId, false);
    socketChannel?.channel?.editMessage({
      message_id: message._id || message?.messageId,
      data: {
        ...message,
        meta: {
          ...message?.meta,
          requestAccept: false,
        },
      },
    });
  };

  const fetchCurrentChat = (message: MessageInterface) => {
    const chat = chatList.find(
      (chat) =>
        chat.target._id === targetUser || chat.initiator._id === targetUser
    );
    const isTargetSender =
      message?.sid === chat?.target?._id ||
      message?.sender_id === chat?.target?._id;

    const target = isTargetSender ? chat?.initiator : chat?.target;
    const user = isTargetSender ? chat?.target : chat?.initiator;
    return {
      target,
      user,
    };
  };

  if (
    !("media" in message?.meta) ||
    (Array.isArray(message?.meta?.media) &&
      message?.meta?.media?.some(
        (m) => m?.status !== "Completed" && m?.status !== "Failed"
      ))
  ) {
    return (
      <div className="p-2">
        <i>
          Processing media <ProcessingLoader />
        </i>
      </div>
    );
  }

  return (
    <div className={`pt-2`}>
      {message?.meta?.free_service && (
        <div className="position-absolute top-0 start-0 m-2 badge text-bg-secondary">
          Free Service
        </div>
      )}
      {message?.meta?.requestAccept === "sent" &&
        (Array.isArray(message?.meta?.media)
          ? message.meta.media[0]?.status !== "Moderating" &&
            message.meta.media[0]?.status !== "Optimizing"
          : message?.meta?.media?.status !== "Moderating" &&
            message?.meta?.media?.status !== "Optimizing") && (
          <div className="d-flex flex-row-reverse py-2 px-1 position-absolute top-0 end-0 m-1 mt-0">
            <div
              className={classNames("badge p-2", {
                "fs-10": window.innerWidth < 576,
              })}
              style={{
                color:
                  usedAs === "receiver"
                    ? "rgba(136, 77, 255, 1)"
                    : "rgba(255, 168, 0, 1)",
                border: `1px solid ${
                  usedAs === "receiver"
                    ? "rgba(136, 77, 255, 1)"
                    : "rgba(255, 168, 0, 1)"
                }`,
                backgroundColor:
                  usedAs === "receiver"
                    ? "rgba(136, 77, 255, 0.2)"
                    : "rgba(255, 168, 0, 0.12)",
              }}
            >
              {usedAs === "receiver" ? "New" : "Waiting for creator"}
            </div>
          </div>
        )}

      {(message?.meta?.requestAccept === "sent" ||
        message?.meta?.requestAccept === false) &&
      Array.isArray(message?.meta?.media) &&
      message?.meta?.media?.[0]?.status === "Completed" ? (
        <div className="d-flex flex-column">
          {message?.meta?.requestAccept !== "sent" && (
            <div className="d-flex flex-row-reverse py-2 px-1 position-absolute top-0 end-0 m-1 mt-0">
              <div
                className={classNames("badge p-2", {
                  "fs-10": window.innerWidth < 576,
                })}
                style={{
                  color:
                    message?.meta?.requestAccept === false
                      ? "rgba(245, 34, 45, 1)"
                      : "rgba(86, 194, 45, 1)",
                  border: `1px solid ${
                    message?.meta?.requestAccept === false
                      ? "rgba(245, 34, 45, 1)"
                      : "rgba(86, 194, 45, 1)"
                  }`,
                  backgroundColor:
                    message?.meta?.requestAccept === false
                      ? "rgba(245, 34, 45, 0.2)"
                      : "rgba(86, 194, 45, 0.2)",
                }}
              >
                {message?.meta?.requestAccept === false
                  ? "Declined"
                  : "Accepted"}
              </div>
            </div>
          )}
          <div
            className={`${
              message?.meta?.requestAccept === "sent" ? "mt-2" : ""
            } d-flex align-items-center gap-2 px-2`}
          >
            <span>
              <RatingSent />
            </span>
            <div>
              <div className="fw-bold">Ratings</div>
              <div className="d-flex gap-2">
                <div>1 turn</div>
                <div className="vr"></div>
                <div>Price: {formatCurrency(message?.meta?.price || 0)}</div>
              </div>
            </div>
          </div>
          {usedAs === "receiver" && (
            <>
              {message?.meta?.requestAccept === "sent" && (
                <div className="d-flex my-2 justify-content-center gap-3 flex-column flex-lg-row px-2">
                  <span
                    onClick={handleAccept}
                    data-bs-toggle="modal"
                    className="w-100"
                    data-bs-target="#rateModal"
                  >
                    <ActionButton className="w-100">
                      Accept for {formatCurrency(message?.meta?.price || 0)}
                    </ActionButton>
                  </span>
                  <ActionButton
                    type="outline"
                    variant="danger"
                    onClick={handleReject}
                  >
                    Decline
                  </ActionButton>
                </div>
              )}
            </>
          )}
        </div>
      ) : !Array.isArray(message?.meta?.media) &&
        (message?.meta?.media?.status === "Moderating" ||
          message?.meta?.media?.status === "Optimizing") ? (
        <div className="color-medium p-2">
          Processing and Optimising{" "}
          {message?.sender_id === userId || message?.sid === userId
            ? "your"
            : "their"}{" "}
          media request...
        </div>
      ) : !Array.isArray(message?.meta?.media) &&
        message?.meta?.media?.status === "Failed" ? (
        <div className="color-medium p-2">
          <InformationIcon />
          <span className="ms-2 ">
            The moderation of the media has been failed due to our content
            moderation policy.
          </span>
        </div>
      ) : (
        <div>
          {message?.meta?.requestAccept === true && (
            <div>
              <div className={` d-flex align-items-center gap-2 mb-2 px-2`}>
                <span>
                  <RatingSent />
                </span>
                <div>
                  <div className="fw-bold">Ratings</div>
                  <div className="d-flex gap-2">
                    <div>1 turn</div>
                    <div className="vr"></div>
                    <div>
                      Price: {formatCurrency(message?.meta?.price || 0)}
                    </div>
                  </div>
                </div>
              </div>
              <div className="d-flex flex-row-reverse py-2 px-1 position-absolute top-0 end-0 m-1">
                <div
                  className={classNames("badge p-2", {
                    "fs-10": window.innerWidth < 576,
                  })}
                  style={{
                    color: "rgba(86, 194, 45, 1)",
                    border: `1px solid ${"rgba(86, 194, 45, 1)"}`,
                    backgroundColor: "rgba(86, 194, 45, 0.2)",
                  }}
                >
                  Completed
                </div>
              </div>
              <div className="d-flex gap-1 px-2">
                <div className="color-medium">Rating request type:</div>
                <div className="text-capitalize text-center fs-7"></div>
                {message?.meta.ratingType}
              </div>
              <div className="d-flex gap-2 mb-2 px-2">
                <span className="color-medium fw-bold">Message:</span>
                <ReadMoreContent text={message?.meta?.details || ""} />
              </div>
              <NativeMediaSwiper
                message={message}
                usedAs={usedAs}
                setImageMessage={setImageMessage}
              />
              <div className="my-2 px-2">
                {Array.from({ length: message?.meta?.stars || 0 }).map(
                  (_, index) => (
                    <RatingStars key={index} />
                  )
                )}
              </div>
              <div className="fw-bold fs-6 px-2">{message?.meta?.rateText}</div>
            </div>
          )}
        </div>
      )}
      {dateRequired && (
        <span className="msg-time text-lowercase text-lowercase d-block fs-9 text-opacity-75 text-dark text-end w-200 mt-1">
          <DateFormatter
            dateString={message.createdAt || message.createdAt}
            formatType="h:mm a"
          />
        </span>
      )}
    </div>
  );
};

export default memo(RatingRequestWrapper);

export const RatingSent = () => (
  <svg
    width="72"
    height="72"
    viewBox="0 0 72 72"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="72" height="72" rx="8" fill="#E9F7FF" />
    <path
      d="M55.1325 28.7447L43.5728 26.985L38.401 16.0089C37.4957 14.0921 34.5013 14.0921 33.5961 16.0089L28.4269 26.985L16.8645 28.7447C14.6763 29.0786 13.7898 31.9076 15.3807 33.5353L23.7478 42.0782L21.7739 54.1432C21.4016 56.4277 23.6943 58.1817 25.6602 57.1012L35.9985 51.4069L46.3396 57.104C48.2894 58.1733 50.6008 56.4473 50.2258 54.146L48.2519 42.081L56.619 33.5381C58.2072 31.9076 57.3207 29.0786 55.1325 28.7447Z"
      fill="#27B1FF"
    />
  </svg>
);

const RatingStars = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M27.9057 11.486L20.713 10.3911L17.495 3.56151C16.9317 2.36882 15.0685 2.36882 14.5052 3.56151L11.2889 10.3911L4.0945 11.486C2.73296 11.6938 2.18134 13.454 3.17125 14.4668L8.37745 19.7824L7.14923 27.2895C6.91758 28.711 8.34412 29.8024 9.56734 29.1301L16.0001 25.5869L22.4345 29.1318C23.6477 29.7971 25.0859 28.7232 24.8526 27.2913L23.6244 19.7841L28.8306 14.4686C29.8189 13.454 29.2672 11.6938 27.9057 11.486Z"
      fill="#FFB800"
    />
  </svg>
);

const NativeMediaSwiperBase = ({
  message,
  usedAs,
  setImageMessage,
}: {
  message: MessageInterface;
  usedAs: "receiver" | "sender";
  setImageMessage: (value: MessageInterface) => void;
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const mediaItems = Array.isArray(message?.meta?.media)
    ? message.meta.media
    : [message?.meta?.media];

  const handleNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === mediaItems.length - 1 ? 0 : prevIndex + 1
    );
  };

  const handlePrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? mediaItems.length - 1 : prevIndex - 1
    );
  };

  if (!mediaItems || mediaItems.length === 0) {
    return <div>No media available</div>;
  }

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        maxWidth: "800px",
        margin: "0 auto",
        overflow: "hidden",
      }}
    >
      <div
        style={{
          display: "flex",
          transition: "transform 0.5s ease",
          transform: `translateX(-${currentIndex * 100}%)`,
        }}
      >
        {mediaItems.map((mediaItem, index) => (
          <div
            key={index}
            style={{
              minWidth: "100%",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {mediaItem!.type === "image" ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={getAssetUrl({ media: mediaItem })}
                alt="Rating Request"
                height={250}
                width={250}
                style={{
                  width: "100%",
                  objectFit: "cover",
                  cursor: "pointer",
                  // border: `3px solid ${
                  //   usedAs === "receiver" ? "#F5F5F6" : "#ac1991"
                  // }`,
                }}
                className="mx-auto d-block"
                onClick={() => {
                  setImageMessage(message);

                  const mediaType = mediaItem?.type;
                  const mediaUrl = getAssetUrl({ media: mediaItem });

                  if (mediaType === "video" || mediaType === "image") {
                    window.KNKY.showFullscreenMedia(mediaType, mediaUrl);
                  }
                }}
              />
            ) : (
              <video
                height={250}
                width={250}
                style={{
                  objectFit: "cover",
                  cursor: "pointer",
                }}
                className="mx-auto d-block"
                src={getAssetUrl({ media: mediaItem })}
                playsInline
                controls
              ></video>
            )}
          </div>
        ))}
      </div>

      {mediaItems.length > 1 && (
        <>
          <button
            onClick={handlePrevious}
            style={{
              position: "absolute",
              top: "50%",
              left: "10px",
              transform: "translateY(-50%)",
              background: "rgba(0, 0, 0, 0.5)",
              color: "#fff",
              border: "none",
              borderRadius: "50%",
              width: "30px",
              height: "30px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
          >
            <Image
              src="/images/chat/left-arrow.png"
              alt=""
              width={30}
              height={30}
            />
          </button>
          <button
            onClick={handleNext}
            style={{
              position: "absolute",
              top: "50%",
              right: "10px",
              transform: "translateY(-50%)",
              background: "rgba(0, 0, 0, 0.5)",
              color: "#fff",
              border: "none",
              borderRadius: "50%",
              width: "30px",
              height: "30px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
          >
            <Image
              src="/images/chat/right-arrow.png"
              alt=""
              width={30}
              height={30}
            />
          </button>
        </>
      )}

      {mediaItems.length > 1 && (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            marginTop: "10px",
          }}
        >
          {mediaItems.map((_, index) => (
            <div
              key={index}
              onClick={() => setCurrentIndex(index)}
              style={{
                width: "6px",
                height: "6px",
                background:
                  currentIndex === index
                    ? "rgb(189 133 179)"
                    : "rgb(185 182 184)",
                borderRadius: "50%",
                margin: "0 5px",
                cursor: "pointer",
              }}
            ></div>
          ))}
        </div>
      )}
    </div>
  );
};

const NativeMediaSwiper = memo(NativeMediaSwiperBase);
