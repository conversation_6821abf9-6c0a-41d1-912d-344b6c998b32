/* eslint-disable @next/next/no-img-element */
"use client";

import classNames from "classnames";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

import LogoutModal from "@/components/settings/logout";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import "./index.scss";

import WalletBalanceBadge from "@/components/common/header/wallet-balance-badge";
import { Divider } from "@/components/common/wrapper";
import { ModalPortal } from "@/components/portals/ModalPortal";
import InstallPWA from "@/components/settings/installPWA";
import ThemeToggle from "@/components/ThemeToggle";
import { settingsActions } from "@/redux-store/actions";
import { KycStatus } from "@/redux-store/slices/user.slice";
import { getAssetUrl } from "@/utils/assets";

const SettingsMenu = () => {
  const router = useRouter();
  const activeItem = useAppSelector((s) => s.settings.activeItem);
  const dispatch = useAppDispatch();
  const isMobile = window.innerWidth < 768;
  const user = useAppSelector((state) => state.user);
  const role = useAppSelector((s) => s.user.role);
  const isAgencyTagged = useAppSelector((s) => s.user.profile.isAgencyTagged);
  const knky_verified = useAppSelector(
    (s) => s.user.profile.badges.user_badges
  ).some((badge) => badge === "VerifiedByKnky");
  const isVerified = useAppSelector((state) => state.user.isVerified);

  const lottieMap: {
    [K in "user" | "creator" | "knky_creator"]: string;
  } = {
    user: "https://lottie.host/embed/a15df9fb-9063-4865-a2f2-3ceb06dd0522/nh9sxFi2ji.json",
    creator:
      "https://lottie.host/embed/080d6ea5-08d1-4421-ab5b-3b1b86970f0d/vxwJHsW3YR.json",
    knky_creator:
      "https://lottie.host/embed/b08402e1-027d-481d-95be-58c0fab603b9/LGRIqsxXLK.json",
  };
  const [isModalOpen, setModalOpen] = useState(false);

  const isOpen = () => {
    setModalOpen(!isModalOpen);
  };

  const handleOptionClick = (path: string) => {
    dispatch(settingsActions.toggleMenuVisibility());
    router.push(path);
  };

  const isSettings =
    activeItem === "2FA" ||
    activeItem === "Privacy & Management" ||
    activeItem === "Change My Password" ||
    activeItem === "Devices";

  return (
    <>
      <div
        className={classNames(
          "nav d-flex gap-2 flex-column col-lg-3 col-md-3 col-sm-12 nav-pills setting-sideBar bg-default z-3  overflow-auto",
          { "rounded-3": !isMobile },
          { "p-3": isMobile }
        )}
      >
        {isMobile && (
          <>
            <div className="my-3  d-flex align-items-center gap-2 color-dark">
              <img
                src="/images/svg/close-comment.svg"
                alt=""
                width={24}
                height={24}
                onClick={() => dispatch(settingsActions.toggleMenuVisibility())}
              />
              <h5 className="mb-0 fsw-semibold">Menu</h5>
            </div>
            <div
              className={classNames(
                "bg-body p-2 d-flex justify-content-between",
                {
                  "rounded-3": isMobile,
                }
              )}
            >
              <div
                onClick={() =>
                  handleOptionClick(`/${user?.role}/${user?.profile?.username}`)
                }
                className="d-flex gap-3 align-items-center"
              >
                <img
                  src={getAssetUrl({
                    media: user.profile.avatar[0],
                    variation: "thumb",
                  })}
                  width={40}
                  height={40}
                  alt=""
                  className="rounded-circle"
                />
                <div className="d-flex flex-column justify-content-around">
                  <span className="fw-semibold">
                    {user.profile.display_name}
                  </span>
                  <span className="fs-7s color-black fw-light">
                    See my profile
                  </span>
                </div>
              </div>
            </div>
          </>
        )}

        <div
          className={classNames("bg-body rounded-top-3", {
            "rounded-3": isMobile,
          })}
        >
          <div
            className={classNames(
              "wallet-balance-container d-flex align-items-center py-2 px-3 justify-content-between  rounded-top-3 "
            )}
          >
            <span className="color-dark fw-semibold z-3">
              Available balance:
            </span>
            <div className="z-2">
              <WalletBalanceBadge from="dropdown" />
            </div>
          </div>
          <div className="d-flex  pointer justify-content-between">
            <div
              onClick={() => handleOptionClick("/settings/wallet")}
              className="d-flex align-items-center gap-2 w-100 justify-content-center  dropdown-item header-dropdown-item pointer"
            >
              <img
                src="/settings/wallet.svg"
                style={{
                  filter:
                    "Wallet" == activeItem ? "grayscale(0)" : "grayscale(1)",
                }}
                alt=""
                width={24}
                height={24}
              />
              <span
                className={classNames({
                  "color-primary": "Wallet" == activeItem,
                })}
              >
                Wallet
              </span>
            </div>
            <Divider direction="end" />
            <div
              onClick={() => handleOptionClick("/settings/addCard")}
              className="d-flex align-items-center gap-2 w-100 justify-content-center dropdown-item header-dropdown-item pointer"
            >
              <img
                src="/settings/card.svg"
                style={{
                  filter:
                    "Card" == activeItem ? "grayscale(0)" : "grayscale(1)",
                }}
                alt=""
                width={24}
                height={24}
              />
              <span
                className={classNames({
                  "color-primary": "Card" == activeItem,
                })}
              >
                Card
              </span>
            </div>
          </div>
        </div>

        <div className={classNames("bg-body", { "rounded-3": isMobile })}>
          <div
            onClick={() => handleOptionClick("/settings/account-information")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src="/settings/user.svg"
              style={{
                filter:
                  "Account Information" == activeItem
                    ? "grayscale(0)"
                    : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames({
                "color-primary": "Account Information" == activeItem,
              })}
            >
              Account Information
            </span>
          </div>
          {isAgencyTagged && (
            <div
              onClick={() => handleOptionClick("/settings/agency")}
              className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
            >
              <img
                src="/settings/agency.svg"
                style={{
                  filter:
                    "Agency" == activeItem ? "grayscale(0)" : "grayscale(1)",
                }}
                alt=""
                width={24}
                height={24}
              />
              <span
                className={classNames({
                  "color-primary": "Agency" == activeItem,
                })}
              >
                Agency
              </span>
            </div>
          )}

          <div
            onClick={() => handleOptionClick("/settings/refer-and-earn")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src="/settings/refer.svg"
              style={{
                filter:
                  "Refer To Earn" == activeItem
                    ? "grayscale(0)"
                    : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames({
                "color-primary": "Refer To Earn" == activeItem,
              })}
            >
              Refer To Earn
            </span>
          </div>
          <div className="d-flex align-items-cente flex-column gap-2 w-100 bg-body accordion-item dropdown-item header-dropdown-item pointer">
            <div className="accordion-header " id="panelsStayOpen-headingTwo">
              <button
                className={classNames({
                  "color-primary": isSettings,
                  "accordion-button collapsed  d-flex justify-content-between fw-medium p-0  align-items-center":
                    true,
                })}
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#panelsStayOpen-collapseTwo"
                aria-expanded="false"
                aria-controls="panelsStayOpen-collapseTwo"
              >
                <div className="d-flex  align-items-center w-100 gap-2">
                  <img
                    src="/settings/settings.svg"
                    style={{
                      filter: isSettings ? "grayscale(0)" : "grayscale(1)",
                    }}
                    alt=""
                    width={24}
                    height={24}
                  />
                  <span> Settings & Privacy</span>
                </div>

                <img
                  src="/images/post/fill-arrow-down.svg"
                  width={12}
                  height={12}
                  alt=""
                />
              </button>
            </div>
            <div
              id="panelsStayOpen-collapseTwo"
              className="accordion-collapse collapse"
              aria-labelledby="panelsStayOpen-headingTwo"
            >
              <div
                className="accordion-body"
                style={{ maxHeight: "initial !important" }}
              >
                <div
                  onClick={() =>
                    handleOptionClick(
                      "/settings/settings-and-privacy?type=devices"
                    )
                  }
                  className="dropdown-item header-dropdown-item pointer color-dark fw-medium  "
                >
                  Devices
                </div>
                <div
                  onClick={() =>
                    handleOptionClick(
                      "/settings/settings-and-privacy?type=change-my-password"
                    )
                  }
                  className="dropdown-item header-dropdown-item pointer color-dark fw-medium"
                >
                  Change my password
                </div>
                <div
                  onClick={() =>
                    handleOptionClick(
                      "/settings/settings-and-privacy?type=privacy-and-management"
                    )
                  }
                  className="dropdown-item header-dropdown-item pointer color-dark fw-medium"
                >
                  Privacy & Management
                </div>
                <div
                  onClick={() =>
                    handleOptionClick("/settings/settings-and-privacy?type=2fa")
                  }
                  className="dropdown-item header-dropdown-item pointer color-dark fw-medium"
                >
                  2FA
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="color-dark fw-semibold">Other</div>
        <div className={classNames("bg-body ", { "rounded-3": isMobile })}>
          {role === "creator" && (
            <div
              onClick={() => handleOptionClick("/settings/creator-settings")}
              className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
            >
              <img
                src="/settings/money.svg"
                style={{
                  filter:
                    "Creator Settings" == activeItem
                      ? "grayscale(0)"
                      : "grayscale(1)",
                }}
                alt=""
                width={24}
                height={24}
              />
              <span
                className={classNames({
                  "color-primary": "Creator Settings" == activeItem,
                })}
              >
                Creator Settings
              </span>
            </div>
          )}
          <div
            onClick={() => handleOptionClick("/settings/link-accounts")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src="/settings/socials.svg"
              style={{
                filter:
                  "Connected Accounts" == activeItem
                    ? "grayscale(0)"
                    : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames({
                "color-primary": "Connected Accounts" == activeItem,
              })}
            >
              Connected Accounts
            </span>
          </div>
          {role === "creator" && (
            <div
              onClick={() => handleOptionClick("/dashboard")}
              className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
            >
              <img
                src="/settings/revenue.svg"
                style={{
                  filter:
                    "Revenue" == activeItem ? "grayscale(0)" : "grayscale(1)",
                }}
                alt=""
                width={24}
                height={24}
              />
              <span
                className={classNames({
                  "color-primary": "Revenue" == activeItem,
                })}
              >
                Revenue
              </span>
            </div>
          )}
          <div
            onClick={() => handleOptionClick("/settings/my-offers")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src="/settings/user.svg"
              style={{
                filter:
                  "My Offers" == activeItem ? "grayscale(0)" : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames({
                "color-primary": "My Offers" == activeItem,
              })}
            >
              My Offers
            </span>
          </div>

          <div
            onClick={() => handleOptionClick("/settings/kyc")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src="/settings/kyc.svg"
              style={{
                filter:
                  "Verification" == activeItem
                    ? "grayscale(0)"
                    : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames(
                {
                  "color-primary": "Verification" == activeItem,
                },
                "d-flex gap-2 align-items-center justify-content-center"
              )}
            >
              Verification
              {isVerified === KycStatus.ACCEPTED ? (
                <>
                  {(role === "creator" || role === "user") && !knky_verified ? (
                    <iframe
                      src={lottieMap[role]}
                      width={25}
                      height={25}
                      loading="lazy"
                    ></iframe>
                  ) : null}
                  {role === "creator" && knky_verified ? (
                    <iframe
                      src={lottieMap["knky_creator"]}
                      width={25}
                      height={25}
                      loading="lazy"
                    ></iframe>
                  ) : null}
                </>
              ) : (
                <span
                  className={
                    isVerified === KycStatus.SUBMITTED
                      ? "extra-svg-review"
                      : "extra-svg-rejected"
                  }
                >
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M9.88699 13.668L4.10699 13.668C1.84699 13.668 0.333658 12.0813 0.333658 9.7213L0.333657 4.27464C0.333657 1.9213 1.84699 0.334636 4.10699 0.334636L9.88699 0.334635C12.147 0.334635 13.667 1.9213 13.667 4.27464L13.667 9.7213C13.667 12.0813 12.147 13.668 9.88699 13.668ZM7.00699 8.9613C7.15966 8.96306 7.30548 9.02494 7.41281 9.13352C7.52014 9.2421 7.58033 9.38863 7.58032 9.5413C7.58097 9.65724 7.54705 9.77073 7.48291 9.86731C7.41876 9.96388 7.32729 10.0391 7.22018 10.0835C7.11306 10.1279 6.99516 10.1393 6.88151 10.1163C6.76787 10.0934 6.66365 10.0371 6.58214 9.95464C6.50064 9.87219 6.44555 9.76732 6.42392 9.65342C6.40228 9.53952 6.41507 9.42176 6.46066 9.31516C6.50625 9.20856 6.58257 9.11797 6.67988 9.05494C6.77719 8.99192 6.89107 8.95932 7.00699 8.9613ZM6.42032 4.4813C6.42032 4.1613 6.68032 3.9013 7.00699 3.9013C7.16082 3.9013 7.30834 3.96241 7.41711 4.07118C7.52588 4.17995 7.58699 4.32748 7.58699 4.4813L7.58699 7.42797C7.58699 7.74797 7.32699 8.01464 7.00699 8.01464C6.68033 8.01464 6.42032 7.74797 6.42032 7.42797L6.42032 4.4813Z"
                    />
                  </svg>
                </span>
              )}
            </span>
          </div>
          <div
            onClick={() => handleOptionClick("/settings/address")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src="/settings/address.svg"
              style={{
                filter:
                  "Address" == activeItem ? "grayscale(0)" : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames({
                "color-primary": "Address" == activeItem,
              })}
            >
              Address
            </span>
          </div>
          <div
            onClick={() => handleOptionClick("/settings/blocks")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src="/settings/block.svg"
              style={{
                filter:
                  "Block List" == activeItem ? "grayscale(0)" : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames({
                "color-primary": "Block List" == activeItem,
              })}
            >
              Block List
            </span>
          </div>
          <div
            onClick={() => handleOptionClick("/platform-plans")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img src="/settings/plans.svg" alt="" width={24} height={24} />
            <span
              className={classNames({
                "color-primary": "Premium plans" == activeItem,
              })}
            >
              Premium plans
            </span>
          </div>

          <div
            onClick={() => handleOptionClick("/settings/help-and-support")}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src={"/settings/info.svg"}
              style={{
                filter:
                  "Help & Support" == activeItem
                    ? "grayscale(0)"
                    : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames({
                "color-primary": "Help & Support" == activeItem,
              })}
            >
              Help & Support
            </span>
          </div>
          {/* {(process.env.environment === "test" ||
            process.env.environment === "development") && (
            <div
              onClick={() => handleOptionClick("/settings/release-form")}
              className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
            >
              <img
                src={"/settings/form.svg"}
                style={{
                  filter:
                    "Release form" == activeItem
                      ? "grayscale(0)"
                      : "grayscale(1)",
                  marginLeft: "0.15rem",
                }}
                alt=""
                width={21}
                height={21}
              />
              <span
                className={classNames({
                  "color-primary": "Release form" == activeItem,
                })}
              >
                Tag & Release
              </span>
            </div>
          )} */}
        </div>

        <div
          className={classNames("bg-body rounded-bottom-3 ", {
            "rounded-3": isMobile,
          })}
        >
          <InstallPWA />
          {process.env.environment !== "live" && (
            <div className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer justify-content-between">
              <ThemeToggle />
            </div>
          )}
          <div
            onClick={isOpen}
            className="d-flex align-items-center gap-2 w-100  dropdown-item header-dropdown-item pointer"
          >
            <img
              src="/settings/logOut.svg"
              style={{
                filter:
                  "Log Out" == activeItem ? "grayscale(0)" : "grayscale(1)",
              }}
              alt=""
              width={24}
              height={24}
            />
            <span
              className={classNames({
                "color-primary": "Log Out" == activeItem,
              })}
            >
              Log Out
            </span>
          </div>
        </div>

        <ul className="d-flex nav   justify-content-lg-center pb-lg-0 justify-content-md-center mb-lg-3 fs-6 d-md-none  gap-1">
          <li className="nav-item fw-medium fs-7 color-dark  px-2 py-1">
            <Link href={"https://lander.knky.co/"} target="_blank">
              About us
            </Link>
          </li>
          <Divider direction="end" />
          <li className="nav-item fw-medium fs-7 color-dark  px-2 py-1">
            <Link href={"/articles/terms-of-service"} target="_blank">
              T&Cs
            </Link>
          </li>
          <Divider direction="end" />
          <li className="nav-item fw-medium fs-7 color-dark  px-2 py-1">
            <Link href={"https://help.knky.co/en/"} target="_blank">
              Help
            </Link>
          </li>
          <Divider direction="end" />
          <li className="nav-item fw-medium fs-7 color-dark  px-2 py-1">
            <Link href={"/platform-plans"} target="_blank">
              Premium Plans
            </Link>
          </li>
        </ul>
        <div>
          <p className=" fs-9 color-medium ps-1 d-md-none d-lg-none d-block">
            © {new Date().getFullYear()} KNKY®
          </p>
        </div>
      </div>
      <ModalPortal>
        <LogoutModal isModalOpen={isModalOpen} isOpen={isOpen} />
      </ModalPortal>
    </>
  );
};

export default SettingsMenu;
