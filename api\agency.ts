import API from ".";

export interface GetAgencyDetailsResponse {
  _id: string;
  agencyId: {
    _id: string;
    agency_name: string;
    email: string;
  };
  creatorId: string;
  senderId: {
    _id: string;
    email: string;
    username: string;
  };
  status: string;
  invitationLetter: string;
  paidBy: string;
  revenueShare: RevenueShare;
  creatorAction?: string;
  accessManagementType: string;
  resourcePermissions: ResourcePermission[];
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  __v: number;
}

interface ResourcePermission {
  resourceName: string;
  permitted: boolean;
  _id: string;
}

interface RevenueShare {
  creator: number;
  platform: number;
  agency: number;
  _id: string;
}

export interface AcceptAgencyCollabBody {
  resourcePermissions: string[];
  revenueShare?: {
    creator: number;
    platform: number;
    agency: number;
  };
}
export const GetAgencyDetails = async (id: string) =>
  API.get(`${API.USERS}/${id}/agency-collab-details`) as Promise<any>;

export const AcceptAgencyCollab = async (
  id: string,
  status: string,
  body: AcceptAgencyCollabBody
) =>
  API.patch(
    `${API.USERS}/${id}/agency-request/${status}`,
    body
  ) as Promise<any>;

export const GetAgencyContractDetails = async () =>
  API.get(`${API.USERS}/agency-collab-details`) as Promise<any>;

export const RevokeAgencyCollab = async (id: string) =>
  API.post(`${API.USERS}/revoke-agency-collab/${id}`, {}) as Promise<any>;
