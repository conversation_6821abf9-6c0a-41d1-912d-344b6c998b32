import { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

import { earningsAnalysis, earningsList } from "@/api/dashboard";
import LoadingSpinner from "@/components/common/loading";
import { useAppSelector } from "@/redux-store/hooks";
import { getDateCategoryInfo, getWeekInfo } from "@/utils/creator-dashboard";

import BarChart from "../../utils/bar-chart";
import AllEarningsTable from "../../utils/table-content/earnings/all-earnings";
import TipsTable from "../../utils/table-content/earnings/tips";

type Types =
  | "tips"
  | "shop_items"
  | "chat_fee"
  | "special_options"
  | "other"
  | "premium_post"
  | "premium_story"
  | "tickets"
  | "subscription"
  | "promotion";

const listingType = {
  tips: "tip",
  shop_items: "sell",
  chat_fee: "chat_fee",
  special_options: "special_options",
  other: "other",
  premium_post: "premium_post",
  premium_story: "premium_story",
  tickets: "ticket",
  subscription: "subscription",
  promotion: "promotion",
};
const trafficColors = ["#000000"];

export default function EarningsTab({
  type,
  total,
}: {
  type: Types;
  total: number;
}) {
  const [earningData, setEarningData] = useState([]) as any[];
  const [currentPage, setCurrentPage] = useState(1);
  const [pageCount, setPageCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [TraficSeries, setTrafficSeries] = useState([]) as any[];
  const [categories, setCategories] = useState([]) as any[];
  const userId = useAppSelector((state) => state.user.id);
  const dateFilter = useAppSelector(
    (state) => state.user.creatorDashboardDateFilter
  );

  useEffect(() => {
    setIsLoading(true);

    const updateData = async () => {
      const from = dateFilter.split("=")[1].split("&")[0];
      const to = dateFilter.split("=")[2];
      const { categoryArray, groupCategory, filter } = getDateCategoryInfo(
        from,
        to
      );

      try {
        const analysisResult = await earningsAnalysis(
          groupCategory,
          dateFilter
        );
        const listResult = await earningsList(listingType[type], 1, dateFilter);
        setCurrentPage(1);

        const { data: hourlyData } = analysisResult;
        const categories = categoryArray;
        const trafficSeries = getTrafficSeries(filter);
        setCategories(categories);
        setTrafficSeries(trafficSeries);
        updateTrafficSeries(hourlyData, filter ? filter : "year", type);
        setEarningData(listResult.data.response);
        setPageCount(listResult.data.pages_available);
        setIsLoading(false);
      } catch (error) {
        const categories = categoryArray;
        setCategories(categories);
        console.error(error);
        setIsLoading(false);
        setEarningData([]);
        setPageCount(0);
        setTrafficSeries([]);
      }
    };

    updateData();
  }, [type, dateFilter]);

  const fetchMoreData = async () => {
    const pageNO = currentPage + 1;
    setCurrentPage(pageNO);
    const listResult = await earningsList(
      listingType[type],
      pageNO,
      dateFilter
    );

    setEarningData((prevData: any) => [
      ...prevData,
      ...listResult.data.response,
    ]);
  };

  const getTrafficSeries = (filter: string) => {
    const lengthsMap: any = {
      today: 24,
      yesterday: 24,
      week: 7,
      month: 31,
      year: 12,
    };
    const length = lengthsMap[filter] || 0;
    return [{ name: "Amount", data: Array.from({ length }, () => 0) }];
  };

  const updateTrafficSeries = (
    hourlyData: any[],
    filter: string,
    selectedType: string
  ) => {
    const updatedSeries = [...getTrafficSeries(filter)];

    let type = selectedType;

    if (selectedType === "subscription") {
      type = "subs";
    }

    hourlyData.forEach((data: any) => {
      const earnings = data[type];
      if (!earnings) return;
      const earnedAmount = earnings.e || 0;

      const index =
        filter === "today"
          ? data.hour
          : filter === "week"
          ? getWeekInfo(data.timestamp).findIndex(
              (day) => parseInt(day.date) === data.day
            )
          : filter === "month"
          ? data.day - 1
          : filter === "year"
          ? data.month - 1
          : -1;

      if (index !== -1) {
        updatedSeries[0].data[index] = earnedAmount;
      }
    });
    setTrafficSeries(updatedSeries);
  };

  return (
    <div className="subscriber-content-wrapper">
      <ul
        className="nav nav-pills mb-3 w-100 flex-nowrap text-nowrap scrollable"
        id="pills-tab"
        role="tablist"
      ></ul>

      <div className="tab-content" id="pills-tabContent">
        <div
          className="tab-pane fade show active"
          id={`${
            type === "tips"
              ? "#pills-total-tips"
              : type === "shop_items"
              ? "#pills-total-sell"
              : type === "chat_fee"
              ? "#pills-total-message"
              : type === "special_options"
              ? "#pills-total-options"
              : type === "other"
              ? "#pills-total-other"
              : "#pills-total"
          }`}
          role="tabpanel"
          aria-labelledby="pills-total-tab"
        >
          {isLoading ? (
            <div
              className="d-flex justify-content-center align-items-center"
              style={{ height: "60vh" }}
            >
              <LoadingSpinner />
            </div>
          ) : (
            <div className="subscriber-options-wrapper w-100 d-flex flex-column">
              <div className="earnings-box-border">
                <BarChart
                  series={TraficSeries}
                  minTick={0}
                  maxTick={total}
                  showCurrency={true}
                  colors={trafficColors}
                  categories={categories}
                  barWidth="20%"
                />
              </div>
              <div id="earningDeatailsScrollable" className="content-wrapper">
                <InfiniteScroll
                  dataLength={earningData.length}
                  hasMore={currentPage < pageCount}
                  scrollableTarget="earningDeatailsScrollable"
                  next={() => {
                    fetchMoreData();
                  }}
                  loader={
                    <div className="d-flex justify-content-center align-items-center">
                      <LoadingSpinner />
                    </div>
                  }
                  endMessage={
                    earningData.length === 0 ? (
                      <div
                        className="d-flex flex-column align-items-center justify-content-center"
                        style={{ height: "50vh" }}
                      >
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          src={"/images/svg/dashboard-user/empty-table.svg"}
                          width={50}
                          height={50}
                          alt="loader"
                          className=""
                        />
                        <p className="text-center mt-3">No data found</p>
                      </div>
                    ) : (
                      <div className="d-flex flex-column align-items-center justify-content-center">
                        <p className="text-center ">
                          You&apos;ve reached the end
                        </p>
                      </div>
                    )
                  }
                >
                  ``
                  {type === "tips" && <TipsTable tableValues={earningData} />}
                  {type !== "tips" && (
                    <AllEarningsTable
                      tableValues={earningData}
                      type={type}
                      userId={userId}
                    />
                  )}
                </InfiniteScroll>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
