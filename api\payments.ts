import type { CardDetails } from "@/components/settings/card/cards/page";

import API from ".";
import type { BillingAddressInterface } from "./user";

interface CheckoutResponse {
  data: {
    tokenisation_id: string;
  };
}
interface RechargeWalletRequest {
  payment_mode: string;
  tokenised_card_id: string;
  amount: number;
}
export const Checkout = async (body: any = {}) =>
  API.post(
    `${API.HANDLER}/cards/tokenisation`,
    body
  ) as Promise<CheckoutResponse>;

export const TokenRegistration = async (tokenisation_id: string, body: any) =>
  API.put(
    `${API.HANDLER}/cards/tokenisation/${tokenisation_id}`,
    body
  ) as Promise<CheckoutResponse>;

export const GetRegisteredToken = async () =>
  API.get(`${API.HANDLER}/cards/tokens`) as Promise<any>;

export const RechargeWallet = async (body: RechargeWalletRequest) =>
  API.post(`${API.HANDLER}/wallets/recharge`, body) as Promise<any>;

export const GetWalletBalance = async () =>
  API.get(`${API.HANDLER}/wallets`) as Promise<any>;

export const GetMasspayBalance = async () =>
  API.get(`${API.HANDLER}/mp-wallet-balance`) as Promise<any>;

interface To {
  amount: number;
  total_amount: number;
  platform_fee: number;
  total_amount_with_tax: number;
  recipient: {
    avatar: string;
    badges: any;
    f_name: string;
    l_name: string;
    user_type: string;
    username: string;
    _id: string;
  };
}
export interface Transactions {
  _id: string;
  to: To[];
  from: {
    avatar: string;
    badges: any;
    f_name: string;
    l_name: string;
    user_type: string;
    username: string;
    _id: string;
  };
  amount: number;
  total_amount: number;
  platform_fee: number;
  method: "Card" | "Wallet";
  card_data?: CardDetails;
  total_amount_with_tax: number;
  created_at: string;
  updated_at: string;
  category: string;
  status: string;
}

export const GetTransactions = async ({
  page,
  limit,
  fromDate,
  toDate,
}: {
  page: number;
  limit: number;
  fromDate?: string;
  toDate?: string;
}) => {
  const query = new URLSearchParams();
  query.set("page", String(page));
  query.set("limit", String(limit));
  if (fromDate) query.set("fromDate", fromDate);
  if (toDate) query.set("toDate", toDate);

  const queryString = query.toString();

  return API.get(`${API.HANDLER}/transactions?${queryString}`) as Promise<
    Transactions[]
  >;
};

export const WithdrawAmont = async ({ amount }: { amount: number }) =>
  API.post(`${API.HANDLER}/withdraw`, { amount }) as Promise<any>;

export const EditCard = async (id: string, type: string) =>
  API.patch(
    `${API.HANDLER}/${id}/edit/card?update_type=${type}`,
    {}
  ) as Promise<any>;

export const GetTransactionInfo = async (id: string) =>
  API.get(`${API.HANDLER}/transactions/${id}`) as Promise<any>;

export const GetMIDSFrameLink = async (body: BillingAddressInterface) =>
  API.post(`${API.HANDLER}/cards/save`, { ...body }) as Promise<any>;

interface RechargeInterface {
  amount: number;
  payment_mode: "card";
  tokenised_card_id: string;
}

export const DepositMoney = async (body: RechargeInterface) =>
  API.post(`${API.HANDLER}/wallets/recharge-with-mids`, body) as Promise<any>;

export const GetTokenisedCards = async () =>
  API.get(`${API.HANDLER}/cards/tokens`) as Promise<any>;
