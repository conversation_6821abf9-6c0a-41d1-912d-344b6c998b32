import Swal from "sweetalert2";

import { EVENT_ERROR_MSGS } from "@/api/event";

export function showErrorResponse(e: any) {
  switch (e?.message) {
    case EVENT_ERROR_MSGS.ALREADY_ENDED:
      return Swal.fire({
        icon: "error",
        title: "Event Ended!",
        text: "This event has already ended.",
      });
    case EVENT_ERROR_MSGS.ACCESS_DENIED:
      return Swal.fire({
        icon: "error",
        title: "Access Denied!",
        text: "You do not have acces to this event.",
      });
    case EVENT_ERROR_MSGS.ALREADY_VIEWING:
      return Swal.fire({
        icon: "error",
        title: "Already Viewing!",
        html: "You are already viewing this stream.<br/>Please close the other tab or window.",
      });
    case EVENT_ERROR_MSGS.KICKED_OUT:
      return Swal.fire({
        icon: "error",
        title: "Kicked Out!",
        text: "You have been kicked out from this event.",
      });
    default:
      console.error(e);
      return Swal.fire({
        icon: "error",
        title: "Ooops something went wrong!",
        text: "Please try again later...",
        timer: 3000,
        timerProgressBar: true,
      });
  }
}
