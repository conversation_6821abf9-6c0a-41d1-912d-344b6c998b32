"use client";

import { use<PERSON><PERSON><PERSON>, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import "./index.scss";

import { DeleteChat, getChannelId, getChatUserList } from "@/api/chat";
import { BlockUser } from "@/api/user";
import ActionButton from "@/components/common/action-button";
import TimeLeft from "@/components/common/chat/TimeLeft";
import ChatBox from "@/components/common/chat/chat-box";
import { ChatHeader } from "@/components/common/chat/chat-header";
import {
  Chat<PERSON><PERSON>,
  ChatPersonUnavailable,
} from "@/components/common/chat/chat-person";
import ChatList from "@/components/common/chat/chatList";
import ChatBar from "@/components/common/chat/chatbar";
import ChattingFeeModal from "@/components/common/chat/chatting-fee-modal";
import useChatNotification from "@/components/common/chat/hooks/useChatNotification";
import useShowChat from "@/components/common/chat/hooks/useShowChat";
import LoadingSpinner from "@/components/common/loading";
import type { ModalRef } from "@/components/common/modal";
import DirectMessageModal from "@/components/common/modal/chat/dm-modal";
import RateModal from "@/components/common/modal/chat/rate-modal";
import { PromoteYourOptions } from "@/components/common/modal/chat/rating-req-creator";
import VideoConfModal from "@/components/common/modal/chat/video-conf";
import { ModalService } from "@/components/modals";
import { useAsyncEffect } from "@/hooks/useAsyncEffect";
import {
  blockActions,
  chatActions,
  configActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Chat, MessageInterface } from "@/redux-store/slices/chat.slice";
import socketChannel from "@/utils/chatSocket";
import { formatCurrency } from "@/utils/formatter";

import type { PlanInfo } from "../(home)/components/sidebar/modal";

const rateReqRef = { method: {} as ModalRef };
const chattingFeeRef = { method: {} as ModalRef };
const ratingRef = { method: {} as ModalRef };
const directMessageRef = { method: {} as ModalRef };
const imageModalRef = { method: {} as ModalRef };
const videoConfRef = { method: {} as ModalRef };

export default function Chats() {
  const dispatch = useAppDispatch();
  const router = useRouter();

  const user = useAppSelector((state) => state.user);
  const chatList = useAppSelector((state) => state.chat.chatList);
  const targetUser = useAppSelector((state) => state.chat.targetUser);
  const prevMessages = useAppSelector((state) => state.chat.prevMessages);
  const currMessages = useAppSelector((state) => state.chat.currentMessages);
  const socketChannelInstance = socketChannel;
  const blockedUser = useAppSelector((state) => state.block.blockedByMe);
  const hasBlockedMe = useAppSelector((state) => state.block.hasBlockedMe);
  const isReconnecting = useAppSelector((state) => state.chat.isReconnecting);

  const [demand, setDemand] = useState<PlanInfo | undefined>({
    _id: "",
    name: "",
    price: 0,
    offer: "",
    type: "",
  });

  const [openOptions, setOpenOptions] = useState<boolean>(false);
  const [chatEnabled, setChatEnabled] = useState<boolean>(false);
  const [channelData, setChannelData] = useState<
    Record<string, any> | undefined
  >();
  const extraOptionRef = useRef<any>(null);
  const location = useSearchParams();

  const hasUserParam = location.has("user");

  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [activeChat, setActiveChat] = useState<any>();
  const [chatBoxDisable, setChatBoxDisable] = useState<boolean>(true);
  const [menuVisible, setMenuVisible] = useState(true);
  const { showChats } = useShowChat({
    targetUser,
    setMenuVisible,
    setActiveChat,
    setChatBoxDisable,
  });
  const dispatchAction = useAppDispatch();
  const blockedByMe = useAppSelector((state) => state.block.blockedByMe);
  const userId = useAppSelector((s) => s.user.id);

  useChatNotification(prevMessages, currMessages);

  useEffect(() => {
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  useEffect(() => {
    const targetId = targetUser;
    const data = chatList.find(
      (chat: any) =>
        chat?.target?._id === targetId || chat?.initiator?._id === targetId
    );

    if (!data) return;

    setChannelData(data);

    const { target, initiator } = data;
    const isTargetUser = target._id === targetUser;
    const targetData = isTargetUser ? target : initiator;

    setChatEnabled(
      data.buyers.some((b) => b.buyer === targetUser || b.buyer === user.id) ||
        !targetData?.latest_chat_fee?.is_active
    );
  }, [chatList, targetUser]);

  useEffect(() => {
    dispatch(configActions.toggleShowPositionWidget());

    return () => {
      dispatch(configActions.toggleShowPositionWidget());
      dispatch(configActions.setOverflowY(true));
      dispatch(chatActions.setTargetUser(""));
      dispatch(chatActions.setChannelId(""));
    };
  }, []);

  useAsyncEffect(async () => {
    const result = await getChatUserList();
    const sortedChatList = result.data
      .sort((a: Chat, b: Chat) => {
        return (
          new Date(
            b?.message?.createdAt || new Date().setFullYear(1990)
          ).getTime() -
          new Date(
            a?.message?.createdAt || new Date().setFullYear(1990)
          ).getTime()
        );
      })
      .filter((chat: Chat) => {
        return !blockedByMe[
          userId === chat?.target?._id
            ? chat?.initiator?._id
            : chat?.target?._id
        ];
      })
      .filter((chat: Chat) => {
        return !hasBlockedMe[
          userId === chat?.target?._id
            ? chat?.initiator?._id
            : chat?.target?._id
        ];
      })
      .filter((chat: Chat) => "message" in chat);

    let total_count = 0;
    sortedChatList.forEach(
      (chat: Chat) => (total_count += chat?.unread_count || 0)
    );

    dispatchAction(chatActions.setTotalCount(total_count));
    dispatchAction(chatActions.setChatUserList(sortedChatList));
  }, []);

  useEffect(() => {
    if (hasUserParam) {
      handleChatClick();
    }

    function handleClickOutside(event: any) {
      if (
        extraOptionRef.current &&
        !extraOptionRef.current.contains(event.target)
      ) {
        setOpenOptions(false);
      }
    }

    document.body.addEventListener("click", handleClickOutside);
    // reset call
    dispatch(chatActions.setCurrentChat([]));
    dispatch(chatActions.setCall(false));

    return () => {
      document.body.removeEventListener("click", handleClickOutside);
      dispatch(chatActions.setReqId(""));
      socketChannel.closeChannel();
    };
  }, []);

  const handleChatClick = () => {
    setMenuVisible(!menuVisible);
  };

  function fetchChatFeeData() {
    if (!chatList.length) return [];

    const relevantChat = chatList.find(
      (chat: Chat) =>
        targetUser === chat.target?._id || targetUser === chat.initiator?._id
    );

    if (!relevantChat) return [];

    const chatFee =
      targetUser === relevantChat.target?._id
        ? relevantChat.target?.chat_fee_services || []
        : relevantChat.initiator?.chat_fee_services || [];

    return chatFee || [];
  }

  function fetchBuyers() {
    if (!chatList.length) return [];

    const relevantChat = chatList.find(
      (chat: Chat) =>
        targetUser === chat.target?._id || targetUser === chat.initiator?._id
    );

    if (!relevantChat) return [];

    return (
      [...relevantChat?.buyers]?.sort(
        (a, b) =>
          new Date(b.expires_at).getTime() - new Date(a.expires_at).getTime()
      ) || []
    );
  }

  function fetchConsumbales() {
    if (!chatList.length) return [];

    const relevantChat = chatList.find(
      (chat: Chat) =>
        targetUser === chat.target?._id || targetUser === chat.initiator?._id
    );

    if (!relevantChat) return [];

    return relevantChat.converse_consumable || [];
  }

  useEffect(() => {
    return () => {
      dispatch(chatActions.setTargetUser(""));
      dispatch(
        chatActions.setCompleteMessages({
          messages: [...(prevMessages ?? []), ...(currMessages ?? [])],
          channelId: channelData?.converse_channel_id || "",
        })
      );
      dispatch(chatActions.setPrevChat([]));
      dispatch(chatActions.setCurrentChat([]));
      socketChannelInstance.closeChannel();
      dispatch(chatActions.setChannelId(""));
      setMenuVisible(true);
    };
  }, []);

  useEffect(() => {
    if (chatBoxDisable && window.innerWidth < 768 && !menuVisible) {
      dispatch(configActions.showFooter(false));
    } else {
      dispatch(configActions.showFooter(true));
    }
  }, [menuVisible]);

  const handleBlockUser = () => {
    Swal.fire({
      icon: "warning",
      text: "Are you sure you want to block?",
      confirmButtonText: "Yes, I am sure!",
      cancelButtonText: "No!",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      customClass: {
        confirmButton: "custom-btn",
        cancelButton: "custom-btn",
      },
    }).then((response) => {
      if (response.isConfirmed) {
        BlockUser(targetUser).then((res) => {
          dispatch(blockActions.addBlockedUser(res.data));
        });
      }
    });
  };

  const switchToChat = async (newChatId: string, newUserId: string) => {
    try {
      dispatch(chatActions.setChannelId(newChatId));
      dispatch(chatActions.updateUnreadCount({ id: newChatId, count: 0 }));

      socketChannelInstance.closeChannel();

      const {
        data: { converse_channel_id: channelId },
      } = await getChannelId(newUserId);
      const channel = await socketChannelInstance.updateChannel(channelId);

      if (channel) {
        const existingIndex = chatList.findIndex(
          (c) => c.converse_channel_id === channelId
        );

        if (
          existingIndex !== -1 &&
          chatList[existingIndex]?.complete_messages?.length
        ) {
          dispatch(
            chatActions.setPrevChat(chatList[existingIndex].complete_messages)
          );
        } else {
          const { msgs } = await channel.getMessages({});
          dispatch(chatActions.setPrevChat([...msgs.read, ...msgs.unread]));
          dispatch(
            chatActions.setCompleteMessages({
              messages: [...msgs.read, ...msgs.unread] as MessageInterface[],
              channelId,
            })
          );
        }
      }
    } catch (err) {
      console.error("Error switching to chat:", err);
    }
  };

  const setNextActiveChat = (nextIndex: number) => {
    const nextChat = chatList[nextIndex];
    const targetUserId =
      user.id === nextChat?.target?._id
        ? nextChat?.initiator?._id
        : nextChat?.target?._id;

    setActiveChat(targetUserId);
    dispatch(chatActions.setTargetUser(targetUserId));

    switchToChat(nextChat.converse_channel_id, targetUserId);
  };

  const handleDeleteChat = async (channelId: string) => {
    try {
      await DeleteChat(channelId);
      Swal.fire({
        icon: "success",
        title: "Chat deleted successfully!",
        showConfirmButton: false,
        timer: 1500,
      });

      dispatch(chatActions.setPrevChat([]));
      dispatch(chatActions.setCurrentChat([]));
      dispatch(chatActions.updateLastMessage({ channelId, message: "" }));

      if (chatList[0]?.converse_channel_id !== channelId) {
        setNextActiveChat(0);
      } else {
        setNextActiveChat(1);
      }

      const updatedChatList = chatList.filter(
        (chat) => chat.converse_channel_id !== channelId
      );
      dispatch(chatActions.setChatUserList(updatedChatList));
    } catch (error) {
      console.error("Error deleting chat:", error);
    }
  };

  const handleDeleteChannel = (channelId: string) => {
    Swal.fire({
      icon: "warning",
      text: "Are you sure you want to delete this chat?",
      confirmButtonText: "Yes, I am sure!",
      cancelButtonText: "No!",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      customClass: {
        confirmButton: "custom-btn",
        cancelButton: "custom-btn",
      },
    }).then((res) => {
      if (res.isConfirmed) {
        handleDeleteChat(channelId);
      }
    });
  };

  return (
    <>
      {isReconnecting && (
        <div className="w-100 p-2 d-flex justify-content-center align-items-center gap-2">
          <LoadingSpinner /> Connecting...
        </div>
      )}
      <div
        className="chat-wrapper container-xxl position-relative py-2 h-100 flex-shrink-0"
        ref={chatContainerRef}
      >
        <div className="row m-0 h-100">
          <div
            className={`col-md-4 col-sm-12 slider d-flex flex-column gap-3 + ${
              menuVisible ? ` slider-move` : ``
            } overflow-y-auto h-100`}
          >
            <div className="chat-sidebar position-relative rounded-md-3 bg-body h-100 d-flex flex-column overflow-y-auto">
              <ChatHeader showChats={showChats} />
              <ChatList
                showChats={showChats}
                activeChat={activeChat}
                setActiveChat={setActiveChat}
                deleteChannel={handleDeleteChannel}
              />
            </div>
            {process.env.environment !== "live" && (
              <div className="rounded-md-3 bg-body p-3">
                <div className="fw-bold fs-5">Send a mass message</div>
                <div className="color-medium">
                  Easily reach out to all your subscribers easily with mass
                  message to spark a conversation. You can message channel,
                  collab and top tier fans separately.
                </div>
                <div>
                  <ActionButton
                    type="outline"
                    className="mt-2 w-50"
                    onClick={() => {
                      router.push("/chat/settings?type=mass");
                    }}
                  >
                    Get Started
                  </ActionButton>
                </div>
              </div>
            )}
          </div>
          {chatBoxDisable && (
            <div className="col-md-8 col-sm-12 p-0 h-100">
              <div className="chat-wrapper rounded-md-3 bg-body h-100 chat-box-container d-flex flex-column overflow-hidden justify-content-between">
                <div className="chat-content-header px-3 pt-md-3">
                  {user.id && targetUser ? (
                    chatList.map((chat: any, i: number) => {
                      if (
                        targetUser ==
                        (user.id == (chat as any).target?._id
                          ? (chat as any).initiator?._id
                          : (chat as any).target?._id)
                      ) {
                        return (
                          <div
                            className="d-flex justify-content-between w-100 position-relative"
                            key={(chat as any)?._id || i}
                          >
                            <ChatPerson
                              chat={chat}
                              user={user}
                              targetUser={targetUser}
                              handleChatClick={handleChatClick}
                              setChatBoxDisable={setChatBoxDisable}
                              socketChannel={socketChannel}
                              router={router}
                              dispatch={dispatch}
                              hasBlockedMe={hasBlockedMe}
                              blockedUser={blockedUser}
                              setOpenOptions={setOpenOptions}
                              handleDeleteChannel={handleDeleteChannel}
                              handleBlockUser={handleBlockUser}
                            />
                          </div>
                        );
                      }
                    })
                  ) : (
                    <ChatPersonUnavailable
                      handleChatClick={handleChatClick}
                      setChatBoxDisable={setChatBoxDisable}
                      openOptions={openOptions}
                      extraOptionRef={extraOptionRef}
                    />
                  )}
                </div>
                {targetUser && (
                  <hr className=" text-dark text-opacity-50 mb-0" />
                )}

                <ChatBox
                  ratingReqref={rateReqRef}
                  chattingFeeRef={chattingFeeRef}
                  chatBoxDisable={menuVisible}
                  rateRef={ratingRef}
                  imageModalRef={imageModalRef}
                  videoConfRef={videoConfRef}
                  directMessageRef={directMessageRef}
                  demand={demand}
                  setDemand={setDemand}
                />

                {/* TODO: Handle per message case */}
                {(() => {
                  const consumbales = fetchConsumbales();
                  const chatFeeData = fetchChatFeeData();
                  const buyersDetails = fetchBuyers();
                  const targetData = chatList.find(
                    (c) =>
                      c.initiator?._id === targetUser ||
                      c.target?._id === targetUser
                  );
                  const messagesLeft =
                    consumbales?.find((c) => c.buyer === user.id)
                      ?.available_message || 0;
                  const targetName =
                    targetData?.initiator._id === targetUser
                      ? targetData?.initiator.display_name
                      : targetData?.target.display_name;

                  const lowestPrice = Math.min(
                    ...chatFeeData
                      .filter((f) => f.is_active)
                      .map((f) => f.price)
                  );

                  if (buyersDetails.length > 0) return null;
                  if (chatFeeData.length === 0) return null;
                  if (
                    consumbales.some((c) => c.buyer === user.id) &&
                    messagesLeft > 0
                  )
                    return null;

                  if (lowestPrice === 0) return null;

                  return (
                    <div className="p-2 fs-7 d-flex justify-content-between chat-fee-detail">
                      <span>
                        {targetName} has set{" "}
                        {chatFeeData.length > 1
                          ? "multiple chat fee starting from"
                          : "a chat fee of"}{" "}
                        {formatCurrency(lowestPrice)}.
                      </span>
                      <span
                        className="underline text-primary pointer me-2"
                        onClick={() => {
                          ModalService.open("SHOW_SERVICES", {
                            filter: {
                              type: "CHAT-FEE",
                            },
                            authorName: targetName || "",
                            targetUserId: targetUser,
                          });
                        }}
                      >
                        See options.
                      </span>
                    </div>
                  );
                })()}

                {(() => {
                  const buyDetails = fetchBuyers().filter(
                    (b) => b.buyer === user.id || b.buyer === targetUser
                  );

                  if (buyDetails.length === 0) {
                    return null;
                  }

                  return (
                    <TimeLeft
                      buyDetails={buyDetails}
                      targetName={
                        channelData?.target?._id === targetUser
                          ? channelData?.initiator?.display_name
                          : channelData?.target?.display_name
                      }
                    />
                  );
                })()}
                {(() => {
                  const consumbales = fetchConsumbales();
                  const messagesLeft =
                    consumbales?.find((c) => c.buyer === user.id)
                      ?.available_message || 0;

                  const buyDetails = fetchBuyers().filter(
                    (b) => b.buyer === user.id || b.buyer === targetUser
                  );

                  if (buyDetails.length > 0) {
                    return null;
                  }

                  if (messagesLeft > 0) {
                    return (
                      <div className="p-2 fs-7 d-flex justify-content-between chat-fee-detail">
                        You have {messagesLeft} messages left.
                      </div>
                    );
                  }
                })()}
                {targetUser &&
                  !blockedUser[targetUser] &&
                  !hasBlockedMe[targetUser] && (
                    <div className="chat-bar-height position-stick bottom-0 bg-body">
                      <ChatBar chattingFeeRef={chattingFeeRef} />
                    </div>
                  )}
              </div>
            </div>
          )}
        </div>
      </div>

      <RateModal
        misc={{
          targetName:
            channelData?.target._id === targetUser
              ? channelData?.target?.display_name
              : channelData?.initiator?.display_name,
        }}
      />
      <ChattingFeeModal />
      <DirectMessageModal setChildRef={directMessageRef} misc={demand} />
      <PromoteYourOptions />
      <VideoConfModal />
    </>
  );
}
