"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { type ChangeEvent, useEffect, useRef, useState } from "react";
import "./index.scss";

import { toast } from "sonner";

import { EditAvatarAndBackground } from "@/api/user";
import PostNavbar from "@/app/(user)/(sections)/navbar";
import { EventBridge } from "@/app/event-bridge";
import ImageCropper from "@/components/common/cropper";
import LoadingSpinner from "@/components/common/loading";
import { configActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Subscription } from "@/types/event-bridge";
import { getAssetUrl } from "@/utils/assets";

export default function EditProfile() {
  const user = useAppSelector((state) => state.user.profile);
  const router = useRouter();
  const fileRef = useRef<any>(null);
  const avatarFileRef = useRef<any>(null);
  const dispatchAction = useAppDispatch();
  const [prevImg, setPrevImg] = useState("");
  const [avatar, setAvatar] = useState<File>();
  const [background, setBackground] = useState<File>();
  const [avatarName, setAvatarName] = useState("");

  const [bgImg, setBgImg] = useState("");
  const [bgName, setBgName] = useState("");
  const [cropActive, setCropActive] = useState(false);

  const [fileCroppedURL, setCroppedImg] = useState<any>("");
  const [avatarCroppedURL, setAvatarCroppedImg] = useState<any>("");
  const [showAvatar, setShowAvatar] = useState(true);
  let s: Subscription;
  useEffect(() => {
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowCreateWidget());
      setCroppedImg("");
    };
  }, []);

  const croppedAvatarImg = (url: Blob) => {
    setShowAvatar(true);
    setAvatarCroppedImg(URL.createObjectURL(url));
    const blob = url;
    const file = new File([blob], avatarName, { type: blob.type });
    setAvatar(file);
  };

  const croppedBackgroundImg = (url: Blob) => {
    setCroppedImg(URL.createObjectURL(url));
    setCropActive(false);
    const blob = url;
    const file = new File([blob], bgName, { type: blob.type });
    setBackground(file);
  };

  const onFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setCropActive(true);
    setBgImg("");
    const file = event.currentTarget.files?.[0];

    if (file) {
      setBgName(file.name.replace(/\s+/g, ""));
      setBgImg(URL.createObjectURL(file));
    }
  };

  const [btnDisable, setBtnDisable] = useState<boolean>(false);
  const [btnText, setBtnText] = useState<string>("Update");
  useEffect(() => {
    return () => {
      s?.unsubscribe();
    };
  });

  return (
    <div className="position-relative">
      <PostNavbar
        icon={"/images/svg/nav-back.svg"}
        buttonText={btnText}
        showBtn={true}
        text="Edit profile"
        btnIsValid={avatarCroppedURL || fileCroppedURL}
        btnIsDirty={avatarCroppedURL || fileCroppedURL}
        btnIsSubmitting={false}
        btnDisable={btnDisable}
        submitPost={() => {
          setBtnDisable(true);
          setBtnText("Uploading...");
          const promise = EditAvatarAndBackground({
            avatar: avatar,
            background: background,
          });
          const waitForSubmissionUpdate = () =>
            new Promise<void>((resolve) => {
              const handler = (notification: any) => {
                if (notification.type === "SubmissionUpdate") {
                  if (
                    notification.data.type === "Banner" ||
                    notification.data.type === "Avatar"
                  ) {
                    setTimeout(() => {
                      toast.success("Profile updated successfully!");
                      router.push(
                        `/${user.user_type.toLowerCase()}/${user.username}`
                      );
                      resolve();
                    }, 300);

                    s.unsubscribe();
                  }
                }
              };

              s = EventBridge.on("Notification", handler);
            });
          toast.promise(promise, {
            loading: "Uploading...",
            success: (data) => {
              toast.dismiss();
              toast.promise(waitForSubmissionUpdate, {
                loading: "Moderating...",
              });
              setBtnText("Moderating...");

              return `Finish uploading`;
            },
            error: (err) => {
              console.error(err);
              setBtnDisable(false);
              setBtnText("Update");

              return err.message;
            },
          });
        }}
      />
      <div className="container min-vh-100 p-3 ">
        {btnText !== "Update" && (
          <div
            style={{ zIndex: "999" }}
            className="d-flex justify-content-center align-items-center position-absolute top-0 start-0 w-100 h-100  bg-dark  "
          >
            <LoadingSpinner />
          </div>
        )}
        <div className="container bg-body rounded-2 d-flex flex-column p-3 gap-3">
          <div className=" d-flex flex-column">
            <p className="fs-6 fw-medium">Upload Avatar</p>
            <div className="update-avatar-container d-flex align-items-center justify-content-center overflow-hidden ">
              {prevImg && !showAvatar && (
                <>
                  <div className="cropper-wrapper d-flex align-items-end flex-column justify-content-center">
                    <ImageCropper
                      img={prevImg}
                      cls="z-3 object-fit-contain rounded-3 avatar-cropper"
                      aspectRatio={1}
                      viewMode={1}
                      dragMode="move"
                      guides={false}
                      cropBoxResizable={true}
                      onCropComplete={croppedAvatarImg}
                      cropperHeight={400}
                    />
                  </div>
                </>
              )}
              {showAvatar && (
                <div className="avatar-container ratio-1x1 rounded-pill overflow-hidden position-relative d-flex align-items-center justify-content-center">
                  {(prevImg && avatarCroppedURL) ||
                  getAssetUrl({ media: user.avatar[0] }) ? (
                    <div className="w-100 h-100 position-absolute">
                      <Image
                        width={1280}
                        height={720}
                        style={{
                          width: "100%",
                          height: "max-content",
                          objectFit: "cover",
                        }}
                        src={
                          avatarCroppedURL ||
                          getAssetUrl({
                            media: user.avatar[0],
                            defaultType: "avatar",
                          })
                        }
                        alt="change-avatar"
                        className="avatar-img"
                      />
                      <p className="position-absolute bottom-0 w-100 bg-black bg-opacity-50 pb-2 m-auto text-center fw-medium change-avatar">
                        Change
                      </p>
                    </div>
                  ) : (
                    <div className="d-flex w-100 h-100 flex-column align-items-center gap-3">
                      <Image
                        className="image-icon"
                        width={25}
                        height={25}
                        src="/images/svg/upload-avatar.svg"
                        alt="avatar"
                      />
                      <p className="text-center lh-1 mb-0 fw-medium">
                        Upload Avatar
                      </p>
                    </div>
                  )}
                  <input
                    ref={avatarFileRef}
                    className="opacity-0 position-absolute bottom-0 end-0 w-100 h-100"
                    type="file"
                    accept="image/*"
                    onChange={(event: ChangeEvent<HTMLInputElement> | any) => {
                      const file = event.currentTarget.files[0];

                      if (!file) {
                        return;
                      }

                      setShowAvatar(false);

                      setAvatarName(file.name.replace(/\s+/g, ""));
                      const reader = new FileReader();

                      reader.onload = (event: any) => {
                        setPrevImg(event.target.result);
                      };

                      reader.readAsDataURL(file);
                    }}
                  />
                </div>
              )}
            </div>
          </div>
          <div className="d-flex flex-column">
            <p className="fs-6 fw-medium">Upload Background</p>
            <div className="background-container bg-cream d-flex position-relative">
              {(bgImg || user.background) && (
                <div className="img-container w-100 position-relative overflow-hidden d-flex justify-content-center align-items center">
                  {cropActive ? (
                    <ImageCropper
                      img={bgImg || ""}
                      cls="z3"
                      aspectRatio={12 / 6}
                      guides={false}
                      cropBoxResizable={true}
                      onCropComplete={croppedBackgroundImg}
                      cropperHeight={320}
                    />
                  ) : (
                    <Image
                      src={
                        fileCroppedURL ||
                        getAssetUrl({ media: user.background[0] })
                      }
                      objectFit="contain"
                      fill
                      alt="cropped-file"
                    />
                  )}
                </div>
              )}
              <div
                className="position-absolute z-3 bottom-0 end-0 p-2 pointer"
                onClick={() => fileRef.current?.click()}
              >
                <input
                  ref={fileRef}
                  className="opacity-0 position-absolute bottom-0 end-0"
                  type="file"
                  accept="image/*"
                  onChange={onFileInputChange}
                />
                <Image
                  src={"/images/creator/edit.svg"}
                  width={25}
                  height={25}
                  alt="edit profile"
                  className="position-relative bottom-0 end-0"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
