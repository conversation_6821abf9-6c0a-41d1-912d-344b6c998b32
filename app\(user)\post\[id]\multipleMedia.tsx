import { memo, useEffect, useRef, useState } from "react";
import { useInView } from "react-intersection-observer";

import { PlyrPlayer } from "@/components/common/plyr";
import { Resolution } from "@/components/common/post";
import { useAppSelector } from "@/redux-store/hooks";
import type { Media } from "@/types/media";
import type { PostType } from "@/types/post";
import { getAssetUrl } from "@/utils/assets";
import "./index.scss";

const MultipleMediaBase = ({
  mediaList,
  id,
  type,
  authorHasPro,
  isPaidSubscription,
}: {
  mediaList: Media[];
  id: string;
  type: PostType;
  authorHasPro?: boolean;
  isPaidSubscription: boolean;
}) => {
  const [viewImageIndex, setViewImageIndex] = useState<{
    [key: string]: number;
  }>({});

  const videoRef = useRef<HTMLVideoElement>(null);
  const hasPrime = useAppSelector((state) =>
    state.user.profile.badges.subscription_badge.includes("Prime")
  );
  const [ref, inView] = useInView({
    triggerOnce: true,
  });

  useEffect(() => {
    if (inView && videoRef.current) {
      videoRef.current.play().catch((error) => {
        console.error("Error playing video:", error);
      });
    } else if (videoRef.current) {
      videoRef.current.pause();
    }
  }, [inView, videoRef]);

  const options: Plyr.Options = {
    controls: [
      "play",
      "rewind",
      "play-large",
      "progress",
      "current-time",
      "fast-forward",
      "mute",
      "settings",
      "fullscreen",
      "quality",
    ],
    muted: true,
    loop: {
      active: true,
    },
    autoplay: true,
    autopause: true,
    fullscreen: {
      iosNative: true,
    },
    clickToPlay: false,
  };

  const Media = ({ media, index }: { media: Media; index: number }) => {
    return (
      <>
        {media?.type === "image" ? (
          <div className="position-relative w-100 h-100">
            <Resolution
              resolution={media.resolution}
              visibility={type}
              isPaidSubscription={isPaidSubscription}
            />
            <div
              className="background-blur"
              style={{
                backgroundImage: `url(${getAssetUrl({
                  media: media,
                })})`,
                filter: "blur(16px) brightness(1.1)",
                clipPath: "border-box",
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                backgroundSize: "cover",
                backgroundPosition: "center",
                zIndex: 1,
              }}
            />
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              width={1280}
              height={720}
              className={`post-img-detail w-100 h-100  z-1 position-relative


                    `}
              alt=""
              src={getAssetUrl({
                media: media,
              })}
              onClick={() => {
                window.KNKY.showFullscreenMedia(
                  "image",
                  undefined,
                  mediaList,
                  index
                );
              }}
            />
          </div>
        ) : (
          <>
            <div className="position-relative w-100 h-100">
              <div
                className="position-absolute top-0 start-0"
                style={{
                  background: `url(${getAssetUrl({
                    media: media,
                    poster: media.poster ? true : null,
                  })})`,
                  backgroundSize: "cover",
                  width: "100%",
                  height: "100%",
                  backgroundPosition: "center",
                  filter: "blur(10px)",
                }}
              ></div>

              <PlyrPlayer
                //  plyrRef={plyrRef}

                source={getAssetUrl({ media: media })}
                hasPrime={hasPrime}
                options={options}
                // !remove h-100 to solve controls cutting issue in iOS.
                classes={`post-img-detail w-100 h-100 z-5 position-relative d-block`}
                visibility={type}
                isPaidSubscription={isPaidSubscription}
                mediaResolution={{ width: 900, height: 500 }}
                onClick={(e) => e.stopPropagation()}
                authorHasPro={authorHasPro}
              />
            </div>
          </>
        )}
      </>
    );
  };

  return (
    <>
      <div className="d-flex flex-column gap-2">
        {mediaList?.map((media, i: number) => {
          return <Media media={media} key={i} index={i} />;
        })}
      </div>
    </>
  );
};

export const MultipleMedia = memo(MultipleMediaBase);
