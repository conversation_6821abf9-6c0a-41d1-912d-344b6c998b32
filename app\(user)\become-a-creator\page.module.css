/* form css */

.info {
  margin-bottom: 5%;
}

.question {
  color: #4d5053;
  font-size: 0.8rem;
  display: block;
  margin-bottom: 0.7%;
}
.required:after {
  content: " *";
  color: red;
}

.info .input {
  border: 0;
  background-color: #f5f5f6;
  border-radius: 6px;
  padding: 1.2%;
  width: 100%;
}

.info .input:focus {
  box-shadow: 0 0 3px 0 var(--primary-color);
  border-color: var(--primary-color);
  outline: none;
}

@media screen and (max-width: 768px) {
  .form {
    width: 100% !important;
  }
}
