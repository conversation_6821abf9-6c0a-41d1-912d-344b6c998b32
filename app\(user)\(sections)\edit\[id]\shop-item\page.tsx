"use client";

import "./index.scss";

import { Formik } from "formik";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Swal from "sweetalert2";
import Swiper from "swiper";
import { Navigation } from "swiper/modules";
import * as Yup from "yup";

import { EditShopItemData, ShopItemCategory } from "@/api/shop";
import KYCwarning from "@/components/common/KYC-warning";
import Button from "@/components/common/button";
import ReadMoreContent from "@/components/common/chat/chat-box/read-more-content";
import FileInput from "@/components/common/file";
import FileHandler from "@/components/common/file/handler";
import Form, { FormHeader } from "@/components/common/form";
import Input from "@/components/common/input";
import InputArray from "@/components/common/input-array";
import InputGroup from "@/components/common/list";
import Select from "@/components/common/select";
import TextArea from "@/components/common/textarea";
import Wrapper, { Divider } from "@/components/common/wrapper";
import useIsMobile from "@/hooks/useIsMobile";
import { useAppSelector } from "@/redux-store/hooks";
import { getAssetUrl } from "@/utils/assets";
import { formatNumber } from "@/utils/number";
import "swiper/css";
import "swiper/css/navigation";

// import { CreateProduct } from "@/api/shop";
async function urlToFile(url: string, filename: string, mimeType: string) {
  const response = await fetch(url);
  const blob = await response.blob();
  return new File([blob], filename, { type: mimeType });
}

export default function EditShopItem() {
  const router = useRouter();
  const isMobile = useIsMobile();
  const userName = useAppSelector((state) => state.user.profile.username);
  const shopItemData = useAppSelector(
    (state) => state.shopSlice.singelShopData
  );

  const [mediaFiles, setMediaFiles] = useState([]) as any;
  const [convertedFiles, setConvertedFiles] = useState([]) as any;
  const initialValues = {
    name: shopItemData.name,
    brand: shopItemData.brand,
    media: mediaFiles,
    category: shopItemData.category,
    custom_category: "",
    quantity: shopItemData.quantity,
    description: shopItemData.description,
    variations: [...shopItemData.variations.colors],
    price: shopItemData.price,
    end_date: "",
    sizes: [...shopItemData.variations.sizes],
    sell: {
      type: "Normal",
      normal: { price: 0 },
      auction: { reserved_price: 0, end_date: "", end_time: "" },
    },
  };
  useEffect(() => {
    async function fetchMediaFiles() {
      const files = await Promise.all(
        shopItemData.media.map((url: any, index: number) => {
          console.log(getAssetUrl({ media: url }));
          return urlToFile(
            getAssetUrl({ media: url }),
            `media_${index}.jpeg`,
            `${url.type}/jpeg`
          );
        })
      );

      const convertedFiles = files.map((file) => {
        return FileHandler.add(file);
      });

      setMediaFiles(files);
      setConvertedFiles(convertedFiles);

      initialValues.media = files;
    }

    fetchMediaFiles();
  }, [shopItemData.media]);

  const [preview, setPreview] = useState(false);

  const [formValues, setFormValues] = useState(initialValues);
  const [shopItem, setShopItem] = useState({
    quantity: initialValues.quantity,
    description: initialValues.description,
    variations: {
      colors: initialValues.variations,
      sizes: initialValues.sizes,
    },
    price: initialValues.price,
    is_public: true,
  });

  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .min(2, "First name must be at least 3 characters")
      .required("Name is required"),
    brand: Yup.string()
      .required("Brand name is required")
      .min(2, "Brand name must be at least 3 characters"),
    media: Yup.array().of(Yup.string()),

    category: Yup.string().min(1).required("Category is required"),
    custom_category: Yup.string().when("category", {
      is: "Others",
      then: () =>
        Yup.string().required(
          'Custom category is required when category is "Others"'
        ),
      otherwise: () => Yup.string().notRequired(),
    }),
    price: Yup.number()
      .min(5, "Price must be greater than or equal to $5.00")
      .max(300000, "Price must be less than or equal to $3,00,000.00")
      .required("Price is required"),
    description: Yup.string().max(400).required("Description is required"),
    variations: Yup.array().max(10, "At max 10 item variation are allowed"),
    sizes: Yup.array().max(10, "At max 10 item size are allowed"),
    sell: Yup.object().shape({
      type: Yup.string()
        .oneOf(["Normal", "Auction"], "Invalid sell type")
        .required("Sell type is required"),
      normal: Yup.object().when("type", {
        is: "normal",
        then: () =>
          Yup.object().shape({
            price: Yup.number()
              .min(5, "Price must be greater than or equal to 5")
              .required("Price is required"),
          }),
      }),
      auction: Yup.object().when("type", {
        is: "auction",
        then: () =>
          Yup.object().shape({
            reserved_price: Yup.number()
              .min(5, "Reserved price must be greater than or equal to 5")
              .required("Reserved price is required"),
            end_date: Yup.date()
              .min(new Date(), "Auction end date must be in the future")
              .typeError("Invalid end date"),
            end_time: Yup.date()
              .min(new Date(), "Auction end time must be in the future")
              .typeError("Invalid end time"),
          }),
      }),
    }),
    quantity: Yup.string()
      .min(1, "Item available quantity must be greater than or equal to 1")

      .required("Item available quantity is required"),
  });

  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );

  const Preview = ({
    setPreview,
  }: {
    setPreview: (value: boolean) => void;
  }) => {
    const _id = useAppSelector((state) => state.user.id);
    const id = `swiper-sell-item-${_id}`;
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [viewImageIndex, setViewImageIndex] = useState(1);

    const initializeSwiper = () => {
      new Swiper(`.img-preview-swiper-container`, {
        direction: "horizontal",
        effect: "slide",
        slidesPerView: 1,
        modules: [Navigation],
        navigation: {
          nextEl: `.swiper-button-next.${id}`,
          prevEl: `.swiper-button-prev.${id}`,
        },
        loop: false,
        grabCursor: true,
        followFinger: true,
        allowTouchMove: true,
        autoHeight: false,
        on: {
          slideChange: (swiper) => setViewImageIndex(swiper.activeIndex + 1),
        },
      });
    };

    useEffect(() => {
      setTimeout(() => {
        initializeSwiper();
      }, 0);
    }, [shopItem]);
    return (
      <>
        <FormHeader
          title="Edit Item"
          closeIcon="back"
          onClick={() => setPreview(false)}
        >
          <div className="d-flex ">
            <Button
              isValid={true}
              dirty={true}
              isSubmitting={isSubmitting}
              text="Submit"
              onClick={() => {
                setIsSubmitting(true);
                EditShopItemData(shopItemData._id, shopItem)
                  .then(() => {
                    Swal.fire({
                      icon: "success",
                      title: "Item updated successfully",
                      confirmButtonText: "Finish",
                      confirmButtonColor: "#AC1991",
                      showCloseButton: true,
                    }).then((res) => {
                      setIsSubmitting(false);

                      if (res.isConfirmed) {
                        router.push(`/creator/${userName}/shop-item`);
                      }
                    });
                  })
                  .catch((error) => {
                    setIsSubmitting(false);

                    if (error.errorCode === 1000) {
                      Swal.fire({
                        icon: "warning",
                        title: "Oops! Some words aren’t allowed.",
                        text: `The word "${error.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
                      });
                    } else {
                      Swal.fire({
                        icon: "error",
                        title: error.message,
                      });
                    }
                  });
              }}
            />
          </div>
        </FormHeader>

        <section className="container-xxl g-lg-3 g-0 py-3 d-flex gap-3 flex-column  flex-lg-row bg-body mt-4 rounded-3 ">
          <div
            className={`overflow-hidden position-relative ${
              isMobile ? "w-100" : "w-50"
            }`}
          >
            <div className={`img-preview-swiper-container h-100 ${id}`}>
              <div className="swiper-wrapper">
                {mediaFiles?.map((image: any) => (
                  <div key={image} className="swiper-slide overflow-hidden">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={URL.createObjectURL(image)}
                      alt=""
                      className="img-fluid h-100"
                    />
                  </div>
                ))}
              </div>
              <div className={`swiper-button-next shop-next-btn ${id}`}></div>
              <div className={`swiper-button-prev shop-prev-btn ${id}`}></div>
              <div className="sell-item-images-count position-absolute left-0 bottom-0 ms-2 mb-2 px-2 py-1 bg-body rounded-3 z-1">
                {formatNumber(viewImageIndex)}/{formatNumber(mediaFiles.length)}
              </div>
            </div>
          </div>

          <div className={`${isMobile ? "w-100 px-3" : "w-50"}`}>
            <div className="pb-3 border-bottom">
              <span className="fs-4 fw-semibold d-block mb-3">
                {shopItemData.name}
              </span>
              <p className=" fs-6 fw-medium">
                <ReadMoreContent
                  text={shopItem.description}
                  characterCount={50}
                />
              </p>
            </div>

            <div className="mt-3 d-flex flex-column gap-3 fs-6 fw-medium border-bottom">
              <div className="row w-100">
                <div className="col-4">Brand:</div>
                <div className="col-8">{shopItemData.brand}</div>
              </div>
              <div className="row w-100">
                <div className="col-4">Category:</div>
                <div className="col-8">{shopItemData.category}</div>
              </div>
              {shopItemData.category === "Others" && (
                <div className="row w-100">
                  <div className="col-4">Custom Category:</div>
                  <div className="col-8">{shopItemData.custom_category}</div>
                </div>
              )}

              <div className="row w-100">
                <div className="col-4">Item Quantity:</div>
                <div className="col-8">{shopItem.quantity}</div>
              </div>

              <div className="row w-100 align-items-center">
                <div className="col-4 mb-3">Variations:</div>
                <div className="col-8 ps-0 d-flex  gap-3  scrollable-container">
                  {shopItem?.variations?.colors?.map((variation) => (
                    <div
                      key={variation}
                      className="bg-cream rounded-3 py-2 px-3 pill pointer"
                    >
                      {variation}
                    </div>
                  ))}
                  {shopItem?.variations?.colors.length === 0 && (
                    <p className="m-0">NA</p>
                  )}
                </div>
              </div>

              <div className="row align-items-center w-100 mb-3">
                <div className="col-4 mb-3">Size:</div>
                <div className="col-8 ps-0 d-flex gap-3 scrollable-container">
                  {shopItem?.variations?.sizes?.map((size) => (
                    <div
                      key={size}
                      className="bg-cream rounded-3 py-2 px-3 pill pointer text-uppercase"
                    >
                      {size}
                    </div>
                  ))}
                  {shopItem?.variations?.sizes.length === 0 && (
                    <p className="m-0">NA</p>
                  )}
                </div>
              </div>
            </div>

            <div className="mt-3 d-flex justify-content-between align-items-center">
              {/* <div className="d-flex gap-3 align-items-center fs-6 fw-medium">
                <div className="form-check form-switch">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    id="darkModeToggle"
                    onChange={() => {
                      post ? setPost(false) : setPost(true);
                    }}
                    checked={!post}
                  />
                </div>

                <span>Show/hide</span>
              </div> */}

              {/* <div className="fs-7">
                <a href="">Options</a>
              </div> */}
            </div>
          </div>
        </section>
      </>
    );
  };

  return (
    <>
      {preview ? (
        <Preview setPreview={setPreview} />
      ) : (
        <Formik
          initialValues={formValues}
          validationSchema={validationSchema}
          validateOnChange={true}
          validateOnBlur={true}
          onSubmit={async (data) => {
            setFormValues(data);
            setShopItem({
              quantity: data.quantity,
              price: data.price,
              description: data.description,
              variations: {
                colors: data.variations,
                sizes: data.sizes,
              },
              is_public: true,
            });
            setPreview(true);
          }}
        >
          {({ values, setFieldValue, isValid, ...rest }) => (
            <Form
              title="Edit Item"
              nextBtnText="Edit item"
              dirty={true}
              formikValues={{ values, setFieldValue, isValid, ...rest }}
            >
              <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1 ">
                <Wrapper title="" className="w-lg-75">
                  <Input
                    name="name"
                    label="Item name"
                    placeholder="Eg. Bondage, Dildo..."
                    className="fs-6"
                    disable={true}
                  />
                  <Input
                    name="brand"
                    label="Brand name"
                    required={false}
                    placeholder="Example: Nike, Adidas,..."
                    disable={true}
                  />
                  <Select
                    name="category"
                    label="Category"
                    array={Object.values(ShopItemCategory).map((category) => ({
                      text: category,
                      value: category,
                    }))}
                    selected={values.category}
                    setFieldValue={setFieldValue}
                    childClass="max-h-50"
                    disabled={true}
                  />
                  {values.category === "Others" && (
                    <Input
                      customErrorClassname="position-absolue top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                      name="custom_category"
                      type="text"
                      label="Custom Category"
                      placeholder="Create your own category"
                    />
                  )}
                  <Input
                    name="quantity"
                    type="number"
                    label="Item quantity"
                    placeholder="1"
                  />
                </Wrapper>
                <Wrapper
                  title="Description"
                  counter={{ value: values.description, max: 400 }}
                >
                  <TextArea
                    name="description"
                    type="text"
                    required={false}
                    maxlength={400}
                    placeholder="Say something about your item here..."
                    customErrorClassname="position-absolut top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                  />
                </Wrapper>
                <Wrapper title="Details">
                  <InputArray
                    name="variations"
                    label="Variations (at max. 10 variants)"
                    required={false}
                    values={values.variations}
                    placeholder="Colours etc..."
                    showSearchList={false}
                    disable={values.variations.length >= 10}
                  />
                  <InputArray
                    name="sizes"
                    label="Size (at max. 10 variants)"
                    required={false}
                    values={values.sizes}
                    placeholder="New Size"
                    showSearchList={false}
                    disable={values.sizes.length >= 10}
                  />
                </Wrapper>
              </div>
              <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
                <Wrapper title="Price & Sell Type">
                  <InputGroup
                    label=""
                    type="radio"
                    required={false}
                    theme="circle-purple"
                    name="sell.type"
                    className="align-items-md-start"
                    childClass="ms-1"
                    array={[
                      {
                        text: "Normal",
                        value: "Normal",
                      },
                      // {
                      //   text: "Auction",
                      //   value: "Auction",
                      // },
                    ]}
                    selected={values?.sell?.type}
                  />
                  <Divider />
                  {values?.sell?.type === "Auction" ? (
                    <>
                      <Input
                        customErrorClassname="position-absolue top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                        name="price"
                        type="number"
                        label="Reserver Price"
                        placeholder="0"
                        priceInput={true}
                        className="w-lg-75"
                      >
                        <span>Minimum $5.00 USD</span>
                      </Input>
                      <Input
                        customErrorClassname="position-absolue top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                        name="end_date"
                        type="date"
                        label="Auction Ends on"
                        placeholder="12/02/2001"
                        className="w-lg-75"
                      />
                    </>
                  ) : (
                    <>
                      <div className="position-relative">
                        <Input
                          customErrorClassname="position-absolue top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                          name="price"
                          type="number"
                          priceInput={true}
                          label="Price"
                          placeholder="0"
                          className="w-lg-75"
                        />
                      </div>
                      <span className="color-light fs-7">
                        Minimum $5.00 USD
                      </span>
                    </>
                  )}
                </Wrapper>
                <Wrapper title="Item photos (minimum 2, maximum 10) ">
                  <FileInput
                    name="media"
                    accept="image"
                    required={false}
                    multiple={true}
                    display={true}
                    isEditable={true}
                    inputClassName="d-none"
                    values={convertedFiles}
                    setFieldValue={setFieldValue}
                  ></FileInput>
                </Wrapper>
                {!isKycCompleted && <KYCwarning type="shop item" />}
              </div>
            </Form>
          )}
        </Formik>
      )}
    </>
  );
}
