.driver-popover {
  background: white;
}

.driver-popover-title {
  text-align: center;
}

.driver-popover-description {
  text-align: center;
  font-size: medium;
}

.driver-popover-navigation-btns {
  justify-content: center !important;
}

.driver-popover-navigation-btns > button {
  width: 8rem;
  text-align: center;
  height: 30px !important;
  border-radius: 8px !important;
}

.driver-popover-navigation-btns > button:nth-child(2) {
  background: #ac1991 !important;
  text-shadow: none !important;
  color: white;
  font-weight: 700;
}

.driver-popover-progress-text {
  display: none !important;
}
// tooltip/
.tooltip {
  position: relative;
}
.tooltip__item {
  position: absolute;
  min-width: 100px;
  padding: 10px;
  visibility: hidden;
  opacity: 0;
  background: white;
  transition: all 0.25s cubic-bezier(0, 0, 0.2, 1);
  color: #484848;
  border: 1px solid #cecece;
  border-radius: 3px;
  font-weight: 500;
  box-shadow: 0 2px 1px #bcbcbc;
  z-index: 500 !important;
}
.tooltip__item:after {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}
.tooltip__initiator {
  cursor: pointer;
}

.tooltip[data-direction="bottom"] .tooltip__initiator:hover ~ .tooltip__item {
  transform: translate3d(-50%, 0, 0);
  visibility: visible;
  opacity: 1;
}
.tooltip[data-direction="bottom"] .tooltip__item {
  top: calc(100% + 1em);
  left: 50%;
  transform: translate3d(-50%, -15px, 0);
}
.tooltip[data-direction="bottom"] .tooltip__item:after {
  top: -0.5em;
  left: 50%;
  transform: translate3d(-50%, 0, 0);
  border-width: 0 0.5em 0.5em 0.5em;
  border-color: transparent transparent white transparent;
  -webkit-filter: drop-shadow(1px 2px 1px #bcbcbc);
  filter: drop-shadow(1px -1px 1px #bcbcbc);
  z-index: 100 !important;
}
.fa.fa-info-circle {
  font-size: 38px;
  color: #21606b;
}

@media (max-width: 768px) {
  .main-popover {
    min-width: min(80vw, 700px) !important;
    transform: translate(0, 0);
    margin: auto;
    #driver-popover-description {
      font-size: 0.8rem !important;
    }
  }
}
@media (min-width: 768px) {
  .main-popover {
    min-width: min(80vw, 700px) !important;
    transform: translate(0, -25%);
    padding: 2rem 9rem;
    .driver-popover-title {
      font-size: 1.5em;
    }
  }
}
