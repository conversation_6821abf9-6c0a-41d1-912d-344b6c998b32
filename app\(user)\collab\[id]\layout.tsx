"use client";

import "./index.scss";

import Image from "next/image";
import Link from "next/link";
import { notFound, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Swal from "sweetalert2";

import type { APIGroup } from "@/api/group";
import { GetGroupByUsername, RespondRequest } from "@/api/group";
import ActionButton from "@/components/common/action-button";
import Badges, { FirstBadgesPriority } from "@/components/common/badges";
import {
  CreateBtn,
  EditBtn,
  transformSubscriptions,
} from "@/components/common/buttons/channel-group-btns";
import ReadMoreContent from "@/components/common/chat/chat-box/read-more-content";
import { EndDateCountDown } from "@/components/common/date-countdown";
import Loader from "@/components/common/loader/loader";
import type { ModalRef } from "@/components/common/modal";
import SubscriptionsOffer from "@/components/common/subscriptions";
import useIsMobile from "@/hooks/useIsMobile";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import { configActions, defaultActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type {
  ModifiedGroupProfile,
  ProfileSelectionType,
  Tags,
} from "@/types/profile";
import type { Children } from "@/types/utils";
import { getAssetUrl } from "@/utils/assets";

function refacData(data: APIGroup, userId?: string): ModifiedGroupProfile {
  const isMember = data.members.some((m) => m.member_id._id === userId);

  return {
    _id: data._id,
    background: getAssetUrl({ media: data.background[0] }),
    name: data.name,
    username: data.tag_name,
    desc: {
      text: data.about,
      images: [],
    },
    author: {
      id: data?.author?._id!,

      username: data?.author?.username!,
      role: data?.author?.role!,
      display_name: data?.author?.display_name!,
    },
    members:
      data.members && data.members?.length !== 0
        ? data.members?.map((member) => ({
            ...member,
            member_id: {
              _id: member.member_id._id,
              f_name: member.member_id.f_name,
              l_name: member.member_id.l_name,
              user_type: member.member_id.user_type,
              username: member.member_id.username,
              avatar: member.member_id.avatar,
            },
            earning: member.earning,
            member_type: member.member_type,
            terms_accepted: member.terms_accepted,
            role: member.role,
          }))
        : [],
    hash: data.hashtags,
    featured: [],
    is_subscribed: data?.is_subscribed,
    my_subscription_data: data.my_subscription_data,
    subscriptions: transformSubscriptions(data.subscription),
    topic: "",
    marked_as_deleted: data.marked_as_deleted,
    counter: {
      posts: data.members.some((member) => member.member_id._id === userId)
        ? data?.counter?.post_count
        : (data?.counter?.post_count || 0) -
          ((data?.counter?.private_post_count || 0) +
            (data?.counter?.scheduled_post_count || 0)),

      media: isMember
        ? (data?.counter?.media_count?.image_count || 0) +
          (data?.counter?.media_count?.video_count || 0)
        : (data?.counter?.media_count?.image_count || 0) -
            ((data?.counter?.media_count?.private_image_count || 0) +
              (data?.counter?.media_count?.scheduled_image_count || 0)) +
            ((data?.counter?.media_count?.video_count || 0) -
              ((data?.counter?.media_count?.private_video_count || 0) +
                (data?.counter?.media_count?.scheduled_video_count || 0))) || 0,
      subscriber: data.counter?.subscriber_count,
      subscribed: 0,

      "sell-item": 0,
      // wishlist: 0,
      followers: data.counter?.follower_count,
    },
    media_count: {
      image_count: data?.counter?.media_count?.image_count || 0,
      video_count: data?.counter?.media_count?.video_count || 0,
      private_image_count: data?.counter?.media_count?.private_image_count || 0,
      private_video_count: data?.counter?.media_count?.private_video_count || 0,
    },

    is_activated: data?.is_activated,
    perks: data?.perks!,
    deletion_request: data?.deletion_request,
    scheduled_deletion_date: data?.scheduled_deletion_date,
    totalLikes: data?.totalLikes || 0,
  };
}

export default function CreatorLayout({
  children,
  params,
}: Children & {
  params: {
    id: string;
    section: ProfileSelectionType;
  };
}) {
  const router = useRouter();

  const [groupData, setGroupData] = useState<APIGroup>();
  const authorId = useAppSelector((state) => state.user.profile._id);

  const badgePriorityMap = FirstBadgesPriority.reduce((acc, badge, index) => {
    acc[badge] = index;
    return acc;
  }, {} as Record<string, number>);

  const sortedMembers = groupData?.members.every(
    (member) =>
      member?.member_id?.badges?.user_badges?.length === 0 ||
      !member?.member_id?.badges
  )
    ? groupData?.members
    : groupData?.members.sort((a, b) => {
        const aMaxPriority = Math.min(
          ...a?.member_id?.badges?.user_badges?.map(
            (badge) => badgePriorityMap[badge] ?? Infinity
          )
        );
        const bMaxPriority = Math.min(
          ...b?.member_id?.badges?.user_badges?.map(
            (badge) => badgePriorityMap[badge] ?? Infinity
          )
        );
        return aMaxPriority - bMaxPriority;
      });

  const groupHashtag = useAppSelector(
    (state) => state.defaults.group_profile.hash
  );

  async function getData(username: string) {
    try {
      const response = await GetGroupByUsername(username);

      setGroupData(response.data);

      if (!response.data) {
        console.error("error");
        throw new Error(`No collab found for username : ${useState}`);
      }

      // Return the data as props
      return {
        err: false,
        data: refacData(response.data, userId),
      };
    } catch (err) {
      console.error(err);
      return { err: true, data: {} as ModifiedGroupProfile };
    }
  }

  const [error, setError] = useState("loading" as string | boolean);
  const [state, setState] = useState({} as ModifiedGroupProfile);
  const [timeOver, setTimeOver] = useState(false);
  const userId = useAppSelector((s) => s.user.id);

  const dispatchAction = useAppDispatch();
  const navigate = useNavigateBack();
  useEffect(() => {
    getData(params.id).then(({ err, data }) => {
      // setTimeout(() => {
      //   if (window.innerWidth > 1024) {
      //     window.scrollTo(0, 40);
      //   }
      // }, 500);
      setError(err);
      setState(data);
      dispatchAction(
        defaultActions.setGroupProfile({ ...data, blinkOffers: false })
      );
    });
    dispatchAction(defaultActions?.setPostTypeFilter("published"));
    dispatchAction(defaultActions?.setMediaTypeFilter("published"));
  }, [params]);

  useEffect(() => {
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  if (error === "loading") {
    return (
      <div className="container-xxl bd-gutter g-lg-4 g-0 d-flex align-items-center justify-content-center vh-100">
        <Loader />
      </div>
    );
  } else if (error) {
    return notFound();
  }

  const handleCollabAction = ({
    groupId,
    action,
  }: {
    groupId: string;
    action: "accept" | "decline";
  }) => {
    const executeAction = () => {
      RespondRequest(groupId, action).catch((error) =>
        console.error("Error making API call: ", error)
      );
    };

    if (action === "decline") {
      Swal.fire({
        title: "Are you sure you want to decline?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, decline it!",
      }).then((result) => {
        if (result.isConfirmed) {
          executeAction();
          Swal.fire({
            icon: "success",
            title: "Collab request declined successfully",
            confirmButtonText: "Finish",
            confirmButtonColor: "#AC1991",

            showCloseButton: true,
          }).then(() => {
            router.push("/");
          });
        }
      });
    } else {
      executeAction();
      window.location.reload();
    }
  };

  const ProfileHeader = () => (
    <>
      {!groupData?.is_activated &&
        groupData?.members.some(
          (member) => member.member_id._id === userId && !member.terms_accepted
        ) && (
          <div className="bg-body d-flex flex-column flex-sm-row align-items-center justify-content-between p-2">
            <div className="d-flex gap-2 align-items-center">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={getAssetUrl({
                  media: groupData?.author?.avatar?.[0],
                  defaultType: "avatar",
                })}
                alt="Avatar"
                className="rounded-circle object-fit-content"
                height={40}
                width={40}
              />
              <span className="fw-bold fs-6">
                {groupData?.author?.display_name}
              </span>
              <div className="color-medium opacity-50">|</div>
              <div>Invited you in this collab.</div>
            </div>
            <div className="d-flex gap-2">
              <ActionButton
                className="btn btn-outline-danger"
                onClick={() =>
                  handleCollabAction({
                    groupId: groupData?._id!,
                    action: "decline",
                  })
                }
                variant="danger"
                type="outline"
              >
                Reject
              </ActionButton>
              <ActionButton
                className="d-flex align-items-center justify-content-center"
                onClick={() =>
                  handleCollabAction({
                    groupId: groupData?._id!,
                    action: "accept",
                  })
                }
                type="solid"
                variant="success"
              >
                <span className="me-2">
                  <svg
                    width="14"
                    height="11"
                    viewBox="0 0 14 11"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M4.43427 7.73743C4.35617 7.81553 4.22953 7.81553 4.15143 7.73743L1.70685 5.29285C1.31638 4.90238 0.683314 4.90238 0.292849 5.29285C-0.0976164 5.68331 -0.0976162 6.31638 0.292849 6.70685L3.58574 9.99974C3.97627 10.3903 4.60943 10.3903 4.99996 9.99974L13.2928 1.70685C13.6833 1.31638 13.6833 0.683314 13.2928 0.292849C12.9024 -0.0976164 12.2693 -0.0976161 11.8788 0.292849L4.43427 7.73743Z"
                      fill="white"
                    />
                  </svg>
                </span>
                Accept
              </ActionButton>
            </div>
          </div>
        )}
      <div className="profile-header position-relative">
        <div onClick={() => navigate()} className=" pointer backButton">
          <Image
            src={"/images/svg/back.svg"}
            className="back-button-drop-shadow"
            width={54}
            height={54}
            alt="back"
          />{" "}
        </div>
        <div className="profile-image">
          {
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={getAssetUrl({ media: groupData?.background[0] })}
              alt="User Profile Background Image"
              width={1280}
              height={720}
              rel="preload"
              onClick={() => {
                window.KNKY.showFullscreenMedia(
                  "image",
                  getAssetUrl({
                    media: groupData?.background[0],
                  })
                );
              }}
            />
          }
        </div>
      </div>
    </>
  );

  const TagContainer = (tags: Tags) => {
    const hashtagRef = { method: {} as ModalRef };
    const userId = useAppSelector((state) => state.user.id);
    return (
      <>
        {tags.array?.length ? (
          <div className="bg-body d-lg-flex  p-3 gap-2 flex-column rounded-lg-3">
            <div className="d-flex justify-content-between">
              <h4 className="">{tags.title}</h4>
              {tags.author?._id === userId && (
                <div
                  className="pointer fw-bold"
                  onClick={() => {
                    router.push(`/collab/${state.username}/edit`);
                  }}
                >
                  Edit
                </div>
              )}
            </div>
            <div className=" d-flex gap-3 flex-wrap">
              {tags?.array?.map((tag) => (
                <a key={tag} href={tag} className="tag rounded-3">
                  {tag}
                </a>
              ))}
            </div>
          </div>
        ) : (
          <></>
        )}
      </>
    );
  };

  // const Topic = () =>
  //   groupData?.topic ? (
  //     <div className="bg-body d-lg-flex d-none p-3 gap-2 flex-column rounded-lg-3">
  //       <h4 className="">Topic</h4>
  //       <div className=" d-flex gap-3 flex-wrap">
  //         {/* {tags.array.map((tag, i) => ( */}
  //         <span className="tag rounded-3">{groupData?.topic}</span>
  //         {/* ))} */}
  //       </div>
  //     </div>
  //   ) : (
  //     <></>
  //   );
  const ProfileAboutMe = () => (
    <div className="bg-body d-flex p-3 gap-2 flex-column rounded-lg-3 ">
      <h4 className="d-lg-block d-none">About Collab</h4>
      <div className=" d-flex gap-3 flex-wrap">
        <ReadMoreContent
          text={groupData?.about + ""}
          characterCount={200}
          btnColor="#ac1991"
        />
        {state.desc.images.map((src) => (
          <Image
            key={src}
            src={src}
            alt="Profile Image"
            width={96}
            height={32}
          />
        ))}
      </div>
    </div>
  );

  const ProfileDetails = () => {
    const isMobile = useIsMobile();
    const [hideTagContainer, setHideTagContainer] = useState(true);

    useEffect(() => {
      const handleResize = () => {
        if (window.innerWidth <= 768) {
          setHideTagContainer(false);
        }
      };

      handleResize(); // Initial check
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }, []);
    return (
      <div className="profile-details d-flex flex-grow-1 flex-column gap-lg-4">
        <div className="profile-view d-flex flex-column p-lg-3 px-3 pt-0 gap-lg-2 gap-0 align-items-lg-center rounded-bottom-lg-4">
          <div className="d-flex justify-content-center position-relative align-items-start mb-4 mb-lg-2">
            <div className="d-flex flex-row profile-view-images align-items-center">
              {state.members.slice(0, 4).map(({ member_id }, index) => (
                <div
                  key={member_id.username}
                  className={`profile-view-image-collab centered-image-group image${
                    index + 1
                  }`}
                  style={{
                    marginLeft: index > 0 ? "-40px" : "",
                  }}
                >
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    className="rounded-pill object-fit-cover bg-cream h-100 w-100 pointer"
                    alt=""
                    width={256}
                    height={256}
                    src={getAssetUrl({
                      media: member_id.avatar[0],
                      defaultType: "avatar",
                    })}
                    onClick={() => {
                      window.KNKY.showFullscreenMedia(
                        "image",
                        getAssetUrl({
                          media: member_id.avatar[0],
                          defaultType: "avatar",
                          variation: member_id.avatar[0].variations.includes(
                            "high"
                          )
                            ? "high"
                            : "compressed",
                        })
                      );
                    }}
                  />
                </div>
              ))}
              {state.members.slice(4).length > 0 && (
                <div
                  className={`profile-view-image-collab centered-image-group image`}
                  style={{
                    marginLeft: -40,
                  }}
                >
                  <div className="rounded-pill h-100 w-100 bg-cream">
                    <div className="bg-purple rounded-pill h-100 w-100 d-flex justify-content-center align-items-center text-white fw-bold">
                      <u>+{state.members.slice(4).length}</u>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="d-flex align-items-center gap-2 mt-4">
            <h4 className="profile-name d-flex m-0  align-items-center gap-1">
              {groupData?.name}
            </h4>
            <span className="fw-medium px-2 py-1 bg-blue text-white rounded-2 fs-8">
              Collab
            </span>
          </div>
          <h5 className="profile-username fw-light color-medium ">
            @{groupData?.tag_name}
          </h5>
          <h6 className="fw-medium color-primary  d-flex flex-wrap justify-content-lg-center justify-content-start">
            {sortedMembers?.map(({ member_id }, i) => (
              <>
                {i === 0 && (
                  <span className="color-medium me-1">Collab by </span>
                )}{" "}
                <Link
                  key={member_id._id}
                  className="d-flex"
                  href={`/${member_id.user_type.toLowerCase()}/${
                    member_id.username
                  }`}
                >
                  <p className="m-0"> @{member_id.username}</p>
                  <Badges array={member_id.badges!} />{" "}
                  {i === state.members.length - 1 ? "" : ","}&nbsp;
                </Link>
              </>
            ))}
          </h6>
          {/* add justify-content-between justify-content-lg-around when removing d-none and want to show followers count */}
          <div className="d-flex   justify-content-around w-100 px-1 px-lg-0   mb-3  mt-2 mt-lg-1 mb-lg-2">
            <div className="d-none gap-1">
              <Image
                src={"/images/home/<USER>"}
                width={24}
                height={24}
                alt="followers"
              />
              <span className="color-dark fw-medium">
                {groupData?.counter?.subscriber_count}
              </span>
            </div>

            <div className="d-flex gap-1">
              <Image
                src={"/images/svg/heart.svg"}
                width={24}
                height={24}
                alt="image"
                className="grayscale"
              />
              <span className="color-dark fw-medium">
                {groupData?.totalLikes}
              </span>
            </div>

            <div className="d-flex gap-1">
              <Image
                src={"/images/home/<USER>"}
                width={24}
                height={24}
                alt="image"
              />
              <span className="color-dark fw-medium">
                {groupData?.counter?.media_count?.image_count}
              </span>
            </div>

            <div className="d-flex gap-1">
              <Image
                src={"/images/home/<USER>"}
                width={24}
                height={24}
                alt="video"
              />
              <span className="color-dark fw-medium">
                {groupData?.counter?.media_count?.video_count}
              </span>
            </div>
          </div>

          {!state.is_activated && (
            <div className="btn btn-secondary w-100 cursor-not-allowed mb-2">
              Request Pending
            </div>
          )}
          {groupData?.marked_as_deleted && (
            <div className="btn btn-secondary w-100 cursor-not-allowed mb-2">
              Marked as deleted
            </div>
          )}
          {/* To Refactor it as its very lame .includes */}
          <EditBtn
            href={`/collab/${params.id}/edit`}
            text={"Edit my collab"}
            visible={
              state.members.some(
                (member) =>
                  member.member_id._id === userId && member.terms_accepted
              ) && !state.marked_as_deleted
            }
          />
          {state.members.some((member) => member.member_id._id === userId) &&
            state.deletion_request?.[0]?.deletion_scheduled_on && (
              <>
                <div className="rounded-3 border px-1 text-center py-1 w-100 fw-500 fs-7 mb-1 mt-lg-1 mt-3">
                  {timeOver ? (
                    "Time's Up"
                  ) : (
                    <>
                      {!state?.scheduled_deletion_date
                        ? "Will be marked as deleted"
                        : "Deletes"}{" "}
                      in <br />
                      <EndDateCountDown
                        endDate={
                          state?.scheduled_deletion_date ||
                          state.deletion_request?.[0]?.deletion_scheduled_on
                        }
                        onOver={() => setTimeOver(true)}
                      />
                    </>
                  )}
                </div>
              </>
            )}
          {/* <FollowBtn
          text={"Follow"}
          visible={
            !state.members.map((m) => m.member_id._id).includes(authorId)
          }
        /> */}
          <div className="d-lg-none mt-2 gap-3">
            {/* To Refactor it as its very lame .includes */}
            <CreateBtn
              href={"/create/new-post?isGroupPost=true"}
              text={"Create new post"}
              visible={
                state.members.map((m) => m.member_id._id).includes(authorId) &&
                !state.marked_as_deleted
              }
            />

            {/* <PostActionSendTip id={state?.members?} authorId={state.id} /> */}
          </div>
        </div>

        <SubscriptionsOffer
          data={state.subscriptions}
          subscription_data={state.my_subscription_data}
          type="Group"
          perks={state.perks}
          id={state._id}
          author={state.members.some(
            (member) => member.member_id._id === userId
          )}
        />
        {/* <ProfileFeatured /> */}
        <ProfileAboutMe />
        {/* <Topic /> */}
        {isMobile && !hideTagContainer && (
          <div
            className="bg-body px-3 color-medium pointer"
            onClick={() => setHideTagContainer(true)}
          >
            See more
          </div>
        )}

        {hideTagContainer && (
          <>
            <TagContainer
              title="Hashtags"
              array={groupHashtag || groupData?.hashtags}
              author={groupData?.author}
            />
          </>
        )}
        {isMobile && hideTagContainer && (
          <div
            className="bg-body px-3 color-medium pointer"
            onClick={() => setHideTagContainer(false)}
          >
            See less
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div
        id="profile-container"
        className="container-xxl bd-gutter g-lg-4 g-0 d-flex flex-column overflow-y-auto"
      >
        <ProfileHeader />
        <div className="d-flex profile-content flex-column flex-lg-row gap-lg-4">
          <ProfileDetails />
          <div className="profile-sections-container d-flex flex-grow-1 flex-column gap-4">
            {children}
          </div>
        </div>
      </div>
    </>
  );
}
