"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import JoinUs from "@/components/common/join-us";
import PostList from "@/components/common/post/list";
import Stories from "@/components/common/stories";
import { defaultActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { PostListType } from "@/types/post";
import { UserRole } from "@/types/user";
import type { HashObject } from "@/types/utils";

import InterestedSection from "./interested";

interface ContentParams {
  path: PostListType;
}

const haveStorySection: HashObject = { fresh: 1, subscribed: 1, following: 1 };
const haveInterestedSection: HashObject = { interested: 1 };

export default function Content({ path }: ContentParams) {
  const user = useAppSelector((state) => state?.user?.tokenData);
  const selectedHashTag = useAppSelector(
    (state) => state?.defaults?.selectedTag
  );
  const [selectedTag, setSelectedTag] = useState<string>("");
  const dispatchAction = useAppDispatch();

  const searchParams = useSearchParams();
  const hasRedirect = searchParams.has("redirect");
  const userKinks = useAppSelector((state) =>
    state.user.profile.preferences.kinks.map((tag) => tag.slice(1))
  );

  const [kinks, setKinks] = useState<string[]>(userKinks);

  useEffect(() => {
    setSelectedTag(selectedHashTag);

    if (
      !kinks.includes(selectedHashTag!) &&
      hasRedirect &&
      selectedHashTag.length
    ) {
      setKinks((kink) => [selectedHashTag, ...kink]);
    }
  }, [selectedHashTag]);

  useEffect(() => {
    !hasRedirect && dispatchAction(defaultActions.setSelectedTag(""));
  }, [UserRole]);

  const storiesView = haveStorySection[path] ? (
    <Stories
      uid=""
      canCreate={user?.role?.toUpperCase() === UserRole.CREATOR.toUpperCase()}
      type={path}
      cls="p-2"
    />
  ) : (
    <></>
  );

  const interestedView = haveInterestedSection[path] ? (
    <div className="interested-wrapper mb-3">
      <InterestedSection data={kinks} active={selectedTag} />
    </div>
  ) : (
    <></>
  );

  if (user?.role === UserRole.GUEST && path !== "fresh" && path !== "premium") {
    return <JoinUs />;
  }

  return (
    <>
      {interestedView}
      <div
        className="d-flex flex-column gap-3 position-relative h-100"
        style={{
          overflowY: "auto",
          maxHeight:
            window.location.pathname === "/interested" ? "fit-content" : "100%",
        }}
      >
        {storiesView}
        <PostList uid={user?.id} type={path} from={"homefeed"} />
      </div>
    </>
  );
}
