"use client";

import Image from "next/image";

import { getAssetUrl } from "@/utils/assets";
import { formatDate } from "@/utils/number";

interface dashboardTable {
  accepted?: boolean;
  completed?: boolean;
  declined?: boolean;
  rating?: boolean;
  videoCall?: boolean;
  empty?: boolean;
  tableValues?: any[];
  tableData?: any[];
}

export default function UserTable(tableProps: dashboardTable) {
  return (
    <>
      {!tableProps.tableValues ? (
        <table className="table">
          <thead>
            <tr className="fs-7">
              <th scope="col" className="bg-cream rounded-start-3">
                ID
              </th>
              <th scope="col" className="bg-cream">
                Date & Time
              </th>
              <th scope="col" className="bg-cream">
                User
              </th>
              <th scope="col" className="bg-cream ">
                Description
              </th>
              <th scope="col" className="bg-cream ">
                Amount
              </th>
              <th scope="col" className="bg-cream">
                Status
              </th>
              <th scope="col" className="bg-cream rounded-end-3">
                {" "}
              </th>
            </tr>
          </thead>
          {!tableProps.empty && (
            <tbody className="bg-body border-0">
              {tableProps.tableData &&
                tableProps.tableData.map((res) => (
                  <>
                    <tr className="fs-7 table-row">
                      <th scope="row" className="py-2 bg-body">
                        <span className="id-text">#RQ039393</span>
                      </th>
                      <td className="py-2 bg-body">
                        <div className="d-flex flex-column">
                          <p className="date mb-0 color-dark">
                            {formatDate(res.created_at)}
                          </p>
                        </div>
                      </td>
                      <td className="py-2 bg-body">
                        <div className="d-flex gap-2">
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={getAssetUrl({
                              media:
                                res?.requestee?.avatar &&
                                res?.requestee?.avatar[0],
                              defaultType: "avatar",
                              variation: "thumb",
                            })}
                            className="rounded-circle"
                            width={35}
                            height={35}
                            alt="dashboard-user"
                          />
                          <div className="d-flex flex-column">
                            <p className="fs-7 mb-0 text-overflow-ellipsis color-dark">
                              {res?.requestee?.display_name}
                            </p>
                            <p className="fs-8 mb-0 color-light">
                              @{res?.requestee?.username}
                            </p>
                          </div>
                        </div>
                      </td>

                      <td className="py-2 w-25 bg-body">
                        <p className="w-100 mb-0 color-dark">
                          Hi, I want to do a photoshoot with you this weekend.
                          Are you available?
                        </p>
                      </td>
                      <td className="py-2 bg-body color-dark">$120</td>
                      <td className="py-2 bg-body">
                        {res.status === "1" ? (
                          <Image
                            src={"/images/svg/dashboard-user/accepted.svg"}
                            width={65}
                            height={30}
                            alt="new-user"
                          />
                        ) : res.status === "2" ? (
                          <Image
                            src={"/images/svg/dashboard-user/completed.svg"}
                            width={70}
                            height={40}
                            alt="new-user"
                          />
                        ) : res.status === "3" ? (
                          <Image
                            src={"/images/svg/dashboard-user/declined.svg"}
                            width={65}
                            height={35}
                            alt="new-user"
                          />
                        ) : (
                          <Image
                            src={"/images/svg/dashboard-user/new.svg"}
                            width={45}
                            height={35}
                            alt="new-user"
                          />
                        )}
                      </td>
                      <td className="py-2 bg-body">
                        <div className="d-flex gap-3 justify-content-center">
                          {!res.completed && (
                            <>
                              {res.accepted && (
                                <button
                                  className={`${
                                    res.videoCall ? "bg-purple" : "bg-complete"
                                  } border-0 shadow-dark px-2 py-1 rounded-3`}
                                >
                                  Complete
                                </button>
                              )}
                              {!res.videoCall && (
                                <button className="btn-purple border-0 shadow-dark px-2 py-1 rounded-3 ">
                                  {!res.rating ? (
                                    <>
                                      {res.accepted
                                        ? "Chat"
                                        : res.declined
                                        ? "Undo"
                                        : "Accept"}
                                    </>
                                  ) : (
                                    "Rate"
                                  )}
                                </button>
                              )}
                              {!res.accepted &&
                                !res.completed &&
                                !res.declined && (
                                  <button className="btn-light border shadow-dark px-2 py-1 rounded-3 ">
                                    Decline
                                  </button>
                                )}
                            </>
                          )}
                          {((res.rating && res.completed) ||
                            (res.videoCall && res.completed)) && (
                            <button className="btn-purple border-0 shadow-dark px-2 py-1 rounded-3 ">
                              Chat
                            </button>
                          )}

                          <button className="border-0 rounded-3 bg-transparent">
                            <Image
                              src={"/images/svg/options-dot.svg"}
                              width={25}
                              height={25}
                              alt="options"
                            />
                          </button>
                        </div>
                      </td>
                    </tr>
                  </>
                ))}
              <tr className="fs-7 table-row">
                <th scope="row" className="py-2 bg-body">
                  <span className="id-text">#RQ039393</span>
                </th>
                <td className="py-2 bg-body">
                  <div className="d-flex flex-column">
                    <p className="date mb-0 color-dark">Jan 24, 2023</p>
                    <p className="time mb-0 color-dark">12:03:03</p>
                  </div>
                </td>
                <td className="py-2 bg-body">
                  <div className="d-flex gap-2">
                    <Image
                      src={"/images/common/default.svg"}
                      width={35}
                      height={35}
                      alt="dashboard-user"
                    />
                    <div className="d-flex flex-column align-items-center">
                      <p className="fs-7 mb-0 text-overflow-ellipsis color-dark">
                        User 1
                      </p>
                      <p className="fs-8 mb-0 color-light">@user1</p>
                    </div>
                  </div>
                </td>
                <td className="py-2 w-25 bg-body">
                  <p className="w-100 mb-0 color-dark">
                    Hi, I want to do a photoshoot with you this weekend. Are you
                    a
                  </p>
                </td>
                <td className="py-2 bg-body color-dark">$120</td>
                <td className="py-2 bg-body">
                  {tableProps.accepted ? (
                    <Image
                      src={"/images/svg/dashboard-user/accepted.svg"}
                      width={65}
                      height={30}
                      alt="new-user"
                    />
                  ) : tableProps.completed ? (
                    <Image
                      src={"/images/svg/dashboard-user/completed.svg"}
                      width={70}
                      height={40}
                      alt="new-user"
                    />
                  ) : tableProps.declined ? (
                    <Image
                      src={"/images/svg/dashboard-user/declined.svg"}
                      width={65}
                      height={35}
                      alt="new-user"
                    />
                  ) : (
                    <Image
                      src={"/images/svg/dashboard-user/new.svg"}
                      width={45}
                      height={35}
                      alt="new-user"
                    />
                  )}
                </td>
                <td className="py-2 bg-body">
                  <div className="d-flex gap-3 justify-content-center">
                    {!tableProps.completed && (
                      <>
                        {tableProps.accepted && (
                          <button
                            className={`${
                              tableProps.videoCall ? "bg-purple" : "bg-complete"
                            } border-0 shadow-dark px-2 py-1 rounded-3`}
                          >
                            Complete
                          </button>
                        )}
                        {!tableProps.videoCall && (
                          <button className="btn-purple border-0 shadow-dark px-2 py-1 rounded-3 ">
                            {!tableProps.rating ? (
                              <>
                                {tableProps.accepted
                                  ? "Chat"
                                  : tableProps.declined
                                  ? "Undo"
                                  : "Accept"}
                              </>
                            ) : (
                              "Rate"
                            )}
                          </button>
                        )}
                        {!tableProps.accepted &&
                          !tableProps.completed &&
                          !tableProps.declined && (
                            <button className="btn-light border shadow-dark px-2 py-1 rounded-3 ">
                              Decline
                            </button>
                          )}
                      </>
                    )}
                    {((tableProps.rating && tableProps.completed) ||
                      (tableProps.videoCall && tableProps.completed)) && (
                      <button className="btn-purple border-0 shadow-dark px-2 py-1 rounded-3 ">
                        Chat
                      </button>
                    )}

                    <button className="border-0 rounded-3 bg-transparent">
                      <Image
                        src={"/images/svg/options-dot.svg"}
                        width={25}
                        height={25}
                        alt="options"
                      />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          )}
        </table>
      ) : (
        <>
          <table className="table">
            {/*Table for earnings */}
            <thead>
              <tr className="fs-7">
                <th scope="col" className="bg-cream rounded-start-3">
                  Payment ID
                </th>
                <th scope="col" className="bg-cream">
                  Date & Time
                </th>
                <th scope="col" className="bg-cream">
                  Subscription
                </th>
                <th scope="col" className="bg-cream ">
                  User
                </th>
                <th scope="col" className="bg-cream ">
                  Package
                </th>
                <th scope="col" className="bg-cream">
                  Amount
                </th>
                <th scope="col" className="bg-cream rounded-end-3">
                  {" "}
                </th>
              </tr>
            </thead>
            {!tableProps.empty && (
              <tbody className="bg-body border-0">
                {tableProps.tableValues!.map((earnings) => (
                  <>
                    <tr className="fs-7 table-row" key={earnings.paymentID}>
                      <th scope="row" className="py-2 bg-body">
                        <span className="id-text">{earnings.paymentID}</span>
                      </th>
                      <td className="py-2 bg-body">
                        <div className="d-flex flex-column">
                          <p className="date mb-0 color-dark">
                            {earnings.dateTime.date}
                          </p>
                          <p className="time mb-0 color-dark">
                            {earnings.dateTime.time}
                          </p>
                        </div>
                      </td>
                      <td className="py-2 bg-body">
                        <p className="date mb-0 color-dark">
                          {earnings.subscription}
                        </p>
                      </td>

                      <td className="py-2 bg-body">
                        <div className="d-flex gap-2">
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={earnings.user.avatar}
                            width={35}
                            height={35}
                            alt="dashboard-user"
                          />
                          <div className="d-flex flex-column align-items-start">
                            <p className="fs-7 mb-0 text-overflow-ellipsis color-dark">
                              {earnings.user.name}
                            </p>
                            <p className="fs-8 mb-0 color-light">
                              {earnings.user.username}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="py-2 bg-body">
                        <p className="date mb-0 color-dark">
                          {earnings.package}
                        </p>
                      </td>
                      <td className="py-2 bg-body">
                        <p className="date mb-0 color-dark">
                          {earnings.amount.cash.length != 0 &&
                            earnings.amount.cash}
                          {earnings.amount.knkyCoin.length != 0 && (
                            <div className="d-flex align-items-center">
                              <Image
                                src={"/images/svg/knky-coin.svg"}
                                width={20}
                                height={20}
                                className="me-1"
                                alt="knky-coin"
                              />
                              <span>{earnings.amount.knkyCoin}</span>
                            </div>
                          )}
                        </p>
                      </td>

                      <td className="py-2 bg-body">
                        <button className="border-0 rounded-3 bg-transparent">
                          <Image
                            src={"/images/svg/options-dot.svg"}
                            width={25}
                            height={25}
                            alt="options"
                          />
                        </button>
                      </td>
                    </tr>
                  </>
                ))}
              </tbody>
            )}
          </table>
        </>
      )}

      {tableProps.empty && (
        <div className="d-flex flex-column justify-content-center align-items-center p-3">
          <Image
            src={"/images/svg/dashboard-user/empty-table.svg"}
            width={50}
            height={50}
            alt="empty-table"
          />
          <p className="fs-6 fw-medium">Empty</p>
        </div>
      )}
    </>
  );
}
