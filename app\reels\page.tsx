"use client";

import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

const Reels = dynamic(() => import("@/components/reels"), {
  ssr: false,
});

const ReelNav = dynamic(() => import("@/components/reels/reel-nav/reelNav"), {
  ssr: false,
});

export default function Page() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const checkMobile = () => setIsMobile(window.innerWidth < 768);

      checkMobile();

      window.addEventListener("resize", checkMobile);

      return () => window.removeEventListener("resize", checkMobile);
    }
  }, []);

  return (
    <div className="position-relative">
      {isMobile && (
        <>
          <div
            className="position-absolute top-0 w-100  z-3"
            style={{
              background:
                "linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%)",
            }}
          >
            <ReelNav />
          </div>
        </>
      )}
      <Reels />
    </div>
  );
}
