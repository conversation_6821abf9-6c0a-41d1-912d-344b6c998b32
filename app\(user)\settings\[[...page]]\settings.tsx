"use client";

import classNames from "classnames";
import { nanoid } from "nanoid";
import { usePathname, useSearchParams } from "next/navigation";
import { memo, useEffect, useState } from "react";

import AccountInformation from "@/components/settings/account-information/page";
import { settingsActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";
import "./index.scss";

import Loading<PERSON>pinner from "@/components/common/loading";
import OffersPage from "@/components/offers";
import Delivery from "@/components/settings/address/delivery";
import Agency from "@/components/settings/agency";
import BlocksPage from "@/components/settings/blocklist";
import Cards from "@/components/settings/card/cards/page";
import YourFee from "@/components/settings/creator-settings/sub-components/your-fee/page";
import EditAccountInformation from "@/components/settings/edit-account-information/page";
import HelpAndSupport from "@/components/settings/help-and-support/page";
import LinkAccounts from "@/components/settings/link-accounts";
import ReferAndEarn from "@/components/settings/refer-and-earn";
import ReleaseForm from "@/components/settings/release-form";
import TwoFactorAuthentication from "@/components/settings/setting-and-privacy/2fa";
import ChangePassword from "@/components/settings/setting-and-privacy/change-password";
import DeviceLogin from "@/components/settings/setting-and-privacy/device-login";
import PrivacyManagement from "@/components/settings/setting-and-privacy/privacy-management";
import SettingsMobileHeader from "@/components/settings/settings-mobile-header/page";
import YourIdentity from "@/components/settings/verify-account/your-identity/page";
import Wallet from "@/components/settings/wallet/page";

import Menu from "./menu";

export const items = {
  _id: nanoid(),
  type: "Chat Settings",
  menuItems: [
    {
      list: {
        icon: "/settings/user.svg",
        title: "Account Information",
        path: "/settings/account-information",
      },
      comp: <AccountInformation />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/agency.svg",
        title: "Agency",
        path: "/settings/agency",
      },
      comp: <Agency />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/user.svg",
        title: "Edit Information",
        path: "/settings/edit-information",
      },
      comp: <EditAccountInformation />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/refer.svg",
        title: "Refer To Earn",
        path: "/settings/refer-and-earn",
      },
      comp: <ReferAndEarn />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/settings.svg",
        title: "Devices",
        path: "/settings/settings-and-privacy?type=devices",
      },
      comp: <DeviceLogin />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/settings.svg",
        title: "Change My Password",
        path: "/settings/settings-and-privacy?type=change-my-password",
      },
      comp: <ChangePassword />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/settings.svg",
        title: "Privacy & Management",
        path: "/settings/settings-and-privacy?type=privacy-and-management",
      },
      comp: <PrivacyManagement />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/settings.svg",
        title: "2FA",
        path: "/settings/settings-and-privacy?type=2fa",
      },
      comp: <TwoFactorAuthentication />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/wallet.svg",
        title: "Wallet",
        path: "/settings/wallet",
      },
      comp: <Wallet />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/card.svg",
        title: "Card",
        path: "/settings/addCard",
      },
      comp: <Cards />,
      partition: "General",
    },

    {
      list: {
        icon: "/settings/money.svg",
        title: "Creator Settings",
        path: "/settings/creator-settings",
      },
      comp: <YourFee />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/socials.svg",
        title: "Connected Accounts",
        path: "/settings/link-accounts",
      },
      comp: <LinkAccounts />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/user.svg",
        title: "My Offers",
        path: "/settings/my-offers",
      },
      comp: <OffersPage />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/kyc.svg",
        title: "Verification",
        path: "/settings/kyc",
      },
      comp: <YourIdentity />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/address.svg",
        title: "Address",
        path: "/settings/address",
      },
      comp: <Delivery />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/block.svg",
        title: "Block List",
        path: "/settings/blocks",
      },
      comp: <BlocksPage />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/info.svg",
        title: "Help & Support",
        path: "/settings/help-and-support",
      },
      comp: <HelpAndSupport />,
      partition: "General",
    },
    {
      list: {
        icon: "/settings/info.svg",
        title: "Release form",
        path: "/settings/release-form",
      },
      comp: <ReleaseForm />,
      partition: "General",
    },
  ],
};

export const partitions = [
  ...new Set(items.menuItems.map((item) => item.partition)),
];

const NewSettings = () => {
  const [activeItem, setActiveElement] = useState("");

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const fullPath =
    pathname + (searchParams.toString() ? `?${searchParams.toString()}` : "");
  const dispatch = useAppDispatch();
  useEffect(() => {
    if (window.innerWidth > 768) {
      const firstMenuItem = items.menuItems[0]?.list.title;
      setActiveElement(firstMenuItem);

      dispatch(settingsActions.setActiveItem(firstMenuItem));
    }
  }, []);

  useEffect(() => {
    const matchedItem = items.menuItems.find((item) =>
      fullPath.includes(item.list.path)
    );

    if (
      pathname === "/settings/settings-and-privacy" &&
      matchedItem === undefined
    ) {
      setActiveElement("Devices");
      dispatch(settingsActions.setActiveItem("Devices"));
    } else if (matchedItem) {
      setActiveElement(matchedItem.list.title);
      dispatch(settingsActions.setActiveItem(matchedItem.list.title));
    } else if (window.innerWidth > 768) {
      setActiveElement(items.menuItems[0]?.list.title);
      dispatch(settingsActions.setActiveItem(items.menuItems[0]?.list.title));
    }
  }, [pathname]);

  const isMobile = window.innerWidth < 768;

  const activeComponent = items.menuItems.find(
    (item) => item.list.title === activeItem
  )?.comp;
  return (
    <>
      <div
        className={
          activeItem == "Wallet" ||
          activeItem == "Setting and Privacy" ||
          activeItem == "Subscriptions" ||
          activeItem == "Wallet"
            ? "d-none "
            : "mobileHeader"
        }
      >
        <SettingsMobileHeader
          titleUpdate={activeItem}
          onBackButtonClick={() =>
            dispatch(settingsActions.toggleMenuVisibility())
          }
        />
      </div>
      <section className="container-xxl bd-gutter g-lg-4 g-0 my-3 settings-container">
        <div className="d-flex row align-items-start mx-md-auto ">
          {!isMobile && <Menu />}
          <div
            className={classNames(
              " py-0 col-lg-9 col-md-9 col-sm-12 ",
              {
                "mobile-visible": activeItem,
              },
              { "pe-0": isMobile }
            )}
          >
            {activeItem ? (
              <div
                className={classNames("content-section bg-body rounded-3  ")}
              >
                {activeComponent}
              </div>
            ) : (
              <div
                className="w-100  d-flex justify-content-center align-items-center"
                style={{ height: "90vh" }}
              >
                <LoadingSpinner />
              </div>
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export default memo(NewSettings);
