import Image from "@/node_modules/next/image";
import { memo, useEffect, useState } from "react";

type ChildComponentProps = {
  setValue1: React.Dispatch<React.SetStateAction<File | undefined>>;
  setValue2: React.Dispatch<React.SetStateAction<string>>;
  isVault: boolean;
  image: string;
  cb: () => void;
  onClose: () => void;
};

const ThumbnailComponent: React.FC<ChildComponentProps> = ({
  setValue1,
  setValue2,
  isVault,
  image,
  cb,
  onClose,
}) => {
  const [fileName, setFileName] = useState("file");
  const [postImg, setPostImg] = useState("");

  const fileUpload = async (event: any) => {
    const file = event.target.files[0];
    setValue1(event.target.files[0]);

    if (file) {
      setFileName(file.name.replace(/\s+/g, ""));
      setPostImg("");
      const blobUrl = URL.createObjectURL(file);
      setValue2(blobUrl);
      console.log("Thumb: ", { blobUrl });
      setPostImg(blobUrl);
    }
  };

  useEffect(() => {
    setPostImg(image);
  }, [image]);

  return (
    <>
      <div className="bg-body  rounded-3">
        <div className=" border-bottom ">
          <div className=" d-flex flex-column   p-3 ">
            <h5>Thumbnail</h5>
            <div className="color-light fw-medium">
              Choose the thumbnail that is displayed before the video plays.
            </div>
            {!postImg ? (
              <div
                onClick={() => isVault && cb()}
                className="d-flex align-items-center justify-content-center p-3 position-relative"
              >
                <Image
                  src={"/images/post/file-selector-icon.svg"}
                  alt=""
                  height={80}
                  width={80}
                />
                {!isVault && (
                  <input
                    type="file"
                    className="media-input position-absolute top-0 bottom-0 start-0 end-0 opacity-0 z-3  pointer"
                    accept="image/*,video/*"
                    onChange={fileUpload}
                  />
                )}
              </div>
            ) : (
              <div className="d-flex align-items-center justify-content-center p-3 position-relative">
                <div
                  onClick={() => setPostImg("")}
                  className="media-remove-btn rounded-2 mt-2 p-1 position-absolute pointer"
                >
                  <Image
                    src={"/images/svg/nav-close-white.svg"}
                    width={20}
                    height={20}
                    alt=""
                  />
                </div>
                <Image
                  src={postImg || ""}
                  alt=""
                  height={120}
                  width={240}
                  style={{ aspectRatio: "3/2", objectFit: "contain" }}
                />
              </div>
            )}
          </div>
        </div>
        <div>
          {isVault ? (
            <div
              onClick={() => cb()}
              className="d-flex gap-2 align-items-center justify-content-center pointer py-3 position-relative"
            >
              <Image
                src={"/images/post/image-select-icon.svg"}
                alt=""
                height={19}
                width={19}
              />
              <span className="color-primary fs-7 fw-500">
                Select from Vault
              </span>
            </div>
          ) : (
            <div className="d-flex gap-2 align-items-center justify-content-center py-3 position-relative">
              <Image
                src={"/images/post/image-select-icon.svg"}
                alt=""
                height={19}
                width={19}
              />
              <span className="color-primary fs-7 fw-500">
                Select from gallery
              </span>
              <input
                type="file"
                className="media-input position-absolute top-0 bottom-0 start-0 end-0 opacity-0 z-3  pointer"
                accept="image/*,video/*"
                onChange={fileUpload}
                // onMouseEnter={() => setBtnHover("media-button")}
                // onMouseLeave={() => setBtnHover("")}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export const Thumbnail = memo(ThumbnailComponent);
