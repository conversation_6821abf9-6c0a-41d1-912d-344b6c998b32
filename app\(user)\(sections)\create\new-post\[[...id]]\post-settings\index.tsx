import { addHours, format, parseISO } from "date-fns";
import Image from "next/image";
import { useEffect } from "react";

import Dot from "@/components/common/dot";
import { configActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { getAssetUrl } from "@/utils/assets";

import TagAndCollab from "../tag-collab";

const PostSettings = ({ props }: { props: any }) => {
  const user = useAppSelector((state) => state.user);

  const channelList = useAppSelector((state) => state?.userData?.channels);
  const groupList = useAppSelector((state) => state?.userData?.groups);
  const dispatchAction = useAppDispatch();
  const hasValidChannels = channelList.some(
    (channel) => !channel.marked_as_deleted
  );
  const hasValidCollabs = groupList.some((collab) => !collab.marked_as_deleted);

  const handleChannelChange = (entity: string) => {
    const index = props.shareToChannels?.findIndex(
      (selectedChannel: string) => selectedChannel === entity
    );

    if (index === -1) {
      props.setShareToChannels([...props.shareToChannels, entity]);
    } else {
      const updatedChannels = [...props.shareToChannels];
      updatedChannels.splice(index, 1);
      props.setShareToChannels(updatedChannels);
    }
  };

  const handleGroupChange = (entity: string) => {
    const index = props.shareToGroups?.findIndex(
      (selectedChannel: string) => selectedChannel === entity
    );

    if (index === -1) {
      props.setShareToGroups([...props.shareToGroups, entity]);
    } else {
      const updatedChannels = [...props.shareToGroups];
      updatedChannels.splice(index, 1);
      props.setShareToGroups(updatedChannels);
    }
  };

  useEffect(() => {
    if (props.visibility.text === "Draft") props.setShareToStory(true);
  }, [props.visibility]);

  useEffect(() => {
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  return (
    <div
      className={`post-settings-display d-flex flex-lg-row flex-column  gap-1 bg-body rounded-3 `}
    >
      <div className="view-options-wrapper w-lg-50 w-100   d-flex flex-column">
        <>
          <div className="post-to-wrapper d-flex flex-column">
            <div className=" align-items-center gap-3 mb-1">
              <div className="mb-3 mb-md-2 d-flex align-items-center gap-3 ">
                <Image
                  src={"/images/common/back-arrow.svg"}
                  height={20}
                  width={20}
                  alt="back"
                  className="pointer d-md-none"
                  onClick={() => props.setPostSettings(false)}
                />
                <span className="fs-5 fw-medium ">Options</span>
              </div>

              <div className="schedule-toggle">
                <div className="form-check form-switch mb-2 user-group-wrapper">
                  <label
                    className="form-check-label"
                    htmlFor="flexSwitchSchedule"
                  >
                    <span className="fw-medium ms-1">Set Schedule</span>
                  </label>
                  <input
                    className="form-check-input shadow-dark pointer"
                    type="checkbox"
                    role="switch"
                    id="flexSwitchSchedule"
                    disabled={props.isEdit}
                    onChange={() => {
                      if (props.isScheduled === false) {
                        props.setIsScheduled(true);

                        props.setShareToStory(true);
                        const now = format(
                          new Date().setDate(new Date().getDate() + 1),
                          "yyyy-MM-dd'T'HH:mm"
                        );
                        props.setScheduleDateTime(now);
                      } else {
                        props.setIsScheduled(false);
                      }
                    }}
                    checked={props.isScheduled}
                  />
                </div>

                <div className="schedule-options d-flex flex-column gap-2">
                  {props.isScheduled && (
                    <div className="schedule-date mb-1 w-100 w-md-75">
                      <label className="fs-7 mb-1">
                        When would you like this post to be published?*
                      </label>
                      <input
                        type="datetime-local"
                        className="form-control"
                        value={props.scheduleDateTime}
                        min={new Date(
                          new Date().setDate(new Date().getDate() + 1)
                        )
                          .toISOString()
                          .slice(0, 16)}
                        disabled={
                          props.isEdit &&
                          new Date(props.scheduleDateTime) < new Date()
                        }
                        onChange={(e) => {
                          props.setScheduleDateTime(e.target.value);
                          const newScheduleDate = parseISO(e.target.value);
                          const newExpiryDate = addHours(newScheduleDate, 1);
                          props.setExpiryTime(
                            format(newExpiryDate, "yyyy-MM-dd'T'HH:mm")
                          );
                        }}
                      />
                    </div>
                  )}
                  <div className="form-check form-switch mb-1 user-group-wrapper mt-0">
                    <label
                      className="form-check-label"
                      htmlFor="flexSwitchExpiry"
                    >
                      <span className="fw-medium ms-1">
                        Has expiration date
                      </span>
                    </label>
                    <input
                      className="form-check-input shadow-dark pointer"
                      type="checkbox"
                      role="switch"
                      id="flexSwitchExpiry"
                      disabled={props.isEdit}
                      onChange={() => {
                        if (props.hasExpiry === false) {
                          props.setHasExpiry(true);
                          props.setShareToStory(true);
                        } else {
                          props.setHasExpiry(false);
                        }
                      }}
                      checked={props.hasExpiry}
                    />
                  </div>{" "}
                  {props.hasExpiry && (
                    <div className="expiry-date w-100 w-md-75">
                      <label className="fs-7  mb-1">Expiration date*</label>
                      <input
                        type="datetime-local"
                        className="form-control"
                        value={props.expiryTime}
                        min={new Date(
                          new Date().setDate(new Date().getDate() + 1)
                        )
                          .toISOString()
                          .slice(0, 16)}
                        disabled={
                          props.isEdit &&
                          new Date(props.expiryTime) < new Date()
                        }
                        onChange={(e) => {
                          props.setExpiryTime(e.target.value);

                          const x = new Date(e.target.value);
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
            {!props.isEdit && (
              <>
                {" "}
                <div className="d-flex align-items-center gap-3 mb-1">
                  {/* {isMobile && (
                  <div className="mb-2">
                    <Image
                      src={"/images/common/back-arrow.svg"}
                      height={20}
                      width={20}
                      alt="back"
                      className="pointer"
                      onClick={() => props.setPostSettings(false)}
                    />
                  </div>
                )} */}
                </div>
                {!props.postingAsSubs && (
                  <>
                    <p className="fs-5 fw-medium mb-2">Post to</p>
                    {(props.bg.backgroundColor || props.mediaFiles) &&
                      !(
                        props.visibility.value === "OnlyMe" ||
                        props.isGroup ||
                        props.isChannel
                      ) &&
                      props.collabUsers.length === 0 && (
                        <div
                          className={`form-check form-switch mb-2 user-group-wrapper  `}
                        >
                          <label
                            className={`form-check-label`}
                            htmlFor="flexSwitchCheckDefault"
                          >
                            <span className="fw-medium ms-1"> My story</span>
                          </label>
                          <input
                            className="form-check-input shadow-dark"
                            type="checkbox"
                            role="switch"
                            id="flexSwitchCheckDefault"
                            disabled={
                              props.postOf.poll ||
                              (props.postOf.background &&
                                (props.visibility.value === "Premium" ||
                                  props.visibility.value === "Prime")) ||
                              props.hasExpiry ||
                              props.isScheduled
                            }
                            onChange={() => {
                              if (props.shareToStory === false) {
                                props.setShareToStory(true);
                              } else {
                                props.setShareToStory(false);
                              }
                            }}
                            checked={!props.shareToStory}
                          />
                        </div>
                      )}
                    {props.visibility.value !== "Public" && (
                      <div className="form-check form-switch mb-2 user-group-wrapper">
                        <label
                          className={`form-check-label`}
                          htmlFor="flexSwitchProfile"
                        >
                          <span className="fw-medium ms-1"> Main profile </span>
                        </label>
                        <input
                          className="form-check-input shadow-dark"
                          type="checkbox"
                          role="switch"
                          id="flexSwitchProfile"
                          onChange={() => {
                            if (props.shareOnProfile === false) {
                              props.setShareOnProfile(true);
                            } else {
                              props.setShareOnProfile(false);
                            }
                          }}
                          checked={props.shareOnProfile}
                        />
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>

          {(props.postOf.tag || props.postOf.tagAndRelease) && (
            <div className="tag-content d-flex flex-column p-1 ps-0 mt-2">
              <TagAndCollab
                getUsers={{
                  tags: props.tagConsent,
                  setTags: props.setTagConsent,
                  collab: props.collabUsers,
                  setCollab: props.setCollabUsers,
                  releaseUsers: props.releaseUsers,
                  setReleaseUsers: props.setReleaseUsers,
                }}
                removeTag={() => props.setPostType({ tag: false })}
                tagAndCollab={false}
              />
            </div>
          )}
        </>
      </div>

      {props.postSettings &&
      user.role === "creator" &&
      !(
        (props.visibility.value === "Prime" ||
          props.visibility.value === "Premium" ||
          props.visibility.value === "Subscription") &&
        !props.collabUsers.length
      ) ? (
        <></>
      ) : (
        <div
          className={`share-channel-wrapper w-lg-50 w-100  mt-2 mt-md-0  d-flex flex-column ${
            window.location.pathname.includes("/edit") ? "disabled" : ""
          }`}
        >
          {channelList[0]?.name && hasValidChannels && (
            <div
              style={{
                minHeight: "16rem",
                maxHeight: "18rem",
                overflow: "scroll",
              }}
            >
              <h6>Share to Channel</h6>

              <div>
                {channelList.map((channel) => {
                  // console.log("Available Channels: ", { channel });
                  if (channel.marked_as_deleted) {
                    return;
                  }

                  return (
                    <div
                      key={channel?._id}
                      className={`d-flex gap-3 align-items-center mt-3 ms-1`}
                      aria-disabled={true}
                    >
                      <input
                        type="checkbox"
                        className="form-check-input"
                        checked={props.shareToChannels.some(
                          (selectedChannel: string) =>
                            selectedChannel === channel._id
                        )}
                        disabled={window.location.pathname.includes("/edit")}
                        onChange={() => handleChannelChange(channel?._id)}
                      />
                      <label className="d-flex gap-3 ">
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          src={getAssetUrl({
                            media: channel.avatar[0],
                          })}
                          alt=""
                          className="rounded-circle bg-body object-fit-cover"
                          width={48}
                          height={48}
                        />

                        <div className="d-flex flex-column justify-content-around ">
                          <h6>{channel?.name}</h6>
                          <div className="d-flex align-items-center">
                            <span className={`  fs-7 color-medium`}>
                              @{channel?.channel_tagname || ""}
                            </span>
                            <Dot />
                            <span className="fs-7 color-medium ">
                              {channel?.counter?.post_count || 0} posts
                            </span>
                          </div>
                        </div>
                      </label>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          {hasValidCollabs && hasValidChannels && <hr />}
          {groupList.map((group) => {
            if (
              group.members.some((u) => !u.terms_accepted) ||
              group.marked_as_deleted
            ) {
              return null;
            }
          })}

          {groupList[0]?.name && hasValidCollabs && (
            <div
              className="mb-2"
              style={{ maxHeight: "18rem", overflow: "scroll" }}
            >
              {<h6>Share to Collab</h6>}

              <div>
                {groupList.map((group) => {
                  if (
                    group.members.some((u) => !u.terms_accepted) ||
                    group.marked_as_deleted
                  ) {
                    return;
                  }

                  return (
                    <div
                      key={group?._id}
                      className="d-flex gap-3 align-items-center mt-3 ms-1"
                    >
                      <input
                        type="checkbox"
                        className="form-check-input"
                        checked={props.shareToGroups.some(
                          (selectedChannel: string) =>
                            selectedChannel === group._id
                        )}
                        onChange={() => handleGroupChange(group?._id)}
                      />
                      <label className="d-flex gap-3 ">
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          src={getAssetUrl({
                            media:
                              group?.members[1]?.member_id?.avatar[0] ||
                              group?.members[0]?.member_id?.avatar[0],
                          })}
                          alt=""
                          className="rounded-circle bg-body object-fit-cover"
                          width={48}
                          height={48}
                        />
                        {group.members.slice(1).length > 0 && (
                          <div
                            style={{
                              marginLeft: -40,
                            }}
                          >
                            <div
                              className="rounded-pill bg-cream"
                              style={{
                                height: 48,
                                width: 48,
                              }}
                            >
                              <div className="bg-purple rounded-pill h-100 w-100 d-flex justify-content-center align-items-center text-white fw-bold">
                                <u className="fs-8 text-wrap">
                                  +{group.members.slice(1).length}
                                </u>
                              </div>
                            </div>
                          </div>
                        )}

                        <div className="d-flex flex-column justify-content-around ">
                          <h6>{group?.name}</h6>
                          <div className="d-flex align-items-center">
                            <span className={`  fs-7 color-medium`}>
                              @{group?.tag_name}
                            </span>
                            <Dot />
                            <span className="fs-7 color-medium ">
                              {group?.counter?.post_count || 0} posts
                            </span>
                          </div>
                        </div>
                      </label>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PostSettings;
