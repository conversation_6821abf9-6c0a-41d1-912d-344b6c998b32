$checked-color: var(--primary-color);
$white: var(--bg-body-color);
$body-color: var(--bg-color);
$lightgray: var(--fg-color);


.checkbox-container {
  &.circle-purple {
    div {
      pointer-events: none;
    }

    label {
      cursor: pointer;
      display: block;
      background-color: transparent;
      box-shadow: 0 0 0 0.1rem $lightgray;
      border-radius: 50%;
      height: 1rem;
      width: 1rem;
      position: relative;

      &:after {
        content: "";
        opacity: 0;
        position: absolute;
        transform: rotate(45deg) translate(100%, -30%);
        height: 60%;
        width: 30%;
        border-top: none;
        border-right: none;
        box-shadow: 1pt 1pt 0 0 $white;
        z-index: 1;
      }
    }

    .check-box:focus + div {
      > label {
        box-shadow: 0 0 0 2pt rgba(var(--primary-rgb), 0.2);
      }
    }

    .check-box:checked + div {
      color: $checked-color;

      > label {
        background-color: $checked-color;
        box-shadow: 0 0 0pt 0.1rem $checked-color;

        &:after {
          opacity: 1;
        }
      }
    }
  }

  &.slider {
    .slider {
      background-color: $white;
      width: 3rem;
      height: 1.5rem;
      transition: 0.4s;
      position: relative;
      border-radius: 1rem;

      &:before {
        content: "";
        position: absolute;
        margin: 0.125rem;
        left: 0;
        width: 1.25rem;
        height: 1.25rem;
        transition: 0.4s;
        background-color: $body-color;
        border-radius: 50%;
        box-shadow: 0 1pt 1pt 0 var(--bg-dark);
      }
    }

    .check-box:checked + div {
      .slider {
        background-color: $checked-color;

        &:before {
          left: unset;
          right: 1.25rem;
          transform: translateX(100%);
        }
      }
    }
  }
}
