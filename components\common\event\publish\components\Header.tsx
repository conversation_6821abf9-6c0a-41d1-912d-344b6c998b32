import { useRouter } from "next/navigation";
import Image from "next/image";

import { FinishBtn } from "./FinishBtn";

export function Header() {
  const router = useRouter();

  return (
    <div className="d-flex bg-cream position-relative">
      <div className="container-xxl d-flex justify-content-center justify-content-md-between align-items-center px-3 py-2">
        <div className="fs-5 user-select-none">Event Finished</div>
        <FinishBtn className="d-none d-md-block" />
        <Image
          src="/images/post/line-close.svg"
          width={16}
          height={16}
          alt="close"
          className="d-block d-md-none cursor-pointer position-absolute top-50 start-0 translate-middle-y ms-3"
          onClick={() => router.push("/fresh")}
        />
      </div>
    </div>
  );
}
