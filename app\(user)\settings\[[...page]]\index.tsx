"use client";

import Image from "next/image";
import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import "./index.scss";

import { detectMacOSAndIOS } from "@/components/PWAInstaller";
import { OnlineDot } from "@/components/common/OnlineDot";
import ActionButton from "@/components/common/action-button";
import JoinUs from "@/components/common/join-us";
import type { ModalRef } from "@/components/common/modal";
import { Divider } from "@/components/common/wrapper";
import { ModalService } from "@/components/modals";
import OffersPage from "@/components/offers";
import AccountInformation from "@/components/settings/account-information/page";
import Delivery, {
  AddAddressModal,
  EditAddressModal,
} from "@/components/settings/address/delivery";
import BlocksPage from "@/components/settings/blocklist";
import AddCardComp from "@/components/settings/card/add-card/page";
import Cards from "@/components/settings/card/cards/page";
import CreatorSettings from "@/components/settings/creator-settings/page";
import PreviewModal from "@/components/settings/creator-settings/sub-components/auto-messages/preview-modal";
import EditAccountInformation from "@/components/settings/edit-account-information/page";
import ComplaintChat from "@/components/settings/help-and-support/modal";
import ReportOthers from "@/components/settings/help-and-support/modal-2";
import HelpAndSupport from "@/components/settings/help-and-support/page";
import LinkAccounts from "@/components/settings/link-accounts";
import ReferAndEarn from "@/components/settings/refer-and-earn";
import SettingAndPrivacy from "@/components/settings/setting-and-privacy/page";
import SettingsMobileHeader from "@/components/settings/settings-mobile-header/page";
import YourIdentity from "@/components/settings/verify-account/your-identity/page";
import PaymentHistory from "@/components/settings/wallet-components/payment-history/page";
import Withdraw from "@/components/settings/wallet-components/withdraw/page";
import Wallet from "@/components/settings/wallet/page";
import useIsPWA from "@/hooks/useIsPWA";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import {
  chatActions,
  matchMakerActions,
  userActions,
  userDataActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { KycStatus } from "@/redux-store/slices/user.slice";
import { getAssetUrl } from "@/utils/assets";
import socketChannel from "@/utils/chatSocket";
import { auth } from "@/utils/firebase";
import { clearLocalStorage } from "@/utils/local-storage";

import styles from "./page.module.css";

const withdrawRef = { method: {} as ModalRef };
const imageRef = { method: {} as ModalRef };
interface InstalledRelatedApp {
  id?: string;
  platform:
    | "chrome_web_store"
    | "play"
    | "chromeos_play"
    | "webapp"
    | "windows"
    | "f-droid"
    | "amazon";
  url?: string;
  version?: string;
}

export default function Settings() {
  const [previewLink, setPreviewLink] = useState<string>("");
  const [isAccountInfoVisible, setisAccountInfoVisible] = useState(true);
  const [isEditInfoVisible, setisEditInfoVisible] = useState(false);
  const [menuTitle, setMenuTitle] = useState("Account Information");
  const [isDarkMode, setIsDarkMode] = useState(false);
  const isVerified = useAppSelector((state) => state.user.isVerified);
  const knky_verified = useAppSelector(
    (s) => s.user.profile.badges.user_badges
  ).some((badge: string) => badge === "VerifiedByKnky");
  const userId = useAppSelector((state) => state.user.id);
  const role = useAppSelector((s) => s.user.role);
  const isPWA = useIsPWA();

  const AddAddressModalRef = { method: {} as ModalRef };
  const EditAddressModalRef = { method: {} as ModalRef };

  const [showModal, setShowModal] = useState(false);
  const [showEditAddressModal, setEditAddressModal] = useState(false);
  const [platform, setPlatform] = useState({
    isMacOS: false,
    isIOS: false,
    isSafari: false,
  });

  useEffect(() => {
    const detectedPlatform = detectMacOSAndIOS();
    setPlatform(detectedPlatform);
  }, []);
  useEffect(() => {
    showEditAddressModal && EditAddressModalRef.method.open();
  }, [showEditAddressModal]);

  useEffect(() => {
    showModal && AddAddressModalRef.method.open();
  }, [showModal]);

  const toggleComponentsVisibility = () => {
    setisAccountInfoVisible(!isAccountInfoVisible);
    setisEditInfoVisible(!isEditInfoVisible);
  };

  const searchParams = useSearchParams();

  const isCardExist = useAppSelector(
    (state) => state?.userData?.cardDetails[0]
  );
  const chatChildRef = { method: {} as ModalRef };
  const reportOthersChildRef = { method: {} as ModalRef };
  const id = useParams();

  const [menuVisible, setMenuVisible] = useState(false);

  const showMenu = searchParams.has("menu");
  const [isVisible, setIsVisible] = useState(false);
  const [installedAndroid, setInstalledInAndroid] = useState(false);

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  useEffect(() => {
    if (showMenu) setMenuVisible(true);
  }, []);

  const handleBackButtonClick = () => {
    // setMenuVisible(true);
    navigateBack();
  };

  useEffect(() => {
    (async () => {
      if (typeof navigator !== "undefined" && userRole.role !== "guest") {
        if ("getInstalledRelatedApps" in navigator) {
          try {
            // @ts-expect-error - TS may not recognize getInstalledRelatedApps

            const relatedApps = await navigator.getInstalledRelatedApps();

            if (relatedApps?.length > 0) {
              const isInstalled = relatedApps.some(
                (app: InstalledRelatedApp) => app.platform === "webapp"
              );
              setInstalledInAndroid(isInstalled);
            } else {
              setInstalledInAndroid(false);
            }
          } catch (error) {
            console.error("Error checking installed related apps:", error);
            setInstalledInAndroid(false);
          }
        } else {
          console.warn(
            "getInstalledRelatedApps is not supported on this platform."
          );
          setInstalledInAndroid(false);
        }
      }
    })();
  }, []);

  const handleMenuOptionClick = (menuName: string) => {
    setMenuVisible(false);
    setMenuTitle(menuName);
  };

  const chatSocket = socketChannel;
  const [isModalOpen, setModalOpen] = useState(false);

  const isOpen = () => {
    setModalOpen(!isModalOpen);
  };

  const userRole = useAppSelector((state) => state.user);
  const dispatchAction = useAppDispatch();
  useEffect(() => {
    if (isDarkMode) {
      document.body.classList.add("dark");
    } else {
      document.body.classList.remove("dark");
    }

    return () => {
      document.body.classList.remove("dark");
    };
  }, [isDarkMode]);

  const handleToggleSwitch = () => {
    setIsDarkMode(!isDarkMode);
  };

  const [showCards, setShowCards] = useState(false);
  const [_addNew, setAddNew] = useState(false);

  useEffect(() => {
    if (id && id.page && id?.page[0] === "") {
      handleMenuOptionClick("Data & Storage");
    }

    if (window.location.href.includes("id=")) {
      setShowCards(true);
    } else {
      setShowCards(false);
    }

    if (window.location.href.includes("?new")) {
      setAddNew(true);
    } else {
      setAddNew(false);
    }

    if (id && id.page && id?.page[0] === "creator-settings") {
      handleMenuOptionClick("Creator Settings");
      toggleComponentsVisibility();
    } else if (id && id.page && id?.page[0] === "settings-and-privacy") {
      setMenuTitle("Setting and Privacy");
    } else if (id && id.page && id?.page[0] === "refer-and-earn") {
      setMenuTitle("Refer to Earn");
    } else if (id && id.page && id?.page[0] === "wallet") {
      setMenuTitle("Wallet");
    } else if (id && id.page && id?.page[0] === "kyc") {
      setMenuTitle("Verification");
    } else if (id && id.page && id?.page[0] === "address") {
      setMenuTitle("Address");
    } else if (id && id.page && id?.page[0] === "blocks") {
      setMenuTitle("BlockList");
    } else if (id && id.page && id?.page[0] === "addCard") {
      handleMenuOptionClick("Card");
    } else if (id && id.page && id?.page[0] === "help-and-support") {
      setMenuTitle("Help & Support");
    } else if (id && id.page && id?.page[0] === "edit-information") {
      handleMenuOptionClick("Edit Account Information");
      toggleComponentsVisibility();
    } else if (id && id.page && id?.page[0] === "my-offers") {
      handleMenuOptionClick("My Offers");
    } else if (id && id.page && id?.page[0] === "link-accounts") {
      handleMenuOptionClick("Connected Accounts");
    }
  }, [id]);
  const complaintId = useAppSelector((state) => state?.settings?.complaintId);

  const [avatar, setAvatar] = useState("/images/common/default.svg");

  const [_username, setUsername] = useState("User");
  const navigateBack = useNavigateBack();

  useEffect(() => {
    setAvatar(
      getAssetUrl({
        media: userRole.profile.avatar?.[0],
        defaultType: "avatar",
      })
    );
    setUsername(userRole.profile.f_name);
  }, [userRole]);

  const router = useRouter();

  const refreshToken = useAppSelector((state) => state.user.refreshToken);

  const logOut = () => {
    try {
      fetch(`${process.env.backend}/v1/users/logout`, {
        headers: {
          Authorization: `Bearer ${refreshToken}`,
          // 'Content-Type': 'application/json'
        },
        method: "POST",
      });
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  if (userRole.role === "guest") {
    return (
      <div>
        <JoinUs />
      </div>
    );
  }

  const lottieMap: {
    [K in "user" | "creator" | "knky_creator"]: string;
  } = {
    user: "https://lottie.host/embed/a15df9fb-9063-4865-a2f2-3ceb06dd0522/nh9sxFi2ji.json",
    creator:
      "https://lottie.host/embed/080d6ea5-08d1-4421-ab5b-3b1b86970f0d/vxwJHsW3YR.json",
    knky_creator:
      "https://lottie.host/embed/b08402e1-027d-481d-95be-58c0fab603b9/LGRIqsxXLK.json",
  };

  return (
    <>
      <div
        className={
          menuTitle == "Wallet" ||
          menuTitle == "Creator Settings" ||
          menuTitle == "Setting and Privacy" ||
          menuTitle == "Subscriptions" ||
          (id?.page?.[0] === "settings-and-privacy" &&
            menuTitle == "Setting and Privacy") ||
          (id?.page?.[0] === "wallet" && menuTitle == "Wallet") ||
          menuTitle == "Creator Settings"
            ? "d-none "
            : styles.mobileHeader
        }
      >
        <SettingsMobileHeader
          titleUpdate={menuTitle}
          onBackButtonClick={handleBackButtonClick}
        />
      </div>
      <section
        className={`container-xxl bd-gutter g-lg-4 g-0 my-3 settings-container ${styles.section}`}
      >
        <div className="d-flex row align-items-start mx-md-auto ">
          <div
            className={
              menuVisible
                ? "nav col-lg-3 col-md-3 col-sm-12 nav-pills setting-sideBar bg-body rounded-lg-3   " +
                  styles.sideBar +
                  " " +
                  styles.sideBarSlide
                : "nav flex-column col-lg-3 col-md-3 col-sm-12 nav-pills setting-sideBar bg-body rounded-3 " +
                  styles.sideBar
            }
            id="v-pills-tab"
            role="tablist"
            aria-orientation="vertical"
            style={{ minHeight: "100%" }}
          >
            <div className="w-100 position-sticky top-0 bg-body z-1">
              <span className="d-flex align-items-center gap-2 w-100">
                <div onClick={() => navigateBack()} className="pointer d-flex">
                  <Image
                    src={"/images/svg/back-no-circle.svg"}
                    width={44}
                    height={64}
                    alt="back"
                    className="w-100"
                  />{" "}
                </div>
                <span className="fs-2 fw-bold mb-1">Settings</span>
              </span>

              <Link
                href={`/${userRole?.role?.toLowerCase()}/${
                  userRole.profile.username
                }`}
                className="d-flex user-row w-100 pt-0 justify-content-between align-items-center p-3 border-bottom d-md-none"
              >
                <div className="user-wrapper d-flex gap-3">
                  <div className="position-relative">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={avatar}
                      width={50}
                      height={50}
                      alt="profile-icon"
                      className="rounded-pill"
                    />
                    <OnlineDot
                      userId={userId}
                      style={{
                        width: "12px",
                        height: "12px",
                        position: "absolute",
                        bottom: "5%",
                        right: "0%",
                      }}
                    />
                  </div>
                  <div className="d-flex flex-column align-items-start">
                    <p className="fs-5 mb-0 fw-medium">
                      {userRole.profile.display_name}
                    </p>
                    <p className="fs-7 mb-0 color-light">
                      Click to see your profile
                    </p>
                  </div>
                </div>
                <Image
                  src={"/images/svg/chevron-right.svg"}
                  width={30}
                  height={30}
                  alt="profile-chevron"
                />
              </Link>
            </div>
            <div className="d-flex w-100 flex-column">
              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  id && id.page && id?.page[0] === "account-information"
                    ? "active"
                    : id && id.page && id?.page[0] === "edit-information"
                    ? "active"
                    : ""
                }`}
                // id="v-pills-home-tab"
                // data-bs-toggle="pill"
                // data-bs-target="#v-pills-home"
                type="button"
                role="tab"
                aria-controls="v-pills-home"
                aria-selected="true"
                onClick={() => {
                  handleMenuOptionClick("Account Information");

                  router.push("/settings/account-information");
                }}
              >
                <div className="d-flex pt-1 align-items-center">
                  <Image
                    src={"/settings/user.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />
                  <span>Account Information</span>
                </div>
              </button>
              <button
                className={`settings-nav-tabs nav-link color-bold  ${
                  id &&
                  id.page &&
                  id?.page[0] === "settings-and-privacy" &&
                  "active"
                }`}
                // id="v-pills-profile-tab"
                // data-bs-toggle="pill"
                // data-bs-target="#v-pills-profile"
                type="button"
                role="tab"
                aria-controls="v-pills-profile"
                aria-selected="true"
                onClick={() => {
                  handleMenuOptionClick("Setting and Privacy");
                  router.push("/settings/settings-and-privacy");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/settings.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />
                  <span>Settings & Privacy</span>
                </div>
              </button>
              {userRole.role === "creator" && (
                <button
                  className={`settings-nav-tabs nav-link color-bold ${
                    id && id.page && id?.page[0] === "knky" && "active"
                  }`}
                  // id="v-pills-blocks-tab"
                  // data-bs-toggle="pill"
                  // data-bs-target="#v-pills-blocks"
                  type="button"
                  role="tab"
                  aria-controls="v-pills-knky"
                  aria-selected="false"
                  onClick={() => {
                    handleMenuOptionClick("Blocks");

                    router.push(`/dashboard`);
                  }}
                >
                  <div className="d-flex align-items-center">
                    <Image
                      src={"/settings/revenue.svg"}
                      width={30}
                      height={30}
                      alt=""
                    />

                    <span>Revenue</span>
                  </div>
                </button>
              )}
              <button
                className={`settings-nav-tabs nav-link color-bold  ${
                  id && id.page && id?.page[0] === "refer-and-earn" && "active"
                }`}
                type="button"
                role="tab"
                aria-controls="v-pills-profile"
                aria-selected="true"
                onClick={() => {
                  handleMenuOptionClick("Refer to Earn");
                  router.push("/settings/refer-and-earn");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/refer.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />
                  <span>Refer to Earn</span>
                </div>
              </button>
              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  id && id.page && id?.page[0] === "my-offers"
                    ? "active"
                    : id && id.page && id?.page[0] === "my-offers"
                    ? "active"
                    : ""
                }`}
                // id="v-pills-home-tab"
                // data-bs-toggle="pill"
                // data-bs-target="#v-pills-home"
                type="button"
                role="tab"
                aria-controls="v-pills-home"
                aria-selected="true"
                onClick={() => {
                  handleMenuOptionClick("My Offers");

                  router.push("/settings/my-offers");
                }}
              >
                <div className="d-flex pt-1 align-items-center">
                  <Image
                    src={"/settings/user.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />
                  <span>My Offers</span>
                </div>
              </button>
              <button
                className={` ${
                  userRole?.role == "creator"
                    ? "settings-nav-tabs nav-link color-bold " + styles.privacy
                    : "d-none"
                }  ${
                  id &&
                  id.page &&
                  id?.page[0] === "creator-settings" &&
                  "active"
                } `}
                // id="v-pills-creator-tab"
                // data-bs-toggle="pill"
                // data-bs-target="#v-pills-creator"
                type="button"
                role="tab"
                aria-controls="v-pills-creator"
                aria-selected="true"
                onClick={() => {
                  handleMenuOptionClick("Creator Settings");
                  router.push("/settings/creator-settings");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/money.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />
                  <span>Creator Settings</span>
                </div>
              </button>

              {userRole.role === "creator" && (
                <button
                  className={` ${"settings-nav-tabs nav-link color-bold "}  ${
                    id && id.page && id?.page[0] === "link-accounts" && "active"
                  } `}
                  type="button"
                  role="tab"
                  aria-controls="v-pills-socials"
                  aria-selected="true"
                  onClick={() => {
                    handleMenuOptionClick("Connected Accounts");
                    router.push("/settings/link-accounts");
                  }}
                >
                  <div className="d-flex align-items-center">
                    <Image
                      src={"/settings/socials.svg"}
                      width={30}
                      height={30}
                      alt=""
                    />
                    <span>Connected Accounts</span>
                  </div>
                </button>
              )}

              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  id && id.page && id?.page[0] === "addCard" ? "active" : ""
                }`}
                // id="v-pills-messages-tab"
                // data-bs-toggle="pill"
                // data-bs-target="#v-pills-messages"
                type="button"
                role="tab"
                aria-controls="v-pills-messages"
                aria-selected="false"
                onClick={() => {
                  handleMenuOptionClick("Card");
                  router.push("/settings/addCard");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/card.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />

                  <span>Card</span>
                </div>
              </button>
              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  id && id.page && id?.page[0] === "wallet" ? "active" : ""
                }`}
                // id="v-pills-settings-tab"
                // data-bs-toggle="pill"
                // data-bs-target="#v-pills-settings"
                type="button"
                role="tab"
                aria-controls="v-pills-settings"
                aria-selected="false"
                onClick={() => {
                  handleMenuOptionClick("Wallet");
                  router.push("/settings/wallet");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/wallet.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />

                  <span>Wallet</span>
                </div>
              </button>

              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  id && id.page && id?.page[0] === "kyc" && "active"
                }`}
                // id="v-pills-kyc-tab"
                // data-bs-toggle="pill"
                // data-bs-target="#v-pills-kyc"
                type="button"
                role="tab"
                aria-controls="v-pills-kyc"
                aria-selected="false"
                onClick={() => {
                  handleMenuOptionClick("KYC");

                  router.push("/settings/kyc");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/kyc.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />

                  <span className="d-flex gap-2 align-items-center justify-content-center">
                    Verification
                    {isVerified === KycStatus.ACCEPTED ? (
                      <>
                        {(role === "creator" || role === "user") &&
                        !knky_verified ? (
                          <iframe
                            src={lottieMap[role]}
                            width={25}
                            height={25}
                            loading="lazy"
                          ></iframe>
                        ) : null}
                        {role === "creator" && knky_verified ? (
                          <iframe
                            src={lottieMap["knky_creator"]}
                            width={25}
                            height={25}
                            loading="lazy"
                          ></iframe>
                        ) : null}
                      </>
                    ) : (
                      <span
                        className={
                          isVerified === KycStatus.SUBMITTED
                            ? "extra-svg-review"
                            : "extra-svg-rejected"
                        }
                      >
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 14 14"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M9.88699 13.668L4.10699 13.668C1.84699 13.668 0.333658 12.0813 0.333658 9.7213L0.333657 4.27464C0.333657 1.9213 1.84699 0.334636 4.10699 0.334636L9.88699 0.334635C12.147 0.334635 13.667 1.9213 13.667 4.27464L13.667 9.7213C13.667 12.0813 12.147 13.668 9.88699 13.668ZM7.00699 8.9613C7.15966 8.96306 7.30548 9.02494 7.41281 9.13352C7.52014 9.2421 7.58033 9.38863 7.58032 9.5413C7.58097 9.65724 7.54705 9.77073 7.48291 9.86731C7.41876 9.96388 7.32729 10.0391 7.22018 10.0835C7.11306 10.1279 6.99516 10.1393 6.88151 10.1163C6.76787 10.0934 6.66365 10.0371 6.58214 9.95464C6.50064 9.87219 6.44555 9.76732 6.42392 9.65342C6.40228 9.53952 6.41507 9.42176 6.46066 9.31516C6.50625 9.20856 6.58257 9.11797 6.67988 9.05494C6.77719 8.99192 6.89107 8.95932 7.00699 8.9613ZM6.42032 4.4813C6.42032 4.1613 6.68032 3.9013 7.00699 3.9013C7.16082 3.9013 7.30834 3.96241 7.41711 4.07118C7.52588 4.17995 7.58699 4.32748 7.58699 4.4813L7.58699 7.42797C7.58699 7.74797 7.32699 8.01464 7.00699 8.01464C6.68033 8.01464 6.42032 7.74797 6.42032 7.42797L6.42032 4.4813Z"
                          />
                        </svg>
                      </span>
                    )}
                  </span>
                </div>
              </button>

              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  styles.dataStorage
                } ${id && id.page && id?.page[0] === "address" && "active"}
              `}
                type="button"
                role="tab"
                aria-controls="v-pills-address"
                aria-selected="false"
                onClick={() => {
                  handleMenuOptionClick("Address");

                  router.push("/settings/address");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/location.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />
                  <span>Address</span>
                </div>
              </button>
              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  id && id.page && id?.page[0] === "blocks" && "active"
                }`}
                type="button"
                role="tab"
                aria-controls="v-pills-blocks"
                aria-selected="false"
                onClick={() => {
                  handleMenuOptionClick("Blocks");

                  router.push("/settings/blocks");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/close.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />
                  <span>Block list</span>
                </div>
              </button>
              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  id && id.page && id?.page[0] === "knky" && "active"
                }`}
                type="button"
                role="tab"
                aria-controls="v-pills-knky"
                aria-selected="false"
                onClick={() => {
                  handleMenuOptionClick("Blocks");

                  router.push("/platform-plans");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/star-circle.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />

                  <span>Knky Premium</span>
                </div>
              </button>
              <button
                className={`settings-nav-tabs nav-link color-bold ${
                  styles.dataStorage
                } ${
                  id &&
                  id.page &&
                  id?.page[0] === "help-and-support" &&
                  "active"
                }
              `}
                type="button"
                role="tab"
                aria-controls="v-pills-help"
                aria-selected="false"
                onClick={() => {
                  handleMenuOptionClick("Help & Support");
                  router.push("/settings/help-and-support");
                }}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src={"/settings/info.svg"}
                    width={30}
                    height={30}
                    alt=""
                  />
                  <span>Help & Support</span>
                </div>
              </button>
              <button
                className={
                  "settings-nav-tabs nav-link color-bold d-none " +
                  styles.dataStorage
                }
                id="v-pills-systemMode-tab"
                type="button"
                role="tab"
                aria-selected="false"
                onClick={() => handleMenuOptionClick("Data & Storage")}
              >
                <div className="d-flex justify-content-between   align-items-center">
                  <div className="d-flex flex-grow-1 ">
                    <Image
                      src="/settings/systemMode.svg"
                      alt=""
                      width={20}
                      height={20}
                    />

                    <span>System mode</span>
                  </div>

                  <div className="d-flex justify-content-center">
                    <div className="form-check form-switch ">
                      <input
                        type="checkbox"
                        className="form-check-input largeSwitch"
                        checked={isDarkMode}
                        onChange={handleToggleSwitch}
                        id="darkModeToggle"
                      />
                    </div>
                  </div>
                </div>
              </button>
              <button
                className={
                  "settings-nav-tabs nav-link color-bold pb-3 d-none  " +
                  styles.revenue
                }
                id="v-pills-language-tab"
                type="button"
                role="tab"
                onClick={() => handleMenuOptionClick("Data & Storage")}
              >
                <div className="d-flex justify-content-between   align-items-center">
                  <div className="d-flex gap-3">
                    <Image
                      src="/settings/language.svg"
                      alt=""
                      width={20}
                      height={20}
                    />

                    <span>Language</span>
                  </div>
                  <select
                    className={styles.input + " border-0 bg-body"}
                    name=""
                    id=""
                  >
                    <option value="">English</option>
                    <option value="">Spanish </option>
                    <option value="">French</option>
                  </select>
                </div>
              </button>
              {!isPWA && (
                <>
                  <hr />
                  <button
                    className={`settings-nav-tabs nav-link color-bold ${
                      id && id.page && id?.page[0] === "knky" && "active"
                    }`}
                    type="button"
                    role="tab"
                    aria-controls="v-pills-knky"
                    aria-selected="false"
                    onClick={() => {
                      if (installedAndroid) return;

                      if (
                        (platform.isIOS || platform.isMacOS) &&
                        platform.isSafari
                      ) {
                        ModalService.open("INSTALL_PWA");
                      } else {
                        // @ts-expect-error - ts doesn't know about prompt
                        window.KNKY?.promptPwaInstall();
                      }
                    }}
                  >
                    <div className="d-flex align-items-center">
                      <Image
                        src={"/settings/download.svg"}
                        width={30}
                        height={30}
                        alt=""
                      />

                      <span>
                        {installedAndroid ? "App installed" : "Install App"}
                      </span>
                    </div>
                  </button>
                </>
              )}

              <button
                className={"settings-nav-tabs nav-link color-bold"}
                id="v-pills-language-tab"
                type="button"
                role="tab"
                onClick={isOpen}
              >
                <div className="d-flex align-items-center">
                  <Image
                    src="/settings/logOut.svg"
                    alt="image"
                    width={30}
                    height={30}
                  />

                  <span>Log Out</span>
                </div>
              </button>

              <ul
                style={{
                  marginTop: "3rem",
                  // paddingBottom: isPWA ? "5.5rem" : "",
                }}
                className="d-flex nav  px-3 justify-content-lg-center pb-lg-0 justify-content-md-center mb-lg-3 fs-6 d-md-none  gap-1"
              >
                <li className="nav-item fw-medium  px-2 py-1">
                  <Link href={"https://lander.knky.co/"} target="_blank">
                    About us
                  </Link>
                </li>
                <Divider direction="end" />
                <li className="nav-item fw-medium  px-2 py-1">
                  <Link href={"/articles/terms-of-service"} target="_blank">
                    T&Cs
                  </Link>
                </li>
                <Divider direction="end" />
                <li className="nav-item fw-medium  px-2 py-1">
                  <Link href={"https://help.knky.co/en/"} target="_blank">
                    Help
                  </Link>
                </li>
                <Divider direction="end" />
                <li className="nav-item fw-medium  px-2 py-1">
                  <Link href={"/platform-plans"} target="_blank">
                    Premium Plans
                  </Link>
                </li>
              </ul>
              <div>
                <p className=" fs-10 color-medium ps-4 px-3 mt-2 d-md-none d-lg-none d-block">
                  © 2024 KNKY®
                </p>
              </div>
            </div>
          </div>
          <div
            className={`tab-content col-lg-9 col-md-9 col-sm-12 ${
              menuVisible ? "menuVisible" : ""
            }`}
            id="v-pills-tabContent"
          >
            <div
              className={`tab-pane fade show ${
                (id && id.page && id?.page[0] === "account-information") ||
                (id && id.page && id?.page[0] === "edit-information")
                  ? "active show"
                  : ""
              }`}
              id="v-pills-home"
              role="tabpanel"
              aria-labelledby="v-pills-home-tab"
            >
              {isAccountInfoVisible && (
                <AccountInformation onToggle={toggleComponentsVisibility} />
              )}
              {isEditInfoVisible && (
                <EditAccountInformation onToggle={toggleComponentsVisibility} />
              )}
            </div>
            {id && id.page && id?.page[0] === "settings-and-privacy" && (
              <div
                className={`tab-pane position-relative fade ${!isCardExist?.payment_method_id}  ${
                  id &&
                  id.page &&
                  id?.page[0] === "settings-and-privacy" &&
                  "active show"
                }`}
                id="v-pills-profile"
                role="tabpanel"
                aria-labelledby="v-pills-profile-tab"
              >
                <SettingAndPrivacy onBackButtonClick={handleBackButtonClick} />
              </div>
            )}
            {id && id.page && id?.page[0] === "refer-and-earn" && (
              <div
                className={`tab-pane position-relative fade ${
                  id &&
                  id.page &&
                  id?.page[0] === "refer-and-earn" &&
                  "active show"
                }`}
                id="v-pills-profile"
                role="tabpanel"
                aria-labelledby="v-pills-profile-tab"
              >
                <ReferAndEarn />
              </div>
            )}
            {id && id.page && id?.page[0] === "my-offers" && (
              <div
                className={`tab-pane fade ${
                  id && id.page && id?.page[0] === "my-offers" && "active show"
                }`}
                id="v-pills-settings"
                role="tabpanel"
                aria-labelledby="v-pills-settings-tab"
              >
                <OffersPage />
              </div>
            )}

            {id && id.page && id?.page[0] === "creator-settings" && (
              <div
                className={`${
                  userRole?.role == "creator" ? "tab-pane fade" : "d-none"
                }
                ${
                  id &&
                  id.page &&
                  id?.page[0] === "creator-settings" &&
                  "active show"
                }

                `}
                id="v-pills-creator"
                role="tabpanel"
                aria-labelledby="v-pills-creator-tab"
              >
                <div className="position-relative">
                  <CreatorSettings
                    onBackButtonClick={handleBackButtonClick}
                    setPreviewLink={setPreviewLink}
                    imageRef={imageRef}
                  />
                </div>
              </div>
            )}
            {id && id.page && id?.page[0] === "link-accounts" && (
              <div
                className={`tab-pane fade
                ${
                  id &&
                  id.page &&
                  id?.page[0] === "link-accounts" &&
                  "active show"
                }

                `}
                id="v-pills-creator"
                role="tabpanel"
                aria-labelledby="v-pills-creator-tab"
              >
                <div className="position-relative">
                  <LinkAccounts />
                </div>
              </div>
            )}

            {id && id.page && id?.page[0] === "addCard" && (
              <div
                className={`tab-pane fade ${!showCards} " " ${!isCardExist?.payment_method_id}  ${
                  id && id.page && id?.page[0] === "addCard" && "active show"
                }`}
                id="v-pills-messages"
                role="tabpanel"
                aria-labelledby="v-pills-messages-tab"
              >
                {/* {((!showCards && !isCardExist?.payment_method_id) || addNew) && (
                <AddCard onBackButtonClick={handleBackButtonClick} />
              )} */}
                {/* {(showCards || isCardExist?.payment_method_id) && !addNew && ( */}
                <Cards />
                {/* )} */}
              </div>
            )}
            {id && id.page && id?.page[0] === "wallet" && (
              <div
                className={`tab-pane fade ${
                  id && id.page && id?.page[0] === "wallet" && "active show"
                }`}
                id="v-pills-settings"
                role="tabpanel"
                aria-labelledby="v-pills-settings-tab"
              >
                <Wallet />
              </div>
            )}

            {id && id.page && id?.page[0] === "blocks" && (
              <div
                className={`tab-pane fade ${
                  id && id.page && id?.page[0] === "blocks" && "active show"
                }`}
                id="v-pills-blocks"
                role="tabpanel"
                aria-labelledby="v-pills-blocks-tab"
              >
                <BlocksPage />
              </div>
            )}

            {id && id.page && id?.page[0] === "kyc" && (
              <div
                className={`tab-pane fade ${
                  id && id.page && id?.page[0] === "kyc" && "active show"
                }   ${styles.payment}`}
                id="v-pills-kyc"
                role="tabpanel"
                aria-labelledby="v-pills-kyc-tab"
              >
                <YourIdentity />
              </div>
            )}

            {id && id.page && id?.page[0] === "payment" && (
              <div
                className={`tab-pane fade ${
                  id && id.page && id?.page[0] === "payment" && "active show"
                }   ${styles.payment}`}
                id="v-pills-payment"
                role="tabpanel"
                aria-labelledby="v-pills-payment-tab"
              >
                <h4>Payment history</h4>
                <PaymentHistory />
              </div>
            )}

            {/* <div
              className={`tab-pane fade ${
                id && id.page && id?.page[0] === "revenue" && "active show"
              }`}
              id="v-pills-revenue"
              role="tabpanel"
              aria-labelledby="v-pills-revenue-tab"
            >
              <RevenueStatistics />
            </div> */}
            {/* <div
              className="tab-pane fade"
              id="v-pills-dataStorage"
              role="tabpanel"
              aria-labelledby="v-pills-dataStorage-tab"
            >
              <DataAndStorage />
            </div> */}
            {id && id.page && id?.page[0] === "address" && (
              <div
                className={`tab-pane fade ${
                  id && id.page && id?.page[0] === "address" && "active show  "
                }`}
                id="v-pills-address"
                role="tabpanel"
                aria-labelledby="v-pills-address-tab"
              >
                <div className={`bg-body p-3   ${styles.payment}`}>
                  <Delivery />
                </div>
              </div>
            )}

            {id && id.page && id?.page[0] === "help-and-support" && (
              <div
                className={`tab-pane fade ${
                  id &&
                  id.page &&
                  id?.page[0] === "help-and-support" &&
                  "active show"
                }`}
                id="v-pills-help"
                role="tabpanel"
                aria-labelledby="v-pills-help-tab"
              >
                <HelpAndSupport />
              </div>
            )}
          </div>
        </div>
      </section>
      <ComplaintChat setChildRef={chatChildRef} misc={complaintId} />
      <ReportOthers setChildRef={reportOthersChildRef} />

      {id && id.page && id?.page[0] === "addCard" && (
        <AddCardComp
          isVisible={isVisible}
          toggleVisibility={toggleVisibility}
        />
      )}
      {showModal && (
        <AddAddressModal
          setChildRef={AddAddressModalRef}
          misc={
            showModal
              ? () => {
                  setShowModal(false);
                }
              : () => setShowModal(true)
          }
        />
      )}

      {showEditAddressModal && (
        <EditAddressModal
          setChildRef={EditAddressModalRef}
          misc={
            showEditAddressModal
              ? () => {
                  setEditAddressModal(false);
                }
              : () => setEditAddressModal(true)
          }
        />
      )}

      {/* footer */}

      <div className="position-fixed left-0 bottom-0 w-100 "></div>

      {/* Log Out Modal */}

      <div
        className={isModalOpen ? "show d-block modal fade " : "modal fade "}
        id="exampleModalCenter"
        // tabindex="-1"
        role="dialog"
        aria-labelledby="exampleModalCenterTitle"
        aria-hidden={isModalOpen ? "false" : "true"}
      >
        <div
          className="modal-dialog modal-dialog-centered modal-xl"
          role="document"
        >
          <div className="modal-content bg-body">
            <div className="modal-header">
              <h5 className="modal-title opacity-0" id="exampleModalLongTitle">
                Modal title
              </h5>
              <button
                type="button"
                className="close"
                data-dismiss="modal"
                aria-label="Close"
                onClick={isOpen}
              >
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div className={"modal-body " + styles.modalBody}>
              <div className="text-center my-5">
                <Image
                  src="/settings/queIcon.png"
                  alt=""
                  width={32}
                  height={44.6}
                ></Image>
                <h2 className={"fw-bold my-4 color-bold "}>
                  Are you sure want to log out?
                </h2>

                <div className="d-flex gap-3 justify-content-center mt-4">
                  <ActionButton type="outline" variant="dark" className="w-25">
                    <Link
                      href="/fresh"
                      onClick={() => {
                        localStorage.setItem("first-walk", "false");
                        auth.signOut();

                        logOut();
                        dispatchAction(userActions.setConverseToken(""));
                        dispatchAction(userDataActions.logOut());
                        dispatchAction(userActions.setFetched(false));
                        chatSocket.closeSocket();
                        dispatchAction(chatActions.resetChatState());
                        dispatchAction(userActions.resetUserProfile());
                        dispatchAction(userActions.setUserToken(""));
                        clearLocalStorage();
                        dispatchAction(matchMakerActions.resetProfile());
                        dispatchAction(
                          userActions.setUserIsVerified(undefined)
                        );
                        router.push("/");
                      }}
                    >
                      Log out
                    </Link>
                  </ActionButton>
                  <ActionButton
                    type="solid"
                    variant="primary"
                    className="w-25"
                    onClick={isOpen}
                  >
                    Cancel
                  </ActionButton>
                </div>
              </div>
            </div>
            <div className="modal-footer"></div>
          </div>
        </div>
      </div>
      {id && id.page && id?.page[0] === "wallet" && (
        <>
          <Withdraw setChildRef={withdrawRef} />
        </>
      )}
      {id && id.page && id?.page[0] === "creator-settings" && (
        <PreviewModal
          setChildRef={imageRef}
          misc={{ imageLink: previewLink }}
        />
      )}
    </>
  );
}
