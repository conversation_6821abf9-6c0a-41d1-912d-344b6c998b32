import Link from "next/link";
import { useEffect, useState } from "react";

import { GetCreatorsWithKinks } from "@/api/user";
import ActionButton from "@/components/common/action-button";
import TrendingShimmer from "@/components/common/shimmer/all-shimmer/trending";
import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";
import type { UserProfile } from "@/types/user";
import { getAssetUrl } from "@/utils/assets";

import type { UserFeatured } from "../../../trending";
import { Featured } from "../../../trending";

function transformData(profiles: UserProfile[]): UserFeatured[] {
  return profiles.map((profile) => ({
    back: getAssetUrl({ media: profile.background[0], variation: "thumb" }),
    profile: getAssetUrl({
      media: profile.avatar[0],
      defaultType: "avatar",
    }),
    //  To Make a mechanism to save all the subscriptions and following peoples for the user
    subscribed: false,
    followed: false,
    role: profile.user_type.toLowerCase(),
    badges: profile?.badges,
    name: `${profile.f_name} ${profile.l_name}`,
    username: profile.username,
    price: profile.chat_fee?.[0]?.price || 0,
    followers: profile.follower_count,
    videos: profile.media_count.video_count || 0,
    photos: profile.media_count.image_count || 0,
    id: profile._id,
    minSubscription: profile?.min_subscription,
    is_subscribed: profile?.is_subscribed,
    f_name: profile?.f_name,
    display_name: profile?.display_name,
    has_purchasable_shop_item: profile?.has_purchasable_shop_item,
    followed_by_you: profile?.followed_by_you,
    follows_you: profile?.follows_you,
    shop_count: profile?.shop_count,
    totalLikes: profile?.totalLikes,
  }));
}

const NoCreators = () => {
  return (
    <div className="d-flex flex-column gap-3 justify-content-center align-items-center bg-body py-5 px-3 rounded-3">
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src="/images/common/join-us-1.svg"
        width={148}
        height={148}
        alt="no subs"
      />

      <div className="d-flex flex-column gap-2 align-items-center mt-3">
        <div className="d-flex flex-column text-center">
          <h4 className="fw-semibold">🚫 No creators found for this kink!</h4>
          <p className="fs-7 color-black m-0 fw-medium">
            Don’t stop now! Dive deeper and explore more creators who share your
            wildest vibes. There’s always something exciting waiting for you. 🔥
          </p>
        </div>

        <div className="d-flex flex-column flex-md-row gap-3 align-items-center mt-3">
          <Link
            href={"/trending"}
            className="btn btn-purple fw-semibold "
            style={{ minWidth: "13rem" }}
          >
            Explore creators
          </Link>
        </div>
      </div>
    </div>
  );
};

const NoPosts = ({ setShowCreators }: { setShowCreators: any }) => {
  return (
    <div className="d-flex flex-column gap-3 justify-content-center align-items-center bg-body py-5 px-3 rounded-3">
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src="/images/common/join-us-1.svg"
        width={148}
        height={148}
        alt="no subs"
      />

      <div className="d-flex flex-column gap-2 align-items-center mt-3">
        <div className="d-flex flex-column text-center">
          <h4 className="fw-semibold">🙈 Oops... No posts for this tag yet!</h4>
          <p className="fs-7 color-black m-0 fw-medium">
            Explore more creators who share your kinks and start connecting with
            the vibes you love.🔥
          </p>
        </div>

        <div className="d-flex flex-column flex-md-row gap-3 align-items-center mt-3">
          <div
            onClick={() => setShowCreators(true)}
            className="btn btn-purple fw-semibold "
            style={{ minWidth: "13rem" }}
          >
            Show creators
          </div>
        </div>
      </div>
    </div>
  );
};

const InterestedCreators = ({ arePosts = false }: { arePosts?: boolean }) => {
  const IsUserKinks = useAppSelector(
    (state) => state.user.profile.preferences.kinks.length
  );
  const userKinks = useAppSelector((state) =>
    state.user.profile.preferences.kinks.map((tag) => tag.slice(1))
  );
  const [showCreators, setShowCreators] = useState<boolean>(false);
  const selectedTag = useAppSelector((s) => s.defaults.selectedTag);
  const [creators, setCreators] = useState<UserFeatured[]>([]);
  const [loading, setLoading] = useState(true);
  const blockedByMe = useAppSelector((state) => state?.block?.blockedByMe);
  const hasBlockedMe = useAppSelector((state) => state?.block?.hasBlockedMe);

  useEffect(() => {
    if (IsUserKinks && showCreators) {
      GetCreatorsWithKinks(selectedTag.length ? [selectedTag] : userKinks)
        .then((res) => {
          setCreators(transformData(res.data));
          setLoading(false);
        })
        .catch(() => {
          setLoading(false);
        });
    }
  }, [showCreators]);

  if (!IsUserKinks) {
    return (
      <div className="d-flex flex-column gap-3 justify-content-center align-items-center bg-body py-5 px-3 rounded-3">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src="/images/common/join-us-1.svg"
          width={148}
          height={148}
          alt="no subs"
        />

        <div className="d-flex flex-column gap-2 align-items-center mt-3">
          <div className="d-flex flex-column text-center">
            <h4 className="fw-semibold">
              Complete your profile to unlock the &quot;For You&quot; feed!
            </h4>
            <p className="fs-7 color-black m-0 fw-medium">
              The &quot;For You&quot; section is where the fun begins! <br />{" "}
              Let us know your preferences to deliver a personalized experience.
            </p>
          </div>

          <div className="d-flex flex-column flex-md-row gap-3 align-items-center mt-3">
            <ActionButton
              className=" btn-responsive-text px-4"
              variant="info"
              type="outline"
              onClick={() => ModalService.open("COMPLETE_PROFILE")}
            >
              Complete Your Profile
            </ActionButton>
          </div>
        </div>
      </div>
    );
  }

  if (!showCreators) {
    if (arePosts) {
      return (
        <div className="text-center bg-body rounded p-3 mt-2 ">
          {" "}
          Yay! You have seen it all.{" "}
          <span
            className="color-primary pointer"
            onClick={() => setShowCreators(true)}
          >
            {" "}
            Explore creators
          </span>
        </div>
      );
    } else {
      return <NoPosts setShowCreators={setShowCreators} />;
    }
  } else {
    return (
      <div className=" overflow-y-auto container-fluid g-0 g-md-4 g-lg-0 users-featured-list my-lg-0 my-md-4">
        {loading ? (
          <TrendingShimmer />
        ) : (
          <>
            {creators.length > 0 ? (
              <div
                className="row g-3 pb-2 me-0 position-relative"
                style={{
                  overflowY: "auto",
                  // height: "calc(100% + var(--bs-gutter-y))",
                }}
              >
                {creators.map((creator) => {
                  if (blockedByMe[creator.id] || hasBlockedMe[creator.id]) {
                    return;
                  }

                  return (
                    <div
                      key={creator.id}
                      className={`${"col-lg-4 col-md-6 col-sm-12 pe-lg-2 pe-0 "}`}
                    >
                      <Featured user={creator} />
                    </div>
                  );
                })}
              </div>
            ) : (
              <NoCreators />
            )}
          </>
        )}
      </div>
    );
  }
};

export default InterestedCreators;
