import Swal from "sweetalert2";

import { GetUserProfile, UserBecomeCreator } from "@/api/user";
import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";

const UserWarning = () => {
  const userProfileComplete = useAppSelector(
    (state) => state.user.profile.is_profile_completed
  );

  const becomeCreator = () => {
    UserBecomeCreator({})
      .then(() => {
        Swal.fire({
          icon: "success",
          title: "You're now creator.",
          confirmButtonText: "Finish",
          confirmButtonColor: "#AC1991",
          showConfirmButton: true,
          allowEscapeKey: false,
          customClass: {
            confirmButton: "custom-btn",
          },
          allowOutsideClick: false,
        }).then((result) => {
          if (result.isConfirmed) {
            GetUserProfile();
          }
        });
      })
      .catch(() => {});
  };

  return (
    <div className="warning-container">
      <p className="fw-medium m-0 color-sky-blue">
        You are limited to post only polls and captions. Become a creator to
        post more.
      </p>

      {userProfileComplete ? (
        <>
          <button className="btn btn-purple" onClick={becomeCreator}>
            Become a creator
          </button>
        </>
      ) : (
        <button
          className="btn btn-purple"
          onClick={() => ModalService.open("COMPLETE_PROFILE")}
        >
          Upgrade now
        </button>
      )}
    </div>
  );
};

export default UserWarning;
