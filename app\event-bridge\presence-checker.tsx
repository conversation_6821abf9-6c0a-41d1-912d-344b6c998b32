import { debounce } from "lodash";

import { EventBridge } from ".";

class PresenceCheckerClass {
  private users = new Map<string, Map<string, (isOnline: number) => void>>();
  private loopRef: NodeJS.Timeout | undefined;
  private debouncedProcess = debounce(this.process.bind(this), 1000);

  constructor() {}

  attach(
    userId: string,
    listenerId: string,
    listenerFn: (isOnline: number) => void
  ) {
    if (!this.users.has(userId)) {
      this.users.set(userId, new Map());
    }

    this.users.get(userId)?.set(listenerId, listenerFn);

    EventBridge.isConnected && this.debouncedProcess();
  }

  detach(userId: string, listenerId: string) {
    this.users.get(userId)?.delete(listenerId);

    if (this.users.get(userId)?.size === 0) {
      this.users.delete(userId);
    }
  }

  startProcessing() {
    this.process();
  }

  stopProcessing() {
    clearInterval(this.loopRef);
  }

  private async process() {
    clearTimeout(this.loopRef);

    if (this.users.size) {
      const ids = Array.from(this.users.keys());
      const res = await EventBridge.request("Presence/Check", ids);

      res.forEach((v, i) => {
        const userId = ids[i];
        this.users.get(userId)?.forEach((fn) => fn(v));
      });
    }

    this.loopRef = setTimeout(this.process.bind(this), 20_000);
  }
}

export const PresenceChecker = new PresenceCheckerClass();
