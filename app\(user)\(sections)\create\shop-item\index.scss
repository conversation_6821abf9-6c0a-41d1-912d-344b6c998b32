.pill {
  width: fit-content;
  border: 1px solid var(--bg-cream);
}

.pill:hover {
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

//switch toggle button css

.form-check-input:checked {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.form-check-input:focus {
  border-color: var(--primary-color) !important;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(253, 13, 209, 0.25) !important;
}
.form-switch .form-check-input {
  width: 3em !important;
  height: 1.6em !important;
}

.sell-item-images {
  width: 100%;
  height: 100%;
}
.img-preview-swiper-container {
  .swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
    aspect-ratio: 3/2;
  }
}
.sell-item-images .swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}

.shop-next-btn {
  background-image: url(/images/common/next.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  background-color: rgba(255, 255, 255, 0.5);
  background-blend-mode: multiply;
  transform: scale(0.8);
  filter: invert(1);
}
.shop-prev-btn {
  background-image: url(/images/common/next.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  background-color: rgba(255, 255, 255, 0.5);
  background-blend-mode: multiply;
  transform: rotate(180deg) scale(0.8);
  filter: invert(1);
}
