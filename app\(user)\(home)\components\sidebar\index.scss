.home-sidebar {
  flex-basis: 12rem;
  max-width: 24rem;
  flex-grow: 18;

  position: sticky;
  top: 1.5em;
  height: 100%;
  overflow-y: auto;

  .home-wrapper {
    top: 8rem;
    // position: sticky;
  }
}

@keyframes glowing {
  0% {
    box-shadow: 0 0 -2px var(--primary-color);
  }
  40% {
    box-shadow: 0 0 4px var(--primary-color);
  }
  60% {
    box-shadow: -3 0 6px var(--primary-color);
  }
  100% {
    box-shadow: 0 0 0px var(--primary-color);
  }
}

.ske-match {
  animation: glowing 5000ms infinite;
}

.ske-container {
  background-color: var(--bg-color);

  .ske-content {
    .ske {
      padding: 0.5rem 1rem;
      border: 1pt solid rgba(var(--fg-rgb), 0.15);
    }
  }

  .ske-highlight-blue {
    color: var(--unlimited-color);
  }
  .ske-highlight-gold {
    color: #ffb800;
  }

  .ske-btn {
    color: var(--primary-color);
    cursor: pointer;
  }

  .ske-icon {
    align-self: baseline;
  }

  .prime-icon {
    border-radius: calc(var(--border-radius) * 0.25);
    background-color: var(--unlimited-color);

    img {
      scale: 0.8;
    }
  }
  .unlimited-icon {
    border-radius: calc(var(--border-radius) * 0.25);
    background-color: #ffb800;

    img {
      scale: 0.8;
    }
  }
}

.chat-home-list {
  min-height: 27.5vh;
  max-height: 27.5vh;
  overflow-y: auto;
}
.chat-header-info .header-search-box {
  // position: absolute;
  z-index: 2;
  // top: 1rem;
  // right: 1rem;
  display: flex;
  flex-direction: column;
}
.chat-custom-menu {
  background-color: var(--bg-cream);
  border: none;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  width: 100%;
  border-top: 0 !important;
  border-radius: 1rem;
  display: flex;
  justify-items: center;
  flex-direction: column;
  max-height: 19rem;
  overflow: auto;
  z-index: 3;
  li {
    list-style: none;
    z-index: 10;
  }
}
.chat-custom-menu::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}
.chat-custom-menu {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.chat-custom-menu::-webkit-scrollbar-thumb {
  background-color: #cecece;
}
.swiper {
  width: 100%;
  height: 100%;
}
.unreads {
  background-color: #ac1991;
  color: white;
  width: 1.7rem;
  height: 1.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.unreads:empty {
  display: none;
}
