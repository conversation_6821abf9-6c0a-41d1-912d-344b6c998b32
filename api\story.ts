import { type Media } from "@/types/media";
import type { PostType } from "@/types/post";
import type { SellItem } from "@/types/sell-item";

import API from ".";

interface Author {
  _id: string;
  f_name: string;
  l_name: string;
  user_type: string;
  username: string;
  badges: Badges[];
  avatar: Media[];
  background: Media[];
  display_name: string;
}

export interface Badges {
  user_badges: any[];
  subscription_badge: any[];
}

export interface Avatar {
  original: string;
}

export const StoryVisbility: any = {
  Premium: "Pay-To-View",
  Prime: "Prime",
  Public: "Public",
};

export interface StorySubmitBody {
  media?: {
    path: string;
    type: "image" | "video";
  }[];
  visibility: string;
  type: string;
  caption: string;
  backgroundColor?: string;
  vault_media_ids?: string[];
  vault_poster_ids?: string[];
  poster?: {
    path: string;
    type: "image" | "video";
  }[];
  poster_generation: boolean | string;
  textColor?: string;
  product_id?: string;
  product?: string;
  pay_and_watch_rate?: number | string;
}
interface StorySubmitResponse {
  data: "success";
}

interface StoryComment {
  message: string;
}
export let floatingProgress: any;

export async function StorySubmit(body: StorySubmitBody) {
  const storyData: StorySubmitBody = {
    media: body.media,
    visibility: body.visibility,
    type: body.type,
    caption: body.caption,
    backgroundColor: body.backgroundColor,
    poster_generation: String(body.poster_generation),
  };

  if (body.poster?.length) {
    storyData.poster = body.poster;
  }

  if (body.textColor && body.backgroundColor) {
    storyData.textColor = body.textColor;
    storyData.backgroundColor = body.backgroundColor;
  }

  if (body.vault_media_ids?.length) {
    storyData.vault_media_ids = body.vault_media_ids!;
  }

  if (body.vault_poster_ids?.length) {
    storyData.vault_poster_ids = body.vault_poster_ids!;
  }

  if (body.poster_generation) {
    storyData.poster_generation = body.poster_generation;
  }

  if (body.product_id) {
    storyData.product = body.product_id;
    delete storyData.media
  }

  if (body.pay_and_watch_rate && body.visibility === "Premium") {
    storyData.pay_and_watch_rate = String(body.pay_and_watch_rate);
  }

  return API.post(API.STORY, storyData) as Promise<StorySubmitResponse>;
}

export function DeleteStory(_id: string) {
  const form = new FormData();
  return API.delete(`${API.STORY}/${_id}`, form);
}

export interface GetStoryListResponse {
  data: GetStory[];
}
export interface GetStory {
  avatar: Media[];
  badges: Badges;
  display_name: string;
  user_type: string;
  has_active_services: boolean;
  username: string;
  _id: string;
  story_data: StoryMedia[];
}

export interface GetStoryViews {
  data: { _id: string; views: StoryViews[] };
}

export interface StoryViews {
  _id: string;
  f_name: string;
  l_name: string;
  user_type: string;
  username: string;
  avatar: Media[];
}

export interface Post_Media {
  original: string;
}

export interface StoryMedia {
  _id: string;
  visibility: string;
  hashtags: any[];
  type: string;
  post_id: PostMedia;
  product: SellItem;
  media: Media[];
  pay_and_watch_rate: number;
  backgroundColor?: string;
  media_categories: string[];
  earning: number;
  is_deleted: boolean;
  is_purchased: boolean;
  is_liked: boolean;
  has_prime: boolean;
  created_at: string;
  updated_at: string;
  __v: number;
}

interface PostMedia {
  _id: string;
  caption: string;
  media: Media[];
  preview: Media[];
  visibility: PostType;
  has_prime: boolean;
  is_purchased: boolean;
  textColor: string;
  backgroundColor: string;
}
interface LikeStory {
  reaction_type: string;
}
export const GetStoryList = async (
  page: number,
  userId?: string,
  isFollowing?: boolean,
  isSubscribed?: boolean
) =>
  API.get(
    `${API.STORY}?page=${page}&limit=10${
      userId
        ? `&user_id=${userId}`
        : isFollowing
        ? "&following=true"
        : isSubscribed
        ? "&subscribed=true"
        : ""
    } `
  ) as Promise<GetStoryListResponse>;

export interface ViewStoryResponse {
  message: string;
}

export const ViewStory = async (storyId: string) =>
  (await API.post(
    `${API.STORY}/${storyId}/viewers`,
    {}
  )) as Promise<ViewStoryResponse>;

export const GetStroyCounts = async (storyId: string, type: string) =>
  API.get(`${API.STORY}/${storyId}/${type}`) as Promise<any>;

export const LikeStory = async (storyId: string, body: LikeStory) =>
  API.post(`${API.STORY}/${storyId}/likes`, body);

export const CommentStory = async (storyId: string, body: StoryComment) =>
  API.post(`${API.STORY}/${storyId}/reply`, body);

export const GetStoryById = async (storyId: string) =>
  API.get(`${API.STORY}/${storyId}`) as Promise<GetStory>;
