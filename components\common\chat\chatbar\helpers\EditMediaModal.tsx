import type { Dispatch, SetStateAction } from "react";
import { memo } from "react";

import ImageCropper from "@/components/common/cropper";
import { ModalPortal } from "@/components/portals/ModalPortal";

const EditMediaModalBase = ({
  currentUrl,
  setCroppedPostUrlFunc,
  cropMedia,
  resetCropper,
}: {
  currentUrl: string;
  setCroppedPostUrlFunc: (croppedImg: Blob) => void;
  resetCropper: Dispatch<SetStateAction<boolean>>;
  cropMedia: boolean;
}) => {
  return (
    <ModalPortal>
      <div
        className="modal fade"
        id="editMediaModal"
        tabIndex={-1}
        data-bs-backdrop="static"
        aria-labelledby="editMediaModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header border-0">
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                id="modal-close"
              ></button>
            </div>
            <div className="modal-body d-flex justify-content-center align-items-center w-100">
              {cropMedia && (
                <ImageCropper
                  img={currentUrl}
                  // NaN does the free form crop
                  aspectRatio={NaN}
                  dragMode="move"
                  guides={false}
                  cropBoxResizable={true}
                  onLoadingStarted={() => {
                    (
                      document.querySelector(
                        "#editMediaModal #modal-close"
                      ) as HTMLElement
                    ).click();
                  }}
                  onCropComplete={(finalBlob) => {
                    setCroppedPostUrlFunc(finalBlob);
                    resetCropper(false);
                  }}
                  cropperHeight={window.innerWidth > 768 ? "70vh" : "50vh"}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export const EditMediaModal = memo(EditMediaModalBase);
