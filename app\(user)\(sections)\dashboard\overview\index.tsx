import classNames from "classnames";
import Image from "next/image";
import { useEffect, useState } from "react";

import { UserOverView } from "@/api/dashboard";
import DashboardShimmer, {
  DashboardShimmerEarning,
  DashboardShimmerOverview,
} from "@/components/common/shimmer/all-shimmer/dashboard";
import { useAppSelector } from "@/redux-store/hooks";
import {
  earningCategories,
  type EarningCategory,
  type OverviewState,
} from "@/types/creator-dashboard";
import {
  ServicesColor,
  ServicesImg,
  ServicesTypeLabel,
} from "@/types/services";
import { formatCurrency } from "@/utils/formatter";

const EarningCategoryData = ({
  earningData,
  servicesData,
}: {
  earningData: EarningCategory[];
  servicesData: EarningCategory[];
}) => {
  const mergedEarningData = earningCategories.map((category) => {
    if (category.text === "Pay-To-View") {
      const premiumPost = earningData.find(
        (data) => data._id === "PremiumPost"
      );
      const premiumStory = earningData.find(
        (data) => data._id === "PremiumStory"
      );

      return {
        ...category,
        totalAmount:
          (premiumPost ? premiumPost.totalAmount : 0) +
          (premiumStory ? premiumStory.totalAmount : 0),
        percentage:
          (premiumPost ? premiumPost.percentage : 0) +
          (premiumStory ? premiumStory.percentage : 0),
        count:
          (premiumPost ? premiumPost.count : 0) +
          (premiumStory ? premiumStory.count : 0),
      };
    }

    if (category.text === "Services") {
      const promotion = earningData.find((data) => data._id === "Promotion");
      const services = earningData.find(
        (data) => data._id === "SpecialOptions"
      );

      return {
        ...category,
        totalAmount:
          (promotion ? promotion?.totalAmount : 0) +
          (services ? services?.totalAmount : 0),
        percentage:
          (promotion ? promotion?.percentage : 0) +
          (services ? services?.percentage : 0),
        count:
          (promotion ? promotion?.count : 0) + (services ? services?.count : 0),
      };
    }

    const match = earningData.find((data) => data._id === category._id);
    return {
      ...category,
      totalAmount: match ? match.totalAmount : 0,
      percentage: match ? match.percentage : 0,
      count: match ? match.count : 0,
    };
  });

  return (
    <>
      {mergedEarningData.map((res, index) => {
        const theme = localStorage.getItem("theme");

        if (res.text === "Services") {
          return (
            <div key={index} className="card">
              <div
                className="accordion accordion-flush"
                id={`accordionFlushExample-${index}`}
              >
                <div className="accordion-item rounded">
                  <h2 className="accordion-header p-3">
                    <button
                      className="accordion-button collapsed p-0 "
                      type="button"
                      data-bs-toggle="collapse"
                      data-bs-target={`#flush-collapse-${index}`}
                      aria-expanded="false"
                      aria-controls={`flush-collapse-${index}`}
                    >
                      <div
                        className="d-flex align-items-center gap-2"
                        style={{
                          width: window.innerWidth > 768 ? "97%" : "149%",
                        }}
                      >
                        <Image
                          src={"/images/dashboard/star.svg"}
                          width={32}
                          height={32}
                          alt={"star"}
                        />
                        <p className="m-0 text-nowrap fw-medium">{res.text}</p>
                      </div>
                      <div className="w-100 text-start">
                        <span className="ms-2">{res.count}</span>
                      </div>
                      <div className="w-100 text-start ms-1">
                        <span>{res.percentage.toFixed(2)}%</span>
                      </div>
                      <div
                        className=" text-start"
                        style={{
                          width: window.innerWidth > 768 ? "64%" : "120%",
                        }}
                      >
                        <h5 className="fw-semibold  m-0">
                          {formatCurrency(res.totalAmount)}
                        </h5>
                      </div>
                    </button>
                  </h2>
                  <div
                    id={`flush-collapse-${index}`}
                    className="accordion-collapse collapse"
                    data-bs-parent={`#accordionFlushExample-${index}`}
                  >
                    <div className="accordion-body p-0">
                      <div className="services-data-header">
                        <div className="d-flex align-items-center justify-content-between gap-2">
                          <div className="d-flex w-100 align-items-center gap-2">
                            <p className="m-0 text-nowrap color-black fs-8">
                              Service
                            </p>
                          </div>
                          <div className="w-100 text-start color-black fs-8">
                            Sold
                          </div>
                          <div className="w-100 text-start color-black fs-8">
                            Service group
                          </div>

                          <div className="w-60 text-start color-black fs-8">
                            Total made
                          </div>
                          <div className="w-10 text-center"></div>
                        </div>
                      </div>
                      {servicesData.map((res, index) => (
                        <div
                          key={index}
                          className={classNames(
                            "d-flex align-items-center justify-content-between gap-2 p-3",
                            "border-bottom"
                          )}
                        >
                          <div className="d-flex w-100 align-items-center gap-2">
                            <div
                              className="p-1 py-0 rounded-3"
                              style={{
                                backgroundColor: ServicesColor[res?._id],
                              }}
                            >
                              <Image
                                src={ServicesImg[res._id]}
                                width={15}
                                height={15}
                                className={"object-fit-cover rounded-2"}
                                alt=""
                              />
                            </div>
                            <p className="m-0">{ServicesTypeLabel[res._id]}</p>
                          </div>
                          <div className="w-100 text-start">{res.count}</div>
                          <div className="w-100 text-start">
                            <p className="m-0">{ServicesTypeLabel[res._id]}</p>
                          </div>

                          <div className="w-60 text-start">
                            {formatCurrency(res.totalAmount)}
                          </div>
                          <div className="w-10 text-center"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        }

        return (
          <div key={index} className="card p-3">
            <div className="d-flex align-items-center justify-content-between gap-2">
              <div
                className="d-flex align-items-center gap-2"
                style={{ width: window.innerWidth > 768 ? "100%" : "150%" }}
              >
                <Image
                  src={res.image}
                  style={{
                    filter: `contrast(${
                      theme === "dark" && res.text === "Collab" ? 0.3 : 1
                    })`,
                  }}
                  width={32}
                  height={32}
                  alt={res.text}
                />
                <p className="m-0 text-nowrap fw-medium">{res.text}</p>
              </div>
              <div className="w-100 text-start">{res.count}</div>
              <div className="w-100 text-start">
                {res.percentage.toFixed(2)}%
              </div>
              <div
                className="w-60 text-start"
                style={{ width: window.innerWidth > 768 ? "60%" : "120%" }}
              >
                <h5 className="fw-semibold m-0">
                  {formatCurrency(res.totalAmount)}
                </h5>
              </div>
              <div className="w-10 text-center"></div>
            </div>
          </div>
        );
      })}
    </>
  );
};

export default function Overview() {
  const initialState: OverviewState = {
    totalRequests: 0,
    totalEarnings: 0,
    total_earning_with_category: [],
    chat_options: [],
    total_earning_as_per_range: 0,
    total_earning_as_per_previous_range: 0,
    totalSubscribers: 0,
  };
  const [count, setCount] = useState<OverviewState>(initialState);
  const [isLoading, setIsLoading] = useState(true);

  const dateFilter = useAppSelector(
    (state) => state.user.creatorDashboardDateFilter
  );
  const filter = useAppSelector((state) => state.user.creatorDashboardFilter);

  useEffect(() => {
    if (window.bootstrap) {
      const tooltipTriggerList = document.querySelectorAll(
        '[data-bs-toggle="tooltip"]'
      );
      tooltipTriggerList.forEach((tooltipTriggerEl) => {
        new window.bootstrap.Tooltip(tooltipTriggerEl);
      });
    }
  }, [window.bootstrap]);

  useEffect(() => {
    setCount(initialState);
    setIsLoading(true);
    UserOverView(dateFilter, filter).then((res) => {
      setCount({
        totalEarnings: res.data?.total_earning,
        totalRequests: res.data.total_requests_count,
        totalSubscribers: res.data.total_subscribers,
        chat_options: res.data.chat_options,
        total_earning_with_category: res.data.total_earning_with_category,
        total_earning_as_per_range: res.data.total_earning_as_per_range,
        total_earning_as_per_previous_range:
          res.data.total_earning_as_per_previous_range,
      });
      setIsLoading(false);
    });
  }, [dateFilter]);

  return (
    <div className="container bg-body p-3  rounded-lg-3 rounded-0 px-0 px-lg-3">
      <div className="d-flex flex-column gap-2">
        <p className="fs-5 fw-semibold m-0 mx-lg-0 mx-3">Overview</p>
        {isLoading ? (
          <div className="d-flex align-items-center gap-3 mx-lg-0 mx-2">
            <div className="d-flex flex-column gap-2 flex-grow-1">
              <DashboardShimmer />
            </div>
            <div className="d-flex flex-column gap-2 flex-grow-1">
              <DashboardShimmer />
            </div>
          </div>
        ) : (
          <div className="d-flex align-items-center gap-3 mx-lg-0 mx-2">
            <div className="d-flex flex-column gap-2 p-3 flex-grow-1 common-border-box rounded-3">
              <div className="overview-title-wrapper d-flex align-items-center gap-2">
                <p className="fs-6 fw-medium mb-0">Total subscribers</p>
                <Image
                  src={"/images/svg/info.svg"}
                  width={20}
                  height={20}
                  alt="overview-info"
                  data-bs-toggle="tooltip"
                  className="pointer"
                  data-bs-placement="top"
                  data-bs-custom-class="custom-tooltip"
                  data-bs-title="The combined count of your channels & collabs"
                  data-bs-delay='{"show":400}'
                />
              </div>
              <h4 className="fw-medium">{count.totalSubscribers}</h4>
            </div>
            <div className="d-flex flex-column gap-2 p-3 flex-grow-1 common-border-box rounded-3">
              <div className="overview-title-wrapper d-flex align-items-center gap-2">
                <p className="fs-6 fw-medium mb-0">Total requests</p>
                <Image
                  src={"/images/svg/info.svg"}
                  width={20}
                  height={20}
                  alt="overview-info"
                  data-bs-toggle="tooltip"
                  className="pointer"
                  data-bs-placement="top"
                  data-bs-custom-class="custom-tooltip"
                  data-bs-title=" All the requests in chat like ratings, promotional, voice & video call "
                  data-bs-delay='{"show":400}'
                />
              </div>
              <h4 className="fw-medium">{count.totalRequests}</h4>
            </div>
          </div>
        )}

        <div className="d-flex align-items-center gap-2 mx-lg-0 mx-3">
          <h5 className="fw-semibold lh-1 m-0">Earnings</h5>
          <Image
            src={"/images/svg/info.svg"}
            width={20}
            height={20}
            alt="overview-info"
            data-bs-toggle="tooltip"
            className="pointer"
            data-bs-placement="top"
            data-bs-custom-class="custom-tooltip"
            data-bs-title="Your total earnings after platform fees"
            data-bs-delay='{"show":400}'
          />
        </div>
        {isLoading ? (
          <DashboardShimmerEarning />
        ) : (
          <>
            <div className="d-flex align-items-center gap-2 mx-lg-0 mx-3">
              {" "}
              <h2 className="fw-bold m-0 lh-1 mx-lg-0 mx-3">
                {formatCurrency(count.total_earning_as_per_range || 0)}
              </h2>
              {filter && (
                <>
                  {" "}
                  <p
                    className={classNames(
                      "m-0 py-1 px-2 rounded-3",
                      (count.total_earning_as_per_range || 0) -
                        (count.total_earning_as_per_previous_range || 0) >=
                        0
                        ? "color-green bg-light-green"
                        : "color-red bg-light-red"
                    )}
                  >
                    {(() => {
                      const difference =
                        (count.total_earning_as_per_range || 0) -
                        (count.total_earning_as_per_previous_range || 0);
                      return difference >= 0
                        ? `+${formatCurrency(difference)}`
                        : formatCurrency(difference);
                    })()}
                  </p>
                  <p className="m-0 fw-medium">vs {filter}</p>
                </>
              )}
            </div>
            <p className="mx-lg-0 fs-7 mx-3">
              Total Earnings : {formatCurrency(count.totalEarnings || 0)}
            </p>
          </>
        )}

        <div className="d-flex flex-column gap-2 overflow-auto">
          <div className="mt-3 earning-header-wrapper mx-lg-0 mx-3">
            <div className="d-flex align-items-center justify-content-between gap-2">
              <div
                className="d-flex  align-items-center gap-2"
                style={{ width: window.innerWidth > 768 ? "100%" : "150%" }}
              >
                <p className="m-0 text-nowrap fw-medium">Earning from</p>
              </div>
              <div className="w-100 text-start fw-medium">Sold</div>
              <div className="w-100 text-start fw-medium">Percentage</div>

              <div
                className=" text-start fw-medium"
                style={{ width: window.innerWidth > 768 ? "60%" : "120%" }}
              >
                Total revenue
              </div>
              <div className="w-10 text-center"></div>
            </div>
          </div>
          {isLoading ? (
            <>
              {" "}
              {Array.from({ length: 11 }).map((_, index) => (
                <div key={index}>
                  <DashboardShimmerOverview />
                </div>
              ))}
            </>
          ) : (
            <div className="earning-category-wrapper mx-lg-0 mx-3">
              <EarningCategoryData
                earningData={count.total_earning_with_category}
                servicesData={count.chat_options}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
