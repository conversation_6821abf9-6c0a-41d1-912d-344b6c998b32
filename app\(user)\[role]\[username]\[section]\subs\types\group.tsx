import classNames from "classnames";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useRef } from "react";

import { type APIGroup } from "@/api/group";
import ActionButton from "@/components/common/action-button";
import { SubscriptionButton } from "@/components/common/subscribed";
import { createActions, defaultActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { getAssetUrl } from "@/utils/assets";
import { checkVerification } from "@/utils/check-verification";
import { formatCurrency } from "@/utils/formatter";

export const AddNewBtn = ({ onClick }: { onClick: any }) => (
  <div
    className="d-flex gap-2 align-self-end align-items-center me-2 pointer"
    onClick={onClick}
  >
    <Image src={"/images/post/line-plus.svg"} alt={""} width={18} height={18} />
    <span>Add new Subscription</span>
  </div>
);

export const GroupSchema = ({
  data,
  uid,
  from,
}: {
  data: APIGroup;
  uid: string;
  from: "post" | "reel";
}) => {
  const user = useAppSelector((state) => state.user);

  const isOwner = user.id === uid;

  if (!data?.is_subscribed && data?.marked_as_deleted && !isOwner) return;
  return (
    <div className={from === "reel" ? "col-12" : `col-lg-6 col-12  `}>
      <Link
        className={`channel-view d-flex flex-column rounded-3 overflow-hidden shadow-dark  position-relative`}
        href={`/collab/${data?.tag_name}`}
        style={{
          filter:
            data.marked_as_deleted && !data?.is_subscribed && !isOwner
              ? "grayscale(1)"
              : "",
        }}
      >
        {data.members.some((member) => !member.terms_accepted) && (
          <span
            className="badge text-bg-secondary position-absolute end-0 fw-medium fs-6"
            style={{
              width: "fit-content",
              zIndex: 1,
              borderRadius: "0 8px",
            }}
          >
            Not active
          </span>
        )}
        <div
          className="channel-pic ratio "
          style={{ aspectRatio: "43.2/12.6" }}
        >
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            src={getAssetUrl({ media: data?.background?.[0] })}
            alt={"collab cover"}
            width={620}
            height={280}
            className="w-100 h-100 object-fit-cover"
          />
        </div>
        <div className="channel-details position-relative">
          <div className="group-profile-image flex-shrink-0 w-100 d-flex justify-content-center ">
            {data.members.slice(0, 4).map((member: any, index) => (
              <>
                <div key={member?.member_id?._id}>
                  {index === 0 ? (
                    // eslint-disable-next-line @next/next/no-img-element
                    <img
                      src={getAssetUrl({
                        media: member?.member_id?.avatar[0],
                        defaultType: "avatar",
                      })}
                      style={{ boxShadow: "0 0 0 2pt var(--bg-color)" }}
                      alt={"group image"}
                      className={`rounded-pill object-fit-cover first-image`}
                      width={from === "reel" ? 64 : 80}
                      height={from === "reel" ? 64 : 80}
                    />
                  ) : (
                    // eslint-disable-next-line @next/next/no-img-element
                    <img
                      src={getAssetUrl({
                        media: member.member_id?.avatar?.[0],
                        defaultType: "avatar",
                      })}
                      style={{ boxShadow: "0 0 0 2pt var(--bg-color)" }}
                      alt={"group image"}
                      className="rounded-pill object-fit-cover user-img"
                      width={from === "reel" ? 64 : 80}
                      height={from === "reel" ? 64 : 80}
                    />
                  )}
                </div>
              </>
            ))}
            {data.members.slice(4).length > 0 && (
              <div
                className="rounded-circle bg-cream"
                style={{
                  height: from === "reel" ? 64 : 80,
                  width: from === "reel" ? 64 : 80,
                  marginLeft: "-1.5rem",
                }}
              >
                <div className="bg-purple rounded-pill h-100 w-100 d-flex justify-content-center align-items-center text-white fw-bold">
                  <u>+{data.members.slice(4).length}</u>
                </div>
              </div>
            )}
          </div>
          <div className={`d-flex  w-100 ${from === "reel" ? "pt-1" : "pt-5"}`}>
            <div
              className={`d-flex w-100  flex-column justify-content-center align-items-center  gap-1 ${
                from === "reel" ? "text-white" : ""
              }`}
            >
              <div className="d-flex align-items-center gap-2 mt-3">
                <span
                  className={` fw-semibold ${
                    from === "reel" ? "fs-6" : "fs-4"
                  }`}
                >
                  {data.name}
                </span>
              </div>
              <div className="d-flex flex-wrap column-gap-3 overflow-hidden row-gap-0 color-medium"></div>
              <div
                className={` d-flex align-items-center fs-7  gap-2 ${
                  from === "reel" ? "color-grey fs-8" : ""
                }`}
              >
                <span>@{data.tag_name}</span>{" "}
                <span className="group-dot"></span> <span>Collab</span>
              </div>
              <div className={` ${from === "reel" ? "color-grey fs-8" : ""}`}>
                {data.members
                  .slice(0, 4)
                  .map((member: any) => `@${member?.member_id?.username}`)
                  .join(", ")}
                {data.members.slice(4).length > 0 &&
                  ` & +${data.members.slice(4).length} members`}
              </div>
              <div
                className={`d-flex align-items-center gap-2 fw-semibold mb-2 ${
                  from === "reel" ? "text-white fs-7" : ""
                }`}
              >
                <span>
                  {user.profile.username === data.author?.username
                    ? data.counter?.post_count
                    : data.counter &&
                      data?.counter?.post_count -
                        data?.counter?.private_post_count}{" "}
                  Posts
                </span>{" "}
                <span
                  className={classNames("group-dot", {
                    "d-none": !data.members.some(
                      (m) => m?.member_id?._id === user.id
                    ),
                  })}
                ></span>
                <span
                  className={classNames({
                    "d-none": !data.members.some(
                      (m) => m?.member_id?._id === user.id
                    ),
                  })}
                >
                  {data.counter?.subscriber_count} Subscribers
                </span>
              </div>

              {!isOwner && (
                <div
                  className={classNames("w-100", {
                    invisible: data.members.some(
                      (m) => m?.member_id?._id === user.id
                    ),
                  })}
                >
                  {data?.is_subscribed ? (
                    <div className={classNames("mt-2 w-100")}>
                      <SubscriptionButton
                        planPrice={
                          data?.my_subscription_data?.other_data?.price
                        }
                        planExpiry={data?.my_subscription_data?.expires_on}
                        is_cancelled={data?.my_subscription_data?.is_cancelled}
                        planType={
                          data?.my_subscription_data.other_data?.package_id
                            ?.validity_type
                        }
                      />
                    </div>
                  ) : (
                    <div className="px-3 pb-3">
                      {!data.marked_as_deleted ? (
                        <ActionButton
                          className={
                            "d-flex justify-content-between w-100 mt-2" +
                            (data.members?.some(
                              (member) => member?.member_id?._id === user.id
                            )
                              ? "invisible"
                              : "")
                          }
                        >
                          {!data.marked_as_deleted && (
                            <p className="m-0">Subscribe</p>
                          )}
                          {!data.marked_as_deleted && (
                            <p className="m-0">
                              {data?.subscription?.[0]?.price > 0
                                ? ` ${formatCurrency(
                                    data?.subscription?.[0]?.price
                                  )} / Month`
                                : " for Free"}
                            </p>
                          )}
                        </ActionButton>
                      ) : (
                        <ActionButton
                          className="fs-6 fw-500   w-100 pe-none my-1"
                          disabled={true}
                        >
                          Marked as deleted
                        </ActionButton>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
};

const GroupList = ({
  uid,
  list,
  from,
}: {
  uid: string;
  list: APIGroup[];
  from: "post" | "reel";
}) => {
  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );
  const user = useAppSelector((state) => state.user);
  const dispatchAction = useAppDispatch();
  const router = useRouter();
  const isOwner = user.id === uid;

  const collabRef = useRef<HTMLDivElement>(null);

  const addNewGroup = async () => {
    const verified = await checkVerification("create collab");

    if (verified) {
      dispatchAction(createActions.resetGroup());
      dispatchAction(defaultActions.resetGroupProfile());
      router.push("/collab/create");
    }
  };

  if (!list?.length && !isOwner)
    return (
      <div className="bg-body p-3 rounded-3 text-center">
        No collab profiles yet!
      </div>
    );

  return (
    <div
      ref={collabRef}
      className={`d-flex flex-column gap-3 p-3 rounded-3 ${
        from === "reel" ? "bg-reel" : " bg-body"
      }`}
    >
      {isOwner ? <AddNewBtn onClick={addNewGroup} /> : <></>}
      <div className="container-fluid g-0 g-md-4 g-lg-0 my-lg-0 my-md-4 ">
        <div className="row g-3">
          {list?.length > 0 ? (
            list.map((group) => (
              <GroupSchema
                key={group?._id}
                data={group}
                uid={uid}
                from={from}
              />
            ))
          ) : (
            <div className="bg-body rounded-3 text-center">
              No collab profiles yet!
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GroupList;
