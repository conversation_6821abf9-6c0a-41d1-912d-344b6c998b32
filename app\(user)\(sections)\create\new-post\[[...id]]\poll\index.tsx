import Image from "next/image";
import React, { memo, useEffect, useState } from "react";

import { SelectPoll } from "@/api/post";
import { ModalService } from "@/components/modals";
import { userDataActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { PostPoll } from "@/types/post";
import "./index.scss";

interface Poll {
  removePoll?: Function;
  setPollBox?: Function;
  postPoll?: PostPoll;
  showElement?: true | false;
  details?: {
    _id: string;
    author: {
      _id: string;
    };
  };
}

function PollComponent(pollProps: Poll) {
  const [active, setActive] = useState(false);
  const [disabled, setDisabled] = useState(false);
  const [hideElement, _setHideElement] = useState(pollProps.showElement);
  const userId = useAppSelector((state) => state.user.id);
  const [hideOptions, _setHideOptions] = useState(false);
  const dispatchAction = useAppDispatch();
  const pollBox = useAppSelector((state) => state.userData?.post?.poll);
  const role = useAppSelector((state) => state.user.role);

  const user = useAppSelector((state) => state.user.profile);

  const [poll, setPoll] = useState(
    (pollProps.postPoll && pollProps.postPoll) || pollBox
  );
  const [votes, setVotes] = useState(0);
  const [tempVote, setTempVote] = useState(
    Array.from(
      { length: pollProps.postPoll?.answer_choices.length || 0 },
      () => 0
    )
  );

  useEffect(() => {
    if (!pollProps.postPoll) {
      dispatchAction(userDataActions.setPostPoll(poll));
    }
  }, [dispatchAction, poll]);

  useEffect(() => {
    const totalVotes = pollProps.postPoll?.answer_choices.reduce(
      (total, choice) => total + choice.votes.length,
      0
    );
    totalVotes && setVotes(totalVotes);
  }, [pollProps.postPoll]);
  useEffect(() => {
    const hasUserVoted = poll.answer_choices.some((choice) =>
      choice.votes.includes(user?._id)
    );

    if (hasUserVoted || userId === pollProps.details?.author._id) {
      setDisabled(true);
    }
  }, []);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    setActive(true);

    if (event.key === "Enter") {
      const newOption = event.currentTarget.value;

      if (poll.answer_choices.length < 4) {
        const updatedAnswerChoices = [
          ...poll.answer_choices,
          { option: newOption, votes: [] },
        ];
        setPoll({
          ...poll,
          answer_choices: updatedAnswerChoices,
        });
        event.currentTarget.value = "";
      } else {
        console.error("Maximum number of options reached (5 options allowed).");
      }
    }
  };

  const handeleDoneBtbClick = () => {
    dispatchAction(userDataActions.setPostPoll(poll));
    pollProps.setPollBox && pollProps.setPollBox(poll);
    setActive(false);
  };

  const removeOption = (indexToRemove: number) => {
    if (poll.answer_choices.length > 2) {
      const updatedAnswerChoices = poll.answer_choices.filter(
        (_: any, index: number) => index !== indexToRemove
      );
      setPoll({
        ...poll,
        answer_choices: updatedAnswerChoices,
      });
      setActive(true);
    }
  };

  const selectPoll = (index: number) => {
    pollProps.details &&
      pollProps.postPoll &&
      SelectPoll(
        pollProps.details?._id,
        pollProps.postPoll?.answer_choices[index]?._id
      ).catch((err) => console.error(err));
  };

  const isUserVoted = (index: number): boolean => {
    return poll.answer_choices[index].votes.includes(user._id);
  };

  const openModal = () => {
    ModalService.open("SIGN_IN");
  };

  const isMobile = window.innerWidth < 768;
  const length = poll.question.length;

  const rows = () => {
    if (isMobile) {
      if (length < 40) return 1;
      else if (length < 80) return 2;
      else if (length < 120) return 3;
      else return 4;
    } else {
      if (length < 120) return 1;
      else if (length < 240) return 2;
      else if (length < 360) return 3;
      else return 4;
    }
  };

  return (
    <div
      className="container m-0"
      onClick={() => role === "guest" && openModal()}
    >
      <div className="d-flex poll-box bg-cream rounded-2 shadow-dark">
        <div className="d-inline-flex w-100 flex-column">
          <div className="input-group d-flex align-items-center justify-content-between p-1 border-bottom">
            <textarea
              className="fs-6 fw-medium mb-0 border-0 form-control bg-transparent shadow-none color-dark"
              placeholder="Question"
              defaultValue={poll.question}
              style={{ resize: "none" }}
              rows={rows()}
              onChange={(e) => {
                if (e.target.value === "") {
                  setActive(false);
                }

                setActive(true);
                setPoll({
                  ...poll,
                  question: e.target.value,
                });
              }}
              disabled={pollProps.postPoll ? true : false}
            />
            {!pollProps.postPoll && (
              <>
                {hideElement && (
                  <>
                    <Image
                      onClick={() =>
                        pollProps.removePoll && pollProps.removePoll()
                      }
                      src={"/images/svg/nav-close.svg"}
                      width={25}
                      height={25}
                      className="invert me-2 svg-icon"
                      alt="remove-question"
                    />
                  </>
                )}
              </>
            )}
          </div>
          <div className="poll-answer-wrapper bg-body d-flex flex-column p-3 gap-3 border-top rounded-bottom">
            {poll.answer_choices.map(
              (
                answer: { option: string; votes: string[]; _id?: string },
                index: number
              ) => (
                <div
                  className="input-group-sm d-flex align-items-center justify-content-between"
                  key={index}
                >
                  {pollProps.postPoll &&
                    (pollProps.postPoll?.answer_choice_type === "multiple" ? (
                      <div className="user-group-wrapper form-check">
                        <input
                          className="form-check-input shadow-dark poll-answers"
                          type="checkbox"
                          value=""
                          id="flexCheckDefault"
                          onChange={() => setVotes(votes + 1)}
                          disabled={role == "guest"}
                        />
                      </div>
                    ) : (
                      <div className="user-group-wrapper form-check">
                        <input
                          className="form-check-input shadow-dark"
                          type="radio"
                          value=""
                          name={pollProps.details?._id}
                          id={`radio`}
                          onChange={() => {
                            if (!isUserVoted(index)) {
                              selectPoll(index);
                              const updatedTempVote = [...tempVote];
                              updatedTempVote.splice(index, 1, 1);
                              setTempVote(updatedTempVote);
                              setVotes(votes + 1);
                              setDisabled(true);
                            }
                          }}
                          checked={isUserVoted(index) || tempVote[index] === 1}
                          disabled={
                            poll.answer_choices.some((choice) =>
                              choice.votes.includes(user?._id)
                            ) ||
                            disabled ||
                            role == "guest"
                          }
                        />
                      </div>
                    ))}

                  <div className="position-relative w-100">
                    <input
                      className={
                        "fs-6 fw-medium mb-0 form-control bg-transparent shadow-none color-dark text-white"
                      }
                      style={{
                        zIndex: 1,
                        borderColor:
                          isUserVoted(index) || tempVote[index] === 1
                            ? "#ac1991"
                            : "",
                      }}
                      ref={(el) =>
                        el &&
                        (isUserVoted(index) || tempVote[index] === 1) &&
                        el.style.setProperty("color", "#ac1991", "important")
                      }
                      placeholder={`Option ${index + 1}`}
                      value={answer.option}
                      onChange={(e) => {
                        if (e.target.value === "") {
                          setActive(false);
                        }

                        const updatedAnswerChoices = [...poll.answer_choices];
                        updatedAnswerChoices[index] = {
                          option: e.target.value,
                          votes: [],
                        };
                        setPoll({
                          ...poll,
                          answer_choices: updatedAnswerChoices,
                        });
                        setActive(true);
                      }}
                      disabled={pollProps.postPoll ? true : false}
                    />
                    {disabled && (
                      <div
                        className={`${
                          isUserVoted(index) || tempVote[index] === 1
                            ? "vote-fill-selected"
                            : "vote-fill-unselected"
                        } rounded`}
                        style={{
                          width: `${
                            answer.votes.length !== 0
                              ? (
                                  ((answer.votes.length + tempVote[index]) /
                                    votes) *
                                  100
                                ).toFixed(0)
                              : tempVote[index] === 1
                              ? ((tempVote[index] / votes) * 100).toFixed(0)
                              : 0
                          }%`,
                        }}
                      />
                    )}
                  </div>

                  {disabled && (
                    <span className="fs-6 fw-medium text-nowrap ps-2">{`${
                      answer.votes.length !== 0
                        ? (
                            ((answer.votes.length + tempVote[index]) / votes) *
                            100
                          ).toFixed(0)
                        : tempVote[index] === 1
                        ? ((tempVote[index] / votes) * 100).toFixed(0)
                        : 0
                    }%`}</span>
                  )}

                  {!pollProps.postPoll && (
                    <>
                      {hideElement && (
                        <>
                          {index !== 0 && index !== 1 && (
                            <Image
                              src={"/images/svg/activepoll.svg"}
                              width={27}
                              height={27}
                              className="invert ms-1"
                              alt="remove-question"
                              onClick={() => removeOption(index)}
                            />
                          )}
                        </>
                      )}
                    </>
                  )}
                </div>
              )
            )}
            {!pollProps.postPoll && (
              <>
                {hideElement && (
                  <>
                    {!hideOptions && (
                      <div className="input-group-sm d-flex align-items-center justify-content-between">
                        <input
                          className="fs-6 fw-medium mb-0 form-control bg-transparent shadow-none color-dark"
                          placeholder="Add an option"
                          onKeyDown={handleKeyDown}
                        />
                        <Image
                          src={"/images/svg/inactivepoll.svg"}
                          width={27}
                          height={27}
                          className="invert ms-1 "
                          alt="remove-question"
                        />
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
          {!pollProps.postPoll ? (
            <>
              {hideElement && (
                <>
                  {/* <div className="poll-btn-wrapper d-flex justify-content-between align-items-center ps-3 pe-3 pb-2 flex-wrap">
                    <div className="user-group-wrapper form-check">
                      <input
                        className="form-check-input shadow-dark"
                        type="checkbox"
                        value=""
                        id="flexCheckDefault"
                        onChange={() => {
                          setActive(true);
                          if (poll.answer_choice_type === "single") {
                            setPoll({
                              ...poll,
                              answer_choice_type: "multiple",
                            });
                          } else {
                            setPoll({
                              ...poll,
                              answer_choice_type: "single",
                            });
                          }
                        }}
                        checked={poll.answer_choice_type === "multiple"}
                      />
                      <label
                        className="form-check-label"
                        htmlFor="flexCheckDefault"
                      >
                        Multichoice
                      </label>
                    </div>
                    <div className="user-group-wrapper form-check form-switch ">
                      <label
                        className="form-check-label"
                        htmlFor="flexSwitchCheckDefault"
                      >
                        Anonymous vote
                      </label>
                      <input
                        className="form-check-input shadow-dark"
                        type="checkbox"
                        role="switch"
                        id="flexSwitchCheckDefault"
                      />
                    </div>
                  </div> */}
                </>
              )}
            </>
          ) : (
            <div className="d-flex bg-body votes-row justify-content-end align-items-center px-3 pb-2 rounded-bottom">
              <span className="fs-6 fw-medium">{`${votes} votes`}</span>
            </div>
          )}
        </div>
      </div>
      {active &&
        poll.question &&
        poll.answer_choices[0].option &&
        poll.answer_choices[1].option && (
          <div className="d-flex done-btn align-items-center justify-content-end pt-3">
            <button
              className="btn btn-purple"
              onClick={() => {
                handeleDoneBtbClick();
              }}
            >
              Done
            </button>
          </div>
        )}
    </div>
  );
}

export const PollBox = memo(PollComponent);
