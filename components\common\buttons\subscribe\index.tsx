"use client";

import { usePathname, useRouter } from "next/navigation";

import { defaultActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

import ActionButton from "../../action-button";

interface Subscribe {
  visible: boolean;
  cls?: string;
  postSource: any;
}

const Subscribe = ({ cls = "", visible, postSource }: Subscribe) => {
  const router = useRouter();
  const isGuest = useAppSelector((state) => state?.user?.role) === "guest";
  const path = usePathname();
  const dispatch = useAppDispatch();
  const subscriptionPost: boolean =
    postSource?.postFrom === "Channel" || postSource?.postFrom === "Group";

  const onClick = () => {
    if (path.includes("/channel") || path.includes("/collab")) {
      const element = document.querySelector(".profile-view");
      element?.scrollIntoView({
        behavior: "smooth",
      });
      dispatch(defaultActions.setBlinkOffers(true));
      setTimeout(() => {
        dispatch(defaultActions.setBlinkOffers(false));
      }, 5000);
      return;
    }

    !isGuest && postSource?.postFrom == "Channel"
      ? router.push(`/channel/${postSource?.id}`)
      : postSource?.postFrom == "Group"
      ? router.push(`/collab/${postSource?.id}`)
      : router.push(
          `/${postSource?.role}/${postSource?.id}/subs?type=channels`
        );
  };

  if (!visible) return <></>;

  return (
    <ActionButton
      type={cls.includes("active") ? "outline" : "solid"}
      variant={cls.includes("active") ? "dark" : "primary"}
      onClick={onClick}
      className={`w-100 ${cls}`}
    >
      {cls.includes("active")
        ? "Subscribed"
        : subscriptionPost
        ? "Subscribe"
        : "View Subs"}
    </ActionButton>
  );
};

export default Subscribe;
