/* eslint-disable @next/next/no-img-element */

import classNames from "classnames";
import { formatDistance } from "date-fns";
import { useEffect, useMemo, useRef, useState } from "react";

import { GetSinglePost } from "@/api/post";
import PostActionSendTip from "@/components/common/buttons/send-tip";
import PostActionShare from "@/components/common/buttons/share";
import { LiveReactionsBtn } from "@/components/common/event/reactions";
import PostAuthor from "@/components/common/post/author";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Post } from "@/types/post";
import { getAssetUrl } from "@/utils/assets";
import socketChannelInstance from "@/utils/chatSocket";

import Badges from "../../badges";
import ServicesBtn from "../chatbar/helpers/ServicesBtn";
import { MsgOptions, type MsgOptionsRef } from "./MsgOptions";
import styles from "./index.module.scss";

export default function LiveStreamChat(props: {
  postId: string;
  iAmOwner?: boolean;
}) {
  const dispatch = useAppDispatch();
  const avatar = useAppSelector((state) => state.user.profile.avatar[0]);
  const badges = useAppSelector((state) => state.user.profile.badges);
  const streamMessages = useAppSelector((state) => state.chat.streamMessages);
  const [post, setPost] = useState<Post | null>(null);
  const commentRef = useRef<HTMLDivElement | null>(null);
  const [_count, setCount] = useState(0);
  const msgOptionsRef = useRef<MsgOptionsRef>(null);

  const myAvatar = useMemo(
    () =>
      getAssetUrl({
        media: avatar,
        defaultType: "avatar",
        variation: "thumb",
      }),
    [avatar]
  );
  const myAvatarFileName = useMemo(() => myAvatar.split("/").pop(), [avatar]);

  const scrollIntoView = () => {
    commentRef.current?.scrollIntoView({
      behavior: "smooth",
      block: "end",
      inline: "nearest",
    });
  };

  useEffect(() => {
    scrollIntoView();
  }, [streamMessages]);

  useEffect(() => {
    (async () => {
      const _post: Post = (await GetSinglePost(props.postId)).data[0];
      setPost(_post);

      // attach to the socket channel
      // TODO: channel should not reconnect on refresh
      await socketChannelInstance.updateChannel(
        _post.event.event_converse_channel_id
      );

      const res = await socketChannelInstance.getNextBucketMessages();

      if (Array.isArray(res?.msgs?.read)) {
        dispatch(chatActions.clearStreamChat());
        dispatch(
          chatActions.setPrevStreamChatMsgs([
            ...(res?.msgs.read || []),
            ...(res?.msgs.unread || []),
          ])
        );
      }
    })();

    return () => socketChannelInstance.closeChannel();
  }, []);

  useEffect(() => {
    const loopRef = setInterval(() => setCount((c) => c + 1), 60000);
    return () => loopRef && clearInterval(loopRef);
  }, []);

  const isRecentMessage = (createdAt: string) => {
    const timeDifferenceInSeconds = Math.abs(
      (new Date().getTime() - new Date(createdAt).getTime()) / 1000
    );
    return timeDifferenceInSeconds < 60;
  };

  const addStreamComment = (e: any) => {
    socketChannelInstance.channel?.sendMessage({
      message: e.target!.value,
      meta: { avatar: avatar && myAvatarFileName, badges, type: "stream" },
    });
    e.target!.value = "";
  };

  // TODO: fetch older messages on scroll up

  return (
    <div
      className={classNames(
        styles["stream-chat"],
        "card rounded-0 rounded-md-3"
      )}
    >
      <div className="card-body p-4 py-3">
        <div
          className={classNames(
            styles["post-head"],
            "d-flex px-3 pt-3 pb-1 justify-content-between align-items-center"
          )}
        >
          {post && (
            <PostAuthor
              author={post.author}
              shared_on_profile={post.shared_on_profile}
              // @ts-expect-error to be handled later
              ts={new Date(post.created_at).getTime()}
              // @ts-expect-error to be handled later
              type={post.visibility}
              post_type={post.type}
            />
          )}
        </div>

        <div className="mt-2 fw-medium fs-5">{post?.event.name}</div>
        <div className="mb-2">{post?.event.description}</div>
        <div
          className={classNames(
            "d-flex align-items-center flex-grow-1",
            props.iAmOwner
              ? "justify-content-center"
              : "justify-content-between"
          )}
        >
          {!props.iAmOwner && <LiveReactionsBtn />}

          <PostActionShare
            postId={props.postId}
            shareText={`Checkout the knky event by ${post?.author.display_name}\n`}
          />

          {!props.iAmOwner && post && (
            <ServicesBtn
              target={{
                target_user_display_name: post.author.display_name,
                target_user_id: post.author._id,
              }}
            />
          )}

          {!props.iAmOwner && post && (
            <PostActionSendTip
              postId={post.event._id}
              author={post.author}
              type="event"
            />
          )}
        </div>

        <div className="comments">
          <div className={classNames(styles["stream-comments"], "mt-3")}>
            {streamMessages?.map((streamMessage, index: number) => (
              <div
                key={index}
                className="comment w-100 d-flex align-items-center mb-2 gap-2"
              >
                <img
                  src={
                    streamMessage?.meta?.avatar
                      ? process.env.public_asset +
                        "/users/" +
                        streamMessage?.meta?.avatar
                      : "/images/common/default.svg"
                  }
                  className="img-fluid rounded-circle"
                  width={40}
                  height={40}
                  alt="profile pic"
                />
                <div className="bg-light w-100 d-flex flex-column p-2 rounded text-break">
                  <div className="d-flex align-items-center">
                    <span className="fw-semibold">{streamMessage.name}</span>
                    <Badges array={streamMessage.meta.badges} cls="ms-1" />
                    {(streamMessage.sid || streamMessage.sender_id) ===
                      post?.author._id && (
                      <span className="color-primary ms-2">Owner</span>
                    )}
                    <span className="ms-auto" style={{ color: "#4D5053" }}>
                      {isRecentMessage(streamMessage.createdAt)
                        ? "Just now"
                        : formatDistance(
                            new Date(streamMessage.createdAt),
                            new Date(),
                            { addSuffix: true }
                          )}
                    </span>
                  </div>
                  <div className="d-flex">
                    <span>{streamMessage.message}</span>
                    {props.iAmOwner && (
                      <img
                        src="/images/post/line-more.svg"
                        alt="options"
                        height={28}
                        className={classNames("ms-auto pointer svg-icon", {
                          "d-none":
                            (streamMessage.sid || streamMessage.sender_id) ===
                            post?.author._id,
                        })}
                        onClick={(e) =>
                          msgOptionsRef.current?.show(e, {
                            sid: streamMessage.sid || streamMessage.sender_id,
                            name: streamMessage.name,
                          })
                        }
                        style={{ marginTop: "-4px" }}
                      />
                    )}
                  </div>
                </div>
              </div>
            ))}
            <div className="scroll-ref" ref={commentRef}></div>
          </div>

          <div className="comment-box d-flex border-top mt-3 gap-2 py-2">
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={myAvatar}
              className="img-fluid rounded-circle border"
              width={40}
              alt="profile pic"
            />

            <input
              className="form-control"
              onKeyDown={(e: any) =>
                e.key === "Enter" ? addStreamComment(e) : null
              }
              type="text"
              placeholder="Add a comment..."
            />
          </div>

          <MsgOptions ref={msgOptionsRef} />
        </div>
      </div>
    </div>
  );
}
