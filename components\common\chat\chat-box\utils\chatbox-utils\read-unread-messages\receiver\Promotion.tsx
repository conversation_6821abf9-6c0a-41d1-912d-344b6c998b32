import classNames from "classnames";
import { memo, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import { respondReq } from "@/api/chat";
import { GetUserProfileById } from "@/api/user";
import { PaymentMethod } from "@/components/common/payment-method";
import {
  AudioSent,
  VideoSent,
} from "@/components/matchmaker/match-cards/utils/svg";
import { useRegisterModal } from "@/hooks/useRegisterModal";
import { chatActions, configActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import { fetchWalletBalance } from "@/redux-store/slices/wallet.slice";
import type { UserProfile } from "@/types/user";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";

import ReadMoreContent from "../../../../read-more-content";
import { RatingSent } from "../../rating-request";
import PromotionActionButtons from "../../show-promotion-modal-btns";

const PromotionReceiver = ({
  message,
  receiver,
  setCallReqId,
}: {
  message: MessageInterface;
  showBuyTicketModal: boolean;
  buyTicketModalRef: any;
  receiver: any;
  setCallReqId: (value: string) => void;
  setShowBuyTicketModal: (value: boolean) => void;
  setReceiver: (value: any) => void;
  ratingReqref: any;
}) => {
  const isRequestSent = message?.meta?.requestAccept === "sent";

  return (
    <div className="receiver d-grid h-100 mt-1 ms-auto w-100 px-2 mt-2 rounded-2 text-break ">
      <div className="d-flex flex-row-reverse position-absolute top-0 end-0 m-2">
        {!isRequestSent && (
          <div
            className={classNames("badge p-2", {
              "fs-10": window.innerWidth < 576,
            })}
            style={{
              color:
                message?.meta?.requestAccept === false
                  ? "rgba(245, 34, 45, 1)"
                  : "rgba(86, 194, 45, 1)",
              border: `1px solid ${
                message?.meta?.requestAccept === false
                  ? "rgba(245, 34, 45, 1)"
                  : "rgba(86, 194, 45, 1)"
              }`,
              backgroundColor:
                message?.meta?.requestAccept === false
                  ? "rgba(245, 34, 45, 0.2)"
                  : "rgba(86, 194, 45, 0.2)",
            }}
          >
            {message?.meta?.requestAccept === false ? "Declined" : "Accepted"}
          </div>
        )}
        {isRequestSent && (
          <div
            className={classNames("badge p-2", {
              "fs-10": window.innerWidth < 576,
            })}
            style={{
              color: "rgba(136, 77, 255, 1)",
              border: "1px solid rgba(136, 77, 255, 1)",
              backgroundColor: "rgba(136, 77, 255, 0.12)",
            }}
          >
            New
          </div>
        )}
      </div>

      <div className="d-flex align-items-center gap-2">
        <div>
          {message?.meta?.type.toLowerCase() === "voice" ? (
            message?.meta?.request_icon &&
            message?.meta?.request_icon?.length > 0 ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={
                  message?.meta?.request_icon?.length! > 0
                    ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                    : ""
                }
                height={72}
                width={72}
                alt="Request Icon"
                className="rounded"
              />
            ) : (
              <AudioSent />
            )
          ) : message?.meta?.type.toLowerCase() === "video" ? (
            message?.meta?.request_icon &&
            message?.meta?.request_icon?.length > 0 ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={
                  message?.meta?.request_icon?.length! > 0
                    ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                    : ""
                }
                height={72}
                width={72}
                alt="Request Icon"
                className="rounded"
              />
            ) : (
              <VideoSent />
            )
          ) : message?.meta?.type === "CUSTOM-SERVICE" ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={
                message?.meta?.request_icon?.length! > 0
                  ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                  : "/images/chat/service-icon.png"
              }
              height={72}
              width={72}
              alt="Request Icon"
              className="rounded"
            />
          ) : message?.meta?.request_icon &&
            message?.meta?.request_icon?.length > 0 ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={
                message?.meta?.request_icon?.length! > 0
                  ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                  : ""
              }
              height={72}
              width={72}
              alt="Request Icon"
              className="rounded"
            />
          ) : (
            <RatingSent />
          )}
        </div>
        <div>
          <div className="fw-bold">
            {message?.meta?.type.toLowerCase() === "voice"
              ? "Voice call"
              : message?.meta?.type.toLowerCase() === "video"
              ? message?.meta?.type === "CUSTOM-SERVICE"
                ? "Request"
                : "Video call"
              : "Ratings"}
          </div>
          <div className="d-flex gap-2">
            <div>
              {["VOICE", "VIDEO"].includes(message?.meta?.type)
                ? `For ${
                    Math.ceil((message?.meta?.duration || 0) / 60) >= 10
                      ? Math.ceil((message?.meta?.duration || 0) / 60)
                      : `${Math.ceil((message?.meta?.duration || 0) / 60)}`
                  } mins`
                : "1 turn"}
            </div>
            <div className="vr"></div>
            <div>
              {formatCurrency(
                (() => {
                  const price = message?.meta?.price;
                  const hasDiscount = message?.meta?.has_discount;
                  const discount = message?.meta?.discount;

                  if (message?.meta?.offered_amount)
                    return message?.meta?.offered_amount;

                  if (
                    price !== undefined &&
                    hasDiscount &&
                    discount?.discount_value !== undefined
                  ) {
                    return discount?.discount_value;
                  }

                  return price ?? 0;
                })()
              )}
            </div>
          </div>
        </div>
      </div>
      <ReadMoreContent
        text={`${message?.meta?.request_note || ""}`}
        textColor="black"
        classes="mt-1 fw-bold"
      />
      <div className="d-flex mt-2">
        {message?.meta?.requestAccept === "sent" ? (
          <PromotionActionButtons
            message={message}
            receiver={receiver}
            setCallReqId={setCallReqId}
          />
        ) : null}
      </div>
    </div>
  );
};

export default PromotionReceiver;

function BuyTicketModalChat() {
  const dispatch = useAppDispatch();

  const modalRef = useRef<any>(null);

  const [misc, setMisc] = useState<any>({});
  const [sender, setSender] = useState<UserProfile>();

  useEffect(() => {
    if (window.bootstrap) {
      const initializeModal = () => {
        const myModal = new window.bootstrap.Modal(
          modalRef.current as HTMLElement,
          {
            backdrop: "static",
            keyboard: false,
          }
        );
        modalRef.current!.modalInstance = myModal;
      };

      setTimeout(initializeModal, 300);
    }
  }, [window.bootstrap]);

  const onCloseModal = () => {
    modalRef?.current?.modalInstance?.hide();
  };

  const setReceiver = async (data: any) => {
    return GetUserProfileById(data?.sender_id || "");
  };

  useEffect(() => {
    setReceiver({ sender_id: misc?.sender_id }).then((res) => {
      res && setSender(res.data?.[0]);
    });
  }, [misc?.sender_id]);

  useRegisterModal(
    "BUY_PROMOTION",
    async (data: any) => {
      modalRef.current?.modalInstance?.show();
      const sender = await setReceiver(data);
      setMisc({
        ...data,
        ...sender,
      });
    },
    () => {
      modalRef?.current?.modalInstance?.hide();
    }
  );

  const onSubmit = () => {
    Swal.fire({
      title: "Do you want to proceed with this payment?",
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "No",
      confirmButtonColor: "#AC1991",
      confirmButtonText: "Yes",

      showCloseButton: true,
    }).then((result) => {
      if (result.isConfirmed) {
        dispatch(configActions.toggleCardProcessing());
        respondReq(misc?.requestId, {
          status: 1,
          ...(misc?.offered_amount && {
            offered_amount: misc?.offered_amount,
          }),
        })
          .then(() => {
            dispatch(fetchWalletBalance());
            Swal.fire({
              icon: "success",
              title: `You have successfully accepted for ${
                sender?.display_name || ""
              }'s promotion.`,
              confirmButtonText: "Finish",
              confirmButtonColor: "#AC1991",

              showCloseButton: true,
            }).then(async () => {
              dispatch(configActions.toggleCardProcessing());
              dispatch(chatActions.setFocusedReq(null));
              await misc?.onClick();
              modalRef?.current?.modalInstance?.hide();
            });
          })
          .catch(() => {
            dispatch(configActions.toggleCardProcessing());
            Swal.fire({
              icon: "error",
              title: "Error! Please try again later.",
              confirmButtonText: "Finish",
              confirmButtonColor: "#AC1991",

              showCloseButton: true,
            });
          });
      }
    });
    return;
  };

  const chatPrice = (
    price: number,
    offered_amount?: number,
    discount?: {
      discount_value?: number;
    }
  ) => {
    return offered_amount
      ? offered_amount
      : discount?.discount_value
      ? discount?.discount_value
      : price;
  };

  return (
    <div
      className="modal fade"
      id={"buyTicketModal"}
      tabIndex={-1}
      ref={modalRef}
      aria-labelledby={`buyTicketModal-label`}
      aria-hidden="true"
    >
      <div className="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id={`buyTicketModal-label`}>
              {"Buy Promotion"}
            </h5>
            <button
              type="button"
              className="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              id="buyPromotionCloseBtn"
              onClick={onCloseModal}
            ></button>
          </div>
          <div className="modal-body">
            <div className="tip-wrapper">
              <div className="d-flex justify-content-center flex-column align-items-center">
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src={getAssetUrl({ media: sender?.avatar?.[0] })}
                  alt=""
                  width={100}
                  height={100}
                  className="rounded-circle mb-3"
                />
                <h4 className="d-flex gap-2 align-items-center m-0">
                  {`${sender?.display_name || ""} `}
                </h4>
                <p className="color-medium fs-7 m-0">@{sender?.username}</p>
              </div>
              <PaymentMethod
                amount={chatPrice(
                  misc?.price,
                  misc?.offered_amount,
                  misc?.discount
                )}
                onClose={onCloseModal}
                onSubmit={onSubmit}
                type={"PromotionChat"}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const BuyPromotionModal = memo(BuyTicketModalChat);
export { BuyPromotionModal };
