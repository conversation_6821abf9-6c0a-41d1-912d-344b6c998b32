type routes =
  | "POST"
  | "STORY"
  | "CHANNEL"
  | "GROUP"
  | "USER_LOGIN"
  | "RENEW_TOKEN"
  | "USER_PROFILE"
  | "USER_REGISTER"
  | "USERNAME_AVAILABLE"
  | "USER_COMPLETE_PROFILE"
  | "USER_FEATURED_PROFILE"
  | "USER_EDIT_PROFILE"
  | "USER_FOLLOWERS"
  | "USER_FOLLOWING"
  | "USER_FOLLOW"
  | "USER_UN_FOLLOW"
  | "BECOME_A_CREATOR"
  | "REPORT"
  | "REQUESTS"
  | "CHAT_ACTIVITY"
  | "UPDATE_ACCOUNT_INFO"
  | "USER_PRIVACY_SETTING"
  | "SPECIAL_OPTIONS"
  | "USERS"
  | "NOTIFICATION"
  | "SEARCH_USER"
  | "SUBSCRIPTIONS"
  | "WISHLIST"
  | "HASHTAGS"
  | "TAGS"
  | "PAYMENTS"
  | "WALLET"
  | "EVENT"
  | "COMPLAINTS"
  | "PROFILE_SETTING"
  | "HANDLER"
  | "MATCH"
  | "SYSTEM_DEFAULTS"
  | "SHOPS"
  | "PAYOUTS"
  | "VAULT"
  | "ADMIN";

type API_ROUTE = {
  [key in routes]: string;
};

const API_ROUTE: API_ROUTE = {
  POST: "/v1/posts",
  STORY: "/v1/stories",
  CHANNEL: "/v1/channels",
  GROUP: "/v1/groups",
  USERS: "/v1/users",
  USER_LOGIN: "/v1/users/login",
  USER_PROFILE: "/v1/users/profile",
  USERNAME_AVAILABLE: "/v1/users/username-availability?username=",
  REQUESTS: "/v1/requests",
  CHAT_ACTIVITY: "/v1/users/converse-activity",
  BECOME_A_CREATOR: "/v1/requests/become-creator",
  RENEW_TOKEN: "/v1/users/renew-token",
  USER_REGISTER: "/v1/users/sign-up",
  USER_COMPLETE_PROFILE: "/v1/users/complete-profile",
  USER_EDIT_PROFILE: "/v1/users/avatar-and-background",
  USER_FEATURED_PROFILE: "/v1/users/featured-profile",
  USER_FOLLOWERS: "/v1/users/get-followers",
  USER_FOLLOWING: "/v1/users/get-followings",
  USER_FOLLOW: "/v1/users/follow-user",
  USER_UN_FOLLOW: "/v1/users/unfollow-user",
  REPORT: "/v1/report",
  UPDATE_ACCOUNT_INFO: "/v1/users/user-info",
  USER_PRIVACY_SETTING: "/v1/users/privacy-setting",
  SPECIAL_OPTIONS: "/v1/users/special-option",
  SHOPS: "/v1/shops",
  NOTIFICATION: "/v1/notification",
  SEARCH_USER: "/v1/users/search-profile",
  SUBSCRIPTIONS: "/v1/subscriptions",
  WISHLIST: "/v1/users/wishlist",
  HASHTAGS: "/v1/hashtags",
  TAGS: "/v1/tags",
  PAYMENTS: "/v1/payments",
  WALLET: "/v1/wallet",
  EVENT: "/v1/events",
  COMPLAINTS: "/v1/complaint",
  PROFILE_SETTING: "/v1/users/price-setting",
  HANDLER: "/v1/wallet-card-handler",
  MATCH: "/v1/match",
  SYSTEM_DEFAULTS: "/v1/systemdefaults",
  PAYOUTS: "/v1/payouts/user",
  VAULT: "/v1/users/vault",
  ADMIN: "/v1/admin",
};

export default API_ROUTE;
