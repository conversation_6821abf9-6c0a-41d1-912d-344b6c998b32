"use client";

import "./index.scss";

import Image from "next/image";
import Link from "next/link";
import { notFound, useParams } from "next/navigation";
import { useEffect, useState } from "react";

import { type APIChannel, GetChannelByUsername } from "@/api/channel";
import ActionButton from "@/components/common/action-button";
import Badges from "@/components/common/badges";
import {
  CreateBtn,
  EditBtn,
  transformSubscriptions,
} from "@/components/common/buttons/channel-group-btns";
import PostActionSendTip from "@/components/common/buttons/send-tip";
import ReadMoreContent from "@/components/common/chat/chat-box/read-more-content";
import ServicesBtn from "@/components/common/chat/chatbar/helpers/ServicesBtn";
import { EndDateCountDown } from "@/components/common/date-countdown";
import Loader from "@/components/common/loader/loader";
import type { ModalRef } from "@/components/common/modal";
import SubscriptionsOffer from "@/components/common/subscriptions";
import { type SocialMedia, SocialMediaImgMap } from "@/global/constants";
import useIsMobile from "@/hooks/useIsMobile";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import {
  configActions,
  createActions,
  defaultActions,
  userDataActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { store } from "@/redux-store/store";
import type { Author } from "@/types/post";
import type {
  ModifiedChannelProfile,
  ProfileSelectionType,
  Tags,
} from "@/types/profile";
import { type Children } from "@/types/utils";
import { getAssetUrl } from "@/utils/assets";

function refacData(data: APIChannel): ModifiedChannelProfile {
  const selfChannel =
    data.author.username === store.getState().user.profile.username;

  return {
    _id: data?._id,
    img: getAssetUrl({
      media: data?.avatar?.[0],
      defaultType: "avatar",
    }),
    high_res_avatar: getAssetUrl({
      media: data?.avatar?.[0],
      variation: data?.avatar?.[0]?.variations.includes("high")
        ? "high"
        : "compressed",
      defaultType: "avatar",
    }),
    background: getAssetUrl({ media: data.background?.[0] }),
    name: data.name,
    username: data.channel_tagname,
    desc: {
      text: data.about,
      images: [],
    },
    author: {
      id: data?.author?._id,

      username: data.author.username,
      role: data.author.role,
      display_name: data.author.display_name,
      badges: data.author.badges!,
    },
    hash: data?.hashtags,
    type: data.channel_type,
    social_media: data?.social_handles,
    my_subscription_data: data.my_subscription_data,
    featured: [],
    subscriptions: transformSubscriptions(data.subscription),
    topics: [],
    services: data.services,
    totalLikes: data.totalLikes,
    counter: {
      posts: selfChannel
        ? data?.counter?.post_count
        : data?.counter?.post_count -
          (data.counter.private_post_count +
            data.counter?.scheduled_post_count),
      media: selfChannel
        ? data?.counter?.media_count?.image_count +
          data?.counter?.media_count?.video_count
        : data?.counter?.media_count?.image_count -
            ((data?.counter?.media_count?.private_image_count || 0) +
              data?.counter?.media_count?.scheduled_image_count) +
            (data?.counter?.media_count?.video_count -
              ((data?.counter?.media_count?.private_video_count || 0) +
                data?.counter?.media_count?.scheduled_video_count)) || 0,
      subscriber: data?.counter?.subscriber_count,
      subscribed: 0,

      "sell-item": 0,
      // wishlist: 0,
      followers: 0,
    },
    free_services: data.free_services ?? [],
    media_count: {
      image_count: data?.counter?.media_count?.image_count,
      video_count: data?.counter?.media_count?.video_count,
      private_image_count: data?.counter?.media_count?.private_image_count,
      private_video_count: data?.counter?.media_count?.private_video_count,
    },
    media: [],
    perks: data.perks,
    marked_as_deleted: data?.marked_as_deleted,
    scheduled_deletion_date: data?.scheduled_deletion_date,
  };
}

async function getData(username: string) {
  try {
    const response = await GetChannelByUsername(username);

    if (!response.data) {
      throw new Error(`No channel found for username : ${useState}`);
    }

    if (response.data.author._id === store.getState().user.profile._id) {
      store.dispatch(userDataActions.AddChannel(response.data));
    }

    // Return the data as props
    return {
      err: false,
      data: refacData(response.data),
    };
  } catch (err) {
    console.error("channel issue testing: ", err);
    return { err: true, data: {} as ModifiedChannelProfile };
  }
}

export default function CreatorLayout({
  children,
  params,
}: Children & {
  params: {
    id: string;
    section: ProfileSelectionType;
  };
}) {
  const [error, setError] = useState("loading" as string | boolean);
  const [state, setState] = useState({} as ModifiedChannelProfile);

  const dispatchAction = useAppDispatch();
  const authorId = useAppSelector((state) => state?.user?.profile?._id);
  const user = useAppSelector((state) => state.user.profile);
  const [author, setAuthor] = useState({} as Author);

  const navigate = useNavigateBack();

  // const [isSubscribed, setIs]
  useEffect(() => {
    console.log({ params });
    getData(params?.id).then(({ err, data }) => {
      setError(err);
      setState(data);

      // setTimeout(() => {
      //   if (window.innerWidth > 1024) {
      //     window.scrollTo(0, 400);
      //   }
      // }, 500);

      dispatchAction(
        defaultActions.setChannelProfile({ ...data, blinkOffers: false })
      );

      setAuthor({
        f_name: data?.name,
        // l_name: "string",
        // avatar: data.media.avatar[0],
        _id: data?._id,

        username: data?.author?.username,
        // badges: data?.author,
        pic: data?.img,
        role: data?.author?.role,
        user_type: data?.author?.role,
        display_name: data?.author?.display_name,
      });
    });
    dispatchAction(defaultActions?.setPostTypeFilter("published"));
    dispatchAction(defaultActions?.setMediaTypeFilter("published"));
  }, [params?.id]);

  useEffect(() => {
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  const handleData = () => {
    dispatchAction(
      createActions.setChannel({
        id: state._id,
        name: state.name,
        username: state.username,
        avatar: state.img,
        background: state.background,
        description: state.desc.text,
        type: state.type,
        my_subscription_data: state.my_subscription_data,
        hashtags: state.hash,
        social_media: state.social_media,
        subscriptions: [],
        from: "updateAvatar",
        saved: false,
        nextBtnText: "",
        perks: state.perks,
        free_services: state.free_services,
        services: state.services,
        totalLikes: state.totalLikes,
        counter: {
          post_count: 0,
          media_count: {
            image_count: 0,
            video_count: 0,
            private_image_count: 0,
            private_video_count: 0,
          },
          subscriber_count: 0,
          follower_count: 0,
          private_post_count: 0,
          private_media_count: 0,
          ...state.counter,
        },
      })
    );
  };

  if (error === "loading") {
    return (
      <div className="container-xxl bd-gutter g-lg-4 g-0 d-flex align-items-center justify-content-center vh-100">
        <Loader />
      </div>
    );
  } else if (error) {
    return notFound();
  }

  const ProfileHeader = () => (
    <div className="profile-header position-relative">
      <div onClick={() => navigate()} className=" pointer backButton">
        <Image
          src={"/images/svg/back.svg"}
          className="back-button-drop-shadow"
          width={54}
          height={54}
          alt="back"
        />{" "}
      </div>
      <div className="profile-image">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src={state.background}
          alt="User Profile Background Image"
          width={1280}
          height={720}
          onClick={() => {
            window.KNKY.showFullscreenMedia("image", state.background);
          }}
        />
        {user._id === state?.author?.id && !state?.marked_as_deleted && (
          <div className="position-absolute z-3 bottom-0 end-0 p-3">
            <Link
              href={`/channel/${params.id}/edit/preview`}
              onClick={handleData}
            >
              <Image
                src={"/images/creator/edit.svg"}
                width={20}
                height={20}
                alt="edit profile"
                className="position-relative bottom-0 end-0"
              />
            </Link>
          </div>
        )}
      </div>
    </div>
  );

  const TagContainer = (tags: Tags) => {
    const hashtagRef = { method: {} as ModalRef };
    return (
      <>
        {tags.array?.length ? (
          <div className="bg-body d-lg-flex p-3 gap-2 flex-column rounded-lg-3">
            <div className="d-flex justify-content-between">
              <h5 className="fw-semibold">{tags.title}</h5>
              {tags.author?.id === user?._id && (
                <Link
                  href={`/channel/${state.username}/edit`}
                  className="pointer fw-bold"
                >
                  Edit
                </Link>
              )}
            </div>
            <div className=" d-flex gap-3 flex-wrap">
              {tags?.array?.map((tag) => (
                <a key={tag} href={tag} className="tag rounded-3">
                  {tag}
                </a>
              ))}
            </div>
          </div>
        ) : (
          <></>
        )}
      </>
    );
  };

  const ProfileAboutMe = () => (
    <div className="bg-body d-flex p-3 gap-2 flex-column rounded-lg-3 ">
      <h5 className="d-lg-block d-none fw-semibold">About Channel</h5>
      <div className=" d-flex gap-3 flex-wrap">
        <ReadMoreContent
          text={state.desc.text}
          characterCount={200}
          btnColor="#ac1991"
        />
        {state.desc.images.map((src) => (
          <Image
            key={src}
            src={src}
            alt="Profile Image"
            width={96}
            height={32}
          />
        ))}
      </div>
      <div className="d-flex gap-2">
        {state.social_media &&
          state.social_media.map((social: any, i) => (
            <a key={i} href={social.external_url} target="_blank" className="">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={SocialMediaImgMap[social.social_media_type as SocialMedia]}
                width={30}
                height={30}
                alt={social}
              />
            </a>
          ))}
      </div>
    </div>
  );

  const ProfileDetails = () => {
    const id = useParams();
    const isMobile = useIsMobile();
    const [hideTagContainer, setHideTagContainer] = useState(true);
    const [timeOver, setTimeOver] = useState(false);

    useEffect(() => {
      const handleResize = () => {
        if (window.innerWidth <= 768) {
          setHideTagContainer(false);
        }
      };

      handleResize(); // Initial check
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }, []);
    const channelInformation = useAppSelector(
      (state) => state.defaults.channel_profile
    );

    return (
      <div className="profile-details d-flex flex-grow-1 flex-column gap-lg-4">
        <div className="profile-view d-flex flex-column p-lg-3 px-3 pt-0 gap-lg-2 gap-1 align-items-lg-center rounded-bottom-lg-4">
          <div className="d-flex justify-content-between align-items-start">
            <div className="profile-view-image centered-image mb-4 mb-lg-0">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                className="rounded-pill object-fit-cover bg-cream pointer"
                alt=""
                width={256}
                height={256}
                src={state.img}
                onClick={() => {
                  window.KNKY.showFullscreenMedia(
                    "image",
                    state.high_res_avatar
                  );
                }}
              />
            </div>
            <div className="d-lg-none d-flex align-self-center gap-3">
              {/* <CreateBtn
                href={`/create/new-post/${id?.id}`}
                text={"Create new post"}
                visible={authorId === state.author.id}
              /> */}
              {authorId !== state.author.id && (
                <>
                  {" "}
                  <ServicesBtn
                    target={{
                      target_user_display_name: state.author.display_name,
                      target_user_id: state.author.id,
                    }}
                  />
                  <PostActionSendTip
                    postId={state?._id}
                    author={author}
                    type="channel"
                  />
                </>
              )}
            </div>
          </div>
          <div className="d-flex align-items-center gap-2">
            <h4 className="profile-name d-flex align-items-center gap-1 m-0">
              {state.name}
            </h4>
            <span className="fw-medium px-2 py-1 bg-green2 text-white rounded-2 fs-8">
              Channel
            </span>
          </div>
          <h5 className="profile-username fs-5 fw-light color-black ">
            @{state.username}
          </h5>
          <h6 className="fw-medium color-black d-flex mb- text-truncate">
            A channel of&nbsp;
            <Link
              href={`/creator/${state.author.username}`}
              className="color-primary fw-medium d-flex gap-1"
            >
              @{state.author.username}
              <Badges array={state.author.badges} />
            </Link>
          </h6>
          {/* add justify-content-between justify-content-lg-around when removing d-none and want to show followers count */}
          <div className="d-flex   justify-content-around w-100 px-1 px-lg-0   mb-3  mb-lg-2">
            {/* changing d-flex to d-none for client's event */}
            <div className="d-none gap-1">
              <Image
                src={"/images/home/<USER>"}
                width={24}
                height={24}
                alt="followers"
              />
              <span className="color-dark fw-medium">
                {state?.counter?.subscriber}
              </span>
            </div>

            <div className="d-flex gap-1">
              <Image
                src={"/images/svg/heart.svg"}
                width={24}
                height={24}
                alt="image"
                className="grayscale"
              />
              <span className="color-dark fw-medium">{state.totalLikes}</span>
            </div>

            <div className="d-flex gap-1">
              <Image
                src={"/images/home/<USER>"}
                width={24}
                height={24}
                alt="image"
              />
              <span className="color-dark fw-medium">
                {state?.media_count?.image_count}
              </span>
            </div>

            <div className="d-flex gap-1">
              <Image
                src={"/images/home/<USER>"}
                width={24}
                height={24}
                alt="video"
              />
              <span className="color-dark fw-medium">
                {state?.media_count?.video_count}
              </span>
            </div>
          </div>
          {!state?.marked_as_deleted ? (
            <>
              <EditBtn
                href={`/channel/${params.id}/edit`}
                text={"Edit my channel"}
                visible={authorId === state.author.id}
                onClick={handleData}
              />
              <div className="d-lg-none mt-2 gap-3">
                <CreateBtn
                  href={`/create/new-post/${id?.id}?isChannelPost=true`}
                  text={"Create new post"}
                  visible={authorId === state.author.id}
                />
              </div>
            </>
          ) : (
            <>
              <ActionButton
                className="fs-6 fw-500 te w-100 pe-none mt-2 mt-lg-0"
                disabled={true}
              >
                Marked as deleted
              </ActionButton>
              {authorId === state.author.id &&
              state?.scheduled_deletion_date &&
              !timeOver ? (
                <ActionButton
                  className="w-100 pe-none"
                  variant="dark"
                  type="outline"
                >
                  Deletes in{" "}
                  <EndDateCountDown
                    endDate={state?.scheduled_deletion_date!}
                    onOver={() => setTimeOver(true)}
                  />
                </ActionButton>
              ) : null}
            </>
          )}
        </div>

        <SubscriptionsOffer
          data={state.subscriptions}
          subscription_data={state.my_subscription_data}
          type="Channel"
          perks={state.perks}
          id={state._id}
          author={state.author.id === user._id}
        />

        <ProfileAboutMe />
        {isMobile && !hideTagContainer && (
          <div
            className="bg-body px-3 color-black fw-medium pointer fs-7 pb-3"
            onClick={() => setHideTagContainer(true)}
          >
            See more
          </div>
        )}

        {hideTagContainer && (
          <>
            <TagContainer title="Topics" array={state?.topics} />
            <TagContainer
              title="Hashtags"
              array={channelInformation.hash || state?.hash}
              author={state?.author}
            />
          </>
        )}
        {isMobile && hideTagContainer && (
          <div
            className="bg-body px-3 color-black fw-medium pointer fs-7 pb-3"
            onClick={() => setHideTagContainer(false)}
          >
            See less
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div
        id="profile-container"
        className="container-xxl bd-gutter g-lg-4 g-0 d-flex flex-column overflow-y-auto"
      >
        <ProfileHeader />
        <div className="d-flex profile-content flex-column flex-lg-row gap-lg-4">
          <ProfileDetails />
          <div className="profile-sections-container d-flex flex-grow-1 flex-column gap-3">
            {children}
          </div>
        </div>
      </div>
    </>
  );
}
