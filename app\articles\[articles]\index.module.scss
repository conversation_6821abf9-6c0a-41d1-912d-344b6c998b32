.articals-main-container {
  height: 100dvh;
  background: #ffff;

  @media (max-width: 1200px) {
    padding: 0 2%;
  }
  @media (max-width: 786px) {
    padding: 0 0;
  }
}
.articals-wrapper {
  @media (max-width: 786px) {
    padding: 0 !important;
  }
}
.articles-container {
  height: 86%;
  width: 100%;
  overflow: hidden;

  @media (max-width: 786px) {
    padding: 0% 2%;
  }
}
.header-wrapper {
  display: flex;
  background: white;
  position: sticky;
  top: 0;
  overflow: auto;
  border-bottom: 1px solid rgba(000, 000, 000, 0.2);
  z-index: 10;
}
.active {
  background: #f9f4f8;
  color: var(--primary-color);
  border-bottom: 1px solid var(--primary-color);
}
.header-section {
  padding: 1% 0.5%;
  text-wrap: nowrap;

  @media (max-width: 768px) {
    padding: 1.5% 1%;
  }
}
.myIframe {
  min-height: 100%;
}
