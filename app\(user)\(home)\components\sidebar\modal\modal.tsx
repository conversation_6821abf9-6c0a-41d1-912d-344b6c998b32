import Image from "next/image";
import { useRouter } from "next/navigation";
import Swal from "sweetalert2";
import "./index.scss";

import { buyPatformSubscriptionPlan } from "@/api/subscriptions";
import Modal, { type ModalImportProps } from "@/components/common/modal";
import type { onSubmitData } from "@/components/common/payment-method";
import { PaymentMethod } from "@/components/common/payment-method";
import { configActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";
import { formatCurrency } from "@/utils/formatter";

export default function PlansSubscriptionModal({
  setChildRef,
  misc,
}: ModalImportProps) {
  const router = useRouter();
  const dispatch = useAppDispatch();

  const onSubmit = (values: onSubmitData) => {
    const cardTip = {
      tokenised_card_id: values?.payment_id,
      payment_mode: values.payment_type,
    };
    const walletTip = {
      payment_mode: values.payment_type,
    };
    const tipObject = values.payment_type === "wallet" ? walletTip : cardTip;
    const type = misc?.name === "Prime" ? "prime" : "pro-creator";
    setChildRef.method.close();

    dispatch(configActions.toggleCardProcessing());

    Swal.fire({
      title: `You will be purchasing ${
        misc?.name === "Prime" ? "Prime" : "Pro Creator"
      } subscription for ${formatCurrency(misc?.price)}, procced?`,
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "No",
      confirmButtonColor: "#AC1991",
      confirmButtonText: "Yes",

      showCloseButton: true,
    }).then((result) => {
      if (result.isConfirmed) {
        dispatch(configActions.toggleCardProcessing());
        buyPatformSubscriptionPlan(misc?._id, type, tipObject)
          .then(() => {
            dispatch(configActions.toggleCardProcessing());
            Swal.fire({
              icon: "success",
              title: `You have successfully subscribed ${
                misc?.name === "Prime" ? "Prime" : "Pro Creator"
              }`,
              confirmButtonText: "Finish",
              confirmButtonColor: "#AC1991",

              showCloseButton: true,
            }).then(() => {
              window.location.reload();
            });
          })
          .catch((error: any) => {
            console.error(error);
            dispatch(configActions.toggleCardProcessing());
            Swal.fire({
              icon: "error",
              title: error.message,
              confirmButtonText: "Finish",
              confirmButtonColor: "#AC1991",

              showCloseButton: true,
            });
          });
      }
    });
  };

  return (
    <Modal
      title={"Platform Subscription"}
      subtitle={[""]}
      setChildRef={setChildRef}
      cls="modal-sm"
      render={"direct"}
    >
      <div className="tip-wrapper">
        <div className="d-flex flex-column align-items-center">
          <p className="color-medium fs-6 fw-500">Subscription Offer</p>

          <div
            className={`border rounded-2 text-center d-flex flex-column gap-2 align-items-center p-2 w-75 m-auto  mb-2 ${
              misc?.name === "Prime" ? "color-prime" : "color-pro-creator"
            }`}
            style={{ minWidth: "5rem" }}
          >
            {misc?.name === "Prime" && (
              <Image
                src={"/images/home/<USER>"}
                alt=""
                width={64}
                height={64}
              />
            )}
            {misc?.name === "Pro Creator" && (
              <Image
                src={"/images/badges/creator-pro.svg"}
                alt=""
                width={64}
                height={64}
              />
            )}

            <h4 className="text-white">{misc?.name}</h4>
            <div className="d-flex align-items-center gap-2 text-white ">
              <h2 className="mb-0 fw-bolder ">${misc?.price}</h2>
              {/* <div className="d-flex  flex-column fs-7">
                <span>{misc?.type?.toLowerCase()}</span>
              </div> */}
            </div>
          </div>
        </div>
        <PaymentMethod
          type="Subscription"
          amount={misc?.price}
          onSubmit={onSubmit}
          onClose={() => setChildRef.method.close()}
        />
      </div>
    </Modal>
  );
}
