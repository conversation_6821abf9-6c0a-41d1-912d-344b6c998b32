"use client";

import classNames from "classnames";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";
import { useEffect, useRef } from "react";

import Header from "@/components/common/header";

import styles from "./index.module.scss";

const Articles = ({ params }: { params: any }) => {
  const selectedArticle = params.articles;
  const linkRefs = useRef<any>({});

  const isElementInViewport = (el: any) => {
    if (!el) return false;
    const rect = el.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <=
        (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  };

  useEffect(() => {
    const activeElement = linkRefs.current[selectedArticle];

    if (activeElement && !isElementInViewport(activeElement)) {
      activeElement.scrollIntoView({
        behavior: "instant",
        block: "center",
        inline: "center",
      });
    }
  }, [selectedArticle]);

  const articalHeader = {
    "Terms Of Service": "terms-of-service",
    "Creator & Collaborator Terms":
      "additional-terms-for-creators-and-collaborators",
    "Fans & Purchasing Terms":
      "additional-terms-for-fans-buying-creator-content",
    "Privacy Policy": "privacy-policy",
    "Cookie Policy": "cookie-policy",
    "Content Moderation Policy": "content-moderation-and-protection-policy",
    "Community Guidelines": "community-guidelines",
    DMCA: "dmca-takedown-policy",
    "USC 2257": "usc-2257",
    "Complaint Policy": "complaint-policy",
  };

  const articles: any = {
    "terms-of-service": "TERMS_OF_SERVICE",
    "community-guidelines": "COMMUNITY_GUIDELINES",
    "content-moderation-and-protection-policy": "CONTENT_MODERATION_POLICY",
    "cookie-policy": "COOKIES_POLICY",
    "dmca-takedown-policy": "DMCA_TAKEDOWN_POLICY",
    "privacy-policy": "PRIVACY_POLICY",
    "usc-2257": "USC_2257",
    "additional-terms-for-creators-and-collaborators":
      "ADDITIONAL_TERMS_FOR_CREATORS_AND_COLLABORATORS",
    "additional-terms-for-fans-buying-creator-content":
      "ADDITIONAL_TERMS_FOR_FANS_BUYING_CREATOR_CONTENT",
    "complaint-policy": "COMPLAINT_POLICY",
  };

  if (!articles[selectedArticle]) {
    return notFound();
  }

  return (
    <div className={classNames(styles["articals-main-container"])}>
      <div
        className={classNames(
          "container-xxl h-100",
          styles["articals-wrapper"]
        )}
      >
        <Header showHeader={true} />

        <div className={classNames(styles["header-wrapper"])}>
          {Object.entries(articalHeader).map(([key, value], index) => (
            <Link
              href={`/articles/${value}`}
              ref={(el) => {
                linkRefs.current[value] = el;
              }}
              className={classNames(
                styles["header-section"],
                "fs-7 fw-semibold",
                value === selectedArticle ? styles["active"] : ""
              )}
              key={index}
            >
              {key}
            </Link>
          ))}
        </div>
        <div
          className={classNames(
            " position-relative",
            styles["articles-container"]
          )}
        >
          <iframe
            src={
              process.env.public_asset +
              "/articles/" +
              articles[selectedArticle] +
              ".html"
            }
            width={"100%"}
            className={classNames("position-relative", styles["myIframe"])}
          >
            <Image
              src={"/images/svg/back.svg"}
              height={40}
              width={40}
              alt=""
              className="position-absolute top-0 start-0 m-4 back-button-drop-shadow"
              onClick={() => {
                window.close();
              }}
            />
          </iframe>
        </div>
      </div>
    </div>
  );
};

export default Articles;
