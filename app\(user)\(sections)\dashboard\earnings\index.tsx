import { useEffect, useState } from "react";
import "./index.scss";

import { earningsOverview } from "@/api/dashboard";
import { useAppSelector } from "@/redux-store/hooks";
import { formatCurrency } from "@/utils/formatter";

import EarningsTab from "./tab-content";

type Types =
  | "tips"
  | "shop_items"
  | "chat_fee"
  | "special_options"
  | "other"
  | "premium_post"
  | "premium_story"
  | "tickets"
  | "subscription"
  | "promotion";

export default function Earnings() {
  const dateFilter = useAppSelector(
    (state) => state.user.creatorDashboardDateFilter
  );
  const initialState = {
    subscription: 0,
    tips: 0,
    shop_items: 0,
    premium_post: 0,
    premium_story: 0,
    special_options: 0,
    other: 0,
    tickets: 0,
    chat_fee: 0,
  };
  const [totals, setTotals] = useState(initialState) as any;
  const [totalAmount, setTotalAmount] = useState(0);

  const [type, setType] = useState<Types>("subscription");
  useEffect(() => {
    setTotals(initialState);
    setTotalAmount(0);
    earningsOverview(dateFilter).then((res) => {
      res.data.forEach((item: any) => {
        setTotalAmount((prevTotalAmount) => prevTotalAmount + item.totalAmount);

        switch (item._id) {
          case "Tip":
            setTotals((prevState: any) => ({
              ...prevState,
              tips: item.totalAmount,
            }));
            break;
          case "ProductSell":
            setTotals((prevState: any) => ({
              ...prevState,
              shop_items: item.totalAmount,
            }));
            break;
          case "PremiumPost":
            setTotals((prevState: any) => ({
              ...prevState,
              premium_post: item.totalAmount,
            }));
            break;
          case "PremiumStory":
            setTotals((prevState: any) => ({
              ...prevState,
              premium_story: item.totalAmount,
            }));
            break;
          case "SpecialOptions":
            setTotals((prevState: any) => ({
              ...prevState,
              special_options: item.totalAmount,
            }));
            break;
          case "Ticket":
            setTotals((prevState: any) => ({
              ...prevState,
              tickets: item.totalAmount,
            }));
            break;
          case "ChannelSubscription":
            setTotals((prevState: any) => ({
              ...prevState,
              subscription: (prevState.subscription || 0) + item.totalAmount,
            }));
            break;
          case "GroupSubscription":
            setTotals((prevState: any) => ({
              ...prevState,
              subscription: (prevState.subscription || 0) + item.totalAmount,
            }));
            break;
          case "ChatFee":
            setTotals((prevState: any) => ({
              ...prevState,
              chat_fee: item.totalAmount,
            }));
            break;
          default:
            setTotals((prevState: any) => ({
              ...prevState,
              other: item.totalAmount,
            }));
            break;
        }
      });
    });
  }, [dateFilter]);

  return (
    <div className="container bg-body p-3 px-lg-3 px-0 rounded-lg-3 rounded-0">
      <div className="d-flex flex-column gap-2">
        <div className="earning-title-wrapper px-2 d-flex align-items-center justify-content-between ">
          <p className="fs-5 fw-semibold">Earnings</p>
          <p className="fs-5 fw-medium">{`Total: ${formatCurrency(
            totalAmount
          )}`}</p>
        </div>
        <div className="earnings-tab-wrapper scrollable px-1 px-lg-2">
          <div
            className={
              type === "subscription"
                ? "activeType pointer"
                : " flex-grow-1 pointer"
            }
            onClick={() => setType("subscription")}
          >
            <p className="m-0">Subcriptions</p>
            <p className="fs-5 fw-semibold ">
              {formatCurrency(totals.subscription)}
            </p>
          </div>
          <div
            className={
              type === "tips" ? "activeType pointer" : " flex-grow-1 pointer"
            }
            onClick={() => setType("tips")}
          >
            <p className="m-0">Tips</p>
            <p className="fs-5 fw-semibold ">{formatCurrency(totals.tips)}</p>
          </div>
          <div
            className={
              type === "shop_items"
                ? "activeType pointer"
                : " flex-grow-1 pointer"
            }
            onClick={() => setType("shop_items")}
          >
            <p className="m-0">Shop Items</p>
            <p className="fs-5 fw-semibold ">
              {formatCurrency(totals.shop_items)}
            </p>
          </div>
          <div
            className={
              type === "tickets" ? "activeType pointer" : " flex-grow-1 pointer"
            }
            onClick={() => setType("tickets")}
          >
            <p className="m-0">Tickets</p>
            <p className="fs-5 fw-semibold ">
              {formatCurrency(totals?.tickets)}
            </p>
          </div>
          <div
            className={
              type === "premium_post"
                ? "activeType pointer"
                : " flex-grow-1 pointer"
            }
            onClick={() => setType("premium_post")}
          >
            <p className="m-0">Post</p>
            <p className="fs-5 fw-semibold ">
              {formatCurrency(totals?.premium_post)}
            </p>
          </div>

          <div
            className={
              type === "premium_story"
                ? "activeType pointer"
                : " flex-grow-1 pointer"
            }
            onClick={() => setType("premium_story")}
          >
            <p className="m-0">Story</p>
            <p className="fs-5 fw-semibold ">
              {formatCurrency(totals?.premium_story)}
            </p>
          </div>
          <div
            className={
              type === "chat_fee"
                ? "activeType pointer"
                : " flex-grow-1 pointer"
            }
            onClick={() => setType("chat_fee")}
          >
            <p className="m-0">Message</p>
            <p className="fs-5 fw-semibold ">
              {formatCurrency(totals?.chat_fee)}
            </p>
          </div>
          <div
            className={
              type === "special_options"
                ? "activeType pointer"
                : " flex-grow-1 pointer"
            }
            onClick={() => setType("special_options")}
          >
            <p className="m-0">Services</p>
            <p className="fs-5 fw-semibold ">
              {formatCurrency(totals.special_options)}
            </p>
          </div>
        </div>
      </div>
      <EarningsTab type={type} total={totals[type]} />
    </div>
  );
}
