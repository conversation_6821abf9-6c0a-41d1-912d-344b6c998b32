"use client";

import "./index.scss";

import classNames from "classnames";
import { usePathname } from "next/navigation";
import { useRef, useState } from "react";

import { useAppSelector } from "@/redux-store/hooks";
import type { Children } from "@/types/utils";

import HomeNavigation from "./components/navigation";
import HomeSidebar from "./components/sidebar";

export default function AppLayout({ children }: Children) {
  const pathname = usePathname();
  const [scrollPosition, setScrollPosition] = useState(0);
  const scrollRef = useRef<any>(null);
  const role = useAppSelector((state) => state.user.role);
  return (
    <div className="home flex-grow-1 overflow-y-auto d-flex flex-column position-relative">
      <HomeNavigation />
      <div
        className="flex-grow-1 overflow-y-auto d-flex flex-column"
        id="infiniteScrollHome"
        ref={scrollRef}
        style={{ overflowAnchor: "none" }}
      >
        <main
          className={classNames(
            "home container-xxl d-flex g-lg-4 g-0 gap-4 mt-lg-4 w-100  ",
            {
              "flex-grow-1 overflow-y-auto":
                [
                  "/interested",
                  "/following",
                  "/subscribed",
                  "/settings",
                ].includes(pathname) && role === "guest",
            }
          )}
        >
          <div className="home-content w-0">{children}</div>
          <HomeSidebar />
        </main>
        {/* <GoBackToPosition
          scrollPosition={scrollPosition}
          scrollableRef={scrollRef}
        />
        <GoUpWidget
          scrollableRef={scrollRef}
          setScrollPosition={setScrollPosition}
        /> */}
      </div>
    </div>
  );
}
