import type { SubscriptionBadge, UserBadge } from "@/types/badge";
import { type Media } from "@/types/media";
import type {
  Collaborator,
  GetSharedEntity,
  InternalTagConsent,
  PostPoll,
  PostType,
  SharedEntity,
  TaggedChannel,
  TaggedCollab,
} from "@/types/post";
import type { SellItem } from "@/types/sell-item";

import API from ".";
import type { GetGroup } from "./group";
import type { ReleaseFormsResponse } from "./user";

export interface MediaInfo {
  path: string;
  type: "image" | "video";
  original_name?: string;
  internal_tag_consent?: string[];
  external_tag_consent?: string[];
  is_author_present?: boolean;
  location?: string;
  date?: string;
  title?: string;
}
export interface PostSubmitBody {
  media?: MediaInfo[];
  poster?: MediaInfo[];
  preview?: MediaInfo[];
  video_poster_mapper?: number[];
  backgroundColor?: string;
  textColor?: string;
  visibility: PostType;
  type: "Normal" | "Poll" | "Product" | "Auction" | "Event";
  pay_and_watch_rate?: number;
  tip_setting?: {
    is_enabled: boolean;
    minimum: number;
  };
  shared_entity?: SharedEntity[];
  channels?: string[];
  groups?: string[];
  share_on_story?: boolean;
  share_on_profile?: boolean;
  caption?: string;
  hashtags?: string[];
  tagged_users?: string[];
  tagged_channels?: string[];
  tagged_groups?: string[];
  tag_consent?: {
    username: string;
  }[];
  products?: string[];
  collaborators?: any[];
  release_form_users?: string[];
  feeling?: string;
  poll?: {
    question: string;
    answer_choice_type: string;
    answer_choices: { option: string; votes: number[] }[];
  };
  product_id?: string;
  isGroup?: boolean;
  isChannel?: boolean;

  vault_media_ids?: string[];
  vault_preview_ids?: string[];
  vault_poster_ids?: string[];
  video_vault_poster_mapper?: number[];

  expired_on?: string;
  scheduled_on?: string;
}

interface PostSubmitResponse {
  data: "success";
}

// async function createMediaPreview(file: File): Promise<string> {
//   if (file.type.startsWith("image/")) {
//     return URL.createObjectURL(file);
//   } else if (file.type.startsWith("video/")) {
//     return await captureVideoThumbnail(file);
//   }

//   throw new Error("Unsupported media type");
// }

// async function captureVideoThumbnail(file: File): Promise<string> {
//   return new Promise((resolve, reject) => {
//     const video = document.createElement("video");
//     video.src = URL.createObjectURL(file);
//     video.currentTime = 1;
//     video.muted = true;
//     video.playsInline = true;

//     video.onloadeddata = () => {
//       const canvas = document.createElement("canvas");
//       canvas.width = video.videoWidth;
//       canvas.height = video.videoHeight;
//       const context = canvas.getContext("2d");

//       if (context) {
//         context.drawImage(video, 0, 0, canvas.width, canvas.height);
//         canvas.toBlob((blob) => {
//           if (blob) {
//             resolve(URL.createObjectURL(blob));
//           } else {
//             reject(new Error("Failed to create thumbnail"));
//           }
//         });
//       } else {
//         reject(new Error("Failed to capture video frame"));
//       }
//     };

//     video.onerror = () =>
//       reject(new Error("Error loading video for thumbnail"));
//   });
// }

export async function PostSubmit(
  body: PostSubmitBody,
  onProgress?: (id: string, progress: number) => void
) {
  // body.media?.length &&
  //   body.media.forEach((item) => {
  //     if (typeof item === "string") {
  //       form.append(`media`, item);
  //     } else if (item instanceof File) {
  //       form.append(`media`, item);
  //     }
  //   });
  // body.poster?.length &&
  //   body?.poster?.forEach((item) => {
  //     if (item instanceof File) {
  //       form.append(`poster`, item);
  //     }
  //   });

  // body.preview &&
  //   (body.visibility === "Premium" || body.visibility === "Prime") &&
  //   form.append("preview", body.preview);

  // body.video_poster_mapper?.length != 0 &&
  //   form.append(
  //     "video_poster_mapper",
  //     JSON.stringify(body.video_poster_mapper)
  //   );

  // body.backgroundColor && form.append("backgroundColor", body.backgroundColor);
  // body.textColor && form.append("textColor", body.textColor);
  // body.poll &&
  //   body.visibility !== "Premium" &&
  //   body.poll.question.length !== 0 &&
  //   form.append("poll", JSON.stringify(body.poll));
  // body.feeling &&
  //   body.feeling.length !== 0 &&
  //   form.append("feeling", body.feeling);

  // body.tagged_users?.length &&
  //   form.append("tagged_users", JSON.stringify(body.tagged_users));
  // body.tagged_channels?.length &&
  //   form.append("tagged_channels", JSON.stringify(body.tagged_channels));
  // body.tagged_groups?.length &&
  //   form.append("tagged_groups", JSON.stringify(body.tagged_groups));
  // body.collaborators?.length &&
  //   form.append("collaborators", JSON.stringify(body.collaborators));
  // body.products?.length &&
  //   form.append("products", JSON.stringify(body.products));
  // form.append("visibility", body.visibility);
  // form.append("type", body.visibility === "Premium" ? "Normal" : body.type);
  // body.pay_and_watch_rate &&
  //   body.visibility === "Premium" &&
  //   //@ts-expect-error overload error showing
  //   form.append("pay_and_watch_rate", body.pay_and_watch_rate);
  // body.tip_setting &&
  //   form.append("tip_setting", JSON.stringify(body.tip_setting));
  // body.share_on_story &&
  //   form.append("share_on_story", JSON.stringify(body.share_on_story));
  // body.share_on_profile &&
  //   form.append("shared_on_profile", JSON.stringify(body.share_on_profile));
  // body.caption && form.append("caption", body.caption);
  // body.hashtags?.length &&
  //   form.append("hashtags", JSON.stringify(body.hashtags));
  // body.product_id && form.append("product_id", body.product_id);

  // body.channels?.length &&
  //   (body.visibility === "Prime" ||
  //     body.visibility === "Premium" ||
  //     body.visibility === "Subscription" ||
  //     body?.isChannel) &&
  //   form.append("channels", JSON.stringify(body.channels));
  // body.groups?.length &&
  //   (body.visibility === "Prime" ||
  //     body.visibility === "Premium" ||
  //     body.visibility === "Subscription" ||
  //     body?.isGroup) &&
  //   form.append("groups", JSON.stringify(body.groups));

  // body?.shared_entity &&
  //   form.append("shared_entity", JSON.stringify(body?.shared_entity));
  // body?.vault_media_ids?.length &&
  //   form.append("vault_media_ids", JSON.stringify(body?.vault_media_ids));
  // body?.vault_poster_ids?.length &&
  //   form.append("vault_poster_ids", JSON.stringify(body?.vault_poster_ids));

  // body.vault_preview_ids?.length &&
  //   (body.visibility === "Premium" || body.visibility === "Prime") &&
  //   form.append("vault_preview_ids", JSON.stringify(body.vault_preview_ids));

  // body.video_vault_poster_mapper?.length != 0 &&
  //   form.append(
  //     "video_vault_poster_mapper",
  //     JSON.stringify(body.video_vault_poster_mapper)
  //   );
  // body.scheduled_on && form.append("scheduled_on", body.scheduled_on);
  // body.expired_on && form.append("expired_on", body.expired_on);

  const postData: Record<string, any> = {};

  // Handling media
  if (body.media?.length) {
    postData.media = body.media;
  }

  // Handling poster
  if (body.poster?.length) {
    postData.poster = body.poster;
  }

  // Handling preview
  if (
    body.preview?.length &&
    (body.visibility === "Premium" ||
      body.visibility === "Prime" ||
      body.visibility === "Subscription")
  ) {
    postData.preview = body.preview;
  }

  // Handling video_poster_mapper
  if (body.video_poster_mapper?.length !== 0) {
    postData.video_poster_mapper = body.video_poster_mapper;
  }

  // Other fields
  if (body.backgroundColor) postData.backgroundColor = body.backgroundColor;
  if (body.textColor) postData.textColor = body.textColor;

  if (
    body.poll &&
    body.visibility !== "Premium" &&
    body.poll.question.length !== 0
  ) {
    postData.poll = body.poll;
  }

  if (body.feeling && body.feeling.length !== 0) {
    postData.feeling = body.feeling;
  }

  if (body.tagged_users?.length) postData.tagged_users = body.tagged_users;
  // if (body.media?.length)
  //   postData.tag_consent = [
  //     { username: store.getState()?.user?.profile?.username },
  //     ...(body?.tag_consent || []),
  //     ...(body?.collaborators?.map((item: any) => ({
  //       username: item.username,
  //     })) || []),
  //   ];
  // if (body.release_form_users?.length)
  //   postData.release_form_users = body.release_form_users;
  if (body.tagged_channels?.length)
    postData.tagged_channels = body.tagged_channels;
  if (body.tagged_groups?.length) postData.tagged_groups = body.tagged_groups;
  if (body.collaborators?.length) postData.collaborators = body.collaborators;
  if (body.products?.length) postData.products = body.products;

  postData.visibility = body.visibility;
  postData.type = body.visibility === "Premium" ? "Normal" : body.type;

  if (body.pay_and_watch_rate && body.visibility === "Premium") {
    postData.pay_and_watch_rate = Number(body.pay_and_watch_rate);
  }

  if (body.tip_setting) postData.tip_setting = body.tip_setting;
  if (body.share_on_story) postData.share_on_story = body.share_on_story;
  if (body.share_on_profile) postData.shared_on_profile = body.share_on_profile;
  if (body.caption) postData.caption = body.caption;
  if (body.hashtags?.length) postData.hashtags = body.hashtags;
  if (body.product_id) postData.product_id = body.product_id;

  // Handling channels
  if (
    body.channels?.length &&
    (body.visibility === "Prime" ||
      body.visibility === "Premium" ||
      body.visibility === "Subscription" ||
      body?.isChannel)
  ) {
    postData.channels = body.channels;
  }

  // Handling groups
  if (
    body.groups?.length &&
    (body.visibility === "Prime" ||
      body.visibility === "Premium" ||
      body.visibility === "Subscription" ||
      body?.isGroup)
  ) {
    postData.groups = body.groups;
  }

  // Shared entity
  if (body?.shared_entity) postData.shared_entity = body.shared_entity;

  // Vault-related fields
  if (body?.vault_media_ids?.length)
    postData.vault_media_ids = body.vault_media_ids;
  if (body?.vault_poster_ids?.length)
    postData.vault_poster_ids = body.vault_poster_ids;

  if (
    body.vault_preview_ids?.length &&
    (body.visibility === "Premium" || body.visibility === "Prime")
  ) {
    postData.vault_preview_ids = body.vault_preview_ids;
  }

  if (body.video_vault_poster_mapper?.length !== 0) {
    postData.video_vault_poster_mapper = body.video_vault_poster_mapper;
  }

  if (body.scheduled_on) postData.scheduled_on = body.scheduled_on;
  if (body.expired_on) postData.expired_on = body.expired_on;

  console.log("calling fun: ", { postData }, { body });

  // const previewUrl = await createMediaPreview(body.media?.[0]! as File);

  // const xhr = new XMLHttpRequest();

  // const headers: Record<string, string> = {
  //   Authorization: `Bearer ${store.getState()?.user?.token}`,
  //   "x-api-key": process.env.x_api_key || "",
  // };

  // xhr.open("POST", API.POST + "/create-post-new", true);
  // Object.keys(headers).forEach((key) => {
  //   if (headers[key]) {
  //     xhr.setRequestHeader(key, headers[key]);
  //   }
  // });

  // toast.success("Post Submitted!");

  // return new Promise((resolve, reject) => {
  //   // Set onload within the Promise
  //   xhr.onload = function () {
  //     if (xhr.status >= 200 && xhr.status < 300) {
  //       try {
  //         const response = JSON.parse(xhr.responseText);

  //         resolve(response);
  //       } catch (error) {
  //         reject(new Error("Failed to parse response JSON"));
  //       }
  //     } else {
  //       console.log({ xhr });

  //       try {
  //         const errorResponse = JSON.parse(xhr.responseText);
  //         reject(errorResponse); // Pass the parsed error response
  //       } catch (error) {
  //         reject(new Error("Request failed with status " + xhr.status));
  //       }
  //     }
  //   };

  //   xhr.onerror = () => reject(new Error("Network error"));

  //   xhr.send(JSON.stringify(postData));
  // }) as Promise<PostSubmitResponse>;

  return API.post(
    ` ${API.POST}/create-post-new`,
    postData
  ) as Promise<PostSubmitResponse>;
}

export function DeletePost(_id: string, body?: any) {
  return API.delete(`${API.POST}/${_id}`, body) as Promise<PostSubmitResponse>;
}

export function PostAppeal(_id: string, body: any) {
  return API.post(
    `${API.POST}/${_id}/appeal`,
    body
  ) as Promise<PostSubmitResponse>;
}

export interface GetPostListResponse {
  data: GetPost[];
}

export interface EventBody {
  _id: string;
  name: string;
  description: string;
  ticket_type: "Fee" | "Free";
  event_type: string;
  price: number;
  is_public: boolean;
  scheduled_on: string;
  media: Media[];
  duration: number;
  quantity: number;
  quantity_type: string;
  has_ended: boolean;
  sold_ticket_count: number;
  event_converse_channel_id: string;
  is_live_stream: boolean;
}

export interface MinSubscription {
  _id: string;
  price: number;
  author: string;
  source: "channel" | "group";
  channel: string;
  validity_type: string;
}

interface CounterObject {
  reactions: number;
  shares: number;
  bookmarks: number;
  comments: number;
  last_like: {
    username: string;
    avatar: Media;
    reaction_type: string;
  };
  private_post_count: number;
  post_count: number;
  event_post_count: number;
  scheduled_post_count: number;
  collab_post_count: number;
  media_count: {
    image_count: number;
    video_count: number;
    private_image_count: number;
    private_video_count: number;
    scheduled_image_count: number;
    scheduled_video_count: number;
    collab_image_count: number;
    collab_video_count: number;
    consent_image_count: number;
    consent_video_count: number;
  };
  follower_count: number;
  following_count: number;
  channel_count: number;
  channel_deleted_count: number;
  group_deleted_count: number;
  group_count: number;
  shop_count: number;
  wishlist_count: number;
  event_count: number;
  pinned_post_count: number;
  purchased_items_count: number;
  subscribed_count: number;
  private_event_count: number;
  ranking_score: number;
  sold_product_count: number;
  total_product_revenue: number;
  trending_score: number;
  consent_post_count: number;
  vault_files_count: number;
  services_count: number;
  totalLikes: number;
}

export interface GetPost {
  _id: string;
  author: Author;
  caption: string;
  comments: Comment[];
  visibility: PostType;
  hashtags: string[];
  tagged_users: any[];
  tag_consent?: InternalTagConsent[];
  collaborators: Collaborator[];
  buyers: object[];
  seat_reserved?: boolean;
  groups: GetGroup[];
  products: SellItem;
  type: string;
  event: EventBody;
  shared_entity: GetSharedEntity[];
  follows_you?: boolean;
  followed_by_you?: boolean;
  status: string;
  media: Media[];
  preview: Media[];
  feeling: string;
  poll: PostPoll;
  scheduled_on?: string;
  expired_on?: string;
  backgroundColor: string;
  textColor: string;
  likes: any;
  counter?: CounterObject;
  tip_setting: string;
  pay_and_watch_rate: number;
  min_subscription: MinSubscription;
  shared_on_profile: boolean;
  is_purchased: boolean;
  is_saved: boolean;
  is_featured: boolean;
  is_deleted: boolean;
  is_subscribed: boolean;
  tagged_channels?: TaggedChannel[];
  tagged_groups?: TaggedCollab[];
  has_prime: boolean;
  has_appealed: boolean;
  created_at: string;
  updated_at: string;
  initial_visibility: PostType;
  media_categories: string[];
  has_consent: boolean;
  people_count: number;
  is_collab?: boolean;
  is_tagged?: boolean;
  release_form_users: ReleaseFormsResponse[];
  channels: {
    _id: string;
    name: string;
    channel_tagname: string;
    channel_type: string;
    marked_as_deleted?: boolean;

    avatar: Media[];
    background: Media[];
  }[];

  __v: number;
}

interface Author {
  counter: any;
  _id: string;
  f_name: string;
  l_name: string;
  user_type: string;
  username: string;
  badges: BadgesType;
  avatar: Media[];
  background: Media[];
  display_name: string;
  follows_you?: boolean;
  followed_by_you?: boolean;
  has_active_services?: boolean;
  account_status?: string;
  totalLikes: number;
}

export interface BadgesType {
  user_badges: UserBadge[];
  subscription_badge: SubscriptionBadge[];
}

export interface Avatar {
  original: string;
}

export interface Post_Media {
  original: string;
}

interface Comment {
  _id: string;
  text: string;
  author: Author;
  replies?: Comment[];
  likes: any[];
  created_at: string;
  updated_at: string;
  counter?: {
    reactions: number;
    replies?: number;
  };
}

export const GetPostList = async (
  inputs: Record<string, any>
): Promise<GetPostListResponse> => {
  const {
    timestamp,
    direction = "backward",
    groupId,
    channelId,
    userId,
    hashtags,
    gender,
    user_type,
    postType,
    limit,
    is_premium,
  } = inputs;
  console.log(hashtags);
  return API.get(
    `${API.POST}?d=${direction}${timestamp ? `&t=${timestamp}` : ""}&limit=${
      limit ? limit : 20
    }${groupId ? `&group=${groupId}` : ""}${
      channelId ? `&channel=${channelId}` : ""
    }${userId ? `&user_id=${userId}` : ""}${
      hashtags ? `&hashtags=${JSON.stringify(hashtags)}` : ""
    }${gender ? `&gender=${gender}` : ""}${
      user_type ? `&user_type=${user_type}` : ""
    }${postType ? `&post_type=${postType}` : ""}${
      is_premium ? `&is_premium=${is_premium}` : ""
    }`
  );
};

export const GetSubscribedPosts = async (
  inputs: Record<string, any>
): Promise<GetPostListResponse> => {
  const { timestamp, gender, user_type, direction = "backward" } = inputs;
  return API.get(
    `${API.POST}?d=${direction}&t=${timestamp}&limit=20&is_subscribed=true${
      gender ? `&gender=${gender}` : ""
    }${user_type ? `&user_type=${user_type}` : ""}`
  );
};

export const GetEventsByUserId = async (
  inputs: Record<string, any>
): Promise<any> => {
  const { timestamp, direction = "backward", userId, eventStatus } = inputs;
  // ! to add &event_status=${eventStatus}
  return API.get(
    `${API.POST}?d=${direction}&t=${timestamp}&limit=20&is_event=true&event_status=${eventStatus}&user_id=${userId}`
  );
};

export const GetFollowingPosts = async (
  inputs: Record<string, any>
): Promise<GetPostListResponse> => {
  const { timestamp, gender, user_type, direction = "backward" } = inputs;
  return API.get(
    `${API.POST}?d=${direction}&t=${timestamp}&limit=20&is_following=true${
      gender ? `&gender=${gender}` : ""
    }${user_type ? `&user_type=${user_type}` : ""}`
  );
};

export const GetLivePosts = async (
  inputs: Record<string, any>
): Promise<GetPostListResponse> => {
  const {
    direction = "backward",
    timestamp,
    status = "live",
    gender,
    user_type,
  } = inputs;
  return API.get(
    `${
      API.POST
    }?d=${direction}&t=${timestamp}&limit=20&is_event=true&event_status=${status}${
      gender ? `&gender=${gender}` : ""
    }${user_type ? `&user_type=${user_type}` : ""}`
  );
};

export const GetPremiumPosts = async (
  inputs: Record<string, any>
): Promise<GetPostListResponse> => {
  const {
    timestamp = new Date(),
    gender,
    user_type,
    direction = "backward",
  } = inputs;
  return API.get(
    `${API.POST}?d=${direction}&t=${timestamp}&limit=20&is_premium=true${
      gender ? `&gender=${gender}` : ""
    }${user_type ? `&user_type=${user_type}` : ""}`
  );
};

interface GetPostCommentResponse {
  data: any[];
}

export const GetPostComments = async (postId: string, page: number = 1) =>
  API.get(
    `${API.POST}/${postId}/comments?page=${page}&limit=10`
  ) as Promise<GetPostCommentResponse>;

interface AddPostCommentResponse {
  data: any;
}

export const AddPostComment = async (postId: string, body: any) =>
  API.post(
    `${API.POST}/${postId}/comments/`,
    body
  ) as Promise<AddPostCommentResponse>;

export const AddCommentReply = async (
  postId: string,
  commentId: string,
  body: any
) =>
  API.post(
    `${API.POST}/${postId}/comments/${commentId}/replies`,
    body
  ) as Promise<AddPostCommentResponse>;

export const AddCommentLike = async (
  postId: string,
  commentId: string,
  body: any
) =>
  API.post(
    `${API.POST}/${postId}/comment/${commentId}/likes`,
    body
  ) as Promise<any>;

export const AddRepliedCommentLike = async (
  postId: string,
  commentId: string,
  parentCommentId: string,
  body: any
) =>
  API.post(
    `${API.POST}/${postId}/comments/${parentCommentId}/replies/${commentId}/likes`,
    body
  ) as Promise<any>;

export const DeleteComment = async (postId: string, commentId: string) =>
  API.delete(`${API.POST}/${postId}/comment/${commentId}`) as Promise<any>;

export const DeleteRepliedComment = async (
  postId: string,
  commentId: string,
  parentCommentId: string
) =>
  API.delete(
    `${API.POST}/${postId}/comment/${parentCommentId}/reply/${commentId}`
  ) as Promise<any>;

interface UserPostLikeBody {
  reaction_type: string;
}

interface UserPostLikeResponse {
  message: "Success";
  status: 200;
}

export const UserPostLike = async (postId: string, body: UserPostLikeBody) =>
  API.post(
    `${API.POST}/${postId}/likes`,
    body
  ) as Promise<UserPostLikeResponse>;

interface UserPostUnLikeResponse {
  message: "Success";
  status: 200;
}

export const UserPostUnLike = async (postId: string) =>
  API.post(
    `${API.POST}/${postId}/likes`,
    {}
  ) as Promise<UserPostUnLikeResponse>;

interface UserSavePostResponse {
  message: "Success";
  status: 200;
}

export const UserSavePost = async (postId: string) =>
  API.post(API.USERS + "/save-post", {
    post_id: postId,
  }) as Promise<UserSavePostResponse>;

export const UserUnSavePost = async (postId: string) =>
  API.patch(API.USERS + "/save-post", {
    post_id: postId,
  }) as Promise<UserSavePostResponse>;

interface UserSharePostResponse {
  message: "Success";
  status: 200;
}

export const UserSharePost = async (postId: string) =>
  API.post(`${API.POST}/${postId}/share`, {}) as Promise<UserSharePostResponse>;

interface UserSendTipResponse {
  message: "Success";
  status: 200;
}

export const UserSendTip = async (postId: string) =>
  API.post(`${API.POST}/${postId}/share`, {}) as Promise<UserSendTipResponse>;

interface SelectPollResponse {
  message: "Success";
}

export async function SelectPoll(postId: string, ansId: string) {
  return API.post(`${API.POST}/${postId}/poll`, {
    answer_id: ansId,
  }) as Promise<SelectPollResponse>;
}

export const GetHashtags = async () => {
  return API.get(`${API.HASHTAGS}`) as Promise<any>;
};

export const GetTrendingHashtags = (limit: number = 20) => {
  return API.get(`${API.TAGS}?limit=${limit}`) as Promise<any>;
};

export const SearchExistingHashtags = async (hashtag: string) => {
  return API.get(`${API.HASHTAGS}?hashtag=${hashtag}`) as Promise<any>;
};

// get Single post by post ID

export const GetSinglePost = async (post_id: any): Promise<any> => {
  return API.get(`${API.POST}/${post_id}`);
};

//edit Post

export interface EditPostBody {
  visibility?: PostType;
  caption?: string;
  backgroundColor?: string;
  hashtags?: any[];
  scheduled_on?: string;
  expired_on?: string;
  tagged_users?: any[];
  textColor?: string;
}

export const EditPost = async (
  post_id: any,
  value: EditPostBody
): Promise<any> => {
  return API.patch(`${API.POST}/${post_id}`, value) as Promise<any>;
};

export const getPostDetailsCounts = async (postId: string) => {
  return API.get(`${API.POST}/${postId}/post-data-counts`) as Promise<any>;
};

export const getPostDetails = async (
  postId: string,
  type: string,
  page: number
) => {
  return API.get(
    `${API.POST}/${postId}/post-data-insights?type=${type}&page=${page}`
  ) as Promise<any>;
};

export interface AddConsentTagsBody {
  media_consent_tag: {
    internal_tag_consent?: string[];
    external_tag_consent?: string[];
    date?: string;
    location?: string;
    media_id: string;
    is_author_present?: boolean;
  }[];
}

export const AddConsentTags = async (body: AddConsentTagsBody) => {
  return API.post(`${API.USERS}/add-media-consent`, body) as Promise<any>;
};

export const resendTagRequest = async (consentId: string, userId: string) => {
  return API.post(`${API.USERS}/resend-media-consent`, {
    user_id: userId,
    consent_id: consentId,
  }) as Promise<any>;
};
