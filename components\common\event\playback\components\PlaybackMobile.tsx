import styles from "./PlaybackMobile.module.scss";

import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import {
  LiveKitRoom,
  RoomAudioRenderer,
  useTracks,
  VideoTrack,
} from "@livekit/components-react";
import { Track } from "livekit-client";
import Swal from "sweetalert2";
import classNames from "classnames";

import { EventBridge } from "@/app/event-bridge";
import type { Subscription } from "@/types/event-bridge";
import {
  EVENT_ERROR_MSGS,
  GetEvent,
  GetEventLatestTips,
  GetEventStreamToken,
} from "@/api/event";
import type { EventGetResponse } from "@/api/event";
import type { Post } from "@/types/post";
import { GetSinglePost } from "@/api/post";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import {
  chatActions,
  configActions,
  liveEventActions,
} from "@/redux-store/actions";
import Wrapper from "@/components/common/wrapper";
import PostAuthor from "@/components/common/post/author";
import LiveStreamChatMobile from "@/components/common/chat/stream-chat-mobile";
import { TipHighlight, TipHistory } from "@/components/common/event/tips";
import { FlyingReactions } from "@/components/common/event/reactions";
import { StreamStatus } from "@/components/common/event/stream-status";
import { ViewerCount } from "@/components/common/event/viewer-count";
import { sleep } from "@/utils/sleep";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import useBrowser from "@/hooks/useBrowser";
import useIsPWA from "@/hooks/useIsPWA";

import { PlaybackControlsAndFallbackImg } from "./PlaybackControlsAndFallbackImg";
import { showErrorResponse } from "./playback-errors";

export function PlaybackMobile(props: { eventId: string }) {
  const router = useRouter();
  const navigateBack = useNavigateBack();
  const dispatch = useAppDispatch();
  const browser = useBrowser();
  const isPWA = useIsPWA();
  const myProfile = useAppSelector((state) => state.user.profile);
  const muted = useAppSelector((state) => state.liveEvent.muted);
  const isFakeFullscreen = useAppSelector((s) => s.liveEvent.isFakeFullscreen);

  const [lkAuthToken, setLkAuthToken] = useState<string>();
  const [event, setEvent] = useState<EventGetResponse["data"]>();
  const [post, setPost] = useState<Post | null>(null);
  const [isEventEnded, setIsEventEnded] = useState(false);

  const subRefs = useRef<Subscription[]>([]);

  useEffect(() => {
    (async () => {
      const _event = (await GetEvent(props.eventId)).data;

      if (_event.author === myProfile._id) {
        // can't watch your own event
        router.push("/fresh");
        return;
      }

      const _token = (await GetEventStreamToken(props.eventId)).data.token;
      const _tips = (await GetEventLatestTips(props.eventId, 1)).data;
      const _post = (await GetSinglePost(_event.post)).data[0];

      if (!EventBridge.isConnected) await sleep(3000);
      const r = await EventBridge.request("LiveStream/AlreadyViewing", {
        event_id: props.eventId,
      });
      if (r.alreadyViewing) throw new Error(EVENT_ERROR_MSGS.ALREADY_VIEWING);

      setEvent(_event);
      setLkAuthToken(_token);
      dispatch(liveEventActions.setLiveEvent(_event));
      dispatch(liveEventActions.addTips({ tips: _tips, trim: 5 }));
      setPost(_post);

      const s1 = EventBridge.join("LiveStream/" + props.eventId);
      const s2 = EventBridge.on("LiveStream/Tip", (data) => {
        dispatch(liveEventActions.addTips({ tips: data.tip, trim: 5 }));
      });
      const s3 = EventBridge.on("LiveStream/End", () => {
        setIsEventEnded(true);
        Swal.fire({
          icon: "info",
          title: "Event Ended!",
          text: "This event has ended. Thank you for watching!",
        }).then(() => {
          dispatch(chatActions.clearStreamChat());
          dispatch(liveEventActions.resetLiveEvent());
          navigateBack();
        });
      });
      const s4 = EventBridge.on("LiveStream/KickedOut", (_data) => {
        setIsEventEnded(true);
        Swal.fire({
          icon: "error",
          title: "Kicked Out!",
          text: "You have been kicked out of this event.",
          timer: 3000,
          timerProgressBar: true,
        }).then(() => {
          dispatch(chatActions.clearStreamChat());
          dispatch(liveEventActions.resetLiveEvent());
          navigateBack();
        });
      });
      subRefs.current.push(s1, s2, s3, s4);
    })().catch((e) => showErrorResponse(e).then(() => navigateBack()));

    return () => {
      subRefs.current.forEach((s) => s.unsubscribe());
    };
  }, []);

  useEffect(() => {
    dispatch(configActions.showHeader(false));
    dispatch(configActions.showKycCompleteBar(false));

    return () => {
      dispatch(configActions.showHeader(true));
      dispatch(configActions.showKycCompleteBar(true));
    };
  }, []);

  return (
    <>
      <LiveKitRoom
        serverUrl={process.env.livekit}
        token={lkAuthToken}
        connect={true}
        className="container-xxl p-0 p-md-3 overflow-y-auto overflow-x-hidden"
      >
        {event && (
          <div className="row">
            <div className="col d-flex flex-column gap-2">
              <div className="position-relative flex-column g-2 bg-black dvh-100">
                <LiveVideoPlayback
                  ended={isEventEnded}
                  bottomOffset={
                    browser.os.name === "iOS" && isPWA ? "4.5em" : "3.4em"
                  }
                />
                {!isEventEnded && (
                  <LiveStreamChatMobile
                    postId={event.post}
                    style={{
                      bottom: browser.os.name === "iOS" && isPWA ? "1em" : "",
                    }}
                    className={classNames({ "d-none": isFakeFullscreen })}
                  />
                )}
              </div>

              {!isEventEnded && !isFakeFullscreen && <TipHistory />}

              {post && !isFakeFullscreen && (
                <Wrapper>
                  <div>
                    <PostAuthor
                      author={post.author}
                      shared_on_profile={post.shared_on_profile}
                      // @ts-expect-error to be handled later
                      ts={new Date(post.created_at).getTime()}
                      // @ts-expect-error to be handled later
                      type={post.visibility}
                      post_type={post.type}
                    />
                    <div className="px-1">
                      <div className="mt-2 fw-medium fs-5">
                        {post?.event.name}
                      </div>
                      <div>{post?.event.description}</div>
                    </div>
                  </div>
                </Wrapper>
              )}
            </div>
          </div>
        )}
        <RoomAudioRenderer muted={muted} />
      </LiveKitRoom>
      <FlyingReactions side="right" />
    </>
  );
}

function LiveVideoPlaybackBase(props: {
  ended: boolean;
  bottomOffset: string;
}) {
  const dispatch = useAppDispatch();
  const isFakeFullscreen = useAppSelector((s) => s.liveEvent.isFakeFullscreen);

  const cameraTrackRef = useTracks([Track.Source.Camera], {
    onlySubscribed: true,
  })[0];
  const isVideoOff = cameraTrackRef?.publication.track?.isMuted;

  const micTrackRef = useTracks([Track.Source.Microphone], {
    onlySubscribed: true,
  })[0];
  const isMicOff = micTrackRef?.publication.track?.isMuted;

  const isStreamPortrait = useMemo(
    () =>
      typeof cameraTrackRef?.publication?.dimensions?.height === "number" &&
      cameraTrackRef?.publication?.dimensions?.height >
        cameraTrackRef?.publication?.dimensions?.width,
    [cameraTrackRef]
  );

  useEffect(() => {
    dispatch(liveEventActions.setStreamPortrait(isStreamPortrait));
  }, [isStreamPortrait]);

  return (
    <div
      className={classNames("text-white", styles["stream-video-container"])}
      id="stream-video-container"
      style={{
        height: isFakeFullscreen
          ? "100%"
          : `calc(100% - ${props.bottomOffset})`,
      }}
    >
      {!props.ended && (
        <div
          className={classNames(
            "position-absolute top-0 start-0 z-1 d-flex gap-2 p-3 user-select-none",
            {
              [styles["streamStatusFakeFS"]]:
                isFakeFullscreen && !isStreamPortrait,
            }
          )}
        >
          <StreamStatus
            status={props.ended ? "ENDED" : cameraTrackRef ? "LIVE" : "WAITING"}
          />
          <ViewerCount />
        </div>
      )}

      {!props.ended && (
        <PlaybackControlsAndFallbackImg
          videoFallback={isVideoOff}
          audioFallback={isMicOff}
          isMobile={true}
          controlsDivClassName={classNames({
            [styles["controlsDivFakeFS"]]:
              isFakeFullscreen && !isStreamPortrait,
          })}
          fallbackImgClassName={classNames({
            [styles["fallbackImgFakeFS"]]:
              isFakeFullscreen && !isStreamPortrait,
          })}
        />
      )}

      {cameraTrackRef && (
        <VideoTrack
          id="stream-video"
          trackRef={cameraTrackRef}
          className={classNames("h-100 flip-x", {
            "w-100": !isStreamPortrait,
            [styles["streamVideoFakeFS"]]:
              isFakeFullscreen && !isStreamPortrait,
            [styles["obscure"]]: props.ended,
          })}
          disablePictureInPicture
        />
      )}

      <TipHighlight
        className={classNames({
          [styles["tipHighlightFakeFS"]]: isFakeFullscreen && !isStreamPortrait,
        })}
      />
    </div>
  );
}

const LiveVideoPlayback = memo(LiveVideoPlaybackBase);
