import Image from "next/image";
import { useRouter } from "next/navigation";
import { memo, useEffect, useState } from "react";
import Swal from "sweetalert2";

import { SeatReservation } from "@/api/event";
import { GetSinglePost } from "@/api/post";
import Badges from "@/components/common/badges";
import type { ModalRef } from "@/components/common/modal";
import EventButtons from "@/components/common/post/live-event/eventButtons";
import BuyTicketModal from "@/components/common/post/modal/buy-ticket";
import { ModalService } from "@/components/modals";
import { ModalPortal } from "@/components/portals/ModalPortal";
import {
  chatActions,
  defaultActions,
  liveEventActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Media as MediaType } from "@/types/media";
import type { Author, Author as AuthorType, Post } from "@/types/post";
import { getAssetUrl } from "@/utils/assets";
import { checkVerification } from "@/utils/check-verification";

interface sendTicketBody {
  postId: string;
  eventId: string;
  author: Author;
  price: number;
}

const Author = ({
  author,
  soldCount,
  quantity,
  quantityType,
}: {
  author: AuthorType;
  soldCount: number;
  quantity: number;
  quantityType: string;
}) => {
  const router = useRouter();

  const handleProfileRedirection = (e: any) => {
    e.stopPropagation();
    router.push(`/${author.role}/${author.username}`);
  };

  return (
    <div className="d-flex gap-2 align-items-center">
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        width={34}
        height={34}
        alt=""
        className="rounded-circle object-fit-cover pointer"
        src={author.pic}
        style={{
          boxShadow: "0 0 0 1pt var(--bg-color)",
        }}
        onClick={(e) => handleProfileRedirection(e)}
      />
      <div className="d-flex flex-column justify-content-between">
        <div
          className="d-flex align-items-center gap-1 pointer"
          onClick={(e) => handleProfileRedirection(e)}
        >
          <span className="text-white fw-semibold">{author?.display_name}</span>
          <Badges array={author.badges!} />
        </div>
        <div className="d-flex align-items-center gap-1">
          <Image
            src="/images/post/fill-ticket.svg"
            alt="live-time-icon"
            width={16}
            height={16}
          />
          <span style={{ color: "#FFFFFFB2" }} className=" fw-normal fs-7s">
            {quantityType == "Unlimited"
              ? `Reserved ${soldCount} `
              : `Sold ${soldCount}/
                        ${quantity}`}
          </span>
        </div>
      </div>
    </div>
  );
};

const Media = ({ media }: { media: MediaType }) => {
  if (media.type === "image") {
    return (
      /* eslint-disable-next-line @next/next/no-img-element */
      <img
        width={1280}
        height={720}
        alt=""
        className="object-fit-cover w-100 h-100 rounded-3"
        style={{ aspectRatio: "44/27" }}
        src={getAssetUrl({ media: media })}
      />
    );
  } else {
    return (
      <video
        src={getAssetUrl({ media: media })}
        autoPlay
        muted
        playsInline
        id="video-post"
        className={`object-fit-cover w-100 h-100 rounded-3 `}
        style={{ aspectRatio: "44/27" }}
      />
    );
  }
};

const LiveCard = ({ details }: { details: Post }) => {
  const [soldCount, setSoldCount] = useState<number>(
    details?.event?.sold_ticket_count!
  );
  const buyTicketModalRef = { method: {} as ModalRef };
  const profile = useAppSelector((state) => state.user.profile);
  const [showBuyTicketModal, setBuyTicketModal] = useState(false);
  const [seatReserved, setSeatReserved] = useState<boolean>(
    details?.seat_reserved!
  );
  const role = useAppSelector((state) => state?.user?.role);
  const isCardExist = useAppSelector(
    (state) => state?.userData?.cardDetails[0]
  );
  const notMyPost = profile._id !== details?.author._id;
  const router = useRouter();
  const dispatchAction = useAppDispatch();

  useEffect(() => {
    const ticketDetails: sendTicketBody = {
      postId: details?._id,
      eventId: details?.event?._id,
      author: details?.author,
      price: details?.event?.price,
    };

    showBuyTicketModal &&
      dispatchAction(defaultActions.setTicketDetails(ticketDetails));
    showBuyTicketModal && buyTicketModalRef.method.open();

    // currentPostId = postId!;
  }, [showBuyTicketModal]);

  const purchaseEvent = async () => {
    console.log("Purchase clicked");
    const verified = await checkVerification("event");

    if (verified) {
      if (role === "guest") {
        ModalService.open("SIGN_IN");
      } else {
        console.log("first");

        if (isCardExist?.payment_method_id) {
          if (showBuyTicketModal) {
            setBuyTicketModal(false);
            setTimeout(() => {
              setBuyTicketModal(true);
            }, 200);
          } else {
            setBuyTicketModal(true);
          }
        } else {
          ModalService.open("ADD_CARD");
        }
      }
    }
  };

  const registerFree = (id: string, body: any) => {
    SeatReservation(id, body)
      .then(() => {
        setTimeout(() => {
          router.push(`/events/${details?.event?._id}/live`);
          GetSinglePost(details?._id).then((res) => {
            setSeatReserved(res?.data[0]?.seat_reserved);
            setSoldCount(res?.data[0]?.event?.sold_ticket_count);
          });
        }, 100);
        // Swal.fire({
        //   icon: "success",
        //   title: `You have successfully register for ${details?.author?.display_name}'s event`,
        //   confirmButtonText: "Finish",
        //   confirmButtonColor: "#AC1991",

        //
        //   showCloseButton: true,
        // });
      })
      .catch((error) => {
        Swal.fire({
          icon: "error",
          title: error.message,
          confirmButtonText: "Finish",
          confirmButtonColor: "#AC1991",

          showCloseButton: true,
          timer: 4000,
          timerProgressBar: true,
          willClose: () => {
            window.location.reload();
          },
        });
      });
  };

  const freeEvent = async () => {
    const verified = await checkVerification("event");

    if (verified) {
      if (role === "guest") {
        ModalService.open("SIGN_IN");
      } else {
        registerFree(details?.event?._id!, {});
      }
    }
  };

  const goWatch = () => {
    dispatchAction(chatActions.clearStreamChat());
    dispatchAction(liveEventActions.resetLiveEvent());
    router.push(`/events/${details?.event?._id}/live`);
  };

  const defaultMedia: MediaType = {
    _id: "",
    path: "/images/common/default.svg",
    type: "image",
    status: "completed",
    variations: ["blur", "compressed"],
  };

  return (
    <>
      <div className="rounded-3 position-relative overflow-hidden">
        <Media
          media={
            details.event?.media?.length
              ? details.event.media[0]
              : details.author.background?.[0] || defaultMedia
          }
        />
        <div
          className="position-absolute bottom-0 d-flex align-items-center justify-content-between px-3 w-100 "
          style={{
            background: "linear-gradient(to top, rgb(0 0 0), transparent)",
            padding: "3% 0",
          }}
        >
          <Author
            author={details?.author}
            soldCount={soldCount}
            quantity={details?.event?.quantity}
            quantityType={details?.event?.quantity_type}
          />
          <EventButtons
            event={details?.event}
            notMyPost={notMyPost}
            soldCount={soldCount}
            seatReserved={seatReserved}
            freeEvent={() => freeEvent()}
            purchaseEvent={() => purchaseEvent()}
            goWatch={() => goWatch()}
            className="fs-7 w-auto"
          />
        </div>
        <div className="position-absolute " style={{ top: "5%", left: "4%" }}>
          <div className="d-flex gap-3 align-items-center">
            <div className="d-flex  bg-red rounded-3 w-fit px-4 py-1">
              <span className="text-white">LIVE</span>
            </div>
            {/* <div
              className=" d-flex align-items-center rounded-3 px-2 py-1 gap-1"
              style={{ backgroundColor: "rgba(0, 0, 0, 0.4)" }}
            >
              <Image
                src={"/images/post/fill-eye.svg"}
                alt=""
                width={12}
                height={9.97}
              />
              <span className="text-white">26.5k</span>
            </div> */}
          </div>
        </div>
      </div>

      {showBuyTicketModal && (
        <ModalPortal>
          <BuyTicketModal
            setChildRef={buyTicketModalRef}
            misc={{ setSeatReserved, setSoldCount }}
          />
        </ModalPortal>
      )}
    </>
  );
};

const LiveComp = ({ details }: { details: Post[] }) => {
  if (details?.length === 0) return;
  return (
    <div className=" h-100 overflow-y-auto container-fluid g-0 g-md-4 g-lg-0  my-lg-0 my-md-4 px-3 py-1 p-md-0  mt-md-0  ">
      <div className="row g-3 pb-2 me-0 position-relative">
        {details?.map((detail: Post, i: number) => {
          return (
            <div key={i} className="col-12 col-md-6   pt-0 pe-0 pe-lg-2 ">
              <LiveCard details={detail} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default memo(LiveComp);
