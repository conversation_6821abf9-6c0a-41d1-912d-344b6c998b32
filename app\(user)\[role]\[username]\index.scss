#profile-container {
  .profile-header {
    .profile-image {
      position: relative;
      padding-top: 37.037%;
      cursor: pointer;

      img {
        position: absolute;
        top: 0;
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  }

  .profile-content {
    .profile-details {
      flex-basis: 250px;

      @media (min-width: 992px) {
        min-width: 18rem;
        max-width: 24rem;
      }
    }

    .profile-sections-container {
      // flex-basis: 35rem;
    }
  }
}
@media screen and (min-width: 992px) {
  .profile-sections-container {
    width: 50%;
  }
}

.profile-view {
  position: relative;
  background: var(--bg-color);
  padding-top: 0;

  .profile-view-image {
    --h: clamp(3rem, calc((100vw - 256px) / 10), 6rem);

    @media (max-width: 992px) {
      margin-left: calc(var(--h) / 2);
    }

    img {
      box-shadow: 0 0 0 2pt var(--bg-color);
    }

    .is-live {
      box-shadow: 0 0 0 2pt #f11e11;
    }
  }
}

.tag {
  padding: 0.5rem 1rem;
  border: 1pt solid var(--bg-dark);
}

.featured-container {
  position: relative;

  .featured-content {
    overflow: hidden;
    overflow-x: auto;
  }

  .featured-item {
    position: relative;
    height: 14rem;
    width: 10rem;
    min-width: 10rem;
    border-radius: var(--border-radius);
    overflow: hidden;

    .featured-title {
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: 2;
      color: var(--bg-color);
    }

    .featured-image {
      position: absolute;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
}
.aboutMe-text {
  word-break: break-word;
}
.backButton {
  // rotate: -90deg;
  position: absolute;
  top: 3%;
  left: 1%;
  z-index: 9;

  @media screen and (max-width: 767px) {
    top: 4%;
  }
}

@keyframes glowing {
  0% {
    box-shadow: 0 0 -2px var(--primary-color);
  }
  25% {
    box-shadow: 0 0 6px var(--primary-color);
  }
  50% {
    box-shadow: -3 0 12px var(--primary-color);
  }
  75% {
    box-shadow: 0 0 8px var(--primary-color);
  }
  90% {
    box-shadow: 0 0 4px var(--primary-color);
  }
  100% {
    box-shadow: 0 0 0 var(--primary-color);
  }
}

.subscribe-btn {
  animation: glowing 4500ms infinite;
}

.live-badge {
  top: 83%;
  left: 26%;
  padding: 0.1rem 0;

  @media screen and (max-width: 992px) {
    top: 74%;
    left: 6%;
  }
}
