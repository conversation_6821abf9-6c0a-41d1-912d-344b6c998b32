"use client";

import { useEffect } from "react";

import { createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

import ChannelView from "../element";

export default function EditChannel() {
  const dispatchAction = useAppDispatch();
  const channelId = useAppSelector((state) => state.create.channel.id);

  useEffect(() => {
    // Info: Check if channel was half edited then reset the channel
    if (!channelId) return;

    dispatchAction(createActions.resetChannel());
  }, [channelId]);

  return ChannelView({
    edit: false,
    from: "/channel/create",
    fromTitle: "Create my channel",
    fromBtnText: "Next",
    nextBtnText: "Create my channel",
    navigateTo: "/channel/create/preview",
  });
}
