import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { LiveKitRoom, useTracks, VideoTrack } from "@livekit/components-react";
import { type Room, Track } from "livekit-client";
import Swal from "sweetalert2";
import classNames from "classnames";

import { EventBridge } from "@/app/event-bridge";
import type { Subscription } from "@/types/event-bridge";
import {
  EVENT_ERROR_MSGS,
  GetEvent,
  GetEventStreamToken,
  GetEventTips,
  GetTotalTipsEarned,
  UpdateEvent,
} from "@/api/event";
import type { Event } from "@/types/event";
import type { Post } from "@/types/post";
import { GetSinglePost } from "@/api/post";
import { useAppDispatch } from "@/redux-store/hooks";
import {
  chatActions,
  configActions,
  liveEventActions,
} from "@/redux-store/actions";
import Wrapper from "@/components/common/wrapper";
import PostAuthor from "@/components/common/post/author";
import LiveStreamChatMobile from "@/components/common/chat/stream-chat-mobile";
import { TipHighlight, TipHistory } from "@/components/common/event/tips";
import { FlyingReactions } from "@/components/common/event/reactions";
import { StreamStatus } from "@/components/common/event/stream-status";
import { ViewerCount } from "@/components/common/event/viewer-count";
import { StreamStats } from "@/components/common/event/stream-insights/components/stream-stats";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import useBrowser from "@/hooks/useBrowser";
import useIsPWA from "@/hooks/useIsPWA";
import { sleep } from "@/utils/sleep";

import { Header } from "./Header";
import { Overview } from "./Overview";
import { DisconnectBtn } from "./DisconnectBtn";
import { TimerFromTS } from "./TimerFromTS";
import { ControlsAndFallbackImg } from "./ControlsAndFallbackImg";
import { showErrorResponse } from "./publish-errors";

export function PublishMobile(props: { eventId: string }) {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const navigateBack = useNavigateBack();
  const browser = useBrowser();
  const isPWA = useIsPWA();

  const [lkAuthToken, setLkAuthToken] = useState<string>();
  const [event, setEvent] = useState<Event | null>(null);
  const [post, setPost] = useState<Post | null>(null);
  const [isEventEnded, setIsEventEnded] = useState(false);
  const isEventEndedRef = useRef(isEventEnded);
  const [timerTs, setTimerTs] = useState<number>(0);
  const liveKitRoomContextRef = useRef<Room>(null);

  const subRefs = useRef<Subscription[]>([]);

  useEffect(() => {
    (async () => {
      if (!sessionStorage.getItem("camDeviceId")) {
        router.push(`/events/${props.eventId}/device-preview`);
        return;
      }

      const _event = (await GetEvent(props.eventId)).data;
      const _token = (await GetEventStreamToken(props.eventId)).data.token;
      const _tips = (await GetEventTips({ eventId: props.eventId, limit: 10 }))
        .data;
      const _total_tips_earned = (await GetTotalTipsEarned(props.eventId)).data
        .earning;
      const _post = (await GetSinglePost(_event.post)).data[0];

      if (!EventBridge.isConnected) await sleep(3000);
      const r = await EventBridge.request("LiveStream/AlreadyViewing", {
        event_id: props.eventId,
      });
      if (r.alreadyViewing) throw new Error(EVENT_ERROR_MSGS.ALREADY_VIEWING);

      setEvent(_event);
      setLkAuthToken(_token);
      dispatch(liveEventActions.setLiveEvent(_event));
      dispatch(liveEventActions.addTips({ tips: _tips }));
      dispatch(liveEventActions.setTotalTips(_total_tips_earned));
      setPost(_post);

      const s1 = EventBridge.join("LiveStream/" + props.eventId);
      const s2 = EventBridge.on("LiveStream/Tip", (data) => {
        window.KNKY.chime();
        dispatch(liveEventActions.addTips({ tips: data.tip }));
      });
      await EventBridge.request("LiveStream/Status", {
        event_id: props.eventId,
        is_live: true,
      });
      const s3 = EventBridge.on("LiveStream/End", (data) => {
        EventBridge.request("LiveStream/Status", {
          event_id: props.eventId,
          is_live: false,
        });
        if (data.reason === "AutoEnd")
          liveKitRoomContextRef.current?.disconnect(true);
        setIsEventEnded(true);
        Swal.fire({
          icon: "info",
          title: "Event Ended!",
          text: "Thank you for Streaming!",
        });
      });
      subRefs.current.push(s1, s2, s3);

      setTimerTs(
        new Date(_event.scheduled_on).getTime() +
          (_event.is_live_stream ? 0 : _event.duration * 60 * 1000)
      );
    })().catch((e) => showErrorResponse(e).then(() => navigateBack()));

    return () => {
      subRefs.current.forEach((s) => s.unsubscribe());

      EventBridge.request("LiveStream/Status", {
        event_id: props.eventId,
        is_live: false,
      });

      if (isEventEndedRef.current) {
        dispatch(chatActions.clearStreamChat());
        dispatch(liveEventActions.resetLiveEvent());
      }
    };
  }, []);

  useEffect(() => {
    isEventEndedRef.current = isEventEnded;
  }, [isEventEnded]);

  useEffect(() => {
    dispatch(configActions.showHeader(false));
    dispatch(configActions.showKycCompleteBar(false));

    return () => {
      dispatch(configActions.showHeader(true));
      dispatch(configActions.showKycCompleteBar(true));
    };
  }, []);

  const onManualEnd = () => {
    UpdateEvent(props.eventId, { has_ended: true });
    setIsEventEnded(true);
  };

  return (
    <>
      <LiveKitRoom
        serverUrl={process.env.livekit}
        token={lkAuthToken}
        connect={true}
        video={{ deviceId: sessionStorage.getItem("camDeviceId") as string }}
        audio={{ deviceId: sessionStorage.getItem("micDeviceId") as string }}
        className="d-flex flex-column"
      >
        {isEventEnded && <Header />}
        <div className="container-xxl p-0 p-md-3 overflow-hidden">
          {event && (
            <div className="row">
              <div className="col d-flex flex-column gap-2">
                <div className="position-relative flex-column g-2 bg-black dvh-100">
                  <CreatorVideoRenderer
                    ended={isEventEnded}
                    bottomOffset={
                      browser.os.name === "iOS" && isPWA ? "4.5em" : "3.4em"
                    }
                  />
                  {!isEventEnded && (
                    <LiveStreamChatMobile
                      postId={event.post}
                      iAmOwner
                      style={{
                        bottom: browser.os.name === "iOS" && isPWA ? "1em" : "",
                      }}
                    />
                  )}
                </div>

                {!isEventEnded && <TipHistory />}

                {post && (
                  <Wrapper>
                    <div>
                      <PostAuthor
                        author={post.author}
                        shared_on_profile={post.shared_on_profile}
                        // @ts-expect-error to be handled later
                        ts={new Date(post.created_at).getTime()}
                        // @ts-expect-error to be handled later
                        type={post.visibility}
                        post_type={post.type}
                      />
                      <div className="px-1">
                        <div className="mt-2 fw-medium fs-5">
                          {post?.event.name}
                        </div>
                        <div>{post?.event.description}</div>
                      </div>
                    </div>
                  </Wrapper>
                )}

                {!isEventEnded && (
                  <Wrapper>
                    <div className="d-flex align-items-center justify-content-between">
                      <span className="fs-4 fw-medium">
                        {!event.is_live_stream && "Will end in: "}
                        <TimerFromTS
                          ts={timerTs}
                          reverse={!event.is_live_stream}
                        />
                      </span>
                      <DisconnectBtn
                        ref={liveKitRoomContextRef}
                        onClick={onManualEnd}
                      />
                    </div>
                  </Wrapper>
                )}

                {!isEventEnded && (
                  <Wrapper>
                    <StreamStats
                      stop={isEventEnded}
                      realTimeStatsType="Count"
                    />
                  </Wrapper>
                )}

                {isEventEnded && (
                  <div className="col-lg-4">
                    <Overview />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </LiveKitRoom>
      <FlyingReactions side="right" />
    </>
  );
}

function CreatorVideoRendererBase(props: {
  ended: boolean;
  bottomOffset: string;
}) {
  const trackRefs = useTracks([Track.Source.Camera]);
  const cameraTrackRef = trackRefs[0];
  const isStreamPortrait = useMemo(
    () =>
      typeof cameraTrackRef?.publication?.dimensions?.height === "number" &&
      cameraTrackRef?.publication?.dimensions?.height >
        cameraTrackRef?.publication?.dimensions?.width,
    [cameraTrackRef]
  );
  const cameraFacingMode = useMemo(
    () =>
      cameraTrackRef?.publication?.track?.mediaStream
        ?.getTracks()[0]
        ?.getSettings().facingMode,
    [cameraTrackRef]
  );

  return (
    <div
      className="text-white w-100"
      style={{ height: `calc(100% - ${props.bottomOffset})` }}
    >
      {!props.ended && (
        <div className="position-absolute top-0 start-0 z-1 d-flex gap-3 p-3 user-select-none">
          <StreamStatus
            status={
              props.ended ? "ENDED" : cameraTrackRef ? "LIVE" : "STARTING"
            }
          />
          <ViewerCount />
        </div>
      )}

      {!props.ended && <ControlsAndFallbackImg />}

      {cameraTrackRef && (
        <VideoTrack
          trackRef={cameraTrackRef}
          className={classNames("h-100", {
            "w-100": !isStreamPortrait,
            "flip-x": cameraFacingMode !== "environment",
          })}
        />
      )}

      <TipHighlight />
    </div>
  );
}

const CreatorVideoRenderer = memo(CreatorVideoRendererBase);
