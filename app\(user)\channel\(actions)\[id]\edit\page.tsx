"use client";

import { useEffect } from "react";

import { GetChannelByUsername } from "@/api/channel";
import { transformApiResponse } from "@/components/common/buttons/channel-group-btns";
import { createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { type Params } from "@/types/utils";
import { getAssetUrl } from "@/utils/assets";

import ChannelView from "../../element";

export default function EditChannel({ params }: Params) {
  const dispatchAction = useAppDispatch();
  const userId = useAppSelector((state) => state?.user?.profile?._id);
  const channelId = useAppSelector((state) => state?.create?.channel.id);

  useEffect(() => {
    // Info: Check if channel was edited then came back then no fetching of data
    if (channelId === params.id) return;

    GetChannelByUsername(params.id)
      .then((response) => {
        const channel = response.data;
        if (channel?.author?._id !== userId)
          throw new Error("User not have sufficient permissions");

        dispatchAction(
          createActions.setChannel({
            id: channel._id,
            name: channel.name,
            username: channel.channel_tagname,
            avatar: getAssetUrl({
              media: channel.avatar[0],
              defaultType: "avatar",
            }),
            background: getAssetUrl({ media: channel.background[0] }),
            description: channel.about,
            type: channel.channel_type,
            hashtags: channel.hashtags,
            social_media: channel.social_handles,
            subscriptions: transformApiResponse(channel.subscription),
            from: "",
            saved: false,
            nextBtnText: "",
            perks: channel.perks,
            services: channel.services,
            counter: channel.counter,
            my_subscription_data: channel.my_subscription_data,
            free_services: channel.free_services ?? [],
          })
        );
      })
      .catch(() => {});
  }, [params.id]);

  return ChannelView({
    edit: true,
    from: `/channel/${params.id}/edit`,
    fromTitle: "Edit my channel",
    fromBtnText: "Next",
    nextBtnText: "Save Changes",
    navigateTo: `/channel/${params.id}/edit/preview`,
  });
}
