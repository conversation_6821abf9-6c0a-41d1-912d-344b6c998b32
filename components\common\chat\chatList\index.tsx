import { useRouter, useSearchParams } from "next/navigation";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

import { getChatUserList } from "@/api/chat";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Chat } from "@/redux-store/slices/chat.slice";
import { getAssetUrl } from "@/utils/assets";

import { OnlineDot } from "../../OnlineDot";
import Badges from "../../badges";
import JoinUs from "../../join-us";
import LoadingSpinner from "../../loading";
import { DeleteIcon } from "../chat-box/utils/svg-utils";
import styles from "./chatList.module.css";

interface ChatListProps {
  setActiveChat: (chatId: string) => void;
  showChats: (userId: string, fetch?: boolean) => void;
  activeChat: string;
  deleteChannel: (channelId: string) => void;
}

export const SubscriptionPlans: Record<string, string> = {
  HALF_YEARLY: "6 Months",
  QUARTERLY: "3 Months",
  MONTHLY: "1 Month",
  YEARLY: "1 Year",
};

export const sortAndFilterChatList = (
  chatList: Chat[],
  queryUser: string | null = null
): Chat[] => {
  return chatList
    .filter((chat: Chat) => chat?.converse_channel_id !== null)
    .sort(
      (a: Chat, b: Chat) =>
        new Date(
          (b?.message?.createdAt ||
            b?.complete_messages?.[b?.complete_messages.length - 1]
              .createdAt) ??
            0
        ).getTime() -
        new Date(
          (a?.message?.createdAt ||
            a?.complete_messages?.[a?.complete_messages.length - 1]
              .createdAt) ??
            0
        ).getTime()
    )
    .filter(
      (chat: Chat) =>
        chat?.message ||
        (queryUser &&
          (queryUser === chat?.initiator?._id ||
            queryUser === chat?.target?._id))
    );
};

const ChatList: React.FC<ChatListProps> = memo(
  ({ setActiveChat, showChats, activeChat, deleteChannel }) => {
    const user = useAppSelector((state) => state.user);
    const chatData = useAppSelector((state) => state.chat);
    const blockedUser = useAppSelector((state) => state.block.blockedByMe);
    const blockedMe = useAppSelector((state) => state.block.hasBlockedMe);
    const targetUser = useAppSelector((s) => s.chat.targetUser);
    const chatList = useAppSelector((s) => s.chat.chatList);

    const dispatch = useAppDispatch();
    const router = useRouter();
    const userChatQuery = useSearchParams();
    const location = useSearchParams();

    const [finalChatList, setFinalChatList] = useState<Chat[]>([]);
    const [tabSelected, setTabSelected] = useState<number>(0);

    const [swipedChatId, setSwipedChatId] = useState<string>("");
    const chatLoading = useAppSelector((state) => !state.chat.isLoaded);

    const [page, setPage] = useState(2);
    const [hasMore, setHasMore] = useState(true);

    const touchCoords = useRef({ startX: 0, startY: 0, endX: 0, endY: 0 });

    const handleTouchStart = (e: React.TouchEvent) => {
      touchCoords.current.startX = e.touches[0].clientX;
      touchCoords.current.startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e: React.TouchEvent, chatId: string) => {
      touchCoords.current.endX = e.changedTouches[0].clientX;
      touchCoords.current.endY = e.changedTouches[0].clientY;

      const deltaX = touchCoords.current.endX - touchCoords.current.startX;
      const deltaY = touchCoords.current.endY - touchCoords.current.startY;

      // Check if the gesture is primarily horizontal (swipe)
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 30) {
        // Only handle swipe if horizontal distance is significant
        handleSwipe(chatId);
      }
    };

    const handleSwipe = (chatId: string) => {
      if (swipedChatId === chatId) {
        setSwipedChatId("");
      } else {
        setSwipedChatId(chatId);
      }
    };

    const handleTabSelected = useCallback((tab: number) => {
      setTabSelected(tab);
    }, []);

    useEffect(() => {
      filterChatList();
    }, [chatData.chatList, tabSelected, user.role]);

    useEffect(() => {
      if (location.has("user")) {
        router.replace(`/chat?user=${chatData?.targetUser}`);
      }
    }, [chatData?.targetUser, location, router]);

    useEffect(() => {
      initializeChat();
    }, []);

    const filterChatList = () => {
      const copyList: any[] = chatData.chatList;
      let filteredList: Chat[] = [];

      if (user.role === "creator") {
        if (tabSelected === 1) {
          filteredList = copyList.filter((chat) => chat?.is_subscriber);
        } else if (tabSelected === 2) {
          filteredList = copyList.filter((chat) => chat?.is_subscribed);
        } else if (tabSelected === 3) {
          filteredList = copyList.filter((chat) => chat?.is_follower);
        } else {
          filteredList = copyList;
        }
      } else if (user.role === "user") {
        if (tabSelected === 1) {
          filteredList = copyList.filter((chat) => chat?.is_subscribed);
        } else if (tabSelected === 2) {
          filteredList = copyList.filter((chat) => chat?.is_following);
        } else if (tabSelected === 3) {
          filteredList = copyList.filter((chat) => chat?.is_matched);
        } else {
          filteredList = copyList;
        }
      }

      setFinalChatList(filteredList);
    };

    const initializeChat = async () => {
      if (userChatQuery?.get("user")) {
        await handleExistingUser();
      } else {
        await handleNewChat();
      }
    };

    const handleExistingUser = async () => {
      const queryUser = userChatQuery?.get("user") as string;
      if (blockedUser[queryUser] || blockedMe[queryUser]) return;

      console.log("%cHere...", "font-size:1.5rem; color:Red;");

      try {
        showChats(queryUser);
        await updateChatList(queryUser);
      } catch (error) {
        console.error("Error handling existing user:", error);
      }
    };

    const handleNewChat = async () => {
      try {
        let checkLatestChatList = [];

        if (chatList.length) {
          checkLatestChatList = chatList;
        } else {
          checkLatestChatList = (await getChatUserList()).data;
        }

        const sortedChatList = sortAndFilterChatList(checkLatestChatList);
        updateTotalUnreadCount(sortedChatList);
        dispatch(
          chatActions.setChatUserList(
            sortAndFilterChatList(checkLatestChatList)
          )
        );

        if (window.innerWidth > 768) {
          await handleDesktopNewChat(sortedChatList);
        }
      } catch (error) {
        console.error("Error handling new chat:", error);
      }
    };

    const updateChatList = async (queryUser: string) => {
      const sortedChatList = sortAndFilterChatList(chatList, queryUser);
      updateTotalUnreadCount(sortedChatList);
      dispatch(chatActions.setChatUserList(sortedChatList));

      const index = findUserIndex(sortedChatList, queryUser);

      if (index !== -1) {
        updateUnreadCountAndActiveChat(sortedChatList[index]);
      }

      dispatch(chatActions.setTargetUser(queryUser));
    };

    const updateTotalUnreadCount = (chatList: Chat[]) => {
      const totalCount = chatList.reduce(
        (total, chat) => total + (chat?.unread_count || 0),
        0
      );
      dispatch(chatActions.setTotalCount(totalCount));
    };

    const findUserIndex = (chatList: Chat[], queryUser: string): number => {
      return chatList.findIndex(
        (channelData) =>
          queryUser === channelData.initiator._id ||
          queryUser === channelData.target._id
      );
    };

    const updateUnreadCountAndActiveChat = (chat: Chat) => {
      dispatch(
        chatActions.updateUnreadCount({
          id: chat.converse_channel_id,
          count: 0,
        })
      );
      const activeChatId =
        user.id === chat?.target?._id
          ? chat?.initiator?._id
          : chat?.target?._id;
      setActiveChat(activeChatId);
    };

    const handleDesktopNewChat = async (sortedChatList: Chat[]) => {
      if (sortedChatList.length > 0) {
        const firstChat = sortedChatList[0];
        updateUnreadCountAndActiveChat(firstChat);
        dispatch(chatActions.setChatUserList(sortedChatList));

        const targetUserId =
          user.id === firstChat?.target?._id
            ? firstChat?.initiator?._id
            : firstChat?.target?._id;

        try {
          showChats(targetUserId);
        } catch (error) {
          console.error("Error handling desktop new chat:", error);
        }
      }
    };

    const renderTabs = () => {
      const tabs =
        user.role === "creator"
          ? ["All", "Subscribers", "Subscribed", "Followers"]
          : ["All", "Subscribed", "Following", "Matches"];

      return (
        <div
          className="d-flex justify-content-between border-bottom w-100 overflow-scroll position-sticky top-0 bg-body z-1"
          id="chat-tabs"
        >
          {tabs.map((tab, index) => (
            <div
              key={tab}
              className={`p-3 pointer ${
                tabSelected === index ? `${styles["chat-tab-active"]}` : ""
              }`}
              onClick={() => handleTabSelected(index)}
            >
              {tab}
            </div>
          ))}
        </div>
      );
    };

    const renderChatItem = ({
      chat,
      user,
      showChats,
      isSwiped,
    }: {
      chat: Chat;
      user: any;
      showChats: (userId: string) => void;
      isSwiped: boolean;
    }) => {
      const isActiveChat =
        (chat?.target?.display_name && chat?.target?._id === activeChat) ||
        chat?.initiator?._id === activeChat;
      const chatPartner =
        user.id === chat?.target?._id ? chat?.initiator : chat?.target;

      const handleDelete = () => {
        deleteChannel(chat.converse_channel_id);
      };

      return (
        <React.Fragment key={chat._id}>
          <div
            className={`${isSwiped ? " " : styles.chatItem} ${
              isSwiped ? styles.swiped : ""
            } d-flex align-items-center justify-content-between w-100 pointer position-relative mt-2 ${
              chatLoading && window.innerWidth >= 576 ? "disabled" : ""
            }`}
            onTouchStart={handleTouchStart}
            onTouchEnd={(e) => handleTouchEnd(e, chat._id)}
            onClick={() => {
              if (chatPartner?._id === targetUser && window.innerWidth > 768)
                return;
              showChats(chatPartner?._id);
            }}
          >
            <div
              className={
                isActiveChat
                  ? "chat-person d-flex my-2 pointer active"
                  : "chat-person d-flex my-2 pointer inactive"
              }
            >
              <div style={{ position: "relative" }}>
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  width={48}
                  height={48}
                  className="rounded-pill object-fit-cover"
                  alt=""
                  src={getAssetUrl({
                    media: chatPartner?.avatar?.[0],
                    defaultType: "avatar",
                  })}
                />
                <OnlineDot
                  userId={chatPartner?._id}
                  style={{
                    width: "14px",
                    height: "14px",
                    position: "absolute",
                    bottom: "0%",
                    right: "0%",
                  }}
                />
              </div>
              <div className="person-details ms-2">
                <span className="mb-0 d-flex gap-1 align-items-center">
                  <span className="profile-last-message ">
                    {chatPartner?.display_name}
                  </span>
                  <Badges array={chatPartner?.badges} />
                  {chat?.package?.type && (
                    <div
                      className="badge"
                      style={{
                        color: "rgba(128, 131, 134, 1)",
                        background: "rgba(235, 235, 236, 1)",
                      }}
                    >
                      {SubscriptionPlans[chat?.package?.type] || ""}
                    </div>
                  )}
                </span>
                <div
                  className="d-flex justify-content-between align-items-center gap-2"
                  ref={(el) => {
                    if (el) {
                      el.style.setProperty("color", "#000", "important");
                    }
                  }}
                >
                  {chat?.message?.meta?.delete_for !== user.id && (
                    <div
                      className={`profile-last-message fs-7  ${
                        chat.unread_count ? "fw-bold" : ""
                      }`}
                      style={{
                        maxWidth:
                          !chat?.buyers?.some((b) => b.buyer === user.id) &&
                          chatPartner?.user_type === "CREATOR"
                            ? "20ch"
                            : "25ch",
                      }}
                    >
                      {chat?.message?.meta?.chat_list_message ||
                        (typeof chat.message === "string" && chat.message) ||
                        (typeof chat.lastmessage === "string" &&
                          chat.lastmessage) ||
                        (typeof chat.lastmessage === "object" &&
                          chat.lastmessage?.message) ||
                        (typeof chat.message === "object" &&
                          chat.message?.message)}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div
              className="unreads me-3 fs-8 background-light p-1 rounded-pill position-absolute end-0"
              style={{
                top: window.innerWidth <= 576 ? 0 : "10px",
              }}
            >
              {chat.unread_count > 9 ? "9+" : chat.unread_count || null}
            </div>
            <div
              className={styles.deleteButton}
              onClick={(e) => {
                e.stopPropagation();
                handleDelete();
              }}
            >
              <DeleteIcon color="red" />
            </div>
          </div>
          <hr className="mx-3 text-dark text-opacity-50" />
        </React.Fragment>
      );
    };

    const fetchMoreData = () => {
      if (page > 1) {
        getChatUserList(page)
          .then((res) => {
            if (res.data.length === 0) {
              setHasMore(false);
              return;
            }

            const sortedChatList = sortAndFilterChatList(res.data);
            updateTotalUnreadCount(sortedChatList);
            dispatch(
              chatActions.setChatUserList(
                sortAndFilterChatList([...finalChatList, ...sortedChatList])
              )
            );
            setPage(page + 1);
          })
          .catch((error) => {
            console.log(error);
          });
      }
    };

    return (
      <div
        className={"chat-list flex-grow-1 overflow-y-auto h-100"}
        id="chat-list"
      >
        <InfiniteScroll
          dataLength={finalChatList.length}
          hasMore={hasMore}
          next={fetchMoreData}
          className="h-100"
          loader={
            <div className="d-flex w-100 justify-content-center align-items-center">
              <LoadingSpinner />
            </div>
          }
          scrollableTarget="chat-list"
        >
          {renderTabs()}
          {user.id ? (
            finalChatList.length > 0 ? (
              finalChatList.map((chat: Chat) =>
                renderChatItem({
                  chat,
                  user,
                  showChats,
                  isSwiped: swipedChatId === chat._id,
                })
              )
            ) : (
              <div
                className="text-center text-md d-flex justify-content-center align-items-center"
                ref={(el) => {
                  if (el) {
                    const big = document.getElementById("chat-list");
                    const small = document.getElementById("chat-tabs");

                    if (!big || !small) return;
                    el.style.setProperty(
                      "height",
                      `${big?.clientHeight - small?.clientHeight}px`,
                      "important"
                    );
                  }
                }}
              >
                Chat list is empty {":("}
              </div>
            )
          ) : (
            <JoinUs />
          )}
        </InfiniteScroll>
      </div>
    );
  }
);

ChatList.displayName = "ChatList";

export default ChatList;
