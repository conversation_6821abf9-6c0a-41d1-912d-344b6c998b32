/* components/DatalistDropdown.module.css */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown input {
  width: 100%;
  box-sizing: border-box;
}

.dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
  left: -35px;
  width: 160px;
  border: 1px solid #ccc;
  max-height: 190px;
  overflow-y: auto;
  background: white;
  z-index: 3;
  border-radius: 8px;
}

.dropdown li {
  padding: 8px;
  cursor: pointer;
}

.dropdown li:hover {
  background: #f0f0f0;
}

.errorMsg {
  position: absolute;
  bottom: -20px;
  color: red;
  font-size: 0.8rem;
}
