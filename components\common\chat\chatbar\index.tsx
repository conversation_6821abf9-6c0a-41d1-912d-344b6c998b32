import { memo } from "react";

import { useAppSelector } from "@/redux-store/hooks";

import "./chatbar.scss";
import CreatorChatBar from "./CreatorChatBar";
import NormalChatBar from "./NormalChatBar";

const views = {
  guest: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  creator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  user: <PERSON><PERSON>hat<PERSON><PERSON>,
  anonymous: NormalChatBar,
};

type UserRole = "guest" | "creator" | "user" | "anonymous";

const ChatBar = ({ chattingFeeRef }: any) => {
  const userRole = useAppSelector((state) => state.user.role) as UserRole;

  const CurrentChatBar = views[userRole];
  return (
    <>
      <CurrentChatBar chattingFeeRef={chattingFeeRef} />
    </>
  );
};

export default memo(ChatBar);
