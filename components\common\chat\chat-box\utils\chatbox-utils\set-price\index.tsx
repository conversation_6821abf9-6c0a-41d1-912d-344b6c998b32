import {
  AudioSent,
  VideoSent,
} from "@/components/matchmaker/match-cards/utils/svg";
import { MessageInterface } from "@/redux-store/slices/chat.slice";
import { RatingSent } from "../rating-request";

const SetPrice = ({
  message,
  usedAs = "sender",
}: {
  message: MessageInterface;
  usedAs?: "sender" | "receiver";
}) => {
  const getIcon = {
    VIDEO: <VideoSent />,
    VOICE: <AudioSent />,
    RATING: <RatingSent />,
    "CUSTOM-SERVICE": (
      <img
        src={"/images/chat/service-icon.png"}
        height={72}
        width={72}
        alt="Request Icon"
        className="rounded"
      />
    ),
  };

  return (
    <div className="d-flex gap-2 p-2 pb-0 align-items-center">
      <div>{getIcon[message?.meta?.subtype || "CUSTOM-SERVICE"]}</div>
      {usedAs === "sender" && <div>Hey! I am interested in the service</div>}
      {usedAs === "receiver" && (
        <div>
          <div>The user has shared their interest for the request</div>
          {/* {!message?.meta?.hasSetPrice && (
            <div
              className="text-primary underline pointer"
              onClick={() => {
                ModalService.open("ADD_SERVICES", {
                  editValues: message?.meta?.serviceData!,
                  cb: () => {
                    socketChannel.channel?.editMessage({
                      message_id: message._id || message?.messageId,
                      data: {
                        ...message,
                        meta: {
                          ...message?.meta,
                          hasSetPrice: true,
                        },
                      },
                    });
                  },
                });
              }}
            >
              Set Price
            </div>
          )} */}
        </div>
      )}
    </div>
  );
};

export default SetPrice;
