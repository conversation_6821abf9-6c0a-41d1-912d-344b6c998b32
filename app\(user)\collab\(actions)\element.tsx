"use client";

import Big from "big.js";
import { <PERSON><PERSON>rray, Formik } from "formik";
import debounce from "lodash/debounce";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import * as Yup from "yup";
import "./index.scss";

import {
  AddGroupMembers,
  type AddMembersBody,
  CancelLeaveGroup,
  LeaveGroup,
} from "@/api/group";
import { GetUserFollowing, getUserProfile } from "@/api/user";
import ErrorMes from "@/components/common/error";
import Form from "@/components/common/form";
import Input from "@/components/common/input";
import InputArray from "@/components/common/input-array";
import KYCwarning from "@/components/common/KYC-warning";
import InputGroup from "@/components/common/list";
import Modal, { type ModalRef } from "@/components/common/modal";
import Select, { type SelectProps } from "@/components/common/select";
import SelectArray from "@/components/common/select-array";
import type { PossibleValues } from "@/components/common/select-array-v2";
import SelectArrayV2 from "@/components/common/select-array-v2";
import TextArea from "@/components/common/textarea";
import Wrapper, { Divider } from "@/components/common/wrapper";
import {
  SubscriptionTime,
  SubscriptionType,
  TrialPeriod,
} from "@/global/constants";
import { configActions, createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Navigators } from "@/redux-store/slices/create.slice";
import type { Group, Member } from "@/types/profile";
import { getAssetUrl } from "@/utils/assets";

const addMember = {
  func: (_index: number, _el: any) => {},
  existingMembers: {} as Record<string, any>,
  maxPercent: 100,
  existingsMembersArray: [] as Member[],
};

interface GroupViewParams {
  id?: string;
  edit: boolean;
  from: string;
  navigateTo: string;
  fromTitle: string;
  fromBtnText: string;
  nextBtnText: string;
}

interface MemberDetails {
  name: string;
  username: string;
  pic: string;
  role: string;
  display_name: string;
}

export default function GroupView(params: GroupViewParams) {
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  const user = useAppSelector((s) => s.user);
  const groupInformation = useAppSelector((s) => s.defaults.group_profile);
  const initialValues: Group & Navigators = useAppSelector(
    (state) => state.create.group
  );

  useEffect(() => {
    dispatchAction(configActions.toggleShowPositionWidget());
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowPositionWidget());
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  const [membersHash, setMembersHash] = useState(
    {} as Record<string, MemberDetails>
  );
  const [memberList, setMemberList] = useState([] as SelectProps[]);
  // const [distribution, setDistribution] = useState<"equal" | "custom">("equal");
  const possibleValues = [
    "discount",
    "phone",
    "show",
    "start",
    "video",
    "chat",
    "media",
    "public",
    "video-play",
    "voice",
    "custom",
  ] as const;

  const shape: Record<string, any> = {
    name: Yup.string()
      .required("Collab Name is required")
      .min(3, "Collab username must be at least 3 characters")
      .matches(
        /^[\p{L} '’-]{3,40}$/u,
        "Collab name must be 3-40 characters long and contain only letters, spaces, apostrophes, and dashes."
      ),
    username: Yup.string()
      .required("Collab Username is required")
      .min(5, "Collab username must be at least 5 characters")
      .matches(
        /^[\p{L}'’-]{5,40}$/u,
        "Collab username must be 5-40 characters long and contain only letters, apostrophes, and dashes."
      ),
    topic: Yup.string(),
    description: Yup.string()
      .required("Description is required.")
      .max(3000, "Description must be at most 3000 characters")
      .min(10, "Description must be at least 10 characters"),
    members: Yup.array()
      .of(
        Yup.object().shape({
          earning: Yup.number()
            .required("Earning is required")
            .min(0, "Earning must be at least 0"),
        })
      )
      .min(2, "At least two members are required")
      .required("Members are required")
      .test(
        "total-earnings",
        "Total earning percentage of collab members should not exceed 100",
        function (members) {
          if (!members || members.length === 0) {
            return true;
          }

          let total = 0;
          members.forEach((val: any) => {
            const { earning } = val;

            if (typeof earning !== "number") {
              return this.createError({
                path: `members.${members.indexOf(val)}.earning`,
                message: "Earning should be a number",
              });
            }

            total += Number(new Big(earning).toFixed(2));
          });

          if ((total > 99.8 && total <= 100) || !params?.edit) {
            return true;
          }
        }
      ),
    hashtags: Yup.array()
      .min(1, "At least one hashtag required")
      .required("Hashtags is required"),
    totalEarning: Yup.number()
      // .required()
      .test("total-earn", "not correct", function () {
        const { members } = this.parent;

        if (!Array.isArray(members)) {
          return this.createError({
            path: `members`,
            message: "Members should be an array",
          });
        }

        let total = 0;
        members.forEach((val: any) => {
          const { earning } = val;

          if (typeof earning !== "number") {
            return this.createError({
              path: `members.${members.indexOf(val)}.earning`,
              message: "Earning should be a number",
            });
          }

          total += Number(new Big(earning).toFixed(2));
        });

        // if (!params.edit)
        if (total > 99.8 && total <= 100) {
          return true;
        }

        return this.createError({
          path: `members.${members.length - 1}.earning`,
          message: "Total earning percentage of collab members should be 100",
        });
      }),
    subscription_perks: Yup.array()
      .of(
        Yup.object().shape({
          icon: Yup.mixed<PossibleValues>()
            .oneOf(possibleValues, "Invalid icon")
            .required("Icon is required"),
          value: Yup.string().required("Value is required"),
        })
      )
      .min(1, "At least one perk is required")
      .required("Subscription perks are required"),
    subscribe: Yup.object().shape({
      array: Yup.array().when("offer_type", {
        is: "FREE",
        then: () => Yup.array().strip(),
        otherwise: () => Yup.array().min(1).required(),
      }),
    }),
  };

  let _total = 0;

  initialValues.members.forEach((val, i) => {
    shape[`members.${i}.earning`] = Yup.number()
      .min(1, "Share should be positive")
      .max(100, "Can't be more than 100");
    _total += val.earning;
  });

  const validationSchema = Yup.object().shape(shape);

  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );
  const addNewMemberModalRef = { method: {} as ModalRef };
  const [updateKey, setUpdateKey] = useState(0);
  const [prevKey, setPrevKey] = useState(-1);
  const [addedMember, _setaddedMember] = useState<Member[]>([]);
  /**
   * This hook is designed to handle scenarios where the modal was not re-rendering
   * as expected. It ensures that the modal is opened when the addNewMemberModalRef state changes
   * that changes when the updateKey states changes.
   */
  useEffect(() => {
    if (updateKey === 0 || updateKey === prevKey) return;

    if (!isKycCompleted) {
      Swal.fire({
        title: "Get Verified to add members.",
        icon: "info",
        confirmButtonText: "KYC",
        confirmButtonColor: "#ac1991",
      }).then((res) => {
        if (res.isConfirmed) {
          router.push("/settings/kyc");
        }
      });
    } else {
      // If updateKey is 0 or hasn't changed, do nothing

      // Set prevKey to the current updateKey to avoid duplicate triggers
      setPrevKey(updateKey);

      addNewMemberModalRef.method.open();
    }
  }, [addNewMemberModalRef]);

  useEffect(() => {
    GetUserFollowing().then((response: any) => {
      const members = {} as Record<string, MemberDetails>;

      const memberList = response.data.map((member: any) => {
        members[member._id] = {
          name: `${member.f_name} ${member.l_name}`,
          username: member.username,
          pic: getAssetUrl({
            media: member.avatar?.[0],
            defaultType: "avatar",
          }),
          role: member.role,
          display_name: member.display_name,
        };

        return {
          text: members[member._id].display_name,
          value: member._id,
        };
      });

      setMembersHash(members);
      setMemberList(memberList);
    });
  }, []);

  return (
    <>
      <Formik
        enableReinitialize={!initialValues.saved}
        initialValues={initialValues}
        validationSchema={validationSchema}
        validateOnChange={true}
        validateOnBlur={true}
        onSubmit={(data: Group & Navigators) => {
          router.push(params.navigateTo);

          dispatchAction(
            createActions.setGroup({
              ...data,
              saved: true,
              from: params.from,
              nextBtnText: params.nextBtnText,
            })
          );
        }}
      >
        {({ values, setFieldValue, ...rest }) => {
          // eslint-disable-next-line react-hooks/rules-of-hooks

          const handleEqualDistribution = () => {
            setFieldValue("distribution", "equal");

            function distributeShares(members: number) {
              const totalPercentage = new Big(100);
              const baseShare = totalPercentage.div(members).round(2);

              const shares = [];

              for (let i = 0; i < members; i++) {
                shares.push(Big(baseShare));
              }

              const sumShares = shares.reduce(
                (acc, val) => acc.plus(val),
                new Big(0)
              );
              const difference = totalPercentage.minus(sumShares);

              shares[shares.length - 1] =
                shares[shares.length - 1].plus(difference);

              return shares.map((share) => parseFloat(share.toFixed(2)));
            }

            if (values.members.length) {
              const equalEarning = distributeShares(values.members.length);

              values.members.forEach((_, i) => {
                setFieldValue(`members.${i}.earning`, equalEarning[i]);
              });
            }
          };

          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            if (values.members.length === 0) {
              setFieldValue("members", [
                {
                  earning: 100,
                  member_type: "MEMBER",
                  member_id: {
                    _id: user.id,
                    display_name: user.profile.display_name,
                    username: user.profile.username,
                    avatar: user.profile.avatar,
                    user_type: user.role,
                  },
                },
              ]);
            }

            if (values.distribution === "equal") {
              handleEqualDistribution();
            }
          }, [values.distribution, values.members.length]);

          return (
            <Form
              title={params.fromTitle}
              nextBtnText={params.fromBtnText}
              dirty={values.saved}
              formikValues={{ values, setFieldValue, ...rest }}
            >
              <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
                <Wrapper title="">
                  <div className="position-relative">
                    <Input
                      name="name"
                      label="Collab Name"
                      placeholder="Enter a name for your collab"
                      customErrorClassname="color-red fs-8 ms-1"
                    />
                  </div>
                  <div className="position-relative">
                    <Input
                      name="username"
                      label="Collab Username"
                      placeholder="Enter your desired @username"
                      disable={params.edit}
                    />
                    <ErrorMes
                      name="username"
                      className="position-absolut top-100 pt-1 color-red fs-8 lh-1 left-0 error"
                    />
                  </div>
                </Wrapper>

                <Wrapper
                  title="About collab"
                  counter={{ value: values.description, max: 3000 }}
                  required
                >
                  <TextArea
                    name="description"
                    type="text"
                    required={false}
                    placeholder="Tell something about your collab profile here"
                    disabled={
                      groupInformation?.deletion_request &&
                      groupInformation.deletion_request.some((request) => {
                        return request.requested_by === user.id;
                      })
                    }
                  />
                </Wrapper>
                <Wrapper title="Hashtags">
                  <InputArray
                    name="hashtags"
                    label={"Add relevant hashtags"}
                    placeholder="#enter_new_tag_here"
                    required={true}
                    replacer={(value: string) =>
                      `#${value.replace(/[^a-z0-9]+/g, "")}`
                    }
                    values={values.hashtags}
                    disable={
                      groupInformation?.deletion_request &&
                      groupInformation.deletion_request.some((request) => {
                        return request.requested_by === user.id;
                      })
                    }
                  />
                </Wrapper>
              </div>
              <div
                className="d-flex col-lg-5 flex-column gap-3 flex-grow-1"
                style={{
                  maxHeight: window.innerWidth < 768 ? "100vh" : "",
                  overflowY: "auto",
                }}
              >
                <Wrapper title="Add your subscription plans">
                  {/* <span className="mb-n1">Subscribe fee</span> */}
                  <InputGroup
                    label=""
                    type="radio"
                    required={false}
                    theme="circle-purple"
                    name="subscribe.offer_type"
                    className="align-items-sm-start mt-2 "
                    childClass="ms-1"
                    array={SubscriptionType}
                    disabled={params?.edit}
                    selected={values.subscribe.offer_type}
                  />
                  {values.subscribe.offer_type === "FREE_TRIAL" ? (
                    <Select
                      label="Free trial in"
                      name="subscribe.trial_period"
                      required={true}
                      array={TrialPeriod}
                      disabled={params?.edit}
                      selected={values.subscribe.trial_period || ""}
                      setFieldValue={setFieldValue}
                    />
                  ) : (
                    <></>
                  )}
                  {values.subscribe.offer_type !== "FREE" && (
                    <Divider className="fs-10 mt-n2" />
                  )}

                  {values.subscribe.offer_type === "FREE" ? (
                    <></>
                  ) : (
                    <>
                      {/* <span className="mb-n1">Subscribe offers</span>
                    <Input
                      name="subscribe.price"
                      type="number"
                      label="Price per month"
                      required={true}
                      placeholder="0"

                      disabled={params?.edit}
                    >
                    </Input>
                    */}
                      <span className="color-medium fs-8">
                        Minimum $5.00 USD
                      </span>
                      <span className="mb-n1">
                        Applies when users subscribe to you
                      </span>
                      <SelectArray
                        label="Complete your plans with pricing"
                        name="subscribe.array"
                        placeholder="Price "
                        objectKey="subscription_type"
                        objectValueKey="price"
                        keyClass="col-5"
                        objectValueType="number"
                        disable={params?.edit}
                        options={SubscriptionTime}
                        values={values.subscribe.array}
                        keyPlaceholder={"Select time package"}
                        inputType="number"
                        optionalInitialValue={{
                          key: "MONTHLY",
                          value: 5,
                        }}
                      />
                    </>
                  )}
                </Wrapper>

                <Wrapper
                  title="Subscription Perks"
                  required
                  className="position-relative"
                >
                  <span className="color-medium fs-7">
                    Apply when people subscribe your collab.
                  </span>
                  {values?.subscription_perks.length === 0 && (
                    <span className="color-red fs-8">
                      Enter perk description
                    </span>
                  )}
                  {values?.subscription_perks.some((v) => v.value === "") && (
                    <span className="color-red fs-8 position-absolute bottom-0">
                      Subscription perks&apos; value is required
                    </span>
                  )}
                  <SelectArrayV2
                    setter={setFieldValue}
                    name="subscription_perks"
                    getter={values.subscription_perks}
                    isEdit={params.edit}
                  />
                </Wrapper>
                <Wrapper
                  title="Member and Earning percent"
                  click={
                    !params.edit
                      ? {
                          text: "Add new member",
                          event: () => {
                            dispatchAction(
                              createActions.resetMember(values.members.length)
                            );
                            setUpdateKey(Math.random());
                          },
                        }
                      : { text: "", event: () => {} }
                  }
                  isAddRequired={!params.edit}
                  disabled={values.members.length >= 2}
                >
                  <div className="fs-7 color-medium">
                    Setup collab profile member and earning percent of each
                    member. (Maximum 2 members){" "}
                    <span className="color-red">*</span>
                  </div>
                  <FieldArray
                    name="members"
                    render={({ replace, remove }) => {
                      addMember.func = replace;
                      addMember.existingMembers = values.members.reduce(
                        (obj, val) => {
                          obj[val.member_id._id] = true;
                          return obj;
                        },
                        {} as Record<string, boolean>
                      );
                      addMember.existingsMembersArray = values.members;

                      addMember.maxPercent = Number(
                        new Big(100).minus(
                          values.members
                            .reduce((acc, val) => {
                              const earning =
                                // @ts-expect-error-string-in-bg-js
                                val.earning === "" || val.earning == null
                                  ? 0
                                  : val.earning;
                              return acc.plus(new Big(earning));
                            }, new Big(0))
                            .toFixed(2)
                        )
                      );

                      return values.members.length > 0 ? (
                        <div className="d-flex flex-column gap-3">
                          {!params.edit && (
                            <>
                              <InputGroup
                                label=""
                                type="radio"
                                required={false}
                                theme="circle-purple"
                                name="distribution"
                                className="align-items-sm-start "
                                childClass="ms-1"
                                array={[
                                  { text: "Equal", value: "equal" },
                                  { text: "Custom", value: "custom" },
                                ]}
                                disabled={params?.edit}
                                selected={values.distribution!}
                              />
                              {Number(
                                new Big(100).minus(
                                  values.members
                                    .reduce((acc, val) => {
                                      const earning =
                                        // @ts-expect-error-string-in-bg-js
                                        val.earning === "" ||
                                        val.earning == null
                                          ? 0
                                          : val.earning;
                                      return acc.plus(new Big(earning));
                                    }, new Big(0))
                                    .toFixed(2)
                                )
                              ) < 0 && (
                                <div className="color-red fs-8">
                                  Distribution can&apos;t be greater than 100
                                </div>
                              )}
                              {values.members.reduce(
                                (acc, val) => acc + val.earning,
                                0
                              ) < 99.8 && (
                                <div className="color-red fs-8">
                                  Distribution should be equal to 100
                                </div>
                              )}
                            </>
                          )}

                          {values.members.map((member, i) => (
                            <div className="" key={member.member_id._id}>
                              <div className="d-flex flex-column justify-content-between gap-3">
                                <div className="d-flex gap-3">
                                  {/* eslint-disable-next-line @next/next/no-img-element */}
                                  <img
                                    src={
                                      membersHash[member.member_id._id]?.pic ||
                                      getAssetUrl({
                                        media:
                                          values?.members[i].member_id
                                            .avatar[0],
                                      }) ||
                                      getAssetUrl({
                                        media: member?.member_id?.avatar[0],
                                      })
                                    }
                                    alt={"author image"}
                                    className="rounded-pill object-fit-cover"
                                    width={56}
                                    height={56}
                                  />
                                  <div className="d-flex flex-column color-medium">
                                    <div className="d-flex gap-2 align-items-center">
                                      <span className="fs-5 color-bold fw-semibold">
                                        {member?.member_id?.display_name ||
                                          values?.members[i]?.member_id?.f_name}
                                      </span>
                                      {params.edit &&
                                        groupInformation?.deletion_request &&
                                        groupInformation.deletion_request.some(
                                          (request) => {
                                            return (
                                              request.requested_by ===
                                              member.member_id._id
                                            );
                                          }
                                        ) && (
                                          <span className="badge rounded-pill text-bg-danger">
                                            Requested to Leave
                                          </span>
                                        )}
                                      {params.edit &&
                                        !member.terms_accepted && (
                                          <span className="badge rounded-pill text-bg-warning">
                                            Request Pending
                                          </span>
                                        )}
                                      {!params.edit &&
                                        member.member_id._id !== user.id && (
                                          <span
                                            className="fs-6 text-decoration-underline pointer"
                                            onClick={() => {
                                              const equalEarning = Number(
                                                (
                                                  100 /
                                                  (values.members.length - 1)
                                                ).toFixed(2)
                                              );

                                              if (
                                                values.distribution === "equal"
                                              ) {
                                                addMember.existingsMembersArray.forEach(
                                                  (_, i) => {
                                                    addMember.func(i, {
                                                      ...addMember
                                                        .existingsMembersArray[
                                                        i
                                                      ],
                                                      earning: equalEarning,
                                                    });
                                                  }
                                                );
                                              }

                                              remove(i);
                                            }}
                                          >
                                            Remove
                                          </span>
                                        )}
                                    </div>
                                    <div>
                                      @
                                      {member?.member_id?.username ||
                                        membersHash[member.member_id._id]
                                          ?.username ||
                                        values?.members[i]?.member_id?.username}
                                    </div>
                                  </div>
                                </div>
                                <Input
                                  name={`members.${i}.earning`}
                                  label="Earning Percent"
                                  percentageInput
                                  placeholder="0"
                                  min={1}
                                  type="number"
                                  disable={
                                    values.distribution === "equal" ||
                                    params.edit
                                  }
                                />
                              </div>
                              <Divider
                                visible={values.members.length - 1 > i}
                              />
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div>No Member Selected yet.</div>
                      );
                    }}
                  />
                </Wrapper>
                {params.edit ? (
                  groupInformation.members.some(
                    (member) =>
                      member.member_id._id === user.id && member.terms_accepted
                  ) && (
                    <button
                      type="button"
                      onClick={() => {
                        if (
                          groupInformation?.deletion_request &&
                          groupInformation.deletion_request.some((request) => {
                            return request.requested_by === user.id;
                          })
                        ) {
                          Swal.fire({
                            title:
                              "Are you sure you want to cancel your leave request?",
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#3085d6",
                            cancelButtonColor: "#d33",
                            confirmButtonText: "Yes, cancel request!",
                          }).then((result) => {
                            if (result.isConfirmed) {
                              CancelLeaveGroup(groupInformation._id)
                                .then(() => {
                                  Swal.fire({
                                    icon: "success",
                                    title:
                                      "Leave request canceled successfully.",
                                    confirmButtonColor: "#ac1991",
                                    confirmButtonText: "Go to Collab",
                                  }).then(() => {
                                    router.push(
                                      `/collab/${groupInformation.username}`
                                    );
                                  });
                                })
                                .catch((err) => console.error(err));
                            }
                          });
                        } else
                          Swal.fire({
                            title:
                              "Are you sure you want to leave this collab?",
                            text: "You will be removed from the collab and you won't be able to access any posts or collaborations.",
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#3085d6",
                            cancelButtonColor: "#d33",
                            customClass: {
                              confirmButton: "custom-btn",
                            },
                            confirmButtonText: "Yes, leave collab",
                          }).then((result) => {
                            if (result.isConfirmed) {
                              LeaveGroup(groupInformation._id)
                                .then(() => {
                                  Swal.fire({
                                    icon: "success",
                                    title:
                                      "You have successfully left the collab.",
                                    confirmButtonColor: "#ac1991",
                                    confirmButtonText: "Go to Profile",
                                    customClass: {
                                      confirmButton: "custom-btn",
                                    },
                                  }).then(() =>
                                    router.push(
                                      `/${user.role}/${user.profile.username}`
                                    )
                                  );
                                })
                                .catch((err) => console.error(err));
                            }
                          });
                      }}
                      className="d-flex align-items-start gap-2 fw-medium danger no-invert bg-body py-2 rounded-lg-3 img-purple img-2red btn my-btn color-red"
                    >
                      {groupInformation?.deletion_request &&
                      groupInformation.deletion_request.some((request) => {
                        return request.requested_by === user.id;
                      })
                        ? "Cancel Leave Request"
                        : "Leave collab"}
                    </button>
                  )
                ) : (
                  <></>
                )}
                {!isKycCompleted && <KYCwarning type="collab profile" />}
              </div>
              <AddNewMember
                addedMember={addedMember}
                key={updateKey}
                params={params}
                totalMembers={values.members.length}
                maxPercent={addMember.maxPercent}
                members={memberList.filter(
                  ({ value }) => !addMember.existingMembers[value]
                )}
                setChildRef={addNewMemberModalRef}
                onSubmitCB={(data) => {
                  const { index, ...subData } = data;

                  const equalEarning = Number(
                    (100 / (values.members.length + 1)).toFixed(2)
                  );

                  if (
                    addMember.existingsMembersArray.some(
                      (mem) => mem.member_id._id === subData.member_id._id
                    )
                  ) {
                    Swal.fire({
                      icon: "error",
                      title: "Member already exists in the group",
                      showCloseButton: true,
                      confirmButtonText: "Okay",
                      confirmButtonColor: "#AC1991",
                    });
                    return;
                  }

                  if (values.distribution === "equal") {
                    addMember.existingsMembersArray.forEach((_, i) => {
                      addMember.func(i, {
                        ...addMember.existingsMembersArray[i],
                        earning: equalEarning,
                      });
                    });
                  }

                  addMember.func(index || 0, subData);
                }}
              />
            </Form>
          );
        }}
      </Formik>
    </>
  );
}

interface AddNewMemberProps {
  setChildRef: any;
  onSubmitCB: (_data: Member) => void;
  totalMembers: number;
  members: SelectProps[];
  maxPercent: number;
  params: GroupViewParams;
  addedMember: Member[];
}

const AddNewMember = ({
  setChildRef,
  onSubmitCB,
  params,
  totalMembers,
  addedMember,
}: AddNewMemberProps) => {
  const initialValues = useAppSelector((state) => state.create.members);
  const [searchTerm, setSearchTerm] = useState("");
  const [userList, setUserList] = useState([]);
  const [followingList, setFollowingList] = useState<any>();
  const [showDropdown, setShowDropdown] = useState(false);
  const [addedMem, setAddedMem] = useState([] as any[]);
  const [mem, setMem] = useState([] as Record<string, any>[]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    debouncedHandleSearch(e.target.value);
  };

  const debouncedHandleSearch = useRef(
    debounce(async (value) => {
      if (!value) {
        setShowDropdown(false);
        return;
      }

      try {
        const data = await getUserProfile(value);
        setUserList(
          data.data?.[0].users
            .filter((u: any) => u.user_type === "CREATOR")
            .filter((u: any) => u?.kyc?.full_kyc_completed === true)
        );
        setShowDropdown(true);
      } catch (error) {
        console.error("Error fetching user data", error);
      }
    }, 600)
  ).current;

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
    }
  };

  useEffect(() => {
    GetUserFollowing().then((response: any) => {
      const members = {} as Record<string, any>;

      const memberList = response.data
        .filter((member: any) => {
          return member.user_type === "CREATOR";
        })
        .map((member: any) => {
          members[member._id] = {
            _id: member._id,
            name: `${member.f_name} ${member.l_name}`,
            username: member.username,
            pic: member.avatar?.[0],
            role: member.role,
            display_name: member.display_name,
          };

          return {
            _id: member._id,
            name: members[member._id].name,
            username: members[member._id].username,
            pic: getAssetUrl({
              media: members[member._id].pic,
              defaultType: "avatar",
            }),
            display_name: member.display_name,
          };
        });

      setFollowingList([...memberList]);
    });
  }, []);

  const handleCollab = async (user: any) => {
    if (mem.length >= 1) {
      Swal.fire({
        icon: "info",
        title: "You can only add one member",
        showCloseButton: true,
      });
      return;
    }

    setMem([user]);
    setAddedMem([user]);
  };

  function removeCollab(index: any) {
    const updatedCollab = [...mem];
    const removedUser: any = updatedCollab.splice(index, 1)[0];

    setMem(updatedCollab);
    setAddedMem((prevUsers) =>
      prevUsers.filter((user) => user.username !== removedUser.username)
    );
  }

  const [memberType, _setMemberType] = useState<"MEMBER" | "ADMIN" | "OWNER">(
    "MEMBER"
  );

  const myId = useAppSelector((state) => state.user.id);

  const Values = useAppSelector((state) => state.create.group);

  const submitAddedMember = () => {
    const addMember: AddMembersBody = {
      member_id: mem[0]?._id,
      member_type: memberType,
      earning: 5,
    };

    AddGroupMembers(Values?.id, addMember)
      .then(() => {
        Swal.fire({
          icon: "success",
          title: `Invitation to ${mem[0].display_name} sent successfully`,
          showCloseButton: true,
        });
      })
      .catch((error) => {
        console.log(error);
        Swal.fire({
          icon: "error",
          title: error.message,
          showCloseButton: true,
        });
      });

    setChildRef.method.close();
  };

  const submitSelectedMembers = () => {
    const data: Member = {
      index: initialValues?.index,
      member_id: {
        _id: mem[0]?._id,
        f_name: "",
        l_name: "",
        user_type: "",
        username: mem[0]?.display_name,
        avatar: [mem[0]?.pic || mem[0]?.avatar?.[0]],
        display_name: mem[0]?.display_name,
        badges: mem[0]?.badges,
      },

      member_type: memberType,
      earning: 0 || Number((100 / (totalMembers + 1)).toFixed(2)),
    };

    onSubmitCB(data);
    setChildRef.method.close();
  };

  return (
    <Modal
      setChildRef={setChildRef}
      title="Add new member"
      subtitle={[]}
      onClose={() => setSearchTerm("")}
      render={"direct"}
    >
      {
        <>
          <div className="container-sm" style={{ maxWidth: "33rem" }}>
            <div
              className={`header-search-box position-relative  bg-cream d-flex  gap-2 align-items-center p-2 rounded-4 mb-2`}
            >
              <div className="w-100 d-flex align-items-center gap-2">
                <Image
                  src="/images/creator/search.svg"
                  alt="Search Logo"
                  className="brand-logo"
                  width={24}
                  height={24}
                  priority
                />

                <form className="w-100">
                  <input
                    type="search"
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={handleInputChange}
                    onKeyPress={handleKeyPress}
                    className="w-100"
                  />
                </form>
              </div>
            </div>
            <div style={{ maxHeight: "12rem", overflow: "scroll" }}>
              {!showDropdown &&
                followingList?.map((user: any, index: any) => {
                  return (
                    <div
                      className="d-flex w-100 align-items-center p-2 justify-content-between"
                      key={index}
                    >
                      <div className="d-flex align-items-center">
                        <div className="rounded-pill profile-wrapper">
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            width={48}
                            height={48}
                            className="img-fluid rounded-pill object-fit-cover"
                            style={{ aspectRatio: "1" }}
                            src={user.pic || "/images/common/default.svg"}
                            alt="profile-img"
                          />
                        </div>
                        <div className="d-flex flex-column ps-2 pe-2">
                          <p
                            className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6"
                            style={{ whiteSpace: "normal" }}
                          >
                            {user.display_name}
                          </p>
                          <p className="user-name text-break-0 tag-box-title-small mb-0 fs-6">
                            {user.username}
                          </p>
                        </div>
                      </div>

                      {addMember.existingsMembersArray.some(
                        (mem) => mem.member_id._id === user._id
                      ) ? (
                        <span className="btn tag-and-collab-btn btn-purple disabled">
                          Added
                        </span>
                      ) : (
                        <button
                          className={`btn tag-and-collab-btn hover-active-hollow bg-light shadow-dark`}
                          onClick={() => handleCollab(user)}
                          type="button"
                        >
                          {"Add"}
                        </button>
                      )}
                    </div>
                  );
                })}

              {showDropdown &&
                (userList.length > 0 ? (
                  userList
                    .filter((user: any) => user._id !== myId)
                    .map((user: any) => {
                      return (
                        <>
                          <div
                            className="d-flex align-items-center p-2 justify-content-between"
                            key={user._id}
                          >
                            <div className="d-flex align-items-center">
                              <div className="rounded-pill profile-wrapper">
                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                <img
                                  width={48}
                                  height={48}
                                  className="img-fluid rounded-pill object-fit-cover"
                                  style={{ aspectRatio: "1" }}
                                  src={getAssetUrl({
                                    media: user.avatar && user?.avatar[0],
                                    defaultType: "avatar",
                                  })}
                                  alt="profile-img"
                                />
                              </div>
                              <div className="d-flex flex-column ps-2 pe-2">
                                <p
                                  className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6"
                                  style={{ whiteSpace: "normal" }}
                                >
                                  {user.display_name}
                                </p>
                                <p className="user-name text-break-0 tag-box-title-small mb-0 fs-6">
                                  {user.username}
                                </p>
                              </div>
                            </div>
                            {addMember.existingsMembersArray.some(
                              (mem) => mem.member_id._id === user._id
                            ) ? (
                              <span className="btn tag-and-collab-btn btn-purple disabled">
                                Added
                              </span>
                            ) : (
                              <button
                                className={`btn tag-and-collab-btn hover-active-hollow bg-light shadow-dark`}
                                onClick={() => handleCollab(user)}
                                type="button"
                              >
                                {"Add"}
                              </button>
                            )}
                          </div>
                          {/* {index < userList.length - 1 && <hr />} */}
                        </>
                      );
                    })
                ) : (
                  <span>No users found</span>
                ))}
            </div>

            {mem.length !== 0 && (
              <>
                <hr />
                <div className="d-flex flex-column w-100">
                  <p className="fs-6 mb-2">Add Member</p>

                  <div style={{ maxHeight: "7rem", overflow: "scroll" }}>
                    {mem.map((user: any, index: number) => (
                      <div
                        className="d-flex w-100 align-items-center p-2 justify-content-between "
                        key={index}
                      >
                        <div className="d-flex align-items-center">
                          <div className="rounded-pill profile-wrapper">
                            {/* eslint-disable-next-line @next/next/no-img-element */}
                            <img
                              width={48}
                              height={48}
                              className="img-fluid rounded-pill object-fit-cover "
                              style={{ aspectRatio: "1" }}
                              src={
                                user.pic ||
                                getAssetUrl({
                                  media: user.avatar && user?.avatar[0],
                                  defaultType: "avatar",
                                })
                              }
                              alt="profile-img"
                            />
                          </div>
                          <div className="d-flex flex-column ps-2 pe-2">
                            <p className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6">
                              {user.display_name}
                            </p>
                            <p className="user-name text-break-0 tag-box-title-small mb-0 fs-6">
                              {user.username}
                            </p>
                          </div>
                        </div>

                        <Image
                          src={"/images/svg/nav-close.svg"}
                          width={20}
                          height={20}
                          className="invert pointer svg-icon"
                          alt="remove-tag"
                          onClick={() => {
                            removeCollab(index);
                          }}
                        />
                      </div>
                    ))}
                  </div>

                  <div className="d-flex flex-md-row flex-column gap-2 mt-3">
                    <button
                      type="button"
                      className="btn btn-gray flex-grow-1 "
                      onClick={() => {
                        setChildRef.method.close();
                      }}
                    >
                      Close
                    </button>
                    <button
                      type="button"
                      className="btn btn-purple flex-grow-1 "
                      onClick={() => {
                        params?.edit
                          ? submitAddedMember()
                          : submitSelectedMembers();
                      }}
                    >
                      Add Member
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      }
    </Modal>
  );
};
