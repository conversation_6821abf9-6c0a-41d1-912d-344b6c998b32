"use client";

import { useEffect } from "react";

import { configActions, createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

import GroupView from "../element";

export default function CreateGroup() {
  const dispatchAction = useAppDispatch();
  const groupId = useAppSelector((state) => state.create.group.id);

  useEffect(() => {
    // Info: Check if group was half edited then reset the group
    if (!groupId) return;

    dispatchAction(createActions.resetGroup());
    dispatchAction(configActions.toggleShowPositionWidget());
    dispatchAction(configActions.toggleShowCreateWidget());
  }, []);

  return GroupView({
    edit: false,
    from: "/collab/create",
    fromTitle: "Create my collab",
    fromBtnText: "Next",
    nextBtnText: "Create my collab",
    navigateTo: "/collab/create/preview",
  });
}
