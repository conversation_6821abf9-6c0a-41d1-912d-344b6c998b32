import Image from "next/image";
import { memo } from "react";

import VideoFrameLoader from "@/components/common/VideoFrameLoader";
import type { Media } from "@/types/media";
import { getAssetUrl } from "@/utils/assets";

const VaultMediaRenderer = ({
  images,
  videos,
  thumbnails,
  selectVideoThumbnail,
  isSettings,
  removeVaultPost,
  removeVaultThumbnail,
}: {
  images: Media[];
  videos: Media[];
  thumbnails: Media[];
  selectVideoThumbnail: (i: number) => void;
  isSettings: boolean;
  removeVaultPost: (type: "image" | "video", index: number) => void;
  removeVaultThumbnail: (i: number) => void;
}) => {
  if (!(images.length || videos.length)) return;

  return (
    <>
      {images.map((image: Media, i: number) => (
        <div key={i} className="col-12 col-md-6 my-3">
          <div className="position-relative d-flex">
            {!isSettings && (
              <div className="position-absolute action-btns d-flex gap-2">
                <div
                  onClick={() => removeVaultPost("image", i)}
                  className="media-remove-btn rounded-2 p-1  pointer"
                >
                  <Image
                    src={"/images/svg/black-cross.svg"}
                    width={25}
                    height={25}
                    alt=""
                  />
                </div>
              </div>
            )}

            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={getAssetUrl({ media: image })}
              alt=""
              style={{
                height: "100%",

                margin: "auto",
                objectFit: "contain",
              }}
              className="img-fluid position-relative"
            />
          </div>
        </div>
      ))}

      {videos.map((video: Media, i: number) => (
        <div key={i} className="col-12 col-md-6 my-3 ">
          <div
            className="position-relative d-flex justify-content-center "
            style={{ width: "fit-content" }}
          >
            {!isSettings && (
              <div className="position-absolute d-flex video-element  action-btns z-3 end-0 gap-2 align-items-center me-3 justify-content-end">
                <div className="position-relative pointer">
                  <div
                    onClick={() => selectVideoThumbnail(i)}
                    className="bg-black text-white fw-semibold pointer rounded-1 p-1 px-3  d-flex align-items-center gap-2"
                  >
                    <span className="fs-8 ">
                      {thumbnails[i] ? "Change Preview" : " Edit Preview"}
                    </span>
                    <Image
                      src={"/images/post/fill-eye.svg"}
                      width={14}
                      height={14}
                      className="invert"
                      alt="remove-media"
                    />
                  </div>
                </div>

                <Image
                  onClick={() => removeVaultPost("video", i)}
                  src={"/images/svg/black-cross.svg"}
                  width={25}
                  height={25}
                  className="invert pointer"
                  alt="remove-media"
                />
              </div>
            )}
            <VideoFrameLoader
              videoFile={getAssetUrl({ media: video })}
              classes={"img-fluid w-auto h-auto"}
              key={getAssetUrl({ media: video })}
              time={0.2}
            />

            {thumbnails[i] && (
              <div
                className="bg-body rounded-3 p-2 d-flex justify-content-between align-items-center position-absolute "
                style={{ bottom: "6%", width: "94%" }}
              >
                <div className="d-flex gap-2 align-items-center">
                  {/* eslint-disable-next-line @next/next/no-img-element*/}
                  <img
                    src={getAssetUrl({
                      media: thumbnails[i],
                      variation: "thumb",
                    })}
                    alt={`Thumbnail for video ${i + 1}`}
                    width="45"
                    height={"45"}
                    className="object-fit-cover rounded-2"
                  />
                  <span className="fw-medium">Preview added</span>
                </div>
                <Image
                  src={"/images/post/line-delete.svg"}
                  alt={`delete`}
                  width="25"
                  height={"25"}
                  onClick={() => removeVaultThumbnail(i)}
                  className="pointer"
                  style={{ filter: "grayscale(1)" }}
                />
              </div>
            )}
          </div>
        </div>
      ))}
    </>
  );
};

export default memo(VaultMediaRenderer);
