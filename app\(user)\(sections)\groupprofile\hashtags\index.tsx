const hashtags = [
  "#xoxo",
  "#art",
  "#art",
  "#digital",
  "#xoxoTeam",
  "#ajsj",
  "#happy",
];

export default function GroupHash() {
  return (
    <div className="bg-body container d-flex flex-column p-3 rounded-3">
      <p className="fs-5 fw-medium">Hashtags</p>
      <div className="d-flex gap-3 flex-wrap">
        {hashtags.map((hashtag, index) => (
          <div
            className="card bg-cream d-flex align-items-center"
            key={index}
            style={{ width: "max-content" }}
          >
            <div
              className="card-body p-0 d-flex align-items-center justify-content-center h-100"
              style={{ width: "max-content" }}
            >
              <p className="fs-6 color-dark text-center text-nowrap mb-0 py-1 px-3">
                {hashtag}
              </p>
            </div>
          </div>
        ))}
        <div className="card bg-cream d-flex align-items-center">
          <div className="card-body p-0 d-flex align-items-center justify-content-center h-100">
            <input
              className=" fs-6 color-dark bg-transparent border-0 py-1 px-3 shadow-none rounded-3 w-100 h-100"
              placeholder="#enter_hashtag"
              type="text"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
