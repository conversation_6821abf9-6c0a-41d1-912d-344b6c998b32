.profile-section-selector {
  box-shadow: 0 1px 0 0 var(--bg-dark);
  height: 4rem;

  .profile-section-selector-content {
    width: 0;

    .profile-sections {
      height: 4.2rem;

      .profile-section {
        height: 4rem;
      }
    }
  }

  .profile-section {
    &.active {
      box-shadow: inset 0 -2pt 0 0 var(--primary-color);

      .section-title {
        color: var(--primary-color);
      }
    }
  }

  .dropdown .dropdown-menu {
    min-width: 10rem !important;
    // transform: translate(-72%, 30px) !important;
  }
}

.profile-sections {
  margin-bottom: -2pt;
}
.group-dot {
  width: 3px;
  height: 3px;
  background: #afb1b3;
  border-radius: 50%;
}
.group-profile-image {
  position: absolute;
  top: -30px;
  cursor: pointer;
  .user-img {
    margin-left: -1.5rem;
  }
}
.subscibe-btn {
  width: 92%;
}
