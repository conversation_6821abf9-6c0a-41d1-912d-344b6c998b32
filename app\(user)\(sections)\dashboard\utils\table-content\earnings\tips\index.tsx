"use client";

import Image from "next/image";
import Link from "next/link";

import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";
import { formatDate } from "@/utils/number";

interface dashboardTable {
  accepted?: boolean;
  completed?: boolean;
  declined?: boolean;
  rating?: boolean;
  videoCall?: boolean;
  empty?: boolean;
  tableValues: any[];
}

export default function TipsTable(tableProps: dashboardTable) {
  const itSelf = useAppSelector((state) => state.user.id);
  return (
    <>
      <table className="table">
        {/*Table for earnings */}
        <thead>
          <tr className="fs-7">
            <th scope="col" className="bg-cream rounded-start-3">
              Payment ID
            </th>
            <th scope="col" className="bg-cream">
              Date & Time
            </th>

            <th scope="col" className="bg-cream ">
              User
            </th>
            <th scope="col" className="bg-cream ">
              Type
            </th>
            <th scope="col" className="bg-cream">
              Amount
            </th>
            <th scope="col" className="bg-cream">
              Net Amount
            </th>
            <th scope="col" className="bg-cream rounded-end-3">
              {" "}
            </th>
          </tr>
        </thead>

        <tbody className="bg-body border-0">
          {tableProps.tableValues.map((earnings, index) => (
            <tr className="fs-7 table-row" key={earnings._id}>
              <th
                scope="row"
                onClick={() =>
                  ModalService.open("TRANSACTION_INFO", {
                    OpenType: "Dashbaord",
                    transaction: earnings._id,
                  })
                }
                className="py-2 bg-body pointer"
              >
                <span className="id-text">PY{earnings._id.slice(0, 6)}</span>
              </th>
              <td className="py-2 bg-body">
                <div className="d-flex flex-column">
                  <p className="date mb-0 color-dark">
                    {formatDate(earnings?.created_at)}
                  </p>
                </div>
              </td>

              <td className="py-2 bg-body">
                <Link
                  href={`/${earnings?.from?.user_type.toLowerCase()}/${
                    earnings?.from?.username
                  }`}
                  className="d-flex gap-2 align-items-center"
                >
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={getAssetUrl({
                      media:
                        earnings?.from?.avatar && earnings?.from?.avatar[0],
                      defaultType: "avatar",
                      variation: "thumb",
                    })}
                    className="rounded-5"
                    width={35}
                    height={35}
                    alt="dashboard-user"
                  />
                  <div className="d-flex flex-column align-items-start">
                    <p className="fs-7 mb-0 text-overflow-ellipsis color-dark">
                      {earnings?.from?.display_name}
                    </p>
                    <p className="fs-8 mb-0 color-medium">
                      {earnings?.from?.username}
                    </p>
                  </div>
                </Link>
              </td>
              <td className="py-2 bg-body">
                <p className="date mb-0 color-dark">
                  <div className="d-flex flex-column align-items-start">
                    <p className="fs-7 mb-0 text-overflow-ellipsis color-dark">
                      {earnings.post ? (
                        <>
                          <p className="m-0">
                            {earnings?.post.collaborators?.length !== 0
                              ? "Collab Post"
                              : "Your Post"}
                          </p>
                          <Link
                            href={`${process.env.client}/post/${earnings.post._id}`}
                          >
                            {process.env.client}/post/{earnings.post._id}
                          </Link>
                        </>
                      ) : (
                        <>
                          <p>Tip</p>
                        </>
                      )}
                    </p>
                  </div>
                </p>
              </td>
              <td className="py-2 bg-body">
                <p className="date mb-0 color-dark">
                  {formatCurrency(
                    earnings?.to.find((res: any) => res.recipient === itSelf)
                      ?.total_amount
                  )}
                </p>
              </td>
              <td className="py-2 bg-body">
                <p className="date mb-0 color-dark">
                  {formatCurrency(
                    earnings?.to.find((res: any) => res.recipient === itSelf)
                      ?.amount
                  )}
                </p>
              </td>

              <td className="py-2 bg-body">
                <button className="border-0 rounded-3 bg-transparent">
                  <Image
                    src={"/images/svg/options-dot.svg"}
                    width={25}
                    height={25}
                    alt="options"
                  />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {tableProps.tableValues.length === 0 && (
        <div className="d-flex flex-column justify-content-center align-items-center p-3 h-100 ">
          <Image
            src={"/images/svg/dashboard-user/empty-table.svg"}
            width={50}
            height={50}
            alt="empty-table"
          />
          <p className="fs-6 fw-medium">Empty</p>
        </div>
      )}
    </>
  );
}
