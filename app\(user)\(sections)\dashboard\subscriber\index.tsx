import "./index.scss";
import { useState } from "react";

import SubscriberTab from "./tab-content";

type DataType = "channel" | "group";

export default function Subscriber() {
  const [type, setType] = useState<DataType>("channel");
  return (
    <div className="container bg-body p-3 rounded-lg-3 rounded-0">
      <div className="d-flex flex-column gap-2">
        <p className="fs-5 fw-semibold">Subscriptions</p>
        <div className="subscriber-tab-wrapper">
          <div className="d-flex w-25">
            <div
              className={
                type === "channel"
                  ? "activeType pointer pb-3 flex-grow-1"
                  : "  pointer pb3 flex-grow-1"
              }
              onClick={() => setType("channel")}
            >
              <p className="m-0 fw-semibold ">Channels</p>
            </div>
            <div
              className={
                type === "group"
                  ? "activeType pointer pb-3 flex-grow-1"
                  : " pointer pb-3 flex-grow-1"
              }
              onClick={() => setType("group")}
            >
              <p className="m-0 fw-semibold ">Collabs</p>
            </div>
          </div>
          <hr className="m-0" />
          <div className="mt-3">
            <div>
              <SubscriberTab type={type} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
