"use client";

import "./index.scss";

import { useEffect } from "react";

import { configActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";

import ButtonNav from "./button-nav";
import DroppedSubscribers from "./dropped-subscribers";
import Earnings from "./earnings";
import OptionsRequest from "./options-request";
import Overview from "./overview";
import Subscriber from "./subscriber";

export default function CreatorDashboard() {
  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(configActions.toggleShowPositionWidget());

    return () => {
      dispatch(configActions.toggleShowPositionWidget());
    };
  }, []);
  return (
    <>
      <ButtonNav />
      <div className="creator-dashboard-wrapper min-vh-100 pt-4">
        <div className="container d-flex flex-column justify-content-center align-items-center p-3 pt-0 px-0 gap-3 z-1">
          <Overview />
          <OptionsRequest />
          <Subscriber />
          <Earnings />
          <DroppedSubscribers />
        </div>
      </div>
    </>
  );
}
