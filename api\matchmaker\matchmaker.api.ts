import API from "..";

export const CreateOrUpdateHashtags = async (hashtag: string) => {
  return API.post(`${API.HASHTAGS}?hashtag`, {
    hashtag,
  }) as Promise<any>;
};

export const CreateMatchProfile = async (profile: Record<string, any>) => {
  return API.post(`${API.MATCH}/profile`, profile) as Promise<any>;
};

export const GetUserMatchProfile = async (userId?: string) => {
  return API.get(`${API.MATCH}/profile/${userId || ""}`) as Promise<any>;
};

export const UpdateMatchProfile = async (
  profile: Record<string, any>,
  match_profile_id: string
) => {
  return API.patch(
    `${API.MATCH}/profile/${match_profile_id}`,
    profile
  ) as Promise<any>;
};

export const ApplyFilters = async (match_profile_id: string, data: any) => {
  return API.patch(
    `${API.MATCH}/profile/${match_profile_id}/filters`,
    data
  ) as Promise<any>;
};

export const GetYourMatches = async ({
  match_profile_id,
}: {
  match_profile_id: string;
}) => {
  return API.get(
    `${API.MATCH}/get-matched-profiles?current_match_id=${match_profile_id}`
  ) as Promise<any>;
};

export const GetMatchSuggestions = async (
  match_profile_id: string,
  user_type: string,
  options?: { limit?: number; page?: number; distance?: string }
) => {
  let url = `${API.MATCH}/suggestions?current_match_id=${match_profile_id}&user_type=${user_type}`;

  if (options) {
    if (options.limit && options.limit > 0) {
      url += `&limit=${options.limit.toString()}`;
    }

    if (options.page && options.page > 0) {
      url += `&page=${options.page.toString()}`;
    }

    if (options.distance) {
      url += `&maxdistance=${(Number(options.distance) * 1000).toString()}`;
    }
  }

  return API.get(url) as Promise<any>;
};

interface InteractionBody {
  current_match_id: string;
  target_match_id: string;
  type: string;
}

export const MatchInteraction = async (interactionBody: InteractionBody) => {
  return API.post(`${API.MATCH}/interactions`, interactionBody) as Promise<any>;
};

export const PeopleWhoLikedYou = async ({
  match_profile_id,
}: {
  match_profile_id: string;
}) => {
  return API.get(
    `${API.MATCH}/like-requests?current_match_id=${match_profile_id}`
  ) as Promise<any>;
};

export const PeopleInteractedWith = async ({
  match_profile_id,
}: {
  match_profile_id: string;
}) => {
  return API.get(
    `${API.MATCH}/interactions?limit=3&current_match_id=${match_profile_id}`
  ) as Promise<any>;
};

export const PeopleYouLiked = async ({
  match_profile_id,
}: {
  match_profile_id: string;
}) => {
  return API.get(
    `${API.MATCH}/interactions?type=Like&current_match_id=${match_profile_id}`
  ) as Promise<any>;
};

export const UndoInteraction = async (
  match_profile_id: string,
  interactionId: string
) => {
  // return new Promise((res) => res("Rewind successful!"));
  return API.delete(
    `${API.MATCH}/interactions/${interactionId}/undo-interaction`,
    {
      current_match_id: match_profile_id,
    }
  ) as Promise<any>;
};

export const RemoveUserFromMatches = async (
  match_profile_id: string,
  userId: string
) => {
  return API.delete(`${API.MATCH}/matched-profiles/${userId}`, {
    current_match_id: match_profile_id,
  }) as Promise<any>;
};

export const PokeUser = async (match_profile_id: string, userId: string) => {
  return API.post(`${API.MATCH}/poke`, {
    current_match_id: match_profile_id,
    target_match_id: userId,
  });
};

export const ToggleIncognitoMode = async (
  match_profile_id: string,
  toggle_incognito_mode: boolean
) =>
  API.patch(`${API.MATCH}/profile/${match_profile_id}/incognito-setting`, {
    enable_incognito: toggle_incognito_mode,
  }) as Promise<any>;

export const EditLocation = async (locationBody: {
  latitude: number;
  longitude: number;
  match_profile_id: string;
}) => {
  const { longitude, latitude } = locationBody;
  return API.patch(
    `${API.MATCH}/profile/location/${locationBody.match_profile_id}`,
    {
      longitude,
      latitude,
    }
  ) as Promise<any>;
};
