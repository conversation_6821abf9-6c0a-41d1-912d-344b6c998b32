import { useRouter } from "next/navigation";
import { useRef } from "react";
import Swal from "sweetalert2";

import type { APIChannel } from "@/api/channel";
import type { APIGroup } from "@/api/group";
import { createActions, defaultActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { checkVerification } from "@/utils/check-verification";

import { ChannelSchema } from "./channel";
import { AddNewBtn, GroupSchema } from "./group";

const AllSubs = ({
  uid,
  channelList,
  groupList,
  from,
}: {
  uid: string;
  channelList: APIChannel[];
  groupList: APIGroup[];
  from: "post" | "reel";
}) => {
  const channelRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );
  const error: boolean = !(groupList.length || channelList.length);
  const user = useAppSelector((state) => state.user);
  const isOwner = user.id === uid;
  const dispatchAction = useAppDispatch();
  if (error && !isOwner)
    return <div className="text-center">No channel and collab found.</div>;
  else if (!(channelList?.length || groupList.length) && !isOwner)
    return (
      <div className="bg-body p-3 rounded-3 text-center">No subs yet!</div>
    );

  const addNewGroup = async () => {
    const verified = await checkVerification("create collab");

    if (verified) {
      dispatchAction(createActions.resetGroup());
      dispatchAction(defaultActions.resetGroupProfile());
      router.push("/collab/create");
    }
  };

  const addNewChannel = () => {
    if (isKycCompleted) {
      dispatchAction(createActions.resetChannel());
      router.push("/channel/create");
    } else {
      Swal.fire({
        title: " Your channel will not be visible to others. ",
        text: "Get Verified to visible to others.",
        icon: "warning",
        confirmButtonColor: "#3085d6",
        confirmButtonText: "Skip",
        showDenyButton: true,
        denyButtonColor: "#ac1991",
        denyButtonText: "Get verified",
        showCloseButton: true,
      }).then((result) => {
        if (result.isConfirmed) {
          dispatchAction(createActions.resetChannel());
          router.push("/channel/create");
        } else if (result.isDenied) {
          router.push("/settings/kyc");
        }
      });
    }
  };

  return (
    <div
      ref={channelRef}
      className={`d-flex flex-column gap-3 ${
        from === "reel" ? "bg-reel" : " bg-body"
      } p-3 rounded-3 scrollable-division`}
    >
      <div className="container-fluid g-0 g-md-4 g-lg-0 my-lg-0 my-md-4 ">
        {channelList.length || groupList.length ? (
          <>
            <div className="">
              <div className="row g-3">
                {channelList.length > 0 && (
                  <>
                    <div className="d-flex justify-content-between">
                      {isOwner ? <AddNewBtn onClick={addNewChannel} /> : <></>}
                    </div>
                    {channelList.map((channel, i) => (
                      <ChannelSchema
                        key={i}
                        data={channel}
                        uid={uid}
                        from={from}
                      />
                    ))}
                  </>
                )}
              </div>
            </div>

            <div className="mt-4">
              <div className="row g-3">
                {groupList.length > 0 && (
                  <>
                    <div className="d-flex justify-content-between">
                      {isOwner ? <AddNewBtn onClick={addNewGroup} /> : <></>}
                    </div>

                    {groupList.map((group, i) => (
                      <GroupSchema key={i} data={group} uid={uid} from={from} />
                    ))}
                  </>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center flex-grow-1">{error}</div>
        )}
      </div>
    </div>
  );
};

export default AllSubs;
