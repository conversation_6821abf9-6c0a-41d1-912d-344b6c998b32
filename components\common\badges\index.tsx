import type { Tooltip } from "bootstrap";
import { memo, useEffect, useRef } from "react";

import { type BadgesType } from "@/api/post";
import type {
  FirstBadgePriority,
  SecondBadgePriority,
  SubscriptionBadge,
  UserBadge,
} from "@/types/badge";

const userBadgeTooltip: Record<UserBadge | SubscriptionBadge, string> = {
  VerifiedUser: "Verified User",
  VerifiedCreator: "Verified Creator",
  // VerifiedByKnky: "Verified Creator by Knky",
  AffiliatedWithKnky: "Affiliated with Knky",
  Prime: "Prime",
  CreatorPro: "Creator Pro",
};
export const FirstBadgesPriority: FirstBadgePriority[] = [
  "CreatorPro",
  "VerifiedCreator",
  "VerifiedUser",
];

const SecondBadgesPriority: SecondBadgePriority[] = [
  "AffiliatedWithKnky",
  "Prime",
];

const badgeImages: Record<UserBadge | SubscriptionBadge, string> = {
  VerifiedUser: "/images/badges/user-verified.svg",
  VerifiedCreator: "/images/badges/creator-verified.svg",
  AffiliatedWithKnky: "/images/badges/affliated.svg",
  Prime: "/images/badges/knky-prime.svg",
  CreatorPro: "/images/badges/creator-verified-official.svg",
};

const getUserBadge = (badges: (UserBadge | SubscriptionBadge)[]) => {
  return FirstBadgesPriority.find((badge) => badges.includes(badge));
};

const getSubscriptionBadge = (badges: (UserBadge | SubscriptionBadge)[]) => {
  return SecondBadgesPriority.find((badge) => badges.includes(badge));
};

interface BadgesProps {
  cls?: string;
  array: BadgesType;
  source?: string;
}

const Badges = ({ cls, array, source }: BadgesProps) => {
  const userBadgeRef = useRef<HTMLImageElement | null>(null);
  const subBadgeRef = useRef<HTMLImageElement | null>(null);

  const allBadges: (UserBadge | SubscriptionBadge)[] = [
    ...(array?.user_badges || []),
    ...(array?.subscription_badge || []),
  ];

  const FirstBadge = getUserBadge(allBadges);
  const SecondBadge = getSubscriptionBadge(allBadges);

  const isGridMobile = source === "grid" && window.innerWidth < 768;
  const isBadgeMobile = source === "reel" && window.innerWidth < 768;

  useEffect(() => {
    let userTooltip: Tooltip | undefined;
    let subTooltip: Tooltip | undefined;

    if (userBadgeRef.current && window.bootstrap) {
      userTooltip = new window.bootstrap.Tooltip(userBadgeRef.current);
    }

    if (subBadgeRef.current && window.bootstrap) {
      subTooltip = new window.bootstrap.Tooltip(subBadgeRef.current);
    }

    return () => {
      userTooltip?.dispose();
      subTooltip?.dispose();
    };
  }, []);

  return (
    <div
      className={`d-flex ${cls ? cls : ""} align-items-center`}
      style={{ gap: "0.125rem" }}
    >
      {FirstBadge && (
        /* eslint-disable-next-line @next/next/no-img-element */
        <img
          ref={userBadgeRef}
          className="user-badge"
          src={badgeImages[FirstBadge]}
          width={
            source === "reel"
              ? isBadgeMobile
                ? 10 // example mobile size for reel
                : 18 // example desktop size for reel
              : isGridMobile
              ? 8
              : window.location.pathname !== "/chat"
              ? 20
              : 18
          }
          height={
            source === "reel"
              ? isBadgeMobile
                ? 10
                : 18
              : isGridMobile
              ? 8
              : window.location.pathname !== "/chat"
              ? 20
              : 18
          }
          alt={userBadgeTooltip[FirstBadge]}
          data-bs-toggle="tooltip"
          data-bs-title={userBadgeTooltip[FirstBadge]}
        />
      )}

      {SecondBadge && (
        /* eslint-disable-next-line @next/next/no-img-element */
        <img
          ref={subBadgeRef}
          className="user-badge"
          src={badgeImages[SecondBadge]}
          width={
            source === "reel"
              ? isBadgeMobile
                ? 10 // example mobile size for reel
                : 18 // example desktop size for reel
              : isGridMobile
              ? 8
              : window.location.pathname !== "/chat"
              ? 20
              : 18
          }
          height={
            source === "reel"
              ? isBadgeMobile
                ? 10
                : 18
              : isGridMobile
              ? 8
              : window.location.pathname !== "/chat"
              ? 20
              : 18
          }
          alt={userBadgeTooltip[SecondBadge]}
          data-bs-toggle="tooltip"
          data-bs-title={userBadgeTooltip[SecondBadge]}
        />
      )}
    </div>
  );
};

export default memo(Badges);
