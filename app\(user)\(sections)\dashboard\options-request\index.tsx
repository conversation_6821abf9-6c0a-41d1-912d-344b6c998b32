import "./index.scss";

import Image from "next/image";
import { useEffect, useState } from "react";

import { requestOverView } from "@/api/dashboard";
import { useAppSelector } from "@/redux-store/hooks";

import ChatOptionsListing from "./listing";

const overviewData: any = {
  "CUSTOM-SERVICE": "Custom Requests",
  RATING: "Rating Requests",
  VIDEO: "Video Calls",
  VOICE: "Voice Calls",
  PROMOTION: "Promotion Options",
};
const overviewTooltips: any = {
  "CUSTOM-SERVICE": "Total count of custom requests",
  RATING: "Total count of rating requests",
  VIDEO: "Total count of video calls",
  VOICE: "Total count of voice calls",
  PROMOTION: "Total number of promotions",
};

const tabComponents: any = {
  Requests: "CUSTOM-SERVICE",
  Rating: "RATING",
  Video: "VIDEO",
  Voice: "VOICE",
  Promote: "PROMOTION",
};

export default function OptionsRequest() {
  const filter = useAppSelector((state) => state.user.creatorDashboardFilter);
  const dateFilter = useAppSelector(
    (state) => state.user.creatorDashboardDateFilter
  );
  const initialState = {
    "CUSTOM-SERVICE": 0,
    RATING: 0,
    VIDEO: 0,
    VOICE: 0,
    PROMOTION: 0,
  };

  const [totalRequestCount, setTotalRequestCount] = useState<any>(initialState);
  const [allCount, setAllCount] = useState(0);
  const [showTab, setShowTab] = useState("Requests");

  useEffect(() => {
    getOverviewCount();
  }, [filter, dateFilter]);

  const getOverviewCount = async () => {
    setTotalRequestCount(initialState);
    setShowTab(showTab);
    setAllCount(0);

    try {
      const res = await requestOverView(dateFilter);
      const newCount: any = { ...initialState };
      let total = 0;

      res.data.forEach((item: any) => {
        newCount[item._id] = item.count;
        total += item.count;
      });

      setTotalRequestCount(newCount);
      setAllCount(total);
    } catch (err) {
      console.error(err);
    }
  };

  const renderRequestCards = () => {
    return Object.keys(totalRequestCount).map((key) => (
      <div key={key} className="d-flex flex-column gap-2 p-2 card">
        <div className="overview-title-wrapper d-flex align-items-center gap-2">
          <div className="d-flex align-items-center gap-2">
            <p className="fs-6 fw-medium mb-0 text-nowrap">
              {overviewData[key]}
            </p>
            <Image
              src={"/images/svg/info.svg"}
              width={20}
              height={20}
              alt="overview-info"
              className="pointer"
              data-bs-toggle="tooltip"
              data-bs-placement="top"
              data-bs-custom-class="custom-tooltip"
              data-bs-title={`${overviewTooltips[key]}`}
              data-bs-delay='{"show":400}'
            />
          </div>
        </div>
        <h4 className="fw-semibold">{totalRequestCount[key]}</h4>
      </div>
    ));
  };

  return (
    <div className="container bg-body p-3 px-0 rounded-lg-3 rounded-0">
      <div className="d-flex flex-column gap-2">
        <div className="px-3">
          <p className="fs-5 fw-semibold">Services Requests</p>
          <div className="options-request-wrapper">
            <div className="d-flex flex-column gap-2 p-2 card">
              <div className="overview-title-wrapper d-flex align-items-center gap-2">
                <div className="d-flex align-items-center gap-2">
                  <p className="fs-6 fw-medium mb-0 text-nowrap">
                    All Requests
                  </p>
                  <Image
                    src={"/images/svg/info.svg"}
                    width={20}
                    height={20}
                    alt="overview-info"
                    data-bs-toggle="tooltip"
                    className="pointer"
                    data-bs-placement="top"
                    data-bs-custom-class="custom-tooltip"
                    data-bs-title="Total number of requests received"
                    data-bs-delay='{"show":400}'
                  />
                </div>
              </div>
              <h4 className="fw-semibold">{allCount}</h4>
            </div>
            {renderRequestCards()}
          </div>
        </div>

        <div className="option-tab-wrapper px-lg-3 px-0">
          <ul
            className="nav nav-pills subscriber-tab flex-nowrap text-nowrap scrollable"
            id="pills-tab"
            role="tablist"
          >
            {Object.keys(tabComponents).map((tab) => (
              <li className="nav-item w-100" role="presentation" key={tab}>
                <button
                  className={`nav-link ${
                    showTab === tab ? "active" : ""
                  } rounded-0 border-0 border-bottom bg-body color-dark h-100 w-100 fs-6 fw-semibold px-3 py-2`}
                  type="button"
                  role="tab"
                  onClick={() => setShowTab(tab)}
                >
                  {tab}
                </button>
              </li>
            ))}
          </ul>
          <hr className="mt-0 mb-3" />
          <div className="tab-content" id="pills-tabContent">
            <ChatOptionsListing
              type={tabComponents[showTab]}
              cb={getOverviewCount}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
