import { useRouter } from "next-nprogress-bar";
import Image from "next/image";

import type { ModalImportProps } from "@/components/common/modal";
import Modal from "@/components/common/modal";
import { createActions, defaultActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";

export default function FileSupports({ setChildRef }: ModalImportProps) {
  const fileSupportInfo = [
    {
      icon: "prime",
      title: "Prime (Discovery)",
      text: "Get discovered by all KNKY Prime users! Feature your short-form content in our ‘Match’, ‘Featured’ and ‘For You’ feeds to boost your visibility and maximize sales.",
      supports: [
        "Up to 20 media per post",
        "FHD (1080p) media quality",
        "Max. total video length 30min",
      ],
    },

    {
      icon: "premium",
      title: "Pay-To-View (Paid)",
      text: "Offer your long-form content and larger media collections on KNKY with Pay-To-View access for your fans!",
      subtext: "*Publishing in 4K requires a ‘ProCreator’ Plan",
      supports: [
        "Up to 60 media per post",
        "QHD (1440p) or *4K (2160p) media quality",
        "Max. total video length 125min",
      ],
    },

    {
      icon: "public",
      title: "Public (Free)",
      text: "Post public clips and teasers to showcase your content, boost fan engagement and drive conversions!",
      supports: [
        "Up to 6 media per post",
        "HD (720p) media quality",
        "Max. total video length 2min",
      ],
    },
  ];
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  return (
    <Modal
      title="File Supports"
      subtitle={[""]}
      setChildRef={setChildRef}
      titleClass="fs-2 fw-bold"
      bodyClass="mh-100 overflow-auto"
      // render={"direct"}
    >
      <div className="container d-flex flex-column gap-4 align-items-center mt-1">
        {fileSupportInfo.map((type, i) => {
          return (
            <div
              className="rounded-3 border p-3 d-flex align-items-center gap-3 w-100 w-md-75 "
              key={i}
            >
              <Image
                src={`/images/post/fill-${type.icon}.svg`}
                width={38}
                height={38}
                alt=""
              />
              <div className="d-flex flex-column ">
                <h5 className="fw-semibold">{type.title}</h5>
                <span className="fs-7 color-black fw-medium">{type.text}</span>
                <span className="fs-7 color-black fw-medium">
                  Supports: <br />
                  {type.supports.map((support, i) => (
                    <span key={i}>
                      -{support} <br />
                    </span>
                  ))}
                </span>
                <span className="fs-7 color-black fw-medium fst-italic">
                  {type.subtext}
                </span>
              </div>
            </div>
          );
        })}

        <div className="fs-8">
          Looking to create a new subscription? Create a{" "}
          <span
            onClick={() => {
              router.push("/channel/create");

              dispatchAction(defaultActions.resetChannelProfile());
              dispatchAction(createActions.resetChannel());
            }}
            className="color-primary fw-bold pointer"
          >
            Channel
          </span>{" "}
          or a{" "}
          <span
            onClick={() => {
              router.push("/collab/create");

              dispatchAction(defaultActions.resetGroupProfile());
              dispatchAction(createActions.resetGroup());
            }}
            className="color-primary fw-bold pointer"
          >
            Collab
          </span>{" "}
          Profile.
        </div>
      </div>
    </Modal>
  );
}
