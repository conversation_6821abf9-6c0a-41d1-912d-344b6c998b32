import { useRouter } from "next/navigation";
import { useEffect, useRef } from "react";
import "./index.scss";

import { defaultActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";

const InterestedSection = ({
  data = [],
  active,
}: {
  data: string[];
  active: string;
}) => {
  const router = useRouter();
  const dispatchAction = useAppDispatch();

  const handleHashtagClick = (hashtag: string) => {
    if (hashtag.charAt(0) === "#") hashtag = hashtag.slice(1);
    // window.location.href = `/interested?hashtag=${encodeURIComponent(hashtag)}`;
    router.push(`/interested?hashtag=${encodeURIComponent(hashtag)}`);
    dispatchAction(defaultActions.setSelectedTag(encodeURIComponent(hashtag)));
  };

  const containerRef = useRef<HTMLDivElement>(null);
  const tagRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    if (containerRef.current && tagRefs.current) {
      const activeTagIndex = data.indexOf(active);

      if (activeTagIndex !== -1 && tagRefs.current[activeTagIndex]) {
        const activeTagElement: any = tagRefs.current[activeTagIndex];
        const containerElement = containerRef.current;

        // Calculate the position to scroll to
        const scrollPosition =
          activeTagElement.offsetLeft -
          containerElement.offsetWidth / 2 +
          activeTagElement.offsetWidth / 2;

        containerElement.scrollTo({
          left: scrollPosition,
          behavior: "smooth",
        });
      }
    }
  }, [active, data]);

  if (!data.some((s) => s?.trim() !== "")) {
    return;
  }

  return (
    <div
      ref={containerRef}
      className="d-flex gap-2 bg-body p-3 rounded-lg-3 scrollable trendingTags"
      style={{ overflowX: "scroll" }}
    >
      <div
        className={`scroll-item py-1 px-3 rounded-3 pointer ${
          active === "" && "active-fill"
        }`}
        onClick={() => handleHashtagClick("")}
      >
        #
      </div>

      {data.map((tag, i) => {
        const selected = tag === active ? "active-fill" : "";

        return (
          <div
            key={i}
            ref={(el) => (tagRefs.current[i] = el)}
            className={`scroll-item py-1 px-3 rounded-3 pointer ${selected}`}
            onClick={() => handleHashtagClick(tag)}
          >
            #{tag}
          </div>
        );
      })}
    </div>
  );
};

export default InterestedSection;
