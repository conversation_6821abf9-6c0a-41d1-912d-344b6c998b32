"use client";

import classNames from "classnames";
import { Formik } from "formik";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import * as Yup from "yup";

import { DeleteChannel } from "@/api/channel";
import { GetServices } from "@/api/services";
import KYCwarning from "@/components/common/KYC-warning";
import Form from "@/components/common/form";
import Input from "@/components/common/input";
import InputArray from "@/components/common/input-array";
import InputGroup from "@/components/common/list";
import { type ModalRef } from "@/components/common/modal";
import Select from "@/components/common/select";
import SelectArray from "@/components/common/select-array";
import SelectArrayV2, {
  type PossibleValues,
} from "@/components/common/select-array-v2";
import TextArea from "@/components/common/textarea";
import Wrapper, { Divider } from "@/components/common/wrapper";
import { ModalService } from "@/components/modals";
import {
  MaxChatFeeDuration,
  SubscriptionTime,
  SubscriptionType,
  TrialPeriod,
} from "@/global/constants";
import { useAsyncEffect } from "@/hooks/useAsyncEffect";
import { configActions, createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { ChannelFreePerkTypes } from "@/types/profile";
import { ChannelFreePerkUnitTypes } from "@/types/profile";
import type { GetServiceResponse } from "@/types/services";
import { ServicesImg } from "@/types/services";
import { getAssetUrl } from "@/utils/assets";
import { waitForBootstrap } from "@/utils/wait-for-bootstrap";

const FeaturedServices = [
  {
    service: "Message",
    icon: "/images/services/Message.svg",
    options: ["mes", "min", "day"],
  },
  {
    service: "Video Call",
    icon: "/images/services/Video.svg",
    options: ["minute"],
  },
  {
    service: "Voice Call",
    icon: "/images/services/Voice.svg",
    options: ["minute"],
  },
  {
    service: "Request",
    icon: "/images/services/Request.svg",
    options: ["turn"],
  },
  {
    service: "Rating",
    icon: "/images/services/Rating.svg",
    options: ["turn"],
  },
];

interface ChannelViewParams {
  id?: string;
  from: string;
  edit: boolean;
  navigateTo: string;
  fromTitle: string;
  fromBtnText: string;
  nextBtnText: string;
}

export default function ChannelView(params: Readonly<ChannelViewParams>) {
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  const stateValues = useAppSelector((state) => state.create.channel);
  const [optionSelected, setOptionSelected] = useState<"mes" | "min" | "day">(
    "mes"
  );

  const initialValues = {
    ...stateValues,
    servicesTicked: true,
    selectedServices:
      params.edit && stateValues.services && stateValues.services?.length > 0
        ? stateValues.services
        : ([] as GetServiceResponse[]),
  };

  const [featuredServiceAmount, setFeaturedServiceAmount] = useState<{
    Message: number;
    "Video Call": number;
    "Voice Call": number;
    Request: number;
    Rating: number;
  }>({
    Message: 0,
    "Video Call": 0,
    "Voice Call": 0,
    Request: 0,
    Rating: 0,
  });

  useEffect(() => {
    if (params.edit) {
      setFeaturedServiceAmount({
        Message:
          initialValues?.free_services?.find((s) => s.type === "Message")
            ?.unit || 0,
        "Voice Call":
          initialValues?.free_services?.find((s) => s.type === "VoiceCall")
            ?.unit || 0,
        "Video Call":
          initialValues?.free_services?.find((s) => s.type === "VideoCall")
            ?.unit || 0,
        Request:
          initialValues?.free_services?.find((s) => s.type === "Request")
            ?.unit || 0,
        Rating:
          initialValues?.free_services?.find((s) => s.type === "Rating")
            ?.unit || 0,
      });
    }
  }, [params.edit]);

  const myUsername = useAppSelector((state) => state.user?.profile?.username);
  const userId = useAppSelector((state) => state.user?.id);
  const [servicesData, setServicesData] = useState<GetServiceResponse[]>([]);

  const formRef = useRef<any>(null);

  useAsyncEffect(async () => {
    await waitForBootstrap();

    const tooltipTriggerList = document.querySelectorAll(
      '[data-bs-toggle="tooltip"]'
    );
    const tooltipList = [...tooltipTriggerList].map(
      (tooltipTriggerEl) => new window.bootstrap.Tooltip(tooltipTriggerEl)
    );

    return () => {
      tooltipList.forEach((tooltip) => tooltip.dispose());
    };
  }, []);

  const fetchServices = async () => {
    try {
      const data = await GetServices(userId);
      return data?.data;
    } catch (error) {
      console.log(error);
      return [];
    }
  };

  useEffect(() => {
    fetchServices().then((res) => {
      setServicesData(res);
    });
  }, []);

  useEffect(() => {
    dispatchAction(configActions.toggleShowPositionWidget());
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowPositionWidget());
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  const possibleValues = [
    "discount",
    "phone",
    "show",
    "start",
    "video",
    "chat",
    "media",
    "public",
    "video-play",
    "voice",
    "custom",
  ] as const;

  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .required("Channel name is required")
      .matches(
        /^[\p{L} '’-]{4,40}$/u,
        "Channel name must be 4-40 characters long and contain only letters, spaces, apostrophes, and dashes."
      ),
    username: Yup.string()
      .required("Channel username is required")
      .min(4, "Channel username must be at least 4 characters")
      .matches(
        /^[\p{L}'’-]{4,40}$/u,
        "Channel username must be 4-40 characters long and contain only letters, apostrophes, and dashes."
      ),
    type: Yup.string(),
    description: Yup.string()
      .max(3000, "Description must be at most 3000 characters")
      .min(10, "Description must be at least 10 characters"),
    hashtags: Yup.array().min(1).required("Hashtags is required"),
    perks: Yup.array().of(
      Yup.object().shape({
        icon: Yup.mixed<PossibleValues>().oneOf(possibleValues, "Invalid icon"),
        value: Yup.string(),
      })
    ),
  });

  const addNewSubscriptionModalRef = { method: {} as ModalRef };
  const [updateKey, _setUpdateKey] = useState(0);
  const [prevKey, setPrevKey] = useState(-1);

  /**
   * This hook is designed to handle scenarios where the modal was not re-rendering
   * as expected. It ensures that the modal is opened when the addNewSubscriptionModalRef state changes
   * that changes when the updateKey states changes.
   */
  useEffect(() => {
    // If updateKey is 0 or hasn't changed, do nothing
    if (updateKey === 0 || updateKey === prevKey) return;

    // Set prevKey to the current updateKey to avoid duplicate triggers
    setPrevKey(updateKey);

    addNewSubscriptionModalRef.method.open();
  }, [addNewSubscriptionModalRef]);

  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );
  const subscribersCount = useAppSelector(
    (s) => s?.defaults?.channel_profile?.counter?.subscriber
  );

  const channelDelete = () => {
    Swal.fire({
      title: "Are you sure you want to delete this channel?",
      text: "Your channel will be flagged for deletion unless the last subscriber's subscription expires.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, don't delete it!",
    }).then((result) => {
      if (result.isConfirmed) {
        DeleteChannel(initialValues?.id!).then(() => {
          Swal.fire({
            title: "Channel deleted successfully",
            icon: "success",
            confirmButtonColor: "#ac1991",
            showConfirmButton: true,
          }).then(() => {
            if (subscribersCount) {
              router.push(`/channel/${initialValues?.username}`);
            } else {
              router.push(`/creator/${myUsername}/channel`);
            }
          });
        });
      }
    });
  };

  return (
    <>
      <Formik
        enableReinitialize={!initialValues.saved}
        initialValues={initialValues}
        validationSchema={validationSchema}
        innerRef={formRef}
        onSubmit={(data) => {
          router.push(params.navigateTo);
          const { servicesTicked, selectedServices, ...rest } = data;

          const free_services = Object.keys(featuredServiceAmount)
            .map((item: any) => {
              let unit_type: ChannelFreePerkUnitTypes;

              if (item === "Message") {
                unit_type =
                  optionSelected === "mes"
                    ? ChannelFreePerkUnitTypes.MessageCount
                    : optionSelected === "day"
                    ? ChannelFreePerkUnitTypes.Day
                    : ChannelFreePerkUnitTypes.Minute;
              } else if (item === "Video Call" || item === "Voice Call") {
                unit_type = ChannelFreePerkUnitTypes.Minute;
              } else {
                unit_type = ChannelFreePerkUnitTypes.Turn;
              }

              return {
                type: item.split(" ").join("") as ChannelFreePerkTypes,
                unit: featuredServiceAmount[
                  item as keyof typeof featuredServiceAmount
                ] as number,
                unit_type,
              };
            })
            .filter((i) => i.unit > 0);

          dispatchAction(
            createActions.setChannel({
              ...rest,
              ...(selectedServices.length > 0
                ? {
                    services: selectedServices,
                  }
                : { services: [] }),
              free_services,
              saved: true,
              from: params.from,
              nextBtnText: params.nextBtnText,
            })
          );
        }}
      >
        {({ values, setFieldValue, ...rest }) => (
          <Form
            title={params.fromTitle}
            nextBtnText={params.fromBtnText}
            dirty={values.saved}
            formikValues={{ values, setFieldValue, ...rest }}
          >
            <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1 ">
              <Wrapper title="" className="w-lg-75">
                <Input
                  name="name"
                  label="Channel Name"
                  placeholder="Enter a name for your channel"
                  // disable={
                  //   params.edit
                  //     ? initialValues.counter.subscriber_count > 0
                  //     : false
                  // }
                  customErrorClassname="color-red fs-8 ms-1"
                />
                <Input
                  name="username"
                  label="Channel Username"
                  placeholder="Enter your desired @username"
                  disable={params.edit}
                  username={true}
                  customErrorClassname="color-red fs-8 ms-1"
                />
              </Wrapper>
              <Wrapper
                title="About Channel"
                counter={{ value: values.description, max: 3000 }}
                required
              >
                <TextArea
                  name="description"
                  type="text"
                  value={values.description}
                  required={false}
                  label=""
                  placeholder="Tell something about your channel here "
                />
              </Wrapper>
              <Wrapper title="Hashtags" required>
                <InputArray
                  name="hashtags"
                  label=""
                  placeholder="#enter_new_tag_here"
                  required={true}
                  replacer={(value: string) =>
                    `#${value.replace(/[^a-z0-9]+/g, "")}`
                  }
                  values={values.hashtags}
                />
              </Wrapper>
            </div>
            <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
              <Wrapper parentClass=" w-100">
                <span className="mb-3">
                  Applies when users subscribe to you
                </span>
                <InputGroup
                  label=""
                  type="radio"
                  required={false}
                  theme="circle-purple"
                  name="subscriptions.offer_type"
                  className="align-items-md-start "
                  childClass="ms-1"
                  array={SubscriptionType}
                  selected={values?.subscriptions?.offer_type}
                  disabled={
                    params?.edit && initialValues.counter.subscriber_count > 0
                  }
                />
                {values?.subscriptions?.offer_type === "FREE_TRIAL" ? (
                  <Select
                    label="Free trial in"
                    name="subscriptions.trial_period"
                    required={true}
                    array={TrialPeriod}
                    selected={values.subscriptions?.trial_period}
                    setFieldValue={setFieldValue}
                    disabled={
                      params?.edit && initialValues.counter.subscriber_count > 0
                    }
                  />
                ) : (
                  <></>
                )}
                {values?.subscriptions?.offer_type !== "FREE" && (
                  <Divider className="fs-10 mt-n2" />
                )}

                {values?.subscriptions?.offer_type === "FREE" ? (
                  <></>
                ) : (
                  <SelectArray
                    label="Complete your plans with pricing"
                    name="subscriptions.array"
                    placeholder="Price "
                    objectKey="subscription_type"
                    objectValueKey="price"
                    inputType="number"
                    objectValueType="number"
                    keyClass="col-5"
                    options={SubscriptionTime}
                    values={values?.subscriptions?.array}
                    keyPlaceholder={"Select time package"}
                    optionalInitialValue={{
                      key: "MONTHLY",
                      value: "",
                    }}
                    disable={
                      params?.edit && initialValues.counter.subscriber_count > 0
                    }
                  />
                )}
              </Wrapper>
              <Wrapper
                titleClass="fs-5 fw-bold"
                title="Included Perks"
                className={
                  params.edit && initialValues.counter.subscriber_count > 0
                    ? "disabled grayscale pe-none"
                    : ""
                }
              >
                <div className="color-medium fs-8 d-none">
                  Please note: Services with per-minute billing units or those
                  disabled in your manager services will not be activated during
                  selection.
                </div>
                <div className="d-flex justify-content-between flex-column">
                  <div className="d-flex gap-1 align-items-center">
                    <div className="fw-bold fs-5">
                      1. Free Featured Services
                    </div>
                    <Image
                      src={"/images/common/information.png"}
                      width={20}
                      height={20}
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      data-bs-title="Offer your featured services for free to your subscribers"
                      alt="Information"
                    />
                  </div>
                  <div className="color-medium fs-8 mb-2">
                    Setting an amount is optional. If you leave it blank, the
                    service will not be available to subscribers.
                  </div>
                  <div className="d-flex flex-column gap-2">
                    {FeaturedServices.map((s, idx) => {
                      return (
                        <div
                          key={idx}
                          className={classNames(
                            "d-flex justify-content-between align-items-center rounded border p-1",
                            {
                              "grayscale disabled": params.edit,
                            }
                          )}
                        >
                          <div className="d-flex align-items-center gap-2 w-100">
                            <div>
                              <Image
                                src={s.icon}
                                width={24}
                                height={24}
                                alt={s.service}
                              />
                            </div>
                            <div>{s.service}</div>
                          </div>
                          <div className="w-100 position-relative">
                            <input
                              placeholder="Enter amount"
                              type="number"
                              className={classNames(
                                "bg-cream w-100 border-0 p-2 rounded",
                                {
                                  "grayscale disabled": params.edit,
                                }
                              )}
                              value={
                                featuredServiceAmount[
                                  s.service as
                                    | "Message"
                                    | "Video Call"
                                    | "Voice Call"
                                    | "Request"
                                    | "Rating"
                                ] ?? ""
                              }
                              disabled={params.edit}
                              min={0}
                              max={
                                optionSelected === "min"
                                  ? MaxChatFeeDuration * 365 * 1440
                                  : optionSelected === "day"
                                  ? MaxChatFeeDuration * 365
                                  : 10_000
                              }
                              onChange={(e) => {
                                const rawValue = e.target.value;
                                let newValue;

                                if (
                                  rawValue.startsWith("0") &&
                                  rawValue.length > 1
                                ) {
                                  newValue = rawValue.slice(1);
                                } else if (rawValue === "") {
                                  newValue = "";
                                } else {
                                  newValue = Math.max(0, +rawValue);
                                  newValue = Math.min(
                                    newValue,
                                    optionSelected === "min"
                                      ? MaxChatFeeDuration * 365 * 1440
                                      : optionSelected === "day"
                                      ? MaxChatFeeDuration * 365
                                      : 10_000
                                  );
                                }

                                setFeaturedServiceAmount({
                                  ...featuredServiceAmount,
                                  [s.service as
                                    | "Message"
                                    | "Video Call"
                                    | "Voice Call"
                                    | "Request"
                                    | "Rating"]: newValue,
                                });
                              }}
                            />
                            <div className="position-absolute top-0 end-0 d-flex gap-1 align-items-center mt-1 me-1">
                              {s.options.map((o, idx) => {
                                return (
                                  <div
                                    key={idx}
                                    className={classNames(
                                      "p-1 rounded pointer",
                                      {
                                        "text-white":
                                          o === optionSelected ||
                                          (s.options.length === 1 &&
                                            featuredServiceAmount[
                                              s.service as
                                                | "Message"
                                                | "Video Call"
                                                | "Voice Call"
                                                | "Request"
                                                | "Rating"
                                            ] > 0),
                                      }
                                    )}
                                    style={{
                                      backgroundColor:
                                        o === optionSelected ||
                                        (s.options.length === 1 &&
                                          featuredServiceAmount[
                                            s.service as
                                              | "Message"
                                              | "Video Call"
                                              | "Voice Call"
                                              | "Request"
                                              | "Rating"
                                          ] > 0)
                                          ? "#AC1991"
                                          : `var(--bg-color)`,
                                    }}
                                    data-bs-toggle="tooltip"
                                    data-bs-placement="top"
                                    data-bs-title={
                                      o === "mes"
                                        ? `Subscribers can send you specified free messages.`
                                        : o === "day"
                                        ? `Subscribers can chat with you for free for the number of days you specify.`
                                        : `Subscribers can chat with you for free for the number of minutes you specify.`
                                    }
                                    onClick={() => {
                                      if (s.service !== "Message") return;
                                      setOptionSelected(
                                        o as "mes" | "day" | "min"
                                      );
                                    }}
                                  >
                                    {o}
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
                <hr />
                <div className="d-flex justify-content-between align-items-center flex-column flex-md-row">
                  <div className="w-100">
                    <input
                      type="checkbox"
                      className="form-check-input me-2 rounded-circle d-none"
                      id="ageConfirmation"
                      checked={true}
                      onChange={(e) =>
                        setFieldValue("servicesTicked", e.target.checked)
                      }
                    />
                    <div className="d-flex gap-1 align-items-center">
                      <div className="fw-bold fs-5">
                        2. Free Custom Services
                      </div>
                      <Image
                        src={"/images/common/information.png"}
                        width={20}
                        height={20}
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-title="Offer your custom services for free to your subscribers"
                        alt="Information"
                      />
                    </div>
                  </div>

                  <>
                    <div
                      className={classNames("dropdown w-100", {
                        "grayscale pe-none disabled cursor-not-allowed":
                          !values.servicesTicked,
                      })}
                    >
                      <div
                        className="border py-1 w-100 rounded px-2 z-3"
                        style={{ cursor: "pointer" }}
                        data-bs-toggle="dropdown"
                      >
                        Choose services
                      </div>
                      <ul
                        className="dropdown-menu pb-0"
                        style={{ maxHeight: "300px", overflowY: "auto" }}
                      >
                        {servicesData
                          .filter((s) => !s.is_default)
                          .filter((s) => s.type !== "CHAT-FEE")
                          .map((service, idx) => {
                            return (
                              <li
                                style={{
                                  borderBottom: "1px solid #cecece",
                                }}
                                className={classNames(
                                  "dropdown-item d-flex gap-2 align-items-center pointer",
                                  {
                                    "grayscale pe-none disabled cursor-not-allowed":
                                      !service.is_active ||
                                      !service.fixed_price ||
                                      service.service_type === "per_minute" ||
                                      service.type === "CHAT-FEE",
                                    "border-bottom-0":
                                      idx === servicesData.length - 1,
                                  }
                                )}
                                key={service._id}
                                onClick={() => {
                                  setFieldValue(
                                    "selectedServices",
                                    values.selectedServices.some(
                                      (s) => s._id === service._id
                                    )
                                      ? values.selectedServices.filter(
                                          (s) => s._id !== service._id
                                        )
                                      : [...values.selectedServices, service]
                                  );
                                }}
                              >
                                <div>
                                  {values.selectedServices.some(
                                    (s) => s._id === service._id
                                  ) ? (
                                    <TickIcon fill={true} />
                                  ) : (
                                    <EmptyCircle />
                                  )}
                                </div>
                                <div>
                                  {/* eslint-disable-next-line @next/next/no-img-element */}
                                  <img
                                    src={
                                      service.avatar.length > 0
                                        ? getAssetUrl({
                                            media: service.avatar[0],
                                            variation: "thumb",
                                          })
                                        : ServicesImg[service.type]
                                    }
                                    alt={service.name}
                                    height={20}
                                    width={20}
                                    fetchPriority="high"
                                  />
                                </div>
                                <div>{service.name}</div>
                              </li>
                            );
                          })}
                        <li
                          style={{
                            backgroundColor: "rgba(249, 244, 248, 1)",
                            color: "rgba(172, 25, 145, 1)",
                          }}
                          className="text-center w-100 p-2 d-flex justify-content-center align-items-center pointer gap-2 rounded-bottom"
                          onClick={() => {
                            ModalService.open("ADD_SERVICES", {
                              editValues: null,
                              cb: (data) =>
                                setServicesData((prevData) => [
                                  ...prevData,
                                  data,
                                ]),
                            });
                          }}
                        >
                          <div>
                            <PlusBtn />
                          </div>
                          <div>Add new custom service</div>
                        </li>
                      </ul>
                    </div>
                  </>
                </div>
                <div className="d-flex flex-wrap gap-2">
                  {values.selectedServices.map((service) => (
                    <div
                      key={service._id}
                      className="border p-2 rounded w-fit position-relative"
                    >
                      <div
                        className="position-absolute pointer"
                        style={{
                          top: -12,
                          right: -9,
                        }}
                        onClick={() => {
                          setFieldValue(
                            "selectedServices",
                            values.selectedServices.filter((s) => s !== service)
                          );
                        }}
                      >
                        <Image
                          className="position"
                          src={"/images/common/cancel.svg"}
                          width={20}
                          height={20}
                          alt=""
                        />
                      </div>
                      <div className="d-flex align-items-center gap-1">
                        <div>
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={
                              service.avatar.length > 0
                                ? getAssetUrl({
                                    media: service.avatar[0],
                                    variation: "thumb",
                                  })
                                : ServicesImg[service.type]
                            }
                            alt={service.name}
                            height={20}
                            width={20}
                            fetchPriority="high"
                          />
                        </div>
                        <div>{service.name}</div>
                      </div>
                    </div>
                  ))}
                </div>
                <hr />
                <div className="fw-bold fs-5">3. Custom Benefits</div>
                {values?.perks?.some((v) => v.value === "") && (
                  <span className="color-red fs-8 position-absolute bottom-0">
                    Subscription perks&apos; value is required
                  </span>
                )}
                <div>
                  <SelectArrayV2
                    name="perks"
                    getter={values.perks}
                    setter={setFieldValue}
                    isEdit={params.edit}
                    isChannel
                    inputPlaceholder="Enter your custom benefits here"
                  />
                  {values?.perks?.length === 0 &&
                    values?.selectedServices?.length === 0 &&
                    Object.keys(featuredServiceAmount).every(
                      (k) =>
                        featuredServiceAmount[
                          k as keyof typeof featuredServiceAmount
                        ] === 0
                    ) && (
                      <span className="color-red fs-8">
                        Custom Benefits are required
                      </span>
                    )}
                </div>
              </Wrapper>
              {params.edit ? (
                <button
                  className="btn color-red border-0 d-flex align-items-start gap-2 fw-medium danger no-invert bg-body py-2 rounded-lg-3 img-purple img-2red"
                  onClick={() => channelDelete()}
                  type="button"
                >
                  {" "}
                  <Image
                    alt=""
                    width={24}
                    height={24}
                    src={"/images/post/line-delete.svg"}
                  />
                  Delete my channel
                </button>
              ) : (
                <></>
              )}
              {!isKycCompleted && <KYCwarning type="channel" />}
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
}

const TickIcon = ({ fill }: { fill: boolean }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 0C4.486 0 0 4.486 0 10C0 15.514 4.486 20 10 20C15.514 20 20 15.514 20 10C20 4.486 15.514 0 10 0ZM9.06052 13.3535C8.47518 13.9388 7.52631 13.9393 6.94034 13.3546L4.99545 11.4139C4.6047 11.024 4.60391 10.3912 4.99369 10.0003C5.38371 9.60918 6.01701 9.60846 6.40793 9.9987L7.64545 11.2341C7.84076 11.429 8.1571 11.4289 8.35225 11.2338L12.586 7C12.9765 6.60953 13.6095 6.60953 14 7C14.3905 7.39046 14.3905 8.02353 14 8.414L9.06052 13.3535Z"
      fill={fill ? "#AC1991" : "none"}
    />
  </svg>
);

const EmptyCircle = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M10 0C4.477 0 0 4.477 0 10C0 15.524 4.477 20 10 20C15.523 20 20 15.524 20 10C20 4.477 15.523 0 10 0ZM10 1.5C12.2543 1.5 14.4163 2.39553 16.0104 3.98959C17.6045 5.58365 18.5 7.74566 18.5 10C18.5 12.2543 17.6045 14.4163 16.0104 16.0104C14.4163 17.6045 12.2543 18.5 10 18.5C7.74566 18.5 5.58365 17.6045 3.98959 16.0104C2.39553 14.4163 1.5 12.2543 1.5 10C1.5 7.74566 2.39553 5.58365 3.98959 3.98959C5.58365 2.39553 7.74566 1.5 10 1.5Z"
      fill="#808386"
    />
  </svg>
);

const PlusBtn = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="4" y="4" width="16" height="16" fill="white" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.33 2H16.66C20.06 2 22 3.92 22 7.33V16.67C22 20.06 20.07 22 16.67 22H7.33C3.92 22 2 20.06 2 16.67V7.33C2 3.92 3.92 2 7.33 2ZM12.82 12.83H15.66C16.12 12.82 16.49 12.45 16.49 11.99C16.49 11.53 16.12 11.16 15.66 11.16H12.82V8.34C12.82 7.88 12.45 7.51 11.99 7.51C11.53 7.51 11.16 7.88 11.16 8.34V11.16H8.33C8.11 11.16 7.9 11.25 7.74 11.4C7.58795 11.559 7.50214 11.77 7.5 11.99C7.5 12.45 7.87 12.82 8.33 12.83H11.16V15.66C11.16 16.12 11.53 16.49 11.99 16.49C12.45 16.49 12.82 16.12 12.82 15.66V12.83Z"
      fill="#AC1991"
    />
  </svg>
);
