#profile-container {
  .profile-header {
    .profile-image {
      position: relative;
      padding-top: 37.037%;
      cursor: pointer;
      img {
        position: absolute;
        top: 0;
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  }

  .profile-content {
    .profile-details {
      flex-basis: 250px;

      @media (min-width: 992px) {
        min-width: 18rem;
        max-width: 24rem;
      }
    }

    .profile-sections-container {
      // flex-basis: 35rem;
    }
  }
}

.centered-image-group {
  --h: 3rem;
  position: relative;
  height: var(--h);
  width: var(--h);

  img {
    position: absolute;
    bottom: 0;
    height: calc(var(--h) * 2);
    width: calc(var(--h) * 2);
    transform: translate(0, 0);
  }
}

.profile-view {
  position: relative;
  background: var(--bg-color);
  padding-top: 0;

  .profile-view-image-collab {
    --h: clamp(5rem, calc((100vw - 256px) / 10), 3.5rem);
    img {
      box-shadow: 0 0 0 2pt var(--bg-color);
    }
  }
}

.tag {
  padding: 0.5rem 1rem;
  border: 1pt solid var(--bg-dark);
}

.featured-container {
  position: relative;

  .featured-content {
    overflow: hidden;
    overflow-x: auto;
  }

  .featured-item {
    position: relative;
    height: 14rem;
    width: 10rem;
    min-width: 10rem;
    border-radius: var(--border-radius);
    overflow: hidden;

    .featured-title {
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: 2;
      color: var(--bg-color);
    }

    .featured-image {
      position: absolute;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
}
.profile-view-images {
  position: absolute;
  top: 0;
  // left: 60%;
  transform: translate(0%, -60%);

  // .image2 {
  //   left: -45px;
  // }
}

@media screen and (max-width: 992px) {
  .profile-view-images {
    left: 0;
    transform: translate(0, -50%);
  }
  // .profile-view-images .image2{
  //   left: -65px !important;
  // }
}
