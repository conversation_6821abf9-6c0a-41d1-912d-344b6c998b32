.post-comments {
  box-shadow: 0 -1pt 0 0 var(--bg-dark);
}

.post-comments-author {
  .dot {
    height: 0.5rem;
  }
}

.input {
  border: 0;
  background-color: #f5f5f6;
  border-radius: 6px;
  padding: 1.2%;
  width: 100%;
}

.live-details {
  display: grid;
  grid-template-areas:
    "icon time"
    "icon count";
  grid-template-columns: min-content;

  .live-time-icon {
    grid-area: icon;
    width: 3rem;
    height: 3rem;

    @media (max-width: 768px) {
      width: 2rem;
      height: 2rem;
    }
  }

  .live-time {
    grid-area: time;
  }

  .live-ticket-counts {
    grid-area: count;
  }

  @media (max-width: 768px) {
    grid-template-areas:
      "icon time time"
      "count count count";
    grid-template-columns: 2rem;
  }
}
