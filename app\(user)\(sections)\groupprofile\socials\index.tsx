"use client";

import { useState } from "react";

import Image from "next/image";

const links = [
  "https://youtube.com/example777",
  "https://youtube.com/example777",
  "https://youtube.com/example777",
  "https://youtube.com/example777",
  "https://youtube.com/example777",
];

const icons = [
  "images/svg/socials/folow.svg",
  "/images/svg/socials/phub.svg",
  "/images/svg/socials/facebook.svg",
  "/images/svg/socials/facebook.svg",
  "/images/svg/socials/facebook.svg",
];

export default function GroupSocial() {
  const [socialLink, setSocialLink] = useState([
    ...Array.from({ length: 5 }, (_, i) => ({
      link: links[i],
      icon: icons[i],
    })),
  ]);
  return (
    <div className="bg-body container d-flex flex-column p-3 rounded-3">
      <p className="fs-5 fw-medium">Social Media</p>
      {socialLink.map((social, index) => (
        <div
          className="input-group mb-3 rounded-3 bg-cream align-items-center justify-content-between"
          key={index}
        >
          <div className="social-input-wrapper d-flex w-75">
            <span
              className="input-group-text bg-transparent border-0"
              id="basic-addon1"
            >
              <Image
                width={50}
                height={24}
                alt="social-media-icon"
                src={social.icon}
              />
            </span>
            <input
              type="text"
              className="color-dark bg-transparent border-0 py-2 px-3 shadow-none w-75 w-sm-100 text-overflow-ellipsis"
              placeholder={social.link}
              aria-label="Social Media link"
              aria-describedby="basic-addon1"
              // value={social.link}
            />
          </div>
          <Image
            width={25}
            height={25}
            alt="remove-social"
            className="invert mx-2 pointer"
            src="/images/svg/activepoll.svg"
          />
        </div>
      ))}
      <div className="input-group mb-3 rounded-3 bg-cream align-items-center justify-content-between">
        <div className="dropdown d-flex w-100">
          <span
            className="input-group-text bg-transparent border-0"
            id="basic-addon1"
          >
            <Image
              width={50}
              height={24}
              alt="social-media-icon"
              className="invert"
              src="/images/svg/socials/empty.svg"
            />
          </span>
          <button
            className="btn bg-transparent color-dark fs-6 fw-normal flex-grow-1 d-flex"
            type="button"
            id="dropdownMenuButton"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
          >
            <span className="flex-grow-1">Add new</span>
            <Image
              src={"/images/svg/chevron-down.svg"}
              width={25}
              height={25}
              alt="dropdown"
              className="svg-icon"
            />
          </button>
          <div
            className="dropdown-menu w-75"
            aria-labelledby="dropdownMenuButton"
          >
            <a className="dropdown-item fs-6" href="#">
              Action
            </a>
            <a className="dropdown-item fs-6" href="#">
              Another action
            </a>
            <a className="dropdown-item fs-6" href="#">
              Something else here
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
