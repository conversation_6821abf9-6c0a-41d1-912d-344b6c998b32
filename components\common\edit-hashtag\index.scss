.edit-serch-hashtag-menu {
  background-color: var(--bg-color);
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 23.5rem;
  border-radius: 1rem;
  padding: 4%;
  display: flex;
  flex-direction: column;
  max-height: 19rem;
  overflow: auto;

  li {
    list-style: none;
  }

  @media (max-width: 768px) {
    min-width: 20.5rem;

    button {
      font-size: 0.8rem;
    }
  }
}
.editTag {
  padding: 0.5rem;
  border: 1pt solid var(--bg-dark);
}
.edit-hashtag-container {
  height: 50vh;
}
.edit-hashtag-width {
  width: 50%;

  @media (max-width: 768px) {
    width: 100%;
  }
}
.touch-event {
  pointer-events: none;
}
.search-box {
  border: 1px solid rgba(19, 20, 22, 1);
}

.ban-word-error {
  position: absolute;
  bottom: -25px;
  color: red;
  font-size: 14px;
}
