import type { MediaBody } from "@/types/post";

import API from ".";

export interface GetMediaListResponse {
  data: MediaBody[];
}

export const GetProfileMedia = async (
  inputs: Record<string, any>
): Promise<GetMediaListResponse> => {
  const { page, userId, mediaPostType, mediaContentType, mediaTypeFilter } =
    inputs;
  return API.get(
    `${API.USERS}/media?page=${page}&user_id=${userId}${
      mediaContentType ? `&type=${mediaContentType}` : ""
    }${mediaPostType ? `&visibility=${mediaPostType}` : ""}${
      mediaTypeFilter ? `&post_media_type=${mediaTypeFilter}` : ""
    }`
  );
};

export const GetGroupMedia = async (
  inputs: Record<string, any>
): Promise<GetMediaListResponse> => {
  const {
    page,
    groupId,
    userId,
    mediaPostType,
    mediaContentType,
    mediaTypeFilter,
  } = inputs;

  return API.get(
    `${API.GROUP}/media?page=${page}&group_id=${groupId}&user_id=${userId}${
      mediaContentType ? `&type=${mediaContentType}` : ""
    }${mediaPostType ? `&visibility=${mediaPostType}` : ""}${
      mediaTypeFilter ? `&post_media_type=${mediaTypeFilter}` : ""
    }`
  );
};

export const GetChannelMedia = async (
  inputs: Record<string, any>
): Promise<GetMediaListResponse> => {
  const {
    page,
    channelId,
    userId,
    mediaPostType,
    mediaContentType,
    mediaTypeFilter,
  } = inputs;
  return API.get(
    `${
      API.CHANNEL
    }/media?page=${page}&channel_id=${channelId}&user_id=${userId}${
      mediaContentType ? `&type=${mediaContentType}` : ""
    }${mediaPostType ? `&visibility=${mediaPostType}` : ""}${
      mediaTypeFilter ? `&post_media_type=${mediaTypeFilter}` : ""
    }`
  );
};
