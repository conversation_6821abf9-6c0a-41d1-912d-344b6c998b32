import Link from "next/link";
import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import { memo, useEffect, useState } from "react";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

import { Get<PERSON>hannelByUsername } from "@/api/channel";
import { GetGroupByUsername } from "@/api/group";
import { GetSinglePost } from "@/api/post";
import { getSingleProduct } from "@/api/shop";
import ActionButton from "@/components/common/action-button";
import DateFormatter from "@/components/common/date";
import { ModalService } from "@/components/modals";
import useIsMobile from "@/hooks/useIsMobile";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import type { Media } from "@/types/media";
import type { Author } from "@/types/post";
import type { SellItem } from "@/types/sell-item";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";

import "swiper/css";
import "swiper/css/navigation";

import LoadingSpinner from "@/components/common/loading";

import ReadMoreContent from "../../../read-more-content";

const Embeds = ({
  message,
  usedAs,
}: {
  usedAs: "receiver" | "sender";
  message: MessageInterface;
}) => {
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const refresh = searchParams.get("refresh");

  const embeds = useAppSelector((s) => s.chat.embeds);
  const channelId = useAppSelector((s) => s.chat.channelId);
  const chatList = useAppSelector((s) => s.chat.chatList);
  const [loading, setLoading] = useState(false);

  const [embedDetails, setEmbedDetails] = useState<any>({});

  useEffect(() => {
    embeds.find((embed) => {
      if (embed._id === message?.meta?.entity_id) setEmbedDetails(embed);
    });
  }, [embeds.length, refresh]);

  useEffect(() => {
    if (loading) return;

    requestAnimationFrame(() => {
      const element = document.getElementsByClassName("chat-content-body")[0];

      if (element) {
        element.scrollTop = element.scrollHeight;
      }
    });
  }, [embedDetails, loading]);

  useEffect(() => {
    if (message?.meta?.type !== "EMBEDS") return;

    const findChat = chatList?.find(
      (chat) => chat.converse_channel_id === channelId
    );

    if (!findChat) return;

    const { entity_id, sub_type } = message.meta;

    const fetchEmbedData = async () => {
      setLoading(true);

      try {
        let response;

        switch (sub_type) {
          case "POST":
            response = await GetSinglePost(entity_id);
            break;
          case "PRODUCT":
            response = await getSingleProduct(entity_id!);
            break;
          case "CHANNEL":
            response = await GetChannelByUsername(entity_id!);
            break;
          case "GROUP":
            response = await GetGroupByUsername(entity_id!);
            break;
          default:
            return;
        }

        if (response?.data) {
          dispatch(chatActions.setEmbeds(response.data?.[0] || response.data));
        }
      } catch (error) {
        console.error("Failed to fetch embed data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEmbedData();
  }, [message?.meta?.entity_id, refresh]);

  const embedMessageTypeRender = () => {
    const baseSubscription = embedDetails?.subscription?.reduce(
      (lowest: any, current: any) => {
        if (current.price < lowest.price) {
          return current;
        }

        return lowest;
      },
      { price: Infinity, validity_type: "" }
    );

    switch (message?.meta?.sub_type) {
      case "POST":
        return (
          <RenderEmbeddBubble
            caption={embedDetails?.caption || ""}
            media={embedDetails?.media || []}
            visibiility="Premium"
            price={embedDetails?.pay_and_watch_rate || 0}
            created_at={embedDetails?.created_at || new Date().toISOString()}
            author={embedDetails?.author}
            message_type={message?.meta?.sub_type}
            entity_id={message?.meta?.entity_id || ""}
            usedAs={usedAs}
            message={message}
            isPurchased={embedDetails?.is_purchased}
          />
        );
      case "PRODUCT":
        return (
          <RenderEmbeddBubble
            name={embedDetails?.name}
            caption={embedDetails?.description || ""}
            media={embedDetails?.media || []}
            visibiility="Public"
            price={embedDetails?.price || 0}
            created_at={embedDetails?.created_at || new Date().toISOString()}
            author={embedDetails?.author}
            category={embedDetails?.category}
            message_type={message?.meta?.sub_type}
            entity_id={message?.meta?.entity_id || ""}
            usedAs={usedAs}
            message={message}
            productInformation={embedDetails}
          />
        );

      case "CHANNEL":
      case "GROUP":
        return (
          <RenderChannelOrCollab
            entity_id={message?.meta?.entity_id || ""}
            entity_type={
              message?.meta?.sub_type === "GROUP" ? "collab" : "channel"
            }
            avatar={
              message?.meta?.sub_type === "GROUP"
                ? embedDetails?.members?.map(
                    (m: any) => m?.member_id?.avatar[0]
                  )
                : embedDetails?.avatar
            }
            background={embedDetails?.background}
            name={embedDetails?.name}
            username={
              message?.meta?.sub_type === "GROUP"
                ? embedDetails?.tag_name
                : embedDetails?.channel_tagname
            }
            no_of_posts={embedDetails?.counter?.post_count || 0}
            no_of_subs={embedDetails?.counter?.subscriber_count}
            usedAs={usedAs}
            base_subscription={{
              price: baseSubscription?.price || 0,
              time: baseSubscription?.validity_type || "",
            }}
            is_subscribed={embedDetails?.is_subscribed}
            subscriptions={embedDetails.subscription}
          />
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="p-2 d-flex flex-column justify-content-center align-items-center">
        <LoadingSpinner />
        <div className="color-medium fs-8 fst-italic">
          Hold tight while we load your embed
        </div>
      </div>
    );
  }

  return <div>{embedMessageTypeRender()}</div>;
};

export default memo(Embeds);

const RenderEmbeddBubbleBase = (props: {
  name?: string;
  media: Media[];
  caption: string;
  price?: number;
  visibiility: "Public" | "Premium" | "Others";
  created_at: string;
  author?: Author;
  category?: string;
  message_type: "POST" | "PRODUCT" | "CHANNEL" | "GROUP";
  entity_id: string;
  usedAs: "receiver" | "sender";
  message: MessageInterface;
  isPurchased?: boolean;
  productInformation?: SellItem;
}) => {
  return (
    <div className="p-2">
      <div className="w-100 position-relative">
        <ImageSlider media={props.media} />
        {props.usedAs === "receiver" && !props.isPurchased && (
          <div
            className="position-absolute z-3"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          >
            {props.price && (
              <ActionButton
                onClick={() => {
                  if (props.message_type === "PRODUCT") {
                    ModalService.open("SHOP_PURCHASE", {
                      selectedItem: props.productInformation!,
                    });
                  } else {
                    ModalService.open("BUY_ENTITY_CHAT", {
                      entity_id: props.entity_id,
                      entity_type: props.message_type,
                      amount: props.price ?? 0,
                    });
                  }
                }}
              >
                Buy {formatCurrency(props.price)}
              </ActionButton>
            )}
          </div>
        )}
      </div>
      {props.name && <div className="mt-2 fw-bold">{props.name}</div>}

      <div className="d-flex">
        <ReadMoreContent
          text={props.caption || ""}
          classes="fs-7"
          characterCount={500}
        />
      </div>
      {props.category && (
        <div className="fs-7 my-2">Category: {props.category}</div>
      )}
      <Link
        className="underline fs-8 text-end w-100"
        style={{
          color: "#ac1991",
        }}
        href={
          props.message_type === "PRODUCT"
            ? `/shop/${props.entity_id}/preview`
            : props.message_type === "POST"
            ? `/post/${props.entity_id}`
            : ""
        }
      >
        View {props.message_type.toLowerCase()}
      </Link>

      <Link
        href={`/${props.author?.user_type?.toLowerCase()}/${
          props.author?.username
        }`}
        className="d-flex gap-2 align-items-center pointer"
      >
        <div className="fw-bold mt-2">
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            src={getAssetUrl({
              media: props.author?.avatar?.[0],
              defaultType: "avatar",
            })}
            height={40}
            width={40}
            alt="Avatar"
            className="rounded-circle"
          />
        </div>
        <div className="d-flex flex-column">
          <span className="fw-bold fs-7">{props.author?.display_name}</span>
          <span className="color-medium fs-8">@{props.author?.username}</span>
        </div>
      </Link>

      <div className="d-flex gap-2 align-items-center color-medium fs-7 w-100">
        <DateFormatter
          dateString={props.created_at}
          formatType={
            new Date(props.created_at).getFullYear() ===
            new Date().getFullYear()
              ? "MMM dd"
              : "MMM dd, yy"
          }
        />
        <div className="d-flex align-items-center justify-content-center">
          <DividerIcon />
        </div>
        <div className="d-flex align-items-center justify-content-center gap-1">
          <div className="w-fit h-100 d-flex align-items-center justify-content-center">
            {props.visibiility === "Premium" ? <PremiumIcon /> : <PublicIcon />}
          </div>
          <div className="w-100 h-100">{props.visibiility}</div>
        </div>
      </div>
      {props.price && (
        <div className="fw-bold">{formatCurrency(props.price)}</div>
      )}
    </div>
  );
};

const RenderEmbeddBubble = memo(RenderEmbeddBubbleBase);

const PremiumIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.99986 10.5803C6.56252 10.5803 5.39852 9.42233 5.39852 8.00033C5.39852 6.57166 6.56252 5.41433 7.99986 5.41433C8.68684 5.41344 9.34607 5.68531 9.83272 6.1702C10.3194 6.6551 10.5936 7.31335 10.5952 8.00033C10.5952 9.42233 9.43053 10.5803 7.99986 10.5803ZM14.6279 7.80632C13.9312 6.19699 12.9625 4.91033 11.8245 4.03099C10.6865 3.14566 9.37853 2.66699 7.99986 2.66699H7.99319C5.24186 2.66699 2.76385 4.58699 1.37252 7.80632C1.34677 7.86659 1.3335 7.93145 1.3335 7.99699C1.3335 8.06253 1.34677 8.12739 1.37252 8.18766C2.76385 11.407 5.24186 13.3337 7.99319 13.3337H7.99986C8.49242 13.3337 8.97604 13.2722 9.44615 13.153C9.37188 12.8897 9.33351 12.6146 9.33351 12.3336C9.33351 11.5323 9.64555 10.7789 10.2122 10.2123C10.7788 9.64569 11.5322 9.33364 12.3335 9.33364C12.8634 9.33364 13.3724 9.47012 13.8203 9.72644C14.1187 9.25367 14.3892 8.73968 14.6279 8.18766C14.6536 8.12738 14.6668 8.06252 14.6668 7.99699C14.6668 7.93146 14.6536 7.8666 14.6279 7.80632ZM9.142 9.138C8.83842 9.43958 8.42778 9.60868 7.99986 9.60833H8.00053C7.10252 9.60833 6.37386 8.88433 6.37386 7.99899C6.37386 7.88899 6.38719 7.78566 6.40586 7.68233H6.43919C6.78391 7.6831 7.11549 7.55013 7.36417 7.3114C7.61285 7.07267 7.75923 6.74679 7.77253 6.40233C7.84386 6.38966 7.92186 6.38299 7.99986 6.38299C8.42882 6.38299 8.84027 6.55312 9.14396 6.85606C9.44766 7.15901 9.6188 7.57003 9.61986 7.99899C9.61739 8.4269 9.44559 8.83642 9.142 9.138Z"
      fill="#FFB800"
    />
    <path
      d="M13.9833 10.6834C13.5425 10.2427 12.9566 10 12.3333 10C11.7101 10 11.1241 10.2427 10.6834 10.6834C10.2427 11.1241 10 11.7101 10 12.3333C10 12.9566 10.2427 13.5425 10.6834 13.9833C11.1241 14.424 11.7101 14.6667 12.3333 14.6667C12.9566 14.6667 13.5425 14.424 13.9833 13.9833C14.424 13.5425 14.6667 12.9566 14.6667 12.3333C14.6667 11.7101 14.424 11.1241 13.9833 10.6834ZM12.3333 12.1875C12.6952 12.1875 12.9896 12.4819 12.9896 12.8438C12.9896 13.1434 12.7876 13.3966 12.5127 13.4749C12.493 13.4805 12.4792 13.4983 12.4792 13.5188V13.6417C12.4792 13.7202 12.4188 13.7878 12.3404 13.7915C12.2566 13.7954 12.1875 13.7287 12.1875 13.6458V13.5188C12.1875 13.4983 12.1738 13.4805 12.1541 13.4749C11.8803 13.3971 11.6789 13.1458 11.6771 12.8478C11.6766 12.7683 11.7391 12.7003 11.8186 12.698C11.9011 12.6956 11.9688 12.7618 11.9688 12.8438C11.9688 13.0534 12.1466 13.2223 12.3594 13.2074C12.539 13.1949 12.6845 13.0494 12.697 12.8698C12.7119 12.657 12.543 12.4792 12.3333 12.4792C11.9715 12.4792 11.6771 12.1848 11.6771 11.8229C11.6771 11.5233 11.879 11.27 12.1539 11.1918C12.1737 11.1862 12.1875 11.1684 12.1875 11.1479V11.025C12.1875 10.9464 12.2478 10.8789 12.3263 10.8752C12.41 10.8712 12.4792 10.938 12.4792 11.0208V11.1479C12.4792 11.1684 12.4929 11.1862 12.5126 11.1918C12.7863 11.2696 12.9878 11.5209 12.9896 11.8188C12.9901 11.8984 12.9275 11.9664 12.848 11.9687C12.7655 11.9711 12.6979 11.9049 12.6979 11.8229C12.6979 11.6133 12.52 11.4444 12.3073 11.4592C12.1277 11.4718 11.9822 11.6173 11.9697 11.7969C11.9548 12.0096 12.1237 12.1875 12.3333 12.1875Z"
      fill="#FFB800"
    />
  </svg>
);

const PublicIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 2C4.6915 2 2 4.6915 2 8C2 11.3085 4.6915 14 8 14C11.3085 14 14 11.3085 14 8C14 4.6915 11.3085 2 8 2ZM8 13C6.75 13 5.6085 12.5355 4.731 11.7745C4.735 11.768 4.7405 11.7625 4.744 11.756C5.57 10.372 5.0505 9.5265 4.667 9.1455C4.566 9.045 4.4505 8.9535 4.3315 8.8615C4.0205 8.62 3.8145 8.4605 3.8145 8C3.8145 7.783 3.9355 7.698 4.533 7.494C4.944 7.3535 5.4095 7.1945 5.71 6.806C6.4075 5.904 5.6405 4.513 5.4795 4.243C5.397 4.1045 5.3075 3.9745 5.2145 3.85C5.966 3.344 6.863 3.042 7.827 3.0085C7.983 3.273 8.2405 3.496 8.4935 3.7135C8.7095 3.8995 9.0525 3.9775 9.229 4.1975C9.33 4.3235 9.281 4.5665 9.2845 4.7255C9.2915 5.041 9.4825 5.4985 10.1265 5.4985C10.155 5.4985 10.1845 5.4975 10.215 5.4955C10.464 5.478 11.241 5.3 12.022 5.042C12.634 5.872 13 6.893 13 8C13 10.757 10.757 13 8 13Z"
      fill="#131416"
    />
    <path
      d="M9.757 6.4515C8.969 6.4515 8.1645 6.826 7.7555 7.383C7.481 7.757 7.401 8.1895 7.53 8.601C7.7875 9.422 7.8125 9.906 7.6515 10.4485C7.5085 10.9305 7.4945 11.6295 8.311 11.9625C8.465 12.0255 8.634 12.0575 8.812 12.0575C9.419 12.0575 10.1375 11.682 10.784 11.0265C11.561 10.239 12.018 9.274 12.007 8.444C11.992 7.327 11.0035 6.4515 9.757 6.4515Z"
      fill="#131416"
    />
  </svg>
);

const DividerIcon = () => (
  <svg
    width="4"
    height="4"
    viewBox="0 0 4 4"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="2" cy="2" r="2" fill="#AFB1B3" />
  </svg>
);

const ImageSliderBase = (props: { media: Media[] }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  return (
    <div className="d-flex justify-content-center align-content-center position-relative">
      <Swiper
        modules={[Navigation]}
        spaceBetween={10}
        slidesPerView={1}
        navigation
        loop={true}
        onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
        className="w-100"
      >
        {props.media.length > 1 && (
          <div className="position-absolute bottom-0 start-0 m-2 z-2 bg-body rounded p-1 fs-8 fw-bold">
            {isNaN(activeIndex) ? 1 : activeIndex + 1} / {props.media.length}
          </div>
        )}
        {props.media.map((media) => (
          <SwiperSlide key={media._id} className="position-relative">
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={getAssetUrl({
                media: media,
                defaultType: "background",
                variation: media?.variations.includes("compressed")
                  ? "compressed"
                  : "blur",
                poster: media?.poster ? true : false,
              })}
              height={150}
              alt={`Slide ${media._id}`}
              className="object-fit-cover w-100 text-center d-block rounded-1 pointer"
            />
            <div
              className="position-absolute top-0 end-0 m-2 z-2 p-1 rounded"
              style={{
                background: "rgba(0,0,0,0.4)",
                filter: "brightness(2)",
              }}
            >
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={`/images/home/<USER>
                height={24}
                width={24}
                alt=""
              />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

const ImageSlider = memo(ImageSliderBase);

const RenderChannelOrCollabBase = (props: {
  entity_type: "channel" | "collab";
  entity_id: string;
  avatar: Media[];
  background: Media[];
  name: string;
  username: string;
  no_of_posts: number;
  no_of_subs: number;
  usedAs: "receiver" | "sender";
  is_subscribed?: boolean;
  base_subscription: {
    price: number;
    time: string;
  };
  subscriptions: {
    _id: string;
    offer_type: string;
    trial_period: number;
    validity_type: string;
    price: number;
    is_active: boolean;
    ownership_type: string;
    is_subscribed: boolean;
  }[];
}) => {
  const router = useRouter();
  const isMobile = useIsMobile();

  return (
    <div className="p-2 position-relative">
      <div
        className={`position-absolute fw-medium px-2 py-1 ${
          props.entity_type === "collab" ? "bg-blue" : "bg-success"
        } text-white rounded-2 fs-8 top-0 end-0 m-2`}
      >
        {props.entity_type.charAt(0).toUpperCase() +
          props.entity_type.slice(1).toLowerCase()}
      </div>
      <div>
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src={getAssetUrl({
            media: props?.background?.[0],
            defaultType: "background",
            variation: "compressed",
          })}
          height={150}
          width={150}
          alt=""
          className="object-fit-cover w-100 text-center d-block rounded-1"
        />
      </div>
      <div
        className="d-flex gap-2 align-items-end"
        style={{
          marginTop: -15,
        }}
        onClick={() => {
          router.push(`/${props.entity_type}/${props.username}`);
        }}
      >
        <div className="d-flex align-items-center">
          {props.avatar?.length > 0 &&
            props.avatar.slice(0, isMobile ? 2 : 3).map((avatar, idx) => (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                key={avatar?._id}
                src={getAssetUrl({
                  media: avatar,
                  defaultType: "avatar",
                  variation: "compressed",
                })}
                alt=""
                className="rounded-circle border border-white"
                style={{
                  width: props.entity_type === "collab" ? 80 : "100px",
                  height: props.entity_type === "collab" ? 80 : "100px",
                  objectFit: "cover",
                  marginLeft: idx > 0 ? "-40px" : "0",
                }}
              />
            ))}
        </div>

        <div>
          <div className="fw-bold">{props.name}</div>
          <div className="d-flex align-items-center color-medium fs-8 gap-2">
            <div>@{props.username}</div>
            <DividerIcon />
            <div>
              {props.no_of_posts} {props.no_of_posts > 1 ? "posts" : "post"}
            </div>
          </div>
          <div className="fw-bold fs-8">
            {props.no_of_subs}{" "}
            {props.no_of_subs > 1 ? "subscribers" : "subscriber"}
          </div>
        </div>
      </div>
      <div className="color-medium fst-italic fs-7 mt-2">
        From {formatCurrency(props.base_subscription.price)} /{" "}
        {props.base_subscription?.time.toLowerCase()}
      </div>
      {props.usedAs === "receiver" && (
        <>
          <br />
          {props.is_subscribed && (
            <Link
              className="underline pointer"
              style={{
                color: "#ac1991",
              }}
              href={`/${props.entity_type}/${props.username}`}
            >
              View {props.entity_type}
            </Link>
          )}
          {!props.is_subscribed && (
            <div
              className="underline pointer"
              style={{
                color: "#ac1991",
              }}
              onClick={() => {
                ModalService.open("BUY_ENTITY_CHAT", {
                  entity_id: props.entity_id,
                  entity_type:
                    props.entity_type === "channel" ? "CHANNEL" : "GROUP",
                  subscriptions: props.subscriptions,
                });
              }}
            >
              Subscribe now!
            </div>
          )}
        </>
      )}
    </div>
  );
};

const RenderChannelOrCollab = memo(RenderChannelOrCollabBase);
