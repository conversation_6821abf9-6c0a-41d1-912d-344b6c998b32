import Image from "next/image";
import type { <PERSON><PERSON><PERSON>Hand<PERSON> } from "react";

interface ButtonValues {
  img?: any;
  width?: number;
  height?: number;
  text?: string;
  onClick?: MouseEventHandler;
  className?: string;
}

export default function PostButton(buttonValues: ButtonValues) {
  return (
    <button
      className={`btn ${buttonValues.className}`}
      onClick={buttonValues.onClick}
    >
      {buttonValues.img && (
        <Image
          src={buttonValues.img}
          width={buttonValues.width}
          height={buttonValues.height}
          alt="svg"
        />
      )}
      <span className="btn-text">{buttonValues.text}</span>
    </button>
  );
}
