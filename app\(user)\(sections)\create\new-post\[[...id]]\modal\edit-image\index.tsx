import { useEffect, useState } from "react";

import Modal, { type ModalImportProps } from "@/components/common/modal";

import { MediaPost } from "../../media";

export default function EditImage({
  setChildRef,
  misc,
  index,
  setCroppedPostUrl,
}: ModalImportProps & any) {
  const [currentUrl, setCurrentUrl] = useState<string>(misc);
  console.log({ misc });

  useEffect(() => {
    setCurrentUrl(misc);
  }, [misc]);

  const setCroppedPostUrlFunc = (dataUrl: any) => {
    setCroppedPostUrl(URL.createObjectURL(dataUrl), dataUrl, index);
  };

  return (
    <Modal
      title="Edit Image"
      subtitle={[""]}
      setChildRef={setChildRef}
      render={"direct"}
    >
      <div className="container ">
        <MediaPost
          postImg={currentUrl}
          // postVid={postVid}
          onCropComplete={setCroppedPostUrlFunc}
          showdDoneButton={true}
          // removePost={removePost}

          multipleImages={true}
        />
      </div>
    </Modal>
  );
}
