import { useEffect, useState } from "react";

import type { TicketResponse } from "@/api/event";
import { Table } from "@/components/common/event/table";
import { User } from "@/components/common/event/user";
import { formatCurrency } from "@/utils/formatter";

import DateFormatter from "../../date";

type TicketSoldCols = "payment_id" | "date" | "user" | "amount";

export function TicketSoldTable({
  data,
  ticket_amount = 0,
}: {
  data: TicketResponse[];
  ticket_amount: number;
}) {
  const TicketSoldColumns: { name: string; key: TicketSoldCols }[] = [
    { name: "Payment ID", key: "payment_id" },
    { name: "Date time", key: "date" },
    { name: "User", key: "user" },
    { name: "Amount", key: "amount" },
  ];
  const [TicketSoldRows, setTicketSoldRows] = useState<
    Record<TicketSoldCols, any>[]
  >([]);

  useEffect(() => {
    (() => {
      const r = data.map((tip) => {
        return {
          payment_id: tip._id,
          date: (
            <DateFormatter
              formatType="MMM dd, yyyy HH:mm:ss"
              dateString={tip.created_at || ""}
            />
          ),
          user: (
            <User
              display_name={tip.buyer.display_name}
              username={tip.buyer.username}
              avatar={tip.buyer.avatar?.[0]?.path}
            />
          ),
          amount: formatCurrency(ticket_amount),
        };
      });
      setTicketSoldRows(r);
    })();
  }, [data]);

  return <Table columns={TicketSoldColumns} rows={TicketSoldRows} />;
}
