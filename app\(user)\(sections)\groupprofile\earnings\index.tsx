import Image from "next/image";

import { useState } from "react";

const members = [
  { name: "<PERSON>" },
  { name: "<PERSON>" },
  { name: "<PERSON>" },
  { name: "<PERSON><PERSON><PERSON>" },
];

export default function GroupEarning() {
  const [owner, setOwner] = useState(0);
  return (
    <div className="bg-body container d-flex flex-column p-3 rounded-3 gap-3">
      <p className="fs-5 fw-medium mb-0">Members and Earnings</p>
      <p className="align-self-start color-medium fs-7 mb-0">
        Setup group profile member and earning percent of each member.
      </p>
      {members.map((member, index) => (
        <div
          className="group-members-wrapper d-flex flex-column gap-3"
          key={member.name}
        >
          <div className="member-box-wrapper d-flex gap-3 align-items-center">
            <div className="profile-wrapper">
              <Image
                width={100}
                height={100}
                className="img-fluid rounded-circle"
                src={"/images/common/default.svg"}
                alt="profile-img"
              />
            </div>
            <div className="d-flex flex-column gap-1">
              <div className="d-flex flex-row gap-3 align-items-center">
                <p className="fs-6 fw-medium mb-0">{member.name}</p>
                <p
                  className={`color-medium mb-0 fs-7 ${
                    owner === index && "color-green"
                  }`}
                >
                  {owner === index ? "Owner" : "Member"}
                </p>
                {owner != index && (
                  <div className="dropdown">
                    <button
                      className="fs-7 color-medium underline pointer bg-transparent border-0"
                      type="button"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                    >
                      Options
                    </button>
                    <ul
                      className="dropdown-menu p-0"
                      style={{ width: "max-content" }}
                    >
                      <li className="d-flex p-1 align-items-center">
                        <button
                          className="fs-6 mb-0 fw-medium border-0 bg-transparent px-2 d-flex align-items-center gap-2 justify-content-start"
                          type="button"
                          onClick={() => {
                            setOwner(index);
                          }}
                        >
                          <Image
                            src={"/images/svg/settings.svg"}
                            width={25}
                            height={25}
                            alt="set as owner"
                          />
                          Set as owner
                        </button>
                      </li>
                      <li className="d-flex p-1 align-items-center w-100">
                        <button
                          className="fs-6 mb-0 fw-medium border-0 bg-transparent px-2 d-flex align-items-center gap-2 justify-content-start"
                          type="button"
                        >
                          <Image
                            src={"/images/svg/delete-group.svg"}
                            width={25}
                            height={25}
                            alt="set as owner"
                          />
                          Remove this participant
                        </button>
                      </li>
                    </ul>
                  </div>
                )}
              </div>
              <p className="align-self-start color-medium fs-7 mb-0">
                @user123
              </p>
            </div>
          </div>
          <div className="member-earning-wrapper">
            <p className="align-self-start color-medium fs-7 mb-1">
              Earning percent<span className="color-red">*</span>
            </p>
            <div className="input-group mb-2 d-flex bg-cream w-75 w-sm-100 rounded-3">
              <span
                className="input-group-text bg-transparent border-0 color-dark fw-medium py-0"
                id="basic-addon1"
              >
                %
              </span>
              <input
                type="text"
                className="color-dark bg-transparent border-0 py-2 px-2 shadow-none flex-grow-1 fs-6 fw-medium"
                placeholder="0"
                aria-label="Earning percent"
                aria-describedby="basic-addon1"
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
