import { useEffect } from "react";

import { EventBridge } from "@/app/event-bridge";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";

const useChatNotification = (prevMessages: any[], currMessages: any[]) => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    const s = EventBridge.on("Notification", (notif) => {
      if (notif.type !== "ChatMessageUpdate") return;

      const { message_id, message_meta } = notif.data;

      // Update the message in prevMessages if found
      const updatedPrevMessages = prevMessages.map((message) => {
        if (message?._id === message_id || message?.messageId === message_id) {
          return {
            ...message,
            meta: {
              ...message?.meta,
              ...message_meta,
            },
          };
        }

        return message;
      });

      // Update the message in currMessages if found
      const updatedCurrMessages = currMessages.map((message) => {
        if (message?._id === message_id || message?.messageId === message_id) {
          return {
            ...message,
            meta: {
              ...message?.meta,
              ...message_meta,
            },
          };
        }

        return message;
      });

      // Dispatch the updated arrays
      dispatch(chatActions.setPrevChat(updatedPrevMessages));
      dispatch(chatActions.setCurrentChat(updatedCurrMessages));
    });

    // Cleanup subscription
    return () => {
      s.unsubscribe();
    };
  }, [currMessages, prevMessages, dispatch]);
};

export default useChatNotification;