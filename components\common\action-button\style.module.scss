@mixin button-styles(
  $bg-color,
  $text-color,
  $hover-bg-color,
  $hover-bg-text,
  $border-color: none,
  $type: solid
) {
  @if $type == solid {
    background: $bg-color !important;
    color: $text-color !important;
    border: 1px solid transparent !important;
  } @else if $type == outline {
    background: transparent !important;
    color: $text-color !important;
    border: 1px solid $border-color !important;
  }

  border-radius: 8px;
  transition: background 0.3s ease, color 0.3s ease;

  &:hover {
    @if $type == solid {
      background: $hover-bg-color !important;
      color: $hover-bg-text !important;
    } @else if $type == outline {
      background: $hover-bg-color !important;
      color: $hover-bg-text !important;
    }
  }

  &:disabled {
    background: var(--disabled-color) !important;
    color: #eaeaea !important;
    cursor: not-allowed;
  }

  .container {
    @if $type == solid {
      --uib-color: #fff;
    } @else if $type == outline {
      --uib-color: #{$border-color};
    }
  }
}

.btn {
  font-weight: bold;
  min-width: 7rem;
  padding: 0.375rem 0.75rem;
}

.btn-knky-primary {
  @include button-styles(
    var(--primary-color),
    #ffffff,
    var(--primary-hover-color),
    #ffffff,
    none,
    solid
  );
  &.outline {
    @include button-styles(
      #ffffff,
      var(--primary-color),
      var(--secondary-hover-color),
      var(--primary-hover-color),
      var(--primary-color),
      outline
    );
  }
  &:disabled {
    background: var(--disabled-color) !important;
    color: #eaeaea;
    cursor: not-allowed;
  }
}
.btn-knky-prime {
  @include button-styles(
    var(--unlimited-color),
    #ffffff,
    var(--unlimited-hover-color),
    #ffffff,
    none,
    solid
  );
  &.outline {
    @include button-styles(
      #ffffff,
      var(--primary-color),
      var(--secondary-hover-color),
      var(--primary-hover-color),
      var(--primary-color),
      outline
    );
  }
}

.btn-knky-dark {
  @include button-styles(
    var(--black-color),
    var(--light-gray-color),
    var(--hover-black-color),
    var(--secondary-hover-color),
    solid
  );
  &.outline {
    @include button-styles(
      var(--secondary-color),
      var(--black-color),
      var(--light-gray-color),
      var(--black-color),
      var(--black-color),
      outline
    );
  }
}

.btn-knky-info {
  @include button-styles(
    var(--subaction-color),
    #ffffff,
    var(--subaction-hover-color),
    #ffffff,
    none,
    solid
  );
  &.outline {
    @include button-styles(
      #ffffff,
      var(--subaction-color),
      #e9f7ff,
      var(--subaction-color),
      var(--subaction-color),
      outline
    );
  }
}

.btn-knky-warning {
  @include button-styles(
    var(--warning-color),
    #ffffff,
    var(--warning-hover-color),
    #ffffff,
    none,
    solid
  );
  &.outline {
    @include button-styles(
      #fff,
      var(--warning-color),
      var(--warning-hover-outline-color),
      var(--warning-color),
      var(--warning-color),
      outline
    );
  }
}

.btn-knky-danger {
  @include button-styles(
    var(--dangerous-color),
    #ffffff,
    rgb(139, 0, 0),
    #ffffff,
    none,
    solid
  );
  &.outline {
    @include button-styles(
      #fff,
      var(--dangerous-color),
      rgba(241, 30, 17, 0.3),
      var(--dangerous-color),
      var(--dangerous-color),
      outline
    );
  }
}

.btn-knky-success {
  @include button-styles(
    var(--green-color),
    #ffffff,
    var(--green-hover-color),
    #ffffff,
    none,
    solid
  );
  &.outline {
    @include button-styles(
      var(--secondary-color),
      var(--green-color),
      rgba(41, 168, 30, 0.3),
      var(--green-color),
      var(--green-color),
      outline
    );
  }
}

.btn-knky-white {
  @include button-styles(#ffffff, black, #d3d4d5, black, none, solid);

  &.outline {
    @include button-styles(#ffffff, white, #d3d4d5, black, white, outline);
  }
}

.container {
  --uib-size: 20px;
  --uib-color: black;
  --uib-speed: 0.9s;
  --uib-bg-opacity: 0.1;
  height: var(--uib-size);
  width: var(--uib-size);
  transform-origin: center;
  overflow: visible;
}

.car {
  fill: none;
  stroke: var(--uib-color);
  stroke-dasharray: 15, 85;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: travel var(--uib-speed) linear infinite;
  will-change: stroke-dasharray, stroke-dashoffset;
  transition: stroke 0.5s ease;
}

.track {
  stroke: var(--uib-color);
  opacity: var(--uib-bg-opacity);
  transition: stroke 0.5s ease;
}

@keyframes travel {
  0% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: -100;
  }
}
