import classNames from "classnames";

import { useNavigateBack } from "@/hooks/useNavigateBack";

export function FinishBtn(props: { className?: string }) {
  const navigateBack = useNavigateBack();

  return (
    <button
      type="button"
      className={classNames("btn fw-normal btn-purple mb-1", props.className)}
      onClick={() => navigateBack(2)}
      style={{ maxWidth: "16em" }}
    >
      Finish
    </button>
  );
}
