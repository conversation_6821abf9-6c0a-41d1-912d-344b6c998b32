"use client";

import Image from "next/image";
import { notFound, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import "./index.scss";

import {
  type AddMembersBody,
  CreateNewGroup,
  UpdateGroup,
  UpdateGroupBackground,
} from "@/api/group";
import PostNavbar from "@/app/(user)/(sections)/navbar";
import ImageCropper from "@/components/common/cropper";
import { configActions, createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

interface GroupPreviewViewParams {
  edit: boolean;
}

export default function GroupPreviewView(params: GroupPreviewViewParams) {
  const router = useRouter();
  const Values: any = useAppSelector((state) => state.create.group);
  const user = useAppSelector((state) => state.user.profile);
  const dispatch = useAppDispatch();

  const fileRef = useRef<any>(null);
  const [initialValues, setInitialValues] = useState(Values);
  const [bgImg, setBgImg] = useState("");
  const [bgName, setBgName] = useState("");
  const [cropActive, setCropActive] = useState(false);
  const [fileCroppedURL, setCroppedImg] = useState<any>("");
  const [btnDisable, setBtnDisable] = useState<boolean>(false);
  const [cover, setcoverFile] = useState() as any;
  const dispatchAction = useAppDispatch();

  const croppedImg = (url: Blob) => {
    // isfileCropped(true);
    setBtnDisable(true);
    setCroppedImg(URL.createObjectURL(url));
    setCropActive(false);
    const blob = url;
    const file = new File([blob], bgName, { type: blob.type });
    setInitialValues((prevState: any) => ({
      ...prevState,
      cover: file,
    }));

    setcoverFile(file);
    setBtnDisable(false);
  };

  const onFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setBtnDisable(true);
    setCropActive(true);
    setBgImg("");
    const file = event.currentTarget.files?.[0];

    if (file) {
      setBgName(file.name.replace(/\s+/g, ""));
      setBgImg(URL.createObjectURL(file));
    }

    setBtnDisable(false);
  };

  useEffect(() => {
    if (cropActive) {
      setBtnDisable(true);
    }
  }, [cropActive]);

  useEffect(() => {
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  if (!initialValues.saved) {
    return notFound();
  }

  return (
    <div className="position-relative">
      <PostNavbar
        icon={"/images/svg/nav-back.svg"}
        buttonText={params.edit ? "Update" : "Create New Collab"}
        text="Back to details"
        showBtn={true}
        btnIsValid={fileCroppedURL || params.edit}
        btnIsDirty={fileCroppedURL || params.edit}
        btnIsSubmitting={false}
        btnDisable={btnDisable}
        submitPost={() => {
          setBtnDisable(true);

          if (params.edit) {
            const editedValues = {
              about: initialValues.description,
              hashtags: initialValues.hashtags,
              name: initialValues.name,
              social_handles: initialValues.social_media,
              perks: initialValues.subscription_perks,
              // members: initialValues.members,
            };

            const editedBackground = {
              cover: cover,
            };
    
            UpdateGroup(editedValues, initialValues?.id)
              .then(() => {
                Swal.fire({
                  icon: "success",
                  title: "Group Updated Successfully",
                  showCloseButton: true,
                });
              })
              .catch((error) => {
                setBtnDisable(false);
                console.error(error);

                if (error.errorCode === 1000) {
                  Swal.fire({
                    icon: "warning",
                    title: "Oops! Some words aren’t allowed.",
                    text: `The word "${error.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
                  });
                } else {
                  Swal.fire({
                    icon: "error",
                    title: error.message,
                  });
                }
              });

            const addMember: AddMembersBody[] = [];
            initialValues.members.forEach((mem: any) => {
              if (!mem?.is_accepted) {
                addMember.push({
                  member_id: mem?.member_id?._id,
                  member_type: mem?.member_type,
                  earning: mem?.earning,
                });
              }
            });
            // {
            //   addMember[0] && AddGroupMembers(initialValues?.id, addMember);
            // }

            if (editedBackground.cover) {
              UpdateGroupBackground(editedBackground, initialValues?.id);
            }

            console.log("state username: ", initialValues.username);
            router.push(`/collab/${initialValues.username}`);
          } else {
            CreateNewGroup(initialValues)
              .then(async () => {
                dispatch(
                  createActions.setGroup({
                    from: "",
                    saved: false,
                    nextBtnText: "",
                    id: "",
                    name: "",
                    description: "",
                    cover: "",
                    topic: "knky",
                    subscribe: {
                      name: "",
                      offer_type: "FEE",
                      is_active: false,
                      trial_period: "",
                      array: [],
                    },
                    username: "",
                    social_media: [],
                    hashtags: [],
                    members: [],
                    subscription_perks: [{ icon: "discount", value: "" }],
                  })
                );
                Swal.fire({
                  icon: "success",
                  title: "Collab Created Successfully",
                  showCloseButton: true,
                });

                router.push(
                  `/${user.user_type.toLowerCase()}/${
                    user.username
                  }/subs?type=collabs`
                );
              })
              .catch((error) => {
                setBtnDisable(false);
                console.error(error);

                if (error.errorCode === 1000) {
                  Swal.fire({
                    icon: "warning",
                    title: "Oops! Some words aren’t allowed.",
                    text: `The word "${error.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
                  });
                } else {
                  Swal.fire({
                    icon: "error",
                    title: error.message,
                  });
                }
              });
          }
        }}
      />
      <div className="container min-vh-100 p-3">
        <div className="container bg-body rounded-2 d-flex flex-column p-3 gap-3">
          <div className="d-flex flex-column">
            <p className="fs-6 fw-medium">Upload the background Image</p>
            <div className="background-container bg-cream d-flex position-relative">
              {(bgImg || user.background) && (
                <div className="img-container w-100 position-relative overflow-hidden d-flex justify-content-center align-items center">
                  {cropActive ? (
                    <>
                      <ImageCropper
                        img={bgImg || ""}
                        cls="z-3"
                        aspectRatio={12 / 6}
                        guides={false}
                        cropBoxResizable={true}
                        onCropComplete={croppedImg}
                        cropperHeight={320}
                      />
                    </>
                  ) : (
                    <>
                      {!fileCroppedURL && !initialValues.cover ? (
                        <Image
                          fill
                          objectFit="cover"
                          src={"/images/common/defaultBack.svg"}
                          alt="background"
                        />
                      ) : (
                        <Image
                          src={fileCroppedURL || initialValues.cover}
                          objectFit="contain"
                          fill
                          alt="cropped-file"
                        />
                      )}
                    </>
                  )}
                </div>
              )}
              <div
                className={`position-absolute z-3 bottom-0 end-0 ${
                  cropActive ? "m-0" : "m-1"
                } pointer`}
                onClick={() => fileRef.current?.click()}
              >
                <input
                  ref={fileRef}
                  className="opacity-0 position-absolute bottom-0 end-0"
                  type="file"
                  accept="image/*"
                  onChange={onFileInputChange}
                />
                <Image
                  src={"/images/creator/edit.svg"}
                  width={25}
                  height={25}
                  alt="edit profile"
                  className="position-relative bottom-0 end-0"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
