import { debounce } from "lodash";
import Image from "next/image";
import { memo, useEffect, useMemo, useState } from "react";

import VideoFrameLoader from "@/components/common/VideoFrameLoader";
import type { Media } from "@/types/media";

type MediaRendererProps = {
  index: number;
  src: string;
  title: string;
  setTitle: any;
  type: "image" | "video";
  isEdit: boolean;
  postSettings?: boolean;
  onEdit?: (src: string, index: number) => void;
  onRemove: (type: "image" | "video", index: number) => void;

  // for videos
  thumbnailUrl?: string;
  onRemoveThumbnail?: (index: number) => void;
  onThumbnailUpload?: (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => void;

  tagsCount: number;
  openTagModal: (index: number) => void;
  editPostMedia: Media;
};

const MediaRenderer: React.FC<MediaRendererProps> = ({
  index,
  src,
  title,
  setTitle,
  type,
  isEdit,
  postSettings,
  onEdit,
  onRemove,
  thumbnailUrl,
  onRemoveThumbnail,
  onThumbnailUpload,

  tagsCount,
  openTagModal,
  editPostMedia,
}) => {
  const [tagsRequired, setTagsRequired] = useState(0);
  const [tagsRejected, setTagsRejected] = useState(0);
  const [tagsRequestPending, setTagsRequestPending] = useState(0);
  const [onlyMe, setOnlyMe] = useState(true);
  const [localTitle, setLocalTitle] = useState(title || "");
  useEffect(() => {
    if (isEdit && editPostMedia?.people_count) {
      const is_author_present = editPostMedia?.is_author_present ? 1 : 0;

      const calTagRequired =
        editPostMedia?.people_count -
        ((editPostMedia?.consent?.internal_tag_consent?.length || 0) +
          (editPostMedia?.consent?.external_tag_consent?.length || 0) +
          is_author_present);

      if (calTagRequired > 0) {
        setTagsRequired(calTagRequired);
      }

      const rejectedInternalTags =
        editPostMedia?.consent?.internal_tag_consent?.filter(
          (tag) => tag.status === "Rejected"
        ).length || 0;

      const rejectedExternalTags =
        editPostMedia?.consent?.external_tag_consent?.filter(
          (tag) => tag.status === "Rejected"
        ).length || 0;

      setTagsRejected(rejectedInternalTags + rejectedExternalTags);
      const pendingInternalTags =
        editPostMedia?.consent?.internal_tag_consent?.filter(
          (tag) => tag.status === "Pending"
        ).length || 0;

      const pendingExternalTags =
        editPostMedia?.consent?.external_tag_consent?.filter(
          (tag) => tag.status === "Pending"
        ).length || 0;

      setTagsRequestPending(pendingInternalTags + pendingExternalTags);
    }
  }, [editPostMedia]);

  useEffect(() => {
    if (tagsCount > 0) {
      setOnlyMe(false);
    } else {
      setOnlyMe(true);
    }
  }, [tagsCount]);

  // Debounced handler (stable between renders)
  const debouncedUpdateTitle = useMemo(
    () =>
      debounce((value: string) => {
        setTitle((prev: any) => {
          const copy = [...prev];
          copy[index] = value;
          return copy;
        });
      }, 300),
    [setTitle, index]
  );

  // Sync local state to global (debounced)
  useEffect(() => {
    debouncedUpdateTitle(localTitle);
  }, [localTitle, debouncedUpdateTitle]);

  if (type === "image") {
    return (
      <>
        <div className="col-12 col-md-6 my-3">
          <div className="position-relative border">
            {!postSettings && !isEdit && (
              <div className="position-absolute action-btns d-flex gap-2">
                <div
                  onClick={() => onEdit?.(src, index)}
                  className="media-remove-btn rounded-2 p-1 pointer"
                >
                  <Image
                    src="/images/svg/black-edit.svg"
                    width={25}
                    height={25}
                    alt="edit"
                  />
                </div>
                <div
                  onClick={() => onRemove("image", index)}
                  className="media-remove-btn rounded-2 p-1 pointer"
                >
                  <Image
                    src="/images/svg/black-cross.svg"
                    width={25}
                    height={25}
                    alt="remove"
                  />
                </div>
              </div>
            )}
            {/* {!isEdit && tagsCount > 0 && (
              <span>
                <span
                  onClick={() => openTagModal(index)}
                  className={`badge underline fs-8  pointer position-absolute top-0 start-0 z-3 text-bg-warning`}
                  style={{ borderRadius: "0 0 0.25rem 0" }}
                >
                  {`Tagged (${tagsCount})`}
                </span>
              </span>
            )} */}
            {/* {isEdit && (
              <span
                onClick={() => openTagModal(index)}
                className={`badge  fs-8  pointer position-absolute top-0 start-0 z-3 ${
                  tagsRequired || tagsRejected
                    ? "text-bg-danger"
                    : tagsRequestPending
                    ? "text-bg-warning"
                    : "text-bg-success"
                }`}
                style={{ borderRadius: "0 0 0.25rem 0" }}
              >
                {tagsRequired > 0
                  ? `Tags needed (${tagsRequired})`
                  : tagsRejected > 0
                  ? `Tags Rejected (${tagsRejected})`
                  : tagsRequestPending > 0
                  ? `Request Pending (${tagsRequestPending})`
                  : "Approved"}
              </span>
            )} */}

            <Image
              src={src}
              alt={`media-${index}`}
              fill
              style={{
                height: "100%",
                margin: "auto",
                // objectFit: "cover",
                // aspectRatio: "4/3",
              }}
              className="img-fluid position-relative"
            />
            {/* <div className="mx-2">
              <span className="fs-8 color-black">Title</span>
              <input
                type="text"
                placeholder="Enter media title"
                value={localTitle || ""}
                name="title"
                onChange={(e) => setLocalTitle(e.target.value)}
                className={`w-100 bg-cream color-dark rounded-3 border-0 p-2 fs-6 `}
              />
            </div>
            <div className="d-flex align-items-center gap-2  mt-3 ms-2 mb-2">
              <Toggle
                isToggled={onlyMe}
                setIsToggled={(value: boolean) => {
                  openTagModal(index);
                }}
              />
              <div className="fw-medium">
                I&apos;m the only one in this media
              </div>
            </div>
            {isEdit && (
              <div
                onClick={(e) => {
                  openTagModal(index);
                }}
                className="d-flex align-items-center gap-1 pointer mx-2 mt-3"
              >
                <img src="/images/svg/tag.svg" alt="" width={24} height={24} />
                <span className="fw-medium">
                  {" "}
                  Tag model {!!tagsCount && `(${tagsCount})`}
                </span>
              </div>
            )} */}
          </div>
        </div>
      </>
    );
  } else {
    return (
      <div className="col-12 col-md-6 my-3">
        <div className="border  w-fit">
          <div
            className="position-relative d-flex justify-content-center"
            // style={{ width: "fit-content" }}
          >
            {!postSettings && !isEdit && (
              <div className="position-absolute d-flex video-element action-btns z-3 end-0 gap-2 align-items-center me-3 justify-content-end">
                <div className="position-relative pointer">
                  <div className="bg-black text-white fw-semibold pointer rounded-1 p-1 px-3 d-flex align-items-center gap-2">
                    <span className="fs-8">
                      {thumbnailUrl ? "Change Preview" : "Edit Preview"}
                    </span>
                    <Image
                      src="/images/post/fill-eye.svg"
                      width={14}
                      height={14}
                      className="invert"
                      alt="eye"
                    />
                  </div>
                  <input
                    type="file"
                    className="media-input position-absolute top-0 bottom-0 start-0 end-0 opacity-0 z-3 pointer"
                    accept="image/*"
                    onChange={(e) => onThumbnailUpload?.(e, index)}
                    onClick={(e) => ((e.target as HTMLInputElement).value = "")}
                  />
                </div>

                <Image
                  onClick={() => onRemove("video", index)}
                  src="/images/svg/black-cross.svg"
                  width={25}
                  height={25}
                  className="invert pointer"
                  alt="remove"
                />
              </div>
            )}
            {/* {!isEdit && tagsCount > 0 && (
              <span>
                <span
                  onClick={() => openTagModal(index)}
                  className={`badge underline fs-8  pointer position-absolute top-0 start-0 z-3 text-bg-warning`}
                  style={{ borderRadius: "0 0 0.25rem 0" }}
                >
                  {`Tagged (${tagsCount})`}
                </span>
              </span>
            )} */}
            {/* {isEdit && (
              <span
                onClick={() => openTagModal(index)}
                className={`badge  fs-8  pointer position-absolute top-0 start-0 z-3 ${
                  tagsRequired || tagsRejected
                    ? "text-bg-danger"
                    : tagsRequestPending
                    ? "text-bg-warning"
                    : "text-bg-success"
                }`}
                style={{ borderRadius: "0 0 0.25rem 0" }}
              >
                {tagsRequired > 0
                  ? `Tags needed (${tagsRequired})`
                  : tagsRejected > 0
                  ? `Tags Rejected (${tagsRejected})`
                  : tagsRequestPending > 0
                  ? `Request Pending (${tagsRequestPending})`
                  : "Approved"}
              </span>
            )} */}

            <VideoFrameLoader
              videoFile={src}
              classes="img-fluid w-auto h-auto"
              key={src}
              time={0.2}
            />

            {thumbnailUrl && (
              <div
                className="bg-body rounded-3 p-2 d-flex justify-content-between align-items-center position-absolute"
                style={{ bottom: "6%", width: "94%" }}
              >
                <div className="d-flex gap-2 align-items-center">
                  <Image
                    src={thumbnailUrl}
                    alt={`Thumbnail for video ${index + 1}`}
                    width={45}
                    height={45}
                    className="object-fit-cover rounded-2"
                  />
                  <span className="fw-medium">Preview added</span>
                </div>
                <Image
                  src="/images/post/line-delete.svg"
                  alt="delete"
                  width={25}
                  height={25}
                  onClick={() => onRemoveThumbnail?.(index)}
                  className="pointer"
                  style={{ filter: "grayscale(1)" }}
                />
              </div>
            )}
          </div>

          {/* <div className="mx-2">
            <span className="fs-8 color-black">Title</span>
            <input
              type="text"
              placeholder="Enter media title"
              value={localTitle || ""}
              name="title"
              onChange={(e) => setLocalTitle(e.target.value)}
              className={`w-100 bg-cream color-dark rounded-3 border-0 p-2 fs-6 `}
            />
          </div> */}
          {/* <div className="d-flex align-items-center gap-2  mt-3 ms-2 mb-2">
            <Toggle
              isToggled={onlyMe}
              setIsToggled={(value: boolean) => {
                openTagModal(index);
              }}
            />
            <div className="fw-medium">I&apos;m the only one in this media</div>
          </div>
          {isEdit && (
            <div
              onClick={(e) => {
                openTagModal(index);
              }}
              className="d-flex align-items-center gap-1 pointer mx-2 mt-3"
            >
              <img src="/images/svg/tag.svg" alt="" width={24} height={24} />
              <span className="fw-medium">
                {" "}
                Tag model {!!tagsCount && `(${tagsCount})`}
              </span>
            </div>
          )} */}
        </div>
      </div>
    );
  }
};

export default memo(MediaRenderer);
