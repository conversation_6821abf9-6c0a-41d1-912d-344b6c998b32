import "cropperjs/dist/cropper.css";
import { useState } from "react";
import { Crop<PERSON> } from "react-cropper";

import LoadingSpinner from "@/components/common/loading";

interface AvatarFiles {
  img: string;
  aspectRatio?: number;
  guides: boolean;
  autoCropArea?: number;
  cropBoxResizable: boolean;
  viewMode?: Cropper.ViewMode;
  dragMode?: Cropper.DragMode;
  cls?: string;
  cropperHeight: number | string;
  onCropComplete: (croppedImg: Blob) => void;
  onLoadingStarted?: () => void;
}

const ImageCropper = (props: AvatarFiles) => {
  const [fileCropped, isfileCropped] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cropper, setCropper] = useState<any>();

  const getCropData = () => {
    setIsLoading(true);
    props?.onLoadingStarted && props?.onLoadingStarted();
    isfileCropped(true);
    setTimeout(() => {
      if (typeof cropper !== "undefined") {
        const canvas = cropper.getCroppedCanvas();
        canvas.toBlob(
          (blob: Blob | null) => {
            if (blob) {
              props.onCropComplete(blob);
            }
          },
          "image/jpeg",
          0.8
        );
      }

      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="d-flex flex-column justify-content-center w-100">
      <div style={{ height: "auto" }}>
        {!fileCropped && (
          <Cropper
            style={{ height: props.cropperHeight, width: "100%" }}
            src={props.img || ""}
            className={props.cls}
            aspectRatio={props.aspectRatio}
            guides={props.guides}
            viewMode={typeof props.viewMode === "number" ? props.viewMode : 2}
            dragMode={props.dragMode || "crop"}
            minCropBoxWidth={50}
            onInitialized={(instance) => {
              setCropper(instance);
            }}
            cropBoxResizable={props.cropBoxResizable}
            alt="media"
          />
        )}
        <div className=" position-absolute- text-center top-50  h-100">
          {isLoading && <LoadingSpinner />}
        </div>
      </div>
      {!fileCropped && (
        <div className="d-flex justify-content-end h-auto mb-4">
          <button
            className="btn btn-purple m-2 position-relative"
            onClick={getCropData}
          >
            Done
          </button>
        </div>
      )}
    </div>
  );
};

export default ImageCropper;
