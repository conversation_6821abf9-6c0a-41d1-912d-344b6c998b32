import classNames from "classnames";
import React from "react";

import DateFormatter from "@/components/common/date";
import {
  AudioSent,
  VideoSent,
} from "@/components/matchmaker/match-cards/utils/svg";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import { getAssetUrl } from "@/utils/assets";

import ReceiverCustomRequest from "../custom-request/Receiver";
import CustomRequestSender from "../custom-request/Sender";
import Embeds from "../embeds";
import { JoinCallBtn } from "../join-call-btn";
import RatingRequestWrapper from "../rating-request";
import MediaRenderer from "../read-unread-messages/receiver/MediaRenderer";
import PromotionReceiver from "../read-unread-messages/receiver/Promotion";
import MediaRendererSender from "../read-unread-messages/sender/MediaRenderer";
import PromotionSender from "../read-unread-messages/sender/Promotion";
import SentTip from "../sent-tip";
import SetPrice from "../set-price";
import TagApproval from "../tag-approval";
import VideoVoiceReceiver from "../video-voice-request/receiver";
import VideoVoiceSender from "../video-voice-request/sender";

const MESSAGE_TYPES = {
  SENT_TIP: "SENT-TIP",
  ACCEPT_CALL: "ACCEPT_CALL",
  SET_PRICE: "SET-PRICE",
  RATING: "RATING",
  CUSTOM_SERVICE: "CUSTOM-SERVICE",
  VIDEO: "VIDEO",
  VOICE: "VOICE",
  EMBEDS: "EMBEDS",
  TAG_APPROVAL: "TAG-APPROVAL",
};

const PROMOTION_TYPES = [
  MESSAGE_TYPES.VIDEO,
  MESSAGE_TYPES.VOICE,
  MESSAGE_TYPES.RATING,
  MESSAGE_TYPES.CUSTOM_SERVICE,
];

interface MessageBubbleProps {
  message: MessageInterface;
  messageRefs: React.MutableRefObject<any>;
  messageRef: React.MutableRefObject<any>;
  type: "receiver" | "sender";
  getMessageWidthClass: (message: MessageInterface) => string;
  getMessageBgClass: (type: "receiver" | "sender") => string;
  getBackgroundStyle: (message: MessageInterface) => React.CSSProperties;
  handleDeleteSenderMessage: (message: MessageInterface, i: number) => void;
  index: number;
  scrollToMessageId: (id: string) => void;
  videoConfRef: any;
  buyTicketModalRef: any;
  ratingReqref: any;
  receiver: any;
  setCallReqId: any;
  setReceiver: any;
  setShowBuyTicketModal: any;
  showBuyTicketModal: any;
  rateRef: any;
  imageModalRef: any;
  setImageMessage: (value: any) => void;
}

const MessageBubble = ({
  message,
  messageRefs,
  messageRef,
  type,
  getMessageWidthClass,
  getMessageBgClass,
  getBackgroundStyle,
  handleDeleteSenderMessage,
  index,
  scrollToMessageId,
  videoConfRef,
  buyTicketModalRef,
  ratingReqref,
  receiver,
  setCallReqId,
  setReceiver,
  setShowBuyTicketModal,
  showBuyTicketModal,
  rateRef,
  imageModalRef,
  setImageMessage,
}: MessageBubbleProps) => {
  const isImageAsset = (message: MessageInterface) => {
    if (Array.isArray(message?.meta?.replyMessage?.meta?.media)) {
      return message?.meta?.replyMessage?.meta?.media[0]?.type?.includes(
        "image"
      );
    } else {
      return message?.meta?.replyMessage?.meta?.media?.type?.includes("image");
    }
  };

  const getReplyMessageBubble = () => {
    return (
      <div
        className="rounded pointer"
        style={{
          padding: "0.75rem",
          background: type === "sender" ? "#F5F5F6" : "#aeaeae",
        }}
        onClick={() =>
          scrollToMessageId(
            message?.meta?.replyMessage?._id ||
              message?.meta?.replyMessage?.messageId ||
              ""
          )
        }
      >
        <div className="d-flex justify-content-between align-items-center gap-3">
          <div>
            <div className="fw-bold">
              {message?.meta?.replyMessage?.display_name}
            </div>
            <span>{message?.meta?.replyMessage?.message}</span>
          </div>
          {message?.meta?.replyMessage?.meta?.media &&
            "is_unlocked" in message?.meta?.replyMessage?.meta &&
            message?.meta?.replyMessage?.meta?.is_unlocked && (
              <div>
                {isImageAsset(message) ? (
                  // eslint-disable-next-line @next/next/no-img-element
                  <img
                    alt=""
                    src={getAssetUrl({
                      media: Array.isArray(
                        message?.meta?.replyMessage?.meta?.media
                      )
                        ? message?.meta?.replyMessage?.meta?.media[0]
                        : message?.meta?.replyMessage?.meta?.media,
                      defaultType: "avatar",
                      variation: "compressed",
                    })}
                    width={30}
                    height={30}
                    className="object-fit-cover rounded"
                  />
                ) : (
                  <video
                    controlsList="nodownload"
                    controls={false}
                    autoPlay
                    loop
                    src={getAssetUrl({
                      media: Array.isArray(
                        message?.meta?.replyMessage?.meta?.media
                      )
                        ? message?.meta?.replyMessage?.meta?.media[0]
                        : message?.meta?.replyMessage?.meta?.media,
                      defaultType: "avatar",
                      variation: "compressed",
                    })}
                    width={30}
                    height={30}
                    className="object-fit-cover rounded"
                  />
                )}
              </div>
            )}
        </div>
      </div>
    );
  };

  const getSentTipBubble = () => {
    return <SentTip message={message} />;
  };

  const getAcceptCallBubble = (isCompleted: boolean) => {
    if (isCompleted) {
      return (
        <div className="d-flex align-items-center gap-3 py-2 ps-2 pb-0">
          <>
            <span className="text-center">
              {message?.message === "Video Call" ? (
                <VideoSent />
              ) : (
                <AudioSent />
              )}
            </span>
            <span className="fw-bold">{message?.message} ended</span>
          </>
        </div>
      );
    } else if (!isCompleted) {
      return <JoinCallBtn message={message} videoConfRef={videoConfRef} />;
    }
  };

  const getSetPriceBubble = () => {
    return <SetPrice message={message} usedAs={type} />;
  };

  const getPromotionBubble = () => {
    return type === "receiver" ? (
      <PromotionReceiver
        buyTicketModalRef={buyTicketModalRef}
        message={message}
        ratingReqref={ratingReqref}
        receiver={receiver}
        setCallReqId={setCallReqId}
        setReceiver={setReceiver}
        setShowBuyTicketModal={setShowBuyTicketModal}
        showBuyTicketModal={showBuyTicketModal}
        key={message._id}
      />
    ) : (
      <PromotionSender message={message} />
    );
  };

  const getRatingBubble = () => {
    return (
      <RatingRequestWrapper
        message={message}
        rateRef={rateRef}
        setCallReqId={setCallReqId}
        imageModalRef={imageModalRef}
        setImageMessage={setImageMessage}
        usedAs={type}
        dateRequired={false}
      />
    );
  };

  const getCustomServiceBubble = () => {
    return type === "receiver" ? (
      <ReceiverCustomRequest message={message} />
    ) : (
      <CustomRequestSender message={message} />
    );
  };

  const getCallBubble = () => {
    return type === "receiver" ? (
      <VideoVoiceReceiver
        message={message}
        setCallReqId={setCallReqId}
        dateRequired={false}
      />
    ) : (
      <VideoVoiceSender message={message} />
    );
  };

  const getMediaRenderer = () => {
    return type == "receiver" ? (
      <MediaRenderer
        message={message}
        setImageMessage={setImageMessage}
        key={message?._id || message?.messageId}
      />
    ) : (
      <MediaRendererSender
        message={message}
        setImageMessage={setImageMessage}
        key={message?._id || message?.messageId}
      />
    );
  };

  const getTimeStamp = () => {
    return (
      <span
        className={
          "msg-time text-lowercase d-block fs-9 text-opacity-75  text-end w-200 mt-0 " +
          `${
            message?.meta?.type === "message-attachment" &&
            message?.message === "Attachment" &&
            message?.meta?.media &&
            (Array.isArray(message.meta.media)
              ? message.meta.media.length > 0 &&
                message.meta.media[0].type !== "audio" &&
                message.meta.media[0].status === "Completed"
              : message.meta.media.type !== "audio" &&
                message.meta.media.status === "Completed")
              ? "position-absolute end-0 bottom-0 m-2 px-1 rounded"
              : "px-2 pe-2 pb-1"
          }`
        }
        style={{
          backgroundColor:
            message?.meta?.type === "message-attachment" &&
            message?.message === "Attachment" &&
            message?.meta?.media &&
            (Array.isArray(message.meta.media)
              ? message.meta.media.length > 0 &&
                message.meta.media[0].type !== "audio" &&
                message.meta.media[0].status === "Completed"
              : message.meta.media.type !== "audio" &&
                message.meta.media.status === "Completed")
              ? "rgba(255,255,255,0.6)"
              : "",
        }}
      >
        <DateFormatter
          dateString={message.createdAt || message.createdAt}
          formatType="h:mm a"
        />
      </span>
    );
  };

  const getEmbedsBubble = () => {
    return <Embeds usedAs={type} message={message} />;
  };

  const getTagAppovalBubble = () => {
    return <TagApproval usedAs={type} message={message} />;
  };

  const getMessageBubbleByMessageType = () => {
    if (
      message?.message === "Promotion" &&
      PROMOTION_TYPES.includes(message?.meta?.type)
    ) {
      return getPromotionBubble();
    }

    switch (message?.meta?.type) {
      case MESSAGE_TYPES.SENT_TIP:
        return getSentTipBubble();

      case MESSAGE_TYPES.ACCEPT_CALL:
        return getAcceptCallBubble(message?.meta?.isCompleted || false);

      case MESSAGE_TYPES.SET_PRICE:
        return getSetPriceBubble();

      case MESSAGE_TYPES.RATING:
        return getRatingBubble();

      case MESSAGE_TYPES.CUSTOM_SERVICE:
        return getCustomServiceBubble();

      case MESSAGE_TYPES.VIDEO:
      case MESSAGE_TYPES.VOICE:
        return getCallBubble();

      case MESSAGE_TYPES.EMBEDS:
        return getEmbedsBubble();

      case MESSAGE_TYPES.TAG_APPROVAL:
        return getTagAppovalBubble();

      default:
        return getMediaRenderer();
    }
  };

  return (
    <div
      key={message._id || message.messageId}
      ref={(el) => {
        const id = message._id || message.messageId;
        messageRefs.current[id] = el;

        if (messageRef.current) {
          messageRef.current(message, el);
        }
      }}
      className={classNames(
        type,
        getMessageWidthClass(message),
        getMessageBgClass(type),
        "h-100  d-block rounded text-break position-relative mt-1"
      )}
      style={getBackgroundStyle(message)}
      id={`message-bubble-${type}`}
      onClick={(e) => {
        e.stopPropagation();
        handleDeleteSenderMessage(message, index);
      }}
      data-message-id={message._id || message.messageId}
      data-message-type={message?.meta?.type}
    >
      {message?.meta?.replyMessage?.message && getReplyMessageBubble()}
      {message?.meta?.forward && <i className="mx-2 fs-8">Forwarded</i>}

      {getMessageBubbleByMessageType()}

      {getTimeStamp()}
    </div>
  );
};

export default MessageBubble;
