.custom-menu-collab {
  background-color: var(--bg-color);
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 31.5rem;
  border-radius: 1rem;
  padding: 4%;
  display: flex;
  flex-direction: column;
  max-height: 19rem;
  overflow: auto;
  z-index: 1;

  li {
    list-style: none;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
  }

  @media (max-width: 767px) {
    min-width: 20rem;
  }
}

.tag-collab-wrapper {
  padding: 0 12%;
}
.tag-and-collab-btn {
  min-width: 6rem;
  margin-right: 0.5rem;
}
.collab-close-btn {
  position: absolute;
  right: 4px;
  top: 7px;
  cursor: pointer;
  z-index: 1;
}

@media screen and (max-width: 767px) {
  .tag-collab-wrapper {
    padding: 0;
  }
}
