$checked-color: var(--primary-color);
.post-type-btn,
.post-type-footer-btn {
  &:disabled {
    filter: grayscale(1);
    background-color: rgba(var(--fg-rgb), 0.3) !important;

    img {
      filter: contrast(0.1);
    }
  }
  @media (max-width: 768px) {
    width: max-content !important;
    min-width: 7rem !important;
    margin: auto;
    font-size: 14px !important;
  }
}

.profile-wrapper {
  // width: 60px;
  // height: 60px;
  border: 1px solid transparent;
  border-radius: 50%;
  padding: 0 !important;
  margin: 0;
}

.background-wrapper {
  height: 25rem;
  position: relative;
}
.action-btns {
  right: 9%;
  top: 1%;
  z-index: 4;
}

.video-element {
  top: 2% !important;
}
.media-remove-btn {
  width: fit-content;
}
// .background-area {
//   font-size: 3em;
//   resize: none;
//   min-width: 1px;
//   min-height: max-content;
//   overflow: hidden;
//   padding: 8px;
//   outline: none;
//   text-align: center;
//   vertical-align: middle;
// }

.tag-collab-box .profile-wrapper {
  width: 50px;
  height: 50px;
}

// .tag-collab-item {
//   width: 9rem;
// }

.tag-collab-item .active {
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
}

.active-post-btn {
  box-shadow: 0 0 0 1pt var(--primary-color);
  color: var(--primary-color) !important;
}

.custom-swal-popup {
  z-index: 1111;
  // width: 35em!important;
  margin: 0 auto;
}
.swal2-title {
  font-size: 1.25rem !important;
  color: #131416 !important;
}
.swal2-html-container {
  color: #4d5053;
}
.swal2-styled.swal2-confirm {
  width: 8rem;
}

.user-group-wrapper .form-check-input {
  &:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }
  &:focus {
    box-shadow: none;
  }
}

.share-channel-wrapper {
  .form-check-input {
    --bs-form-check-bg: #a8a7a7 !important;
  }
}

.share-channel-wrapper .caption-content {
  textarea {
    &:focus {
      outline: none;
    }
    resize: none;
    height: 50vh;
  }
}

.share-channel-wrapper .dropdown input:focus {
  box-shadow: none;
}

#media-button {
  box-shadow: 0 0 0 1pt var(--primary-color);
  color: var(--primary-color) !important;
}

.post-loader {
  z-index: 5;
  background: var(--bg-dark);
}

.create-post-content {
  height: auto;
  padding: 2% 1% 3% !important;
}
.disable {
  pointer-events: none;
}
.middle-line {
  margin: 1rem 0;
  border: 1px solid rgb(0 0 0 / 10%);
}

// .create-post-container {
//   padding-top: 4%;
// }

@media screen and (min-width: 768px) {
  .post-footer,
  .mobile-post-audience {
    display: none !important;
  }
  .media-content img,
  .media-content video {
    max-height: 30rem;
  }

  .media-content > div {
    max-height: 37rem;
  }
}

@media screen and (max-width: 767px) {
  .post-type-btn-container {
    min-width: 25% !important;
  }
  .post-footer {
    z-index: 9 !important;
    display: block;
    position: fixed !important;
    bottom: 0 !important;
    width: 100% !important;
    top: auto !important;
  }
  .post-type-footer-btn {
    min-width: auto !important;
  }
  // .create-post-container {
  //   padding: 13% 0 0 !important;
  // }
  .post-content-height {
    min-height: 45rem !important;
  }
  .container-xxl .post-type-wrapper {
    display: none !important;
  }
  .tag-collab-item {
    width: 8rem;
  }
  .create-post-content {
    margin-bottom: 2rem !important;
  }
  .create-post-container {
    padding: 0 !important;
  }
  .action-btns {
    right: 3%;
    top: 1.48%;
  }
}

.radio-btn {
  background-color: rgba(0, 0, 0, 0.3);
}

.cropper-content-wrapper {
  height: 460px;
}

.caption-content textarea {
  font-size: 16px !important;
}
