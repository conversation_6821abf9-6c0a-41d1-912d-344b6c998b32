import classNames from "classnames";
import { memo } from "react";

import ActionButton from "@/components/common/action-button";
import DateFormatter from "@/components/common/date";
import {
  AudioSent,
  VideoSent,
} from "@/components/matchmaker/match-cards/utils/svg";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import socketChannel from "@/utils/chatSocket";
import { formatCurrency } from "@/utils/formatter";

import { handleReq } from "../../../apiUtils";

const VideoVoiceReceiver = ({
  message,
  setCallReqId,
  dateRequired = true,
}: {
  message: MessageInterface;
  setCallReqId: (value: any) => void;
  dateRequired?: boolean;
}) => {
  const handleAcceptReject = async (accept: boolean) => {
    await handleReq(message?.meta.reqId, accept);

    const newMeta = {
      ...message?.meta,
      requestAccept: accept,
    };
    socketChannel?.channel?.editMessage({
      message_id: message._id || message.messageId,
      data: {
        ...message,
        meta: newMeta,
      },
    });
  };

  const isVideoOrVoice =
    message?.meta?.type === "VIDEO" || message?.meta?.type === "VOICE";
  const isRequestSent = message?.meta?.requestAccept === "sent";
  const isAccepted = message?.meta?.requestAccept || message?.meta?.paid;

  const hascreatedAt = message.createdAt || message.createdAt;

  return (
    <div key={message._id || message.messageId}>
      {message?.meta?.free_service && (
        <div className="position-absolute top-0 start-0 m-2 badge text-bg-secondary z-1">
          Free Service
        </div>
      )}
      <div className="position-absolute top-0 end-0 my-2 mx-2">
        {isVideoOrVoice && !isRequestSent && (
          <div
            className={classNames("badge p-2", {
              "fs-10": window.innerWidth < 576,
            })}
            style={{
              color: isAccepted
                ? "rgba(86, 194, 45, 1)"
                : "rgba(245, 34, 45, 1)",
              border: `1px solid ${
                isAccepted ? "rgba(86, 194, 45, 1)" : "rgba(245, 34, 45, 1)"
              }`,
              backgroundColor: isAccepted
                ? "rgba(86, 194, 45, 0.2)"
                : "rgba(245, 34, 45, 0.2)",
            }}
          >
            {isAccepted ? "Accepted" : "Declined"}
          </div>
        )}
        {isRequestSent && (
          <div
            className={classNames("badge p-2", {
              "fs-10": window.innerWidth < 576,
            })}
            style={{
              color: "rgba(136, 77, 255, 1)",
              border: "1px solid rgba(136, 77, 255, 1)",
              backgroundColor: "rgba(136, 77, 255, 0.2)",
            }}
          >
            New
          </div>
        )}
      </div>
      <div className="d-flex gap-2 align-items-center p-2">
        <div className="d-flex justify-content-center align-items-center">
          {message?.meta?.type === "VOICE" ? <AudioSent /> : <VideoSent />}
        </div>
        <div className="d-flex flex-column align-items-start ">
          <div className="fw-bold">
            {message?.meta?.type === "VOICE" ? "Voice Call" : "Video Call"}
          </div>
          {message?.meta?.duration && (
            <div className="d-flex gap-1 fs-7">
              <span>For {Math.ceil(message?.meta?.duration / 60)} mins</span>
              <div className="vr bg-black opacity-50"></div>
              <span>Price: {formatCurrency(message?.meta?.price || 0)}</span>
            </div>
          )}
        </div>
      </div>
      {["VOICE", "VIDEO"].includes(message?.meta?.type) && isRequestSent && (
        <div className="d-flex gap-3 mt-2 flex-column flex-lg-row p-2">
          <ActionButton
            onClick={() => handleAcceptReject(true)}
            className="w-100"
          >
            Accept with {formatCurrency(message?.meta?.price || 0)}
          </ActionButton>
          <ActionButton
            type="outline"
            variant="danger"
            onClick={() => handleAcceptReject(false)}
          >
            Decline
          </ActionButton>
        </div>
      )}
      {dateRequired && hascreatedAt && (
        <span className="msg-time text-lowercase text-lowercase d-block fs-9 text-opacity-75 text-dark text-end mt-3">
          <DateFormatter
            dateString={message.createdAt || message.createdAt}
            formatType="h:mm a"
          />
        </span>
      )}
    </div>
  );
};

export default memo(VideoVoiceReceiver);
