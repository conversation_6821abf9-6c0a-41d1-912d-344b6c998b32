import Image from "next/image";

import ActionButton, { type ActionButtonVariant } from "../action-button";

interface InputBtn {
  text: string;
  icon?: string;
  variant?: ActionButtonVariant;
  isValid: boolean;
  isSubmitting: boolean;
  loader?: boolean;
  className?: string;
  tabIndex?: number;
  dirty: boolean;
  onClick?: Function;
}

const Button = ({
  text = "",
  icon = "",
  variant,
  isValid,
  isSubmitting,
  loader,
  dirty,
  className = "",
  onClick = (_e: any) => {},
}: InputBtn) => {
  const active = isValid && dirty;
  const disable = !isValid || !dirty || isSubmitting;

  return (
    <ActionButton
      className={className}
      variant={variant}
      btnType="submit"
      disabled={disable || !active}
      onClick={(e) => onClick(e)}
      loading={loader}
    >
      {icon && <Image src={icon} alt="btn_icon" width={24} height={24} />}
      {text}
    </ActionButton>
  );
};

export default Button;
