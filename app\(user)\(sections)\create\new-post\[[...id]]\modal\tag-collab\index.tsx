import debounce from "lodash/debounce";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import "./index.scss";

import type { ReleaseFormsResponse } from "@/api/user";
import { GetUserFollowing, getUserProfile } from "@/api/user";
import ActionButton from "@/components/common/action-button";
import Modal, { type ModalImportProps } from "@/components/common/modal";
import { ModalService } from "@/components/modals";
import { userDataActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { getAssetUrl } from "@/utils/assets";

export default function TagCollab({
  setChildRef,
  releaseUsers,
  setReleaseUsers,
  collabUsers,
  setCollabUsers,
  tagConsent,
  setTagConsent,
  showRelease,
  showCollab,
  showTagConsent,
  type = "tag",
  onclose,
}: ModalImportProps & {
  releaseUsers: any[];
  setReleaseUsers: any;
  collabUsers?: any[];
  setCollabUsers?: any;
  tagConsent: any[];
  setTagConsent: any;
  showRelease?: any;
  showCollab?: any;
  showTagConsent?: any;
  type: "collab" | "tag";
} & { onclose: () => void }) {
  const dispatchAction = useAppDispatch();
  const [tag, setTag] = useState([] as string[]);
  const [collab, setCollab] = useState([] as string[]);
  const [modalType, setModalType] = useState(type);
  const [userList, setUserList] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [memberList, setMemberList] = useState<any>();
  const [showDropdown, setShowDropdown] = useState(false);
  const [formsArray, setFormArray] = useState<ReleaseFormsResponse[]>([]);

  const userId = useAppSelector((state) => state.user.id);

  const handleSearch = async (searchKey: string) => {
    try {
      const data = await getUserProfile(searchKey);
      setUserList(
        data.data?.[0].users
          .filter((u: any) => u.user_type === "CREATOR")
          .filter((u: any) => u._id !== userId)
      );
      setShowDropdown(true);
    } catch (error) {
      console.error("Error fetching user data", error);
    }
  };

  const debouncedSearch = useRef(debounce(handleSearch, 600)).current;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);

    if (searchTerm === "") {
      setShowDropdown(false);
    } else {
      debouncedSearch(e.target.value);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      // handleSearch(e.target.value);
    }
  };

  const handleCollab = async (user: any) => {
    // Check if the user is already added as a collaborator
    const isUserAlreadyAdded = collabUsers?.some(
      (existingUser: any) => existingUser.username === user.username
    );

    if (!isUserAlreadyAdded) {
      const userWithPercentage = { ...user, percentage: 0 };

      const updatedCollab = [...collab, userWithPercentage];
      const updatedCollabUsers = [...collabUsers!, userWithPercentage];

      await dispatchAction(userDataActions.setPostCollab(updatedCollab));
      setCollab(updatedCollab);

      setCollabUsers(updatedCollabUsers);
    } else {
      console.warn("User is already a collaborator.");
    }
  };

  const handleTagConsent = async (user: any) => {
    // Check if the user is already added as a collaborator
    const isUserAlreadyAdded = tagConsent.some(
      (existingUser: any) => existingUser.username === user.username
    );

    if (!isUserAlreadyAdded) {
      const updatedCollabUsers = [...tagConsent, user];
      setTagConsent(updatedCollabUsers);
    } else {
      console.warn("User is already a collaborator.");
    }
  };

  const handleRelease = async (user: ReleaseFormsResponse) => {
    // Check if the user is already added as a releaseUser
    const isUserAlreadyAdded = releaseUsers.some(
      (existingUser: any) => existingUser.form_id === user.form_id
    );

    if (!isUserAlreadyAdded) {
      const userWithPercentage = { ...user, percentage: 0 };

      const updatedReleaseUsers = [...releaseUsers, userWithPercentage];

      // await dispatchAction(userDataActions.setPostCollab(updatedCollab));

      setReleaseUsers(updatedReleaseUsers);
    } else {
      console.warn("User is already a collaborator.");
    }
  };

  const removeCollab = (index: number) => {
    const updatedCollabs = [...collab];

    const removed: any = updatedCollabs.splice(index, 1)[0];

    setCollab(updatedCollabs);
    dispatchAction(userDataActions.setPostCollab(updatedCollabs));
    setCollabUsers((prevUsers: any) =>
      prevUsers.filter((user: any) => user.username !== removed.username)
    );
  };

  const removeTagConsent = (index: number) => {
    const updatedTagConsent = [...tagConsent];

    const removed: any = updatedTagConsent.splice(index, 1)[0];

    setTagConsent((prevUsers: any[]) =>
      prevUsers.filter((user: any) => user.username !== removed.username)
    );
  };

  const removeRelease = (index: number) => {
    const updatedRelease = [...releaseUsers];

    const removed: ReleaseFormsResponse = updatedRelease.splice(index, 1)[0];

    // setCollab(updatedRelease);
    // dispatchAction(userDataActions.setPostCollab(updatedRelease));
    setReleaseUsers((prevUsers: ReleaseFormsResponse[]) =>
      prevUsers.filter(
        (user: ReleaseFormsResponse) => user.form_id !== removed.form_id
      )
    );
  };

  useEffect(() => {
    GetUserFollowing().then((response: any) => {
      setMemberList(
        response.data.filter((u: any) => u.user_type === "CREATOR")
      );
    });

    // GetReleaseForms("success").then((res: any) => {
    //   setFormArray(res.data);
    // });
  }, []);
  console.log({ type });
  useEffect(() => {
    setModalType(type);
  }, [type]);

  return (
    <Modal
      title={""}
      subtitle={[""]}
      setChildRef={setChildRef}
      render={"direct"}
      onClose={() => onclose()}
    >
      <div
        className="tag-collab-container container-sm"
        style={{ maxWidth: "33rem" }}
      >
        <h3 className="w-100 text-center mb-2">
          {" "}
          {type === "collab" ? "Add collaborator" : ""}{" "}
        </h3>
        {type === "tag" && (
          <ul
            className="nav nav-pills mb-3 justify-content-center "
            id="pills-tab"
            role="tablist"
          >
            <li className="nav-item tag-collab-item" role="presentation">
              <button
                className="nav-link active rounded-0 border-0 border-bottom bg-body color-dark h-100 w-100 fs-6 fw-medium"
                id="pills-collab-tab"
                data-bs-toggle="pill"
                data-bs-target="#pills-collab"
                type="button"
                role="tab"
                aria-controls="pills-profile"
                aria-selected="false"
              >
                Add Tags
              </button>
            </li>
            <li className="nav-item tag-collab-item" role="presentation">
              <button
                className="nav-link  rounded-0 border-0 border-bottom bg-body color-dark h-100 w-100 fs-6 fw-medium"
                id="pills-home-tab"
                data-bs-toggle="pill"
                data-bs-target="#pills-release"
                type="button"
                role="tab"
                aria-controls="pills-home"
                aria-selected="true"
              >
                Add Release form
              </button>
            </li>
          </ul>
        )}
        <div className="tab-content" id="pills-tabContent">
          <div
            className="tab-pane fade show active"
            id="pills-collab"
            role="tabpanel"
            aria-labelledby="pills-profile-tab"
          >
            <div className="tag-collab-box tag-collab-wrapper">
              <div className="input-group mb-3">
                <span
                  className="input-group-text border-0 bg-cream"
                  id="basic-addon1"
                >
                  <Image
                    width={25}
                    height={25}
                    src="/images/svg/search.svg"
                    alt="search-icon"
                  />
                </span>
                <input
                  type="text"
                  className="form-control border-0 bg-cream color-dark shadow-none "
                  placeholder="Search"
                  aria-label="Search"
                  aria-describedby="basic-addon1"
                  value={searchTerm}
                  onChange={handleInputChange}
                  // onBlur={handleBlur}
                  onKeyPress={handleKeyPress}
                />
              </div>

              <div style={{ maxHeight: "12rem", overflow: "scroll" }}>
                {!showDropdown && (
                  <>
                    {memberList?.map((user: any, index: any) => (
                      <div
                        className="d-flex w-100 align-items-center p-2 justify-content-between"
                        key={index}
                      >
                        <div className="d-flex align-items-center">
                          <div className="rounded-pill profile-wrapper">
                            <Image
                              width={100}
                              height={100}
                              className="img-fluid rounded-pill object-fit-cover"
                              style={{ aspectRatio: "1" }}
                              src={getAssetUrl({
                                media: user.avatar?.[0],
                                variation: "thumb",
                                defaultType: "avatar",
                              })}
                              alt="profile-img"
                            />
                          </div>
                          <div className="d-flex flex-column ps-2 pe-2">
                            <p
                              className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6"
                              style={{ whiteSpace: "normal" }}
                            >
                              {user.display_name}
                            </p>
                            <p className="user-name text-break-0 tag-box-title-small mb-0 fs-6">
                              @{user.username}
                            </p>
                          </div>
                        </div>

                        <>
                          {type === "collab" ? (
                            <>
                              {collabUsers?.some(
                                (addedUser) =>
                                  addedUser.username === user.username
                              ) ? (
                                <span className="btn tag-and-collab-btn btn-purple">
                                  Collabed
                                </span>
                              ) : (
                                <button
                                  className={`btn tag-and-collab-btn hover-active-hollow bg-light shadow-dark  `}
                                  onClick={() => handleCollab(user)}
                                >
                                  Collab
                                </button>
                              )}
                            </>
                          ) : (
                            <>
                              {/* {tagConsent.some(
                                (addedUser) =>
                                  addedUser.username === user.username
                              ) ? (
                                <span className="btn tag-and-collab-btn btn-purple">
                                  Tagged
                                </span>
                              ) : (
                                <button
                                  className="btn tag-and-collab-btn hover-active-hollow bg-light shadow-dark"
                                  onClick={() => handleTagConsent(user)}
                                >
                                  Tag
                                </button>
                              )} */}
                            </>
                          )}
                        </>
                      </div>
                    ))}
                  </>
                )}
                {showDropdown && (
                  <>
                    {userList.length > 0 ? (
                      userList.map((user: any, index) => (
                        <>
                          <div
                            className="d-flex align-items-center justify-content-between"
                            key={user._id}
                          >
                            <div onClick={() => setShowDropdown(false)}>
                              {/* eslint-disable-next-line @next/next/no-img-element */}
                              <img
                                src={getAssetUrl({
                                  media: user.avatar?.[0],
                                  defaultType: "avatar",
                                  variation: "thumb",
                                })}
                                width={38}
                                height={38}
                                alt="no-image"
                                className="rounded-circle object-fit-cover"
                              />
                              <span className="mx-2">{user.username}</span>
                            </div>
                            {type === "collab" ? (
                              <>
                                {collabUsers?.some(
                                  (addedUser) =>
                                    addedUser.username === user.username
                                ) ? (
                                  <ActionButton
                                    disabled={true}
                                    className="tag-and-collab-btn "
                                  >
                                    Collabed
                                  </ActionButton>
                                ) : (
                                  <button
                                    className={`btn tag-and-collab-btn hover-active-hollow bg-light shadow-dark  `}
                                    onClick={() => handleCollab(user)}
                                  >
                                    Collab
                                  </button>
                                )}
                              </>
                            ) : (
                              <>
                                {/* {tagConsent.some(
                                  (addedUser) =>
                                    addedUser.username === user.username
                                ) ? (
                                  <span className="btn tag-and-collab-btn btn-purple">
                                    Tagged
                                  </span>
                                ) : (
                                  <button
                                    className="btn tag-and-collab-btn hover-active-hollow bg-light shadow-dark"
                                    onClick={() => handleTagConsent(user)}
                                  >
                                    Tag
                                  </button>
                                )} */}
                              </>
                            )}
                          </div>
                          {index < userList.length - 1 && <hr />}
                        </>
                      ))
                    ) : (
                      <span>No users found</span>
                    )}
                  </>
                )}
              </div>

              {type === "collab" && collabUsers?.length != 0 && (
                <>
                  <hr />
                  <div className="d-flex flex-column w-100">
                    <p className="fs-6 fw-medium">Collaborators</p>

                    <div style={{ maxHeight: "7rem", overflow: "scroll" }}>
                      {collabUsers?.map((user: any, index: number) => (
                        <div
                          className="d-flex w-100 align-items-center p-2 justify-content-between "
                          key={index}
                        >
                          <div className="d-flex align-items-center">
                            <div className="rounded-pill profile-wrapper">
                              {/* eslint-disable-next-line @next/next/no-img-element */}
                              <img
                                width={100}
                                height={100}
                                className="img-fluid rounded-pill object-fit-cover"
                                style={{ aspectRatio: "1" }}
                                src={getAssetUrl({
                                  media: user.avatar?.[0],
                                  defaultType: "avatar",
                                  variation: "thumb",
                                })}
                                alt="profile-img"
                              />
                            </div>
                            <div className="d-flex flex-column ps-2 pe-2">
                              <p className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6">
                                {user.display_name}
                              </p>
                              <p className="user-name text-break-0 tag-box-title-small mb-0 fs-6">
                                {user.username}
                              </p>
                            </div>
                          </div>

                          <Image
                            src={"/images/svg/nav-close.svg"}
                            width={20}
                            height={20}
                            className="invert svg-icon"
                            alt="remove-tag"
                            onClick={() => {
                              removeCollab(index);
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          <div
            className="tab-pane fade show "
            id="pills-release"
            role="tabpanel"
            aria-labelledby="pills-profile-tab"
          >
            <div className="tag-collab-box tag-collab-wrapper">
              <div
                className={`${formsArray.length > 0 ? "mb-2" : ""} px-2 w-100`}
              >
                <ActionButton
                  btnType="button"
                  onClick={() => ModalService.open("RELEASE_FORM")}
                  type="outline"
                  className="w-100  "
                >
                  {" "}
                  Generate Link
                </ActionButton>
              </div>
            </div>
          </div>
        </div>
        <div className="d-flex w-100 justify-content-center align-items-center p-3 mt-2">
          <button
            className="btn w-75 btn-purple hover-active-hollow"
            onClick={() => {
              showRelease(releaseUsers);
              showCollab(collabUsers);
              showTagConsent(tagConsent);
              setChildRef.method.close();

              dispatchAction(userDataActions.setPostTagged(tag));
            }}
          >
            Complete
          </button>
        </div>
      </div>
    </Modal>
  );
}
