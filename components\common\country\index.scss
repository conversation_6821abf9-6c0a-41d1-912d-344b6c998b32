.country-selection {
  display: flex;
  align-items: center;
  padding: 1.69%;
  border-radius: 8px;
  width: 100%;

  @media (max-width: 767px) {
    padding: 2%;
  }
}
.country-selection-select {
  border: none !important;
  background-color: transparent !important;
}
.country-selection-select:focus {
  box-shadow: none !important;
}

.country-selection {
  position: relative;
}

.custom-dropdown {
  //   padding: 10px;
  cursor: pointer;
  width: 100%;

  position: relative;
  user-select: none;
}

.custom-dropdown.disabled {
  background-color: #f9f9f9;
  cursor: not-allowed;
}

.custom-dropdown-header {
  font-size: 16px;
}

.custom-dropdown-options {
  position: absolute;
  width: 100%;
  border: 1px solid #ccc;
  z-index: 10;
  background: #f5f5f6;
  max-height: 250px;
  top: 34px;
  overflow-y: auto;
}

.custom-dropdown-option {
  padding: 10px;
  cursor: pointer;
}

.custom-dropdown-option:hover {
  background-color: #f0f0f0;
}
