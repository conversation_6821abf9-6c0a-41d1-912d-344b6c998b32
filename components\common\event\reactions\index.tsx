/* eslint-disable @next/next/no-img-element */
import styles from "./index.module.scss";

import Image from "next/image";
import { memo, useEffect, useRef, useState } from "react";
import classNames from "classnames";
import { fromUint8Array as fromUint8ArrayToBase64 } from "js-base64";
import throttle from "lodash/throttle";

import type { EventReaction } from "@/types/event";
import type { Subscription } from "@/types/event-bridge";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { liveEventActions } from "@/redux-store/actions";
import { EventBridge } from "@/app/event-bridge";
import { randomInRange } from "@/utils/number";

function LiveReactionsBtnBase(props: { onlyIcon?: boolean }) {
  const event_id = useAppSelector((state) => state.liveEvent._id);
  const display_name = useAppSelector(
    (state) => state.user.profile.display_name
  );
  const reactions_base64 = useAppSelector(
    (state) => state.liveEvent.reactions_base64
  );

  const menuRef = useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [icons, setIcons] = useState<{ type: EventReaction; data: string }[]>(
    []
  );

  useEffect(() => {
    (async () => {
      menuRef.current?.addEventListener("shown.bs.dropdown", () =>
        setIsOpen(true)
      );

      menuRef.current?.addEventListener("hidden.bs.dropdown", () =>
        setIsOpen(false)
      );

      if (props.onlyIcon) {
        menuRef.current?.setAttribute("data-bs-theme", "dark");
      }
    })();
  }, []);

  useEffect(() => {
    if (!reactions_base64.love) return;
    setIcons(
      Object.keys(reactions_base64).map((type) => ({
        type: type as EventReaction,
        data: reactions_base64[type as EventReaction],
      }))
    );
  }, [reactions_base64]);

  const sendReaction = useRef(
    throttle(
      (reaction: EventReaction) => {
        try {
          EventBridge.send(
            "LiveStream/Reaction",
            { event_id, display_name, reaction },
            true
          );
        } catch (e) {
          console.error(e);
        }
      },
      500,
      { trailing: false }
    )
  );

  return (
    <div className="btn-group dropup" ref={menuRef}>
      <button
        type="button"
        className={classNames(
          styles.reactionBtn,
          "btn fw-normal dropdown-toggle",
          {
            [styles["mw-fit"]]: props.onlyIcon,
            "p-0": props.onlyIcon,
          }
        )}
        data-bs-toggle="dropdown"
        data-bs-auto-close="outside"
      >
        <Image
          src="/images/event/reactions/love.png"
          alt="heart"
          width={24}
          height={24}
          className={classNames({
            [!props.onlyIcon ? styles.inActive : styles.inActiveMobile]:
              !isOpen,
            "me-2": !props.onlyIcon,
          })}
        />
        {!props.onlyIcon && "Reactions"}
      </button>
      <div
        className={classNames("dropdown-menu p-2 text-body-secondary", {
          [styles["mb-2nh"]]: props.onlyIcon,
        })}
        style={{ width: "auto" }}
      >
        <div className="d-flex gap-3 gap-md-4 px-2 px-md-3">
          {icons.map((icon, i) => (
            <div
              key={i}
              className={classNames(
                styles.icon,
                "d-flex flex-column justify-content-center align-items-center"
              )}
              onClick={() => sendReaction.current(icon.type)}
            >
              <img src={icon.data} alt={icon.type} />
              <span className="fs-6 user-select-none">{icon.type}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export const LiveReactionsBtn = memo(LiveReactionsBtnBase);

function FlyingReactionsBase({ side = "left" }: { side?: "left" | "right" }) {
  const dispatch = useAppDispatch();
  const reactions_base64 = useAppSelector(
    (state) => state.liveEvent.reactions_base64
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const subRef = useRef<Subscription | null>();

  useEffect(() => {
    (async () => {
      let icons: Record<EventReaction, string> = {} as any;

      if (reactions_base64.love) {
        icons = reactions_base64;
      } else {
        for (const icon of [
          "love",
          "excited",
          "kiss",
          "horny",
          "peachy",
        ] as EventReaction[]) {
          const res = await fetch(`/images/event/reactions/${icon}.png`);
          const blob = await res.blob();
          const arrayBuffer = await blob.arrayBuffer();
          icons[icon] = `data:image/png;base64,${fromUint8ArrayToBase64(
            new Uint8Array(arrayBuffer)
          )}`;
        }

        dispatch(liveEventActions.setReactions(icons));
      }

      subRef.current = EventBridge.on("LiveStream/Reaction", (data) => {
        const img = document.createElement("img");
        img.src = icons[data.reaction];
        img.classList.add(styles.flyingReaction);
        img.onanimationend = () => img.remove();
        img.style[side] = `${randomInRange(0, 20)}%`;
        containerRef.current?.appendChild(img);
      });
    })();

    return () => {
      subRef.current?.unsubscribe();
    };
  }, []);

  return <div className="flying-reactions" ref={containerRef}></div>;
}

export const FlyingReactions = memo(FlyingReactionsBase);
