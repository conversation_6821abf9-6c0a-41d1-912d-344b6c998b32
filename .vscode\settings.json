{"typescript.tsdk": "node_modules/typescript/lib", "svg.preview.background": "editor", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnPaste": true, "editor.formatOnSave": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[snippets]": {"editor.defaultFormatter": "vscode.json-language-features"}, "cSpell.words": ["overscan", "<PERSON><PERSON>"], "[svg]": {"editor.defaultFormatter": "jock.svg"}}