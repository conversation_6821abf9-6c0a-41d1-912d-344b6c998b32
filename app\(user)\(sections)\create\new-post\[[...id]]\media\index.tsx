import "cropperjs/dist/cropper.css";
import Image from "next/image";
import {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import { Cropper } from "react-cropper";
import "../index.scss";

import LoadingSpinner from "@/components/common/loading";

interface Media {
  postImg?: string;
  postVid?: string;
  removePost?: Function;
  showdDoneButton: boolean;
  onCropComplete: (croppedImg: Blob) => void;
  multipleImages?: boolean;
}

const MediaPostCropper = forwardRef((mediaProps: Media, ref) => {
  const [fileCropped, isfileCropped] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cropper, setCropper] = useState<Cropper>();
  const [currentUrl, setCurrentUrl] = useState(mediaProps.postImg);

  // const cropperRef = useRef<ReactCropperElement>(null);

  useEffect(() => {
    setCurrentUrl(mediaProps.postImg);
  }, [mediaProps.postImg]);

  const getCropData = () => {
    if (!mediaProps.multipleImages) {
      setIsLoading(true);
      isfileCropped(true);
    }

    setTimeout(() => {
      if (cropper) {
        const canvas = cropper.getCroppedCanvas();
        canvas.toBlob(
          (blob: Blob | null) => {
            if (blob) {
              mediaProps.onCropComplete(blob);
            }
          },
          "image/jpeg",
          0.8
        );
      }

      setIsLoading(false);
    }, 1000);
  };

  useImperativeHandle(ref, () => ({
    triggerCrop: getCropData,
  }));

  // Following function can be use to maintain the specific ratio of media when cropped extreme width or height.

  // const handleCrop = () => {
  //   const cropper = cropperRef.current?.cropper;
  //   if (cropper) {
  //     const croppedCanvas = cropper.getCroppedCanvas();
  //     const { width, height } = croppedCanvas;

  //     // Ensure height is not more than double the width (1:2 ratio)
  //     if (height > width * 2) {
  //       // If height exceeds 1:2, adjust the crop box to maintain a valid aspect ratio
  //       const newHeight = width * 2;
  //       cropper.setCropBoxData({
  //         width: cropper.getCropBoxData().width,
  //         height: newHeight,
  //       });
  //     }
  //   }
  // };
  return (
    <>
      {currentUrl?.length !== 0 && (
        <>
          <div className="media-content-wrapper bg-cream position-relative w-100">
            <div className="d-flex justify-content-center align-items-center p-3 position-relative cropper-content-wrapper">
              <div>
                {!mediaProps.multipleImages && (
                  <div className="position-absolute end-0 top-0 p-2">
                    <Image
                      onClick={() => {
                        mediaProps.removePost && mediaProps.removePost();
                      }}
                      src={"/images/svg/nav-close.svg"}
                      width={25}
                      height={25}
                      className="invert z-3 pointer svg-icon"
                      alt="remove-media"
                    />
                  </div>
                )}
                {!fileCropped && (
                  <Cropper
                    src={currentUrl || ""}
                    className={"mt-4 img-fluid m-auto"}
                    style={{ height: 400, width: "100%" }}
                    autoCropArea={1}
                    guides={false}
                    onInitialized={(instance) => {
                      setCropper(instance);
                    }}
                    viewMode={1}
                    alt="post-media"
                    // ref={cropperRef}
                    // crop={handleCrop}
                    minCropBoxWidth={100}
                    minCropBoxHeight={50}
                  />
                )}
                <div className=" position-absolute text-center h-100">
                  {isLoading && (
                    <>
                      <LoadingSpinner />
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {!fileCropped && mediaProps.showdDoneButton && (
            <div className="d-flex justify-content-end h-auto w-100 ">
              <button
                className="btn btn-purple mt-2 position-relative"
                onClick={getCropData}
              >
                Done
              </button>
            </div>
          )}
        </>
      )}
    </>
  );
});

MediaPostCropper.displayName = "MediaPostCropper";

export const MediaPost = memo(MediaPostCropper);
