import Swal from "sweetalert2";

import { respondReq } from "@/api/chat";
import { GetUserProfileById } from "@/api/user";

export const handleReq = async (reqId: string, isAccepted: boolean) => {
  if (isAccepted) {
    await respondReq(reqId, {
      status: 1,
    }).then(() => {
      Swal.fire({
        icon: "success",
        title: "You've now accepted the request!",
        confirmButtonText: "Close",
        confirmButtonColor: "#AC1991",
        customClass: {
          confirmButton: "custom-btn",
        },
        showCloseButton: true,
      });
    });
  } else {
    await respondReq(reqId, {
      status: 2,
    }).then(() => {});
  }
};

export const handlePay = (userId: string, setReceiver: Function) => {
  GetUserProfileById(userId).then((res) => setReceiver(res?.data[0]));
};
