import debounce from "lodash/debounce";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import "./index.scss";

import { EditChannelTags } from "@/api/channel";
import {
  CreateHashTag,
  EditSearchTags,
  type GetTagResponse,
  SearchCategory,
  type TagObject,
  updateHashTag,
} from "@/api/hashtags";
import { defaultActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { kinksCount, lifestyle } from "@/utils/hashtags-limit";

import type { ModalRef } from "../modal";
import Modal from "../modal";

const EditHashTag = ({
  setChildRef,
  misc,
  title,
}: {
  setChildRef: { method: ModalRef };
  misc: any;
  title: string;
}) => {
  const dispatch = useAppDispatch();
  const [tag, setTags] = useState<string[]>([]);
  const [removedTag, setRemovedTags] = useState<string[]>([]);
  const [initialTags, setInitialTags] = useState<string[]>([]);

  const [disbleBtn, setDisabledBtn] = useState(true);

  const [showSearchHashtag, setSearchHashtag] = useState(true);
  const channelInformation = useAppSelector(
    (state) => state.defaults.channel_profile
  );

  const updateHashTags = () => {
    const newTags: string[] = tag.filter((t) => !initialTags.includes(t));

    //#region
    // if (window.location.pathname.includes("/collab")) {
    //   console.log({ newTags, removedTag });
    //   UpdateGroup(
    //     {
    //       hashtags: [...newTags],
    //     },
    //     groupInformation?._id
    //   )
    //     .then(() => {
    //       dispatch(
    //         defaultActions.setGroupProfile({
    //           ...groupInformation,
    //           hash: tag,
    //         })
    //       );
    //       setChildRef.method.close();
    //       Swal.fire({
    //         icon: "success",
    //         title: `Group hashtags updated successfully`,
    //         confirmButtonText: "Done",
    //         confirmButtonColor: "#AC1991",
    //
    //       });
    //     })
    //     .catch((e) => console.error(e));
    //   return;
    // }
    //#endregion
    if (window.location.pathname.split("/")[1] === "channel") {
      EditChannelTags(window.location.pathname.split("/")[2], {
        add_hashtags: newTags,
        remove_hashtags: removedTag,
      })
        .then(() => {
          dispatch(
            defaultActions.setChannelProfile({
              ...channelInformation,
              hash: tag,
            })
          );
          setChildRef.method.close();
          Swal.fire({
            icon: "success",
            title: `Channel hashtags updated successfully`,
            confirmButtonText: "Done",
            confirmButtonColor: "#AC1991",
          });
        })
        .catch((e) => console.error(e));
      return;
    }

    const body: any = {};

    if (misc.title === "Kinks") {
      body.add_kinks = newTags;

      if (removedTag.length > 0) {
        body.removable_kinks = [...removedTag];
      }
    } else if (misc.title === "Lifestyle") {
      body.add_lifestyle_topics = newTags;

      if (removedTag.length > 0) {
        body.removable_lifestyle_topics = [...removedTag];
      }
    } else if (misc.title === "Hashtags") {
      body.add_hashtags = newTags;

      if (removedTag.length > 0) {
        body.removable_hashtags = [...removedTag];
      }
    }

    updateHashTag(body).then(() => {
      setDisabledBtn(false);
      // router.push(`/${user?.role?.toLowerCase()}/${user?.profile?.username}`);

      Swal.fire({
        icon: "success",
        title: `${misc.title} updated successfully`,
        confirmButtonText: "Done",
        confirmButtonColor: "#AC1991",
      }).then(() => {
        // setChildRef.method.close();
        window.location.reload();
      });
    });
  };

  useEffect(() => {
    const initialTagList = misc.array || [];
    setTags(initialTagList);
    setInitialTags(initialTagList);
  }, [misc]);

  const user = useAppSelector((state) => state.user);
  const [tagsSearch, settagsSearch] = useState<TagObject | any>(null);

  const [tagList, settagList] = useState<TagObject[]>();
  const [showDropdown, setShowDropdown] = useState(false);
  const [isError, setisError] = useState(false);

  const requestHashTag = (tag: string) => {
    const body = {
      hashtag: `#${tag}`,

      is_explicit: misc.title === "Kinks" ? true : false,
    };
    Swal.fire({
      title: `Do you want to request ${tag} tag`,
      icon: "question",

      confirmButtonText: "Yes",
      confirmButtonColor: "#AC1991",
    }).then((res) => {
      if (res.isConfirmed) {
        CreateHashTag(body).then(() => {
          setSearchHashtag(true);

          settagsSearch(null);

          setDisabledBtn(true);
          Swal.fire({
            title: "Request send successfully",
            icon: "success",
            text: "We will notify you after the request has been accepted",
            confirmButtonText: "OK",
            confirmButtonColor: "#AC1991",
          });
        });
      }
    });
  };

  const removeTag = (removeTag: any, index: number) => {
    setRemovedTags((prevRemovedTags) => [...prevRemovedTags, removeTag]);

    if (tag.length < 6) {
      setDisabledBtn(true);
    } else {
      setDisabledBtn(false);
    }

    const updatedTag: any = [...tag];
    // eslint-disable-next-line no-unused-expressions
    updatedTag.splice(index, 1)[0];
    setTags(updatedTag);
  };

  const handleTags = (hashtag: TagObject) => {
    if (
      (misc.title === "Kinks" && tag.length >= kinksCount) ||
      (misc.title === "Lifestyle" && tag.length >= lifestyle)
    ) {
      setShowDropdown(false);
      Swal.fire({
        title: "Tag limit reached",
        icon: "warning",
        text: `You can only add up to ${
          misc.title === "Kinks" ? kinksCount : lifestyle
        } tags.`,
        confirmButtonText: "OK",
        confirmButtonColor: "#AC1991",
      });
      return;
    }

    if (!tag.includes(hashtag._id)) {
      const newTags = [...tag, hashtag._id];
      setTags(newTags);
      setDisabledBtn(false);
    }
  };

  const handleHashTags = (hashtag: string) => {
    console.log({ hashtag });

    if (tag.length >= 20) {
      setShowDropdown(false);
      Swal.fire({
        title: "Tag limit reached",
        icon: "warning",
        text: "You can only add up to 20 tags.",
        confirmButtonText: "OK",
        confirmButtonColor: "#AC1991",
      });
      return;
    } else {
      const newTag = [...tag, hashtag];
      setTags(newTag);
      setShowDropdown(true);
      setDisabledBtn(false);
    }
  };

  const handleBlur = () => {
    // Hide the dropdown when the input is out of focus
    setTimeout(() => {
      setShowDropdown(false);
      settagsSearch("");
    }, 600);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputtext = e.target.value.replace(/[^a-z0-9]+/g, "");

    settagsSearch(inputtext);

    if (inputtext === "") {
      setTimeout(() => {
        setShowDropdown(false);
      }, 600);
    } else {
      debouncedSearchTags(inputtext, misc.title);
    }
  };

  const searchTags = async (searchText: string, title: string) => {
    try {
      const data: GetTagResponse = await EditSearchTags(
        searchText,
        (title === "Kinks" && true) ||
          (title === "Lifestyles" && false) ||
          (title === "More Hashtags" && undefined)
      );
      settagList(data?.data);
      setisError(false);
      setShowDropdown(true);
    } catch (error: any) {
      if (error.errorCode === 1000) {
        setisError(true);
        setShowDropdown(false);
      }

      console.error("Error fetching user data", error);
    }
  };

  const searchCategory = async (searchText: string, title: string) => {
    try {
      const data = await SearchCategory({
        category: searchText,
        explicit:
          (title === "Kinks" && true) ||
          (title === "Lifestyles" && false) ||
          (title === "More Hashtags" && undefined),
      });

      settagList(data?.data);
      setShowDropdown(true);
    } catch (error: any) {
      if (error.errorCode === 1000) {
        setisError(true);
        setShowDropdown(false);
      }

      console.error("Error fetching user data", error);
    }
  };

  const debouncedSearchTags = useRef(
    debounce((tagsSearch, title) => {
      searchTags(tagsSearch, title);
    }, 600)
  ).current;

  const debouncedSearchCategory = useRef(
    debounce((categorySearch, title) => {
      searchCategory(categorySearch, title);
    }, 600)
  ).current;
  // useEffect(() => {
  //   debouncedHandleSearch();
  // }, [tagsSearch]);

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
    }
  };

  console.log({ title });

  return (
    <Modal
      title={title}
      subtitle={[""]}
      setChildRef={setChildRef}
      render={"direct"}
    >
      <>
        {
          <div className="d-flex flex-column  align-items-center edit-hashtag-container ">
            <div className="w-100 d-flex justify-content-center ">
              <div className="d-flex gap-2 mt-1  edit-hashtag-width">
                <div
                  className={`header-search-box search-box position-relative w-100  d-flex  gap-2 align-items-center px-2 py-1 rounded-3 ${
                    user?.role == "guest" ? "opacity-50" : ""
                  }`}
                  style={{ alignSelf: "flex-start" }}
                >
                  <Image
                    src="/images/creator/search.svg"
                    alt="Search Logo"
                    className="brand-logo"
                    width={20}
                    height={20}
                    priority
                  />
                  <form className="w-100">
                    <input
                      type="search"
                      placeholder={
                        showSearchHashtag
                          ? "Search hashtags"
                          : "Search Category"
                      }
                      className="fs-7 color-dark fw-medium w-100 "
                      value={tagsSearch}
                      onChange={handleInputChange}
                      onBlur={handleBlur}
                      onKeyPress={handleKeyPress}
                      disabled={user?.role == "guest"}
                    />
                  </form>
                  {isError && (
                    <span className="ban-word-error">
                      The word {tagsSearch} isn’t allowed. Please keep it
                      friendly!
                    </span>
                  )}
                  {showDropdown && (
                    <>
                      <ul
                        className="edit-serch-hashtag-menu mt-2 border fs-7"
                        style={{ zIndex: "9999" }}
                      >
                        {tagList?.[0] ? (
                          tagList?.map((tags: TagObject) => (
                            <div
                              className="d-flex justify-content-between mb-2"
                              key={tags?._id}
                            >
                              <li key={tags._id}>
                                <span className="mx-2">{tags?._id}</span>
                              </li>
                              <div
                                className="border border-dark rounded-2 py-1 px-4 fw-medium pointer"
                                onClick={() => handleTags(tags)}
                              >
                                {tag &&
                                tag.some(
                                  (tagItem: any) => tagItem === tags?._id
                                )
                                  ? "Added"
                                  : "Add"}
                              </div>
                            </div>
                          ))
                        ) : (
                          <>
                            {misc.title === "Lifestyle" ||
                            misc.title === "Kinks" ? (
                              <div className="d-flex justify-content-between mb-2 align-items-center">
                                <li>
                                  <span className="">{"#" + tagsSearch}</span>
                                  <p className="color-medium m-0 ">
                                    This hashtag is not available.
                                  </p>
                                </li>
                                <div
                                  className="border border-dark rounded-2 py-1 px-4 fw-medium pointer text-nowrap"
                                  onClick={() => requestHashTag(tagsSearch)}
                                >
                                  Request admin
                                </div>
                              </div>
                            ) : (
                              <li className="d-flex align-items-center justify-content-between align-items-center ">
                                <div>
                                  <p className="m-0 fw-medium ">
                                    #{tagsSearch}
                                  </p>
                                  {/* <p className="color-medium m-0 ">
                                    This hashtag is not available.
                                  </p> */}
                                </div>
                                <div
                                  className="border border-dark rounded-2 py-1 px-4 fw-medium pointer"
                                  onClick={() =>
                                    handleHashTags("#" + tagsSearch)
                                  }
                                >
                                  {tag &&
                                  tag.some(
                                    (tagItem: any) =>
                                      tagItem === tagsSearch?._id
                                  )
                                    ? "Added"
                                    : "Add"}
                                </div>
                              </li>
                            )}
                          </>
                        )}
                      </ul>
                    </>
                  )}
                </div>
              </div>
            </div>
            {showSearchHashtag && (
              <>
                {" "}
                <div className="mt-5 edit-hashtag-width overflow-auto h-100 ">
                  {/* <h2 className="text-center">{misc.title}</h2> */}

                  <div className=" d-flex gap-3 flex-wrap mt-3">
                    {tag.map((tag: any, i: number) => (
                      <div key={tag} className="editTag rounded-3">
                        <span>{tag}</span>{" "}
                        <Image
                          src={"/images/svg/nav-close.svg"}
                          width={18}
                          height={18}
                          alt=""
                          className="pointer svg-icon"
                          onClick={() => removeTag(tag, i)}
                        />
                      </div>
                    ))}
                  </div>
                  {tag.length < 5 && (
                    <div className="fs-7 color-medium mt-2">
                      Minimum 5 tags required.
                    </div>
                  )}
                </div>
                <div className="pt-2">
                  <button
                    className={`btn  ${
                      disbleBtn ? "btn-disabled" : "btn-purple"
                    }`}
                    disabled={disbleBtn}
                    onClick={updateHashTags}
                  >
                    Update Tags
                  </button>
                </div>
              </>
            )}
          </div>
        }
      </>
    </Modal>
  );
};

export default EditHashTag;
