import { memo, useEffect, useState } from "react";

import ActionButton from "@/components/common/action-button";
import { ModalPortal } from "@/components/portals/ModalPortal";
import { MediaFeeChat } from "@/global/constants";
import { useAppSelector } from "@/redux-store/hooks";
import { formatCurrency } from "@/utils/formatter";

const MediaFeeModalBase = ({
  setMediaFee,
  setHasMediaFee,
}: {
  mediaFee: number;
  setMediaFee: (value: number) => void;
  hasMediaFee: boolean;
  setHasMediaFee: (value: boolean) => void;
}) => {
  const targetUser = useAppSelector((s) => s.chat.targetUser);
  const [internalFee, setInternalFee] = useState("5");

  const handleSetFee = () => {
    setMediaFee(+internalFee);
    setHasMediaFee(true);
  };

  useEffect(() => {
    setMediaFee(5);
    setInternalFee("5");
  }, [targetUser]);

  useEffect(() => {
    const element = document.getElementById("mediaFeeModal");

    element?.addEventListener("hide.bs.modal", () => {
      setMediaFee(5);
      setInternalFee("5");
    });

    return () => {
      element?.removeEventListener("hide.bs.modal", () => {
        setMediaFee(5);
        setInternalFee("5");
      });
    };
  }, []);

  return (
    <ModalPortal>
      <div
        className="modal fade"
        id="mediaFeeModal"
        tabIndex={-1}
        aria-labelledby="mediaFeeModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header">
              <h1 className="modal-title fs-5" id="mediaFeeModalLabel">
                Set Media Fee
              </h1>
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                id="btn-close"
                aria-label="Close"
              ></button>
            </div>
            <div className="modal-body">
              <form>
                <div className="mb-3">
                  <label htmlFor="recipient-name" className="col-form-label">
                    Fee:{" "}
                    <span className="color-medium fs-8">
                      (Minimum {formatCurrency(MediaFeeChat)})
                    </span>
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    value={internalFee}
                    placeholder="For eg. $5"
                    onChange={(e) => {
                      const value = e.target.value;
                      const numericValue = value.match(/^\d*\.?\d*$/);

                      if (numericValue) {
                        setInternalFee(numericValue[0]);
                      }
                    }}
                  />
                  <div
                    className={`color-red fs-8 mt-1 ${
                      +internalFee > 50_000 ? "visible" : "invisible"
                    }`}
                  >
                    Maximum {formatCurrency(50_000)}
                  </div>
                </div>
              </form>
            </div>
            <div className="modal-footer">
              <span data-bs-dismiss="modal">
                <ActionButton
                  btnType="button"
                  onClick={handleSetFee}
                  disabled={
                    +internalFee < MediaFeeChat || +internalFee > 50_000
                  }
                >
                  Confirm
                </ActionButton>
              </span>
            </div>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export const MediaFeeModal = memo(MediaFeeModalBase);
