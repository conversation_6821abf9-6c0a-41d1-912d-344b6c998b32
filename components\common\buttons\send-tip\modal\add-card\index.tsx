import { Formik } from "formik";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

import { useRegisterModal } from "@/hooks/useRegisterModal";

export default function AddCardModal() {
  const initialValues = {
    tip: 0,
  };

  const modalRef = useRef<any>(null);
  const router = useRouter();
  const [title, setTitle] = useState("");
  useEffect(() => {
    const initializeModal = () => {
      if (window && window.bootstrap) {
        const myModal = new window.bootstrap.Modal(
          modalRef.current as HTMLElement,
          {
            backdrop: "static",
            keyboard: false,
          }
        );
        modalRef.current!.modalInstance = myModal;
      }
    };

    setTimeout(initializeModal, 300);
  }, []);

  useRegisterModal("ADD_CARD", (data) => {
    modalRef.current?.modalInstance?.show();
    setTitle(data?.type || "");
  });

  const closeModal = () => {
    modalRef.current?.modalInstance.hide();
  };

  return (
    <div
      className="modal fade"
      id="AddCardModal"
      tabIndex={-1}
      ref={modalRef}
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content">
          <div className="modal-body">
            <div className="d-flex justify-content-end">
              <button
                type="button"
                className="btn-close"
                onClick={() => closeModal()}
                aria-label="Close"
              ></button>
            </div>
            <div className="container w-75 d-flex flex-column justify-content-center gap-3 align-items-center">
              <Formik initialValues={initialValues} onSubmit={() => {}}>
                <>
                  <h1>{title}</h1>
                  <h2 className="text-center">
                    You need to add your payment method first.
                  </h2>

                  <button
                    onClick={() => {
                      router.push("/settings/addCard");
                      closeModal();
                    }}
                    className="btn btn-purple mt-2 w-50"
                  >
                    Add new Card{" "}
                  </button>
                </>
              </Formik>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
