"use client";

import Image from "next/image";
import { useSearchParams } from "next/navigation";
import Swal from "sweetalert2";
import { Formik } from "formik";
import * as Yup from "yup";
import { useEffect } from "react";

import { forgetPasswordErrors } from "@/components/common/modal/sign-in/welcome/placeholder";
import { VerifyResetPassword } from "@/api/user";
import Input from "@/components/common/input";
import Button from "@/components/common/button";
import Form from "@/components/common/form";

export default function ResetPassword() {
  const token = useSearchParams().get("token");

  useEffect(() => {
    if (!token) window.location.href = "/";
  });

  const initialValues = {
    password: "",
    confirmPassword: "",
  };

  const validationSchema = Yup.object({
    password: Yup.string()
      .matches(
        /[A-Z]/,
        "Password must contain at least one uppercase character"
      )
      .matches(
        /[!@#$%^&*(),.?":{}|<>]/,
        "Password must contain at least one special character"
      )
      .min(8, "Password must be at least 8 characters")
      .required("Password is required"),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref("password"), undefined], "Passwords must match")
      .required("Confirm Password is required"),
  });

  const onSubmit = (values: any) => {
    VerifyResetPassword({
      new_password: values.password,
      confirm_new_password: values.confirmPassword,
      token: token!,
    }).then(() => {
      Swal.fire({
        title: "Your account password has been updated.",
        text: "It’s time to login and enjoy our content 🌟",
        icon: "success",
        confirmButtonText: "Okay",
        showConfirmButton: true,
        timerProgressBar: true,
        allowOutsideClick: false,
      })
        .then(() => {
          window.location.href = "/";
        })
        .catch((error) => {
          Swal.fire({
            title: forgetPasswordErrors[error] || "Error Occurred",
            icon: "error",
            confirmButtonText: "Finish",
            showConfirmButton: true,
            timerProgressBar: true,
            allowOutsideClick: false,
          });
        });
    });
  };

  return (
    token && (
      <div
        className="w-100 d-flex justify-content-center align-items-center"
        style={{ height: "100Vh" }}
      >
        <div className="d-flex flex-column gap-3 align-items-center">
          <Image
            src={"/images/knky.svg"}
            alt="KNKY"
            className="brand-logo header-icon"
            width={70}
            height={40}
            priority
          />
          <div className="d-flex flex-column align-items-center">
            <div className="fs-3 fw-bold">Create new Password</div>
            <div className="color-medium">
              Congratulation! Now you can create a new password for your
              account.
            </div>
          </div>
          <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validationSchema={validationSchema}
          >
            {({
              values,
              setFieldValue,
              isSubmitting,
              isValid,
              dirty,
              ...rest
            }) => (
              <Form
                formikValues={{
                  values,
                  setFieldValue,
                  isSubmitting,
                  isValid,
                  dirty,
                  ...rest,
                }}
                className="w-100"
              >
                <div className="d-flex flex-column gap-4 w-100">
                  <Input
                    name="password"
                    type="password"
                    placeholder={"New Password"}
                    label="Enter New Password"
                    className="w-100"
                  />
                  <Input
                    name="confirmPassword"
                    type="password"
                    placeholder={"Confirm Password"}
                    label="Confirm Password"
                    className="w-100"
                  />
                  <Button
                    text="Reset Password"
                    isValid={isValid}
                    isSubmitting={isSubmitting}
                    dirty={dirty}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    )
  );
}
