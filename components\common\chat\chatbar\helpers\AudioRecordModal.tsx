import { memo, useCallback, useEffect, useState } from "react";

import { ChatMedia } from "@/api/user";
import ActionButton from "@/components/common/action-button";
import AudioRecorder from "@/components/common/audio-recorder";
import { ModalPortal } from "@/components/portals/ModalPortal";
import useFileUploader from "@/hooks/useFileUploader";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Chat } from "@/redux-store/slices/chat.slice";

const AudioRecordModalBase = () => {
  const { uploadFile } = useFileUploader();

  const dispatch = useAppDispatch();
  const chatData = useAppSelector((s) => s.chat);
  const user = useAppSelector((s) => s.user);
  const channelId = useAppSelector((s) => s.chat.channelId);

  const [sendWithAFee, setSendWithAFee] = useState(true);
  const [message, setMessage] = useState("");
  const [fee, setFee] = useState("5");

  const [audioBlob, setAudioBlob] = useState<File | null>(null);
  const [modalClose, setModalClose] = useState(false);
  const [channelData, setChannelData] = useState<Chat>();
  const [loading, setLoading] = useState(false);

  const targetUserId = useAppSelector((s) => s.chat.targetUser);
  const chatList = useAppSelector((s) => s.chat.chatList);

  useEffect(() => {
    const targetId = chatData.targetUser;
    const data = chatData.chatList.filter((chat: any) => {
      return (
        chat?.target?._id === targetId || chat?.initiator?._id === targetId
      );
    })?.[0];

    if (!data) return;

    setChannelData(data);

    return () => {
      dispatch(chatActions.setHasMedia(false));
      setMessage("");
    };
  }, [chatData.targetUser, chatList]);

  const sendMessage = async () => {
    if (!audioBlob) return;
    if (!chatData.targetUser) return;

    setLoading(true);

    const updatedChatOnTop = chatData.chatList.filter((chat: any) => {
      return (
        chat?.initiator?._id === targetUserId ||
        chat?.target?._id === targetUserId
      );
    });
    const index = chatData.chatList.findIndex((chat: any) => {
      return (
        chat?.initiator?._id === targetUserId ||
        chat?.target?._id === targetUserId
      );
    });

    if (index !== -1) {
      const updatedChatList = [
        ...chatData.chatList.slice(0, index),
        ...chatData.chatList.slice(index + 1),
      ];

      updatedChatList.unshift(...updatedChatOnTop);

      dispatch(chatActions.setChatUserList(updatedChatList));
    }

    dispatch(chatActions.updateChatActivity(chatData.channelId));

    const shouldDeduct =
      !channelData?.buyers.some(
        (b) => b.buyer === user.id || b.buyer === targetUserId
      ) &&
      channelData?.converse_consumable &&
      (channelData?.converse_consumable?.find((c) => c.buyer === user.id)
        ?.available_message ?? 0) > 0;

    if (audioBlob) {
      dispatch(
        chatActions.setMediaLoading({
          channel_id: chatData.channelId,
          loading: true,
        })
      );

      const media = audioBlob;

      let mediaResponse;
      if (media) mediaResponse = await uploadFile(media, "Chat");
      const filePath =
        mediaResponse?.[0]?.uploadURL?.split("/")[
          mediaResponse?.[0]?.uploadURL?.split("/").length - 1
        ];

      if (media) {
        ChatMedia({
          ...(media &&
            filePath &&
            mediaResponse?.[0]?.type && {
              media: [
                {
                  path: (mediaResponse as any)?.[0]?.s3Multipart?.key,
                  type: mediaResponse[0].type?.split("/")[0] as
                    | "image"
                    | "video"
                    | "audio",
                },
              ],
            }),
          message_text:
            message ||
            `${user.profile.display_name.split(" ")[0]} sent a voice note.`,
          meta: {
            type: "message-attachment",
            converseId: channelId,
            chat_list_message:
              message ||
              `${user.profile.display_name.split(" ")[0]} sent a voice note.`,
            ...(audioBlob
              ? {
                  is_audio: true,
                  is_unlocked: sendWithAFee && fee ? false : true,
                }
              : {}),
            ...(fee && sendWithAFee ? { media_fee: Number(fee) } : {}),
          },
          channel_id: chatData.channelId,
          ...(shouldDeduct ? { deduct_count: true } : {}),
        })
          .then((res: any) => {
            dispatch(
              chatActions.setMediaLoading({
                channel_id: chatData.channelId,
                loading: false,
              })
            );
            dispatch(chatActions.updateLoadingState(true));
            dispatch(
              chatActions.updateLastMessage({
                channelId: chatData.channelId,
                message:
                  message ||
                  `${
                    user.profile.display_name.split(" ")[0]
                  } sent a voice note.`,
              })
            );
            // socketChannel?.channel?.sendMessage({
            //   message: message ? message : "Attachment",
            //   meta: {
            //     type: "message-attachment",
            //     chat_list_message: "Sending media...",
            //     media: res?.data?.media,
            //     ...(audioBlob
            //       ? {
            //           is_audio: true,
            //           is_unlocked: sendWithAFee && fee ? false : true,
            //         }
            //       : {}),
            //     ...(fee ? { media_fee: Number(fee) } : {}),
            //   },
            // });
          })
          .catch((err) => {
            dispatch(
              chatActions.setMediaLoading({
                channel_id: chatData.channelId,
                loading: false,
              })
            );
            console.error(err);
          });
      }
    }

    setAudioBlob(null);

    dispatch(chatActions.setNewMessage(message));

    setMessage("");
    setLoading(false);

    if (document) {
      document.getElementById("modal-close")?.click();
    }
  };

  const setCaption = useCallback((e: any) => {
    setMessage(e.target.value);
  }, []);

  return (
    <ModalPortal>
      <div
        className="modal fade"
        id="audioRecordModal"
        tabIndex={-1}
        data-bs-backdrop="static"
        aria-labelledby="audioRecordModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header flex-column">
              <h1 className="modal-title fs-5" id="audioRecordModalLabel">
                Record a voice message
              </h1>

              <button
                type="button"
                className="btn-close position-absolute top-0 end-0 m-2"
                data-bs-dismiss="modal"
                onClick={() => {
                  setSendWithAFee(false);
                  setMessage("");
                  setFee("");

                  if (audioBlob === null) {
                    setModalClose(true);
                    setTimeout(() => {
                      setModalClose(false);
                    }, 100);
                  }

                  setAudioBlob(null);
                }}
                id="modal-close"
                aria-label="Close"
              ></button>
            </div>
            <div className="modal-body d-flex gap-2 flex-column justify-content-center align-items-center">
              <AudioRecorder
                setAudioBlob={setAudioBlob}
                audioBlob={audioBlob}
                modalClose={modalClose}
              />
              <div className="d-flex flex-column w-100">
                <textarea
                  rows={5}
                  placeholder="Say something here"
                  value={message}
                  className="w-100 bg-cream color-dark rounded-3 border-0 p-2 fs-6"
                  onChange={setCaption}
                />
              </div>
              <div className="form-check form-switch d-flex justify-content-center align-items-center">
                <input
                  className="form-check-input mt-0"
                  type="checkbox"
                  role="switch"
                  id="fee_checkbox"
                  checked={sendWithAFee}
                  onChange={(e) => setSendWithAFee(e.target.checked)}
                />
                <label className="form-check-label ms-2" htmlFor="fee_checkbox">
                  Send with a fee
                </label>
              </div>
              {sendWithAFee && (
                <div className="d-flex flex-column w-100 position-relative">
                  <label className="color-medium fs-7">
                    Set an unlock fee
                    <span className="color-red">*</span>
                  </label>
                  <div className="input-wrapper rounded-3  d-flex bg-cream align-items-center gap-2">
                    <div className="color-medium fw-bold bg-cream ps-2">$</div>
                    <input
                      type="number"
                      value={fee}
                      min={5}
                      onChange={(e) => {
                        setFee(e.target.value);
                      }}
                      placeholder="5"
                      className="w-100 bg-cream  rounded-3 color-dark border-0 p-2 fs-6 px-1"
                    />
                  </div>
                  <span className="color-medium fs-8">Minimum $5.00 USD</span>
                </div>
              )}
              <ActionButton
                disabled={
                  !audioBlob || (sendWithAFee && Number(fee) < 5) || loading
                }
                onClick={sendMessage}
              >
                Send your voice message
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export const AudioRecordModal = memo(AudioRecordModalBase);
