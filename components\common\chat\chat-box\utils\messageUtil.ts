import type { MessageInterface } from "@/redux-store/slices/chat.slice";

export const fetchRequestStatus = async (
  reqId: string,
  socketChannelInstance: any
) => {
  try {
    const res = await socketChannelInstance?.channel?.getMessages({});

    if (res && res.msgs && res.msgs.unread) {
      const val = res.msgs.unread.filter((el: any) => el.reqId !== reqId);
      return val[0];
    } else {
      throw new Error("Unable to fetch request status");
    }
  } catch (error) {
    console.error("Error fetching request status:", error);
    return null;
  }
};

export const updateMessageInArray = (
  array: Array<Record<string, any>>,
  message: MessageInterface
) => {
  try {
    const foundMessageIndex = array.findIndex((mes) => {
      return (
        mes?._id === message.messageId || mes?.messageId === message.messageId
      );
    });

    if (foundMessageIndex !== -1) {
      const updatedMessage = {
        ...array[foundMessageIndex],
        meta: {
          ...array[foundMessageIndex].meta,
          requestAccept: message?.meta.requestAccept,
        },
      };

      if (message?.meta.url) {
        updatedMessage.meta = {
          ...updatedMessage.meta,
          url: message?.meta.url,
        };
      }

      if (message?.meta?.paid) {
        updatedMessage.meta = {
          ...updatedMessage.meta,
          paid: message?.meta.paid,
        };
      }

      const updatedArray = [...array];
      updatedArray[foundMessageIndex] = updatedMessage;
      return updatedArray;
    } else {
      throw new Error("Message not found in array");
    }
  } catch (error) {
    console.error("Error updating message in array:", error);
    return array;
  }
};
