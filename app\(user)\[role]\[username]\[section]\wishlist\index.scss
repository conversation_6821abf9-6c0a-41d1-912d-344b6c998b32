$in-size: 60px;
.wishlist-container {
  background: #ffff;
  padding: 1rem;
  border-radius: 8px;
}
.button-wrapper {
  cursor: pointer;
}
.wishlistData-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
  }
}
.wishlistInfo-wrapper {
  border: 1px solid #ebebec;
  padding: 4%;
  border-radius: 8px;
  .img-background {
    background-color: #f1f1f1;
  }
  img {
    object-fit: contain;
    margin: auto;
    display: block;
  }
  p {
    font-weight: 600;
  }
}
.wishlist-input {
  appearance: none;
  height: 24px;
  width: 45px;
  background-color: rgb(220, 221, 224);
  border-radius: 50px;
  margin: auto;
  cursor: pointer;
  outline: none;
  transition: 0.3s;
  position: relative;
}
.wishlist-input:before {
  content: "";

  content: "";
  height: 20px;
  width: 20px;
  background-color: #f1f1f1;
  border-radius: 50%;
  left: 4px;
  position: absolute;
  top: 2px;
  transition: 0.3s;
}
.wishlist-input:after {
  content: "";
  position: absolute;
  font-family: "Montserrat", sans-serif;
  color: #d5d5d5; /*#ff6161*/

  left: 125px;
  bottom: -6px;
  letter-spacing: 1px;
  transition: 0.3s;
}
.wishlist-input:checked {
  background-color: #ac1991;
}
.wishlist-input:checked:before {
  background-color: #ffffff;
  left: 22px;
}
.wishlist-input:checked:after {
  content: "";
  color: #ac1991;
}
.underline {
  color: #4d5053;
}
