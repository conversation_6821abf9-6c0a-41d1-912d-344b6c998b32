.content-warning {
  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: 1.5rem;
    padding: 5%;
    .btn {
      min-width: 19rem !important;
      @media (max-width: 400px) {
        min-width: 16rem !important;
      }
    }
  }

  .color-black {
    @media (max-width: 768px) {
      font-size: 0.8rem;
    }
  }
  h4 {
    @media (max-width: 768px) {
      font-size: 0.9rem;
    }
  }
  @media (max-width: 768px) {
    max-height: 80vh;
    overflow: auto;
  }
}
.content-warning::-webkit-scrollbar {
  width: 3px;
  background-color: #f5f5f5;
}
.content-warning::-webkit-scrollbar-thumb {
  background-color: rgba(172, 25, 145, 1);
  border: 2px solid rgba(172, 25, 145, 1);
}
.content-warning::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}

@media screen and (max-width: 768px) {
  .main-text {
    font-size: 0.9rem;
  }

  .knky-text {
    line-height: 0.88rem; 
  }
}
