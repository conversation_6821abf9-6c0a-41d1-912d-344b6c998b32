import classNames from "classnames";
import { useState } from "react";
import { toast } from "sonner";

import { ApprovalConsent, ConsentStatus, GetTagDetails } from "@/api/chat";
import { GetSignedUrl } from "@/api/user";
import ActionButton from "@/components/common/action-button";
import DateFormatter from "@/components/common/date";
import { ModalService } from "@/components/modals";
import { useAsyncEffect } from "@/hooks/useAsyncEffect";
import { useDeepCompareEffect } from "@/hooks/useDeepCompareEffect";
import { useAppSelector } from "@/redux-store/hooks";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import type { Media } from "@/types/media";
import { getAssetUrl } from "@/utils/assets";
import socketChannel from "@/utils/chatSocket";

const getStatusMeta = (status: "sent" | boolean | undefined) => {
  if (status === "sent") {
    return {
      text: "Waiting",
      color: "rgba(255, 168, 0, 1)",
      backgroundColor: "rgba(255, 168, 0, 0.12)",
    };
  }

  if (status) {
    return {
      text: "Approved",
      color: "rgba(86, 194, 45, 1)",
      backgroundColor: "rgba(86, 194, 45, 0.2)",
    };
  }

  return {
    text: "Rejected",
    color: "rgba(245, 34, 45, 1)",
    backgroundColor: "rgba(245, 34, 45, 0.2)",
  };
};

const TagApproval = ({
  usedAs,
  message,
}: {
  usedAs: "sender" | "receiver";
  message: MessageInterface;
}) => {
  const userId = useAppSelector((s) => s.user.id);
  const targetUser = useAppSelector((s) => s.chat.targetUser);

  const {
    requestAccept: status,
    media,
    sub_type: source_type,
  } = message?.meta || {};

  const [customMediaUrl, setCustomMediaUrl] = useState("");

  const mediaItem = Array.isArray(media) ? media?.[0] : media;
  const { text, color, backgroundColor } = getStatusMeta(status);
  const [allMedias, setAllMedias] = useState<Media[]>([]);

  useDeepCompareEffect(() => {
    if (!mediaItem?.path) return;

    const [name, ext] = String(mediaItem.path).split(".");
    if (!name || !ext) return;

    GetSignedUrl({
      assets_path: [`${name}_compressed.${ext}`],
      type: "signedFullUrl",
    }).then((res) => {
      const signedUrl = res.result?.[`${name}_compressed.${ext}`];
      if (signedUrl) setCustomMediaUrl(signedUrl);
    });
  }, [media]);

  async function handleForms() {
    if (!media || !mediaItem) return;

    const response = await GetTagDetails(
      source_type === "POST" ? mediaItem.post! : mediaItem._id!,
      source_type as "POST" | "VAULT"
    );

    const internalTagConsent =
      response.data.media?.[0]?.consent?.internal_tag_consent;

    if (internalTagConsent) {
      const userConsent = internalTagConsent?.find(
        // @ts-expect-error Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
        (u) => u.user === userId || u.user === targetUser
      );
      if (!userConsent || userConsent?.status !== ConsentStatus.APPROVED)
        return;

      const { pdf_1, pdf_2 } = userConsent;

      if (pdf_1) window.open(pdf_1, "_blank");
      if (pdf_2) window.open(pdf_2, "_blank");
    }
  }

  useAsyncEffect(async () => {
    if (!mediaItem) return;

    const response = await GetTagDetails(
      source_type === "POST" ? mediaItem.post! : mediaItem._id!,
      source_type as "POST" | "VAULT"
    );

    setAllMedias(response.data.media);

    const internalTagConsent =
      response.data.media?.[0]?.consent?.internal_tag_consent;

    // @ts-expect-error Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
    const userConsent = internalTagConsent?.find((u) => u.user === userId);

    if (userConsent) {
      socketChannel?.channel?.editMessage({
        message_id:
          message._id || message.message_id || message?.messageId || "",
        data: {
          ...message,
          meta: {
            ...message.meta,
            requestAccept:
              userConsent.status === ConsentStatus.PENDING
                ? "sent"
                : userConsent.status === ConsentStatus.APPROVED,
          },
        },
      });
    }
  }, [
    mediaItem,
    userId,
    message?._id,
    message?.messageId,
    message?.message_id,
  ]);

  const handleConsent = async (action: "accept" | "reject") => {
    if (!media || !allMedias.length) return;

    const body = allMedias.map((m) => ({
      media_id: m._id,
      status:
        action === "accept" ? ConsentStatus.APPROVED : ConsentStatus.REJECTED,
    }));

    try {
      await ApprovalConsent(body);

      toast.success(`Consent ${action}ed successfully.`);

      socketChannel?.channel?.editMessage({
        message_id:
          message._id || message.message_id || message?.messageId || "",
        data: {
          ...message,
          meta: {
            ...message.meta,
            requestAccept: action === "accept",
          },
        },
      });
    } catch (error: any) {
      console.error("Error consenting tag:", error);
      toast.error(error.message || "Error consenting the tag.");
    }
  };

  const handleViewDetails = () => {
    ModalService.open("TAG_DETAILS", {
      entity_id:
        source_type === "POST" ? mediaItem?.consent?.post! : mediaItem?._id!,
      entity_type: source_type as "VAULT" | "POST",
      from:"Internal",
      user_type: usedAs,
      ...(usedAs === "sender" && { target_user: targetUser }),
    });
  };

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center p-2">
        <div className="fs-7 color-medium">Compliance tag</div>
        <div
          className={classNames("badge p-1", {
            "fs-10": window.innerWidth < 576,
          })}
          style={{ color, border: `1px solid ${color}`, backgroundColor }}
        >
          {text}
        </div>
      </div>

      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src={
          customMediaUrl ||
          getAssetUrl({
            media: mediaItem,
            defaultType: "background",
            poster: true,
          })
        }
        width={350}
        height={150}
        className="object-fit-cover mw-100 d-block rounded"
        alt="tag-approval"
      />

      <div className="d-flex justify-content-between align-items-center p-2">
        <div className="fw-bold fs-8 w-100 text-truncate">
          {mediaItem?.original_path}
        </div>
        <div className="color-medium fw-bold fs-8 w-100 text-end">
          {(source_type || "").charAt(0).toUpperCase() +
            (source_type || "").slice(1).toLowerCase()}
        </div>
      </div>

      <div className="fs-7 px-2">
        Uploaded:{" "}
        <DateFormatter
          dateString={message?.createdAt}
          formatType="MMM dd, yyyy hh:mm:ss"
        />
      </div>

      {usedAs === "receiver" && status === "sent" && (
        <div className="d-flex align-items-center justify-content-between p-2">
          <ActionButton
            type="outline"
            variant="danger"
            onClick={() => handleConsent("reject")}
          >
            Decline All
          </ActionButton>
          <ActionButton
            variant="success"
            onClick={() => handleConsent("accept")}
          >
            Accept All
          </ActionButton>
        </div>
      )}

      {status === true && (
        <div
          className="d-flex align-items-center justify-content-between w-fit m-2 p-2 gap-2 border rounded pointer"
          onClick={handleForms}
        >
          <span title="Get forms">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M17 21H6.99999C5.93162 21 4.92714 20.5839 4.17159 19.8284C3.41608 19.0729 3 18.0684 3 17V15.9999C3 15.4476 3.44769 14.9999 4.00001 14.9999C4.55232 14.9999 5.00001 15.4476 5.00001 15.9999V17C5.00001 17.5342 5.20807 18.0364 5.58576 18.4141C5.96355 18.7919 6.46577 19 6.99999 19H17C17.5342 19 18.0364 18.7919 18.4141 18.4141C18.7919 18.0363 19 17.5341 19 17V15.9999C19 15.4476 19.4477 14.9999 20 14.9999C20.5522 14.9999 21 15.4476 21 15.9999V17C21 18.0683 20.5839 19.0728 19.8284 19.8284C19.0728 20.5839 18.0683 21 17 21ZM12 16.9999C11.8617 16.9999 11.73 16.9719 11.6101 16.9211C11.4984 16.8739 11.3933 16.8053 11.3014 16.7155C11.3014 16.7155 11.3014 16.7155 11.3013 16.7155C11.3007 16.7148 11.3 16.7142 11.2993 16.7135C11.2992 16.7134 11.299 16.7131 11.2988 16.7129C11.2982 16.7124 11.2977 16.7119 11.2972 16.7114C11.2969 16.7111 11.2966 16.7108 11.2962 16.7104C11.2959 16.71 11.2954 16.7096 11.2951 16.7093C11.2944 16.7086 11.2936 16.7078 11.2929 16.7071L7.2929 12.7071C6.90239 12.3166 6.90239 11.6834 7.2929 11.2928C7.6834 10.9023 8.31662 10.9023 8.70713 11.2928L11 13.5857V4.00001C11 3.44769 11.4477 3 12 3C12.5523 3 13 3.44769 13 4.00001V13.5857L15.2929 11.2928C15.6833 10.9023 16.3166 10.9023 16.7071 11.2928C17.0976 11.6834 17.0976 12.3166 16.7071 12.7071L12.7071 16.7071C12.7064 16.7078 12.7056 16.7085 12.7049 16.7092C12.7045 16.7096 12.7041 16.71 12.7038 16.7103C12.7034 16.7107 12.7031 16.711 12.7028 16.7113C12.7023 16.7119 12.7017 16.7124 12.7012 16.7129C12.7011 16.713 12.7008 16.7133 12.7007 16.7134C12.7 16.7141 12.6994 16.7148 12.6987 16.7154C12.6987 16.7154 12.6987 16.7154 12.6986 16.7155C12.6876 16.7262 12.6765 16.7366 12.6651 16.7467C12.5814 16.8214 12.4881 16.8796 12.3895 16.9212C12.3891 16.9213 12.3888 16.9215 12.3885 16.9216C12.3881 16.9218 12.3878 16.922 12.3874 16.9221C12.2682 16.9723 12.1374 16.9999 12 16.9999Z"
                fill="currentColor"
              />
            </svg>
          </span>
          Get forms
        </div>
      )}

      <div className="p-2 underline fs-7 pointer" onClick={handleViewDetails}>
        View Details
      </div>
    </div>
  );
};

export default TagApproval;
