import { useState } from "react";

import { getAssetUrl } from "@/utils/assets";
import { type Media } from "@/types/media";

interface User {
  _id: string;
  name: string;
  username: string;
  media: Media[];
}

interface Promote {
  removeTag: Function;
  getShopItems: any;
}

export default function PromoteItem(promoteItemProps: Promote) {
  const [showItem] = useState<User[]>(promoteItemProps.getShopItems);

  return (
    <>
      {showItem.length != 0 && <hr className="invert w-100 h-25" />}
      <div className="d-flex gap-3 tag-collab-box flex-row">
        {showItem.length != 0 && (
          <div className="d-flex flex-column w-100">
            <p className="tag-collab-text fw-bold">Promoted Item</p>
            {showItem.map((item) => (
              <div
                className="d-flex w-100 align-items-center p-2 justify-content-between "
                key={item._id}
              >
                <div className="d-flex align-items-center">
                  <div className="rounded-pill profile-wrapper">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      width={100}
                      height={100}
                      className="img-fluid rounded-pill"
                      style={{ aspectRatio: "1", objectFit: "cover" }}
                      src={getAssetUrl({ media: item?.media?.[0] })}
                      alt="profile-img"
                    />
                  </div>
                  <div className="d-flex flex-column ps-2 pe-2">
                    <p className="user-name text-overflow-ellipsis tag-box-title fw-medium mb-0 fs-6">
                      {item.name}
                    </p>
                    <p className="user-name text-overflow-ellipsis fw-medium tag-box-title-small mb-0 fs-6">
                      {item.username}
                    </p>
                  </div>
                </div>
                <div>
                  <button
                    type="button"
                    className="btn btn-outline-danger btn-sm"
                    onClick={() => promoteItemProps.removeTag()}
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
}
