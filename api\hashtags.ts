import API from ".";

export interface TagObject {
  _id: string;
  total_count: number;
  post_count: number;
  category: string[];
}

interface CreateTag {
  hashtag: string;

  is_explicit: boolean;
}
export interface GetTagResponse {
  data: TagObject[];
}

export const GetTags = async (limit: number, is_explicit: string) => {
  return API.get(
    `${API.TAGS}/profile-tags?limit=${limit}&is_explicit=${is_explicit}`
  ) as Promise<any>;
};

export const GetMoreTags = async (limit: number, selected_tags: string) => {
  return API.get(
    `${API.TAGS}/profile-tags?limit=${limit}&selected_tags=${selected_tags}`
  ) as Promise<any>;
};

export const SearchTags = async (
  tag: string,
  explicit?: string,
  limit: number = 10,
  page: number = 1,
  tag_type?: string
) => {
  const params = new URLSearchParams();

  if (explicit) {
    params.append("explicit", explicit);
  }

  params.append("limit", limit.toString());
  params.append("page", page.toString());

  return API.get(
    `${API.TAGS}/${tag}?${params.toString()}${
      tag_type ? `&tag_type=${tag_type}` : ""
    }`
  ) as Promise<any>;
};

export const SearchTrendingTags = async (
  explicit?: string,
  limit: number = 10,
  page: number = 1,
  tag_type?: string
) => {
  const params = new URLSearchParams();

  if (explicit) {
    params.append("explicit", explicit);
  }

  params.append("limit", limit.toString());
  params.append("page", page.toString());

  return API.get(
    `${API.TAGS}/top?${params.toString()}${
      tag_type ? `&tag_type=${tag_type}` : ""
    }`
  ) as Promise<any>;
};

export const EditSearchTags = async (tag: string, explicit?: boolean) => {
  const url =
    explicit !== undefined
      ? `${API.TAGS}/${tag}?explicit=${explicit}&tag_type:Matchtags`
      : `${API.TAGS}/%23${tag}`;
  return API.get(url) as Promise<any>;
};

export const CreateHashTag = async (tag: CreateTag) => {
  return API.post(`${API.TAGS}?type=matchtag`, tag) as Promise<any>;
};

export const updateHashTag = async (body: any) => {
  return API.patch(`${API.USERS}/tags-updation`, body);
};

export const RequestAdminHashtag = async ({
  type,
  hashtag,
  category,
}: {
  type: string;
  hashtag: string;
  category?: string;
}) => {
  return API.post(`${API.TAGS}?type=${type}`, {
    hashtag: `#${hashtag}`,
    category,
  }) as Promise<any>;
};

export const SearchCategory = async ({
  category,
  explicit,
}: {
  category: string;
  explicit?: boolean | string;
}) => {
  let url = `${API.TAGS}/category/${category}`;

  if (explicit !== undefined || explicit !== "") {
    if (typeof explicit === "boolean" || typeof explicit === "string") {
      url += `?explicit=${explicit}`;
    }
  }

  return API.get(url) as Promise<any>;
};
