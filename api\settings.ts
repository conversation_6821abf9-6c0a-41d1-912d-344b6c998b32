import type { Media } from "@/types/media";
import type { Author } from "@/types/post";
import type { SocialMedia } from "@/types/profile";

import API from ".";

export interface UpdateAccountInfoBody {
  f_name?: string;
  l_name?: string;
  display_name?: string;
  // gender?: string;
  dob?: string;
  country_code?: string;
  phone_number?: string;
  country?: string;
  // location?: string;
  about?: string;
  username?: string;
  social_handles?: SocialMedia[];
  walkthrough?: boolean;
  action?: "revert" | "change";
}
export interface InstalledRelatedApp {
  id?: string;
  platform:
    | "chrome_web_store"
    | "play"
    | "chromeos_play"
    | "webapp"
    | "windows"
    | "f-droid"
    | "amazon";
  url?: string;
  version?: string;
}
interface UpdateAccountInfoResponse {
  data: any;
}

export const UpdateAccountInfo = async (body: UpdateAccountInfoBody) =>
  API.put(API.UPDATE_ACCOUNT_INFO, body) as Promise<UpdateAccountInfoResponse>;

export interface UserPrivacySettingBody {
  showActiveStatus: boolean;
  showSubscribedChannels: boolean;
  showMyFollowers: boolean;
  showMyFollowings: boolean;
  isAnonymous: boolean;
}

interface UserPrivacySettingResponse {
  data: any;
}

export const UserPrivacySetting = async (body: UserPrivacySettingBody) =>
  API.put(
    API.USER_PRIVACY_SETTING,
    body
  ) as Promise<UserPrivacySettingResponse>;

export interface specialOptionsBody {
  // type: string;
  voice: number;
  video: number;
  rating: number;
}

export interface PostSpecialOptionsBody {
  type: string;
  price: number | string;
}

interface specialOptionsBodyResponse {
  data: any;
}

export interface ChattingFeeBody {
  type?: string;
  price: number | string;
  free_until?: Date | string;
  is_active?: boolean;
}

export const specialOptions = async (body: specialOptionsBody) =>
  API.post(API.SPECIAL_OPTIONS, body) as Promise<specialOptionsBodyResponse>;

export const GetSpecialOptions = async () =>
  API.get(API.SPECIAL_OPTIONS) as Promise<specialOptionsBodyResponse>;

export const PutSpecialOptions = async (
  optionId: string,
  body: { price: number; is_active: boolean }
) =>
  API.put(
    API.SPECIAL_OPTIONS + `/` + optionId,
    body
  ) as Promise<specialOptionsBodyResponse>;

export interface ChangePasswordBody {
  password?: string;
  new_password: string;
}

export const ChangePasswordAPI = async (body: ChangePasswordBody) =>
  API.patch(`${API.USERS}/change-password`, body) as Promise<any>;

export const SetNewPassword = async (body: { new_password: string }) =>
  API.post(`${API.USERS}/change-password-social`, body) as Promise<any>;

export interface PrivacySettingBody {
  showActiveStatus: boolean;
  showMyFollowings: boolean;
  showMyFollowers: boolean;
  showSubscribedChannels: boolean;
  isAnonymous: boolean;
}

export const PrivacyManagementAPI = async (body: PrivacySettingBody) =>
  API.put(`${API.USERS}/privacy-setting`, body) as Promise<any>;

// Chatting Fee APIs

export const SetChattingFee = async (body: ChattingFeeBody) =>
  API.post(`${API.USERS}/chatting-fee`, body) as Promise<any>;

export const GetChattingFee = async () =>
  API.get(`${API.USERS}/chatting-fee`) as Promise<any>;

export const ChangeChattingFee = async (
  chatPackageId: number | string,
  body: ChattingFeeBody
) =>
  API.put(`${API.USERS}/chatting-fee/${chatPackageId}`, body) as Promise<any>;

export interface AutoMessagesBody {
  message: string;
  is_active: string;
}

export const GetAutoMessages = async () =>
  API.get(`${API.USERS}/auto-message`) as Promise<any>;

export const ChangeAutoMessage = async (optionType: string, body: FormData) =>
  API.put(`${API.USERS}/auto-message/${optionType}`, body) as Promise<any>;

export interface Data {
  _id?: string;
  user?: string;
  name: string;
  phone_number: string;
  street?: string;
  state?: string;
  city?: string;
  flat?: string;
  country_code?: string;
  zip_code?: string;
  country?: string;
  type: string;
  label: string;
  is_default?: boolean;
}
export interface GetAddressResponse {
  data: Data[];
}
export const GetAddress = async (type: string) =>
  API.get(`${API.USERS}/address?type=${type}`) as Promise<GetAddressResponse>;

export const AddAddress = async (body: Data) =>
  API.post(`${API.USERS}/address`, body) as Promise<any>;

export const EditAddress = async (id: string | undefined, body: Data | any) =>
  API.patch(`${API.USERS}/address/${id}`, body) as Promise<any>;

export const DeleteAddress = async (id: string | undefined) =>
  API.delete(`${API.USERS}/address/${id}`) as Promise<any>;

export interface GetPurchasedBody {
  _id: string;
  user: string;
  author: Author;
  vault_medias?: Media[];
  product_medias?: Media[];
  transaction_id?: any;
  meta: {
    type: "PremiumPost" | "ShopItem";
    title?: string;
    desc?: string;
    price?: number;
    category?: string;
  };
  created_at: string;
}
export interface GetPurchasedResponse {
  data: GetPurchasedBody[];
}

export const GetPurchased = async () =>
  API.get(`${API.USERS}/purchase-media`) as Promise<GetPurchasedResponse>;
interface VerifyOtpBody {
  otp: string;
  totp_url?: string;
}
export const Enable2FA = async () =>
  API.post(`${API.USERS}/2fa/topt`, {}) as Promise<any>;

export const Disable2FA = async (body: VerifyOtpBody) =>
  API.delete(`${API.USERS}/2fa/topt`, body) as Promise<any>;

export const Verify2FAOtp = async (body: VerifyOtpBody) =>
  API.post(`${API.USERS}/2fa/topt/verify`, body) as Promise<any>;

export const forgot2FAOtp = async (body: any) =>
  API.post(`${API.USERS}/totp-forgot-password`, body) as Promise<any>;

export const VerifyForget2FAOtp = async (body: any) =>
  API.post(`${API.USERS}/totp-verify-password`, body) as Promise<any>;
