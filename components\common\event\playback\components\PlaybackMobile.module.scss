.stream-video-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  video::-webkit-media-controls-overlay-play-button,
  video::-webkit-media-controls-current-time-display,
  video::-webkit-media-controls-volume-control-container {
    display: none;
  }

  .streamVideoFakeFS {
    transform-origin: center;
    height: 100vw !important;
    width: initial !important;
    transform: rotate(90deg) scaleX(-1);
  }

  .streamStatusFakeFS {
    transform-origin: top left;
    transform: rotate(90deg);
    left: 100% !important;
  }

  .controlsDivFakeFS {
    transform-origin: top right;
    transform: rotate(90deg);
    top: 100% !important;
  }

  .fallbackImgFakeFS {
    transform-origin: center;
    transform: translate(-50%, -50%) rotate(90deg) !important;
  }

  .tipHighlightFakeFS {
    top: 0 !important;
    transform-origin: top left;
    transform: rotate(90deg) translateY(-100%);
  }
}

.obscure {
  filter: blur(8px) brightness(0.5);
}
