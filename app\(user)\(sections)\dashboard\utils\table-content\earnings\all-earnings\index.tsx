"use client";

import Image from "next/image";
import Link from "next/link";

import ActionButton from "@/components/common/action-button";
import { ModalService } from "@/components/modals";
import {
  ServicesColor,
  ServicesImg,
  ServicesTypeLabel,
} from "@/types/services";
import { subscriptionTypeLabels } from "@/types/user";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";
import { formatDate } from "@/utils/number";

interface dashboardTable {
  accepted?: boolean;
  completed?: boolean;
  declined?: boolean;
  rating?: boolean;
  videoCall?: boolean;
  empty?: boolean;
  userId?: string;
  tableValues: any[];
  type: any;
}

const premiumType: any = {
  PremiumPost: "Pay-To-View",
  PremiumStory: "Pay-To-View",
};

const renderTableHeader = (type: string) => (
  <thead>
    <tr className="fs-7">
      <th scope="col" className="bg-cream rounded-start-3">
        Payment ID
      </th>
      <th scope="col" className="bg-cream">
        {type === "subscription"
          ? "Subscriber"
          : type === "shop_items"
          ? "Buyer"
          : "User"}
      </th>
      {type !== "subscription" && type !== "shop_items" && (
        <th scope="col" className="bg-cream">
          {type === "special_options" ? "Service Type" : "Type"}
        </th>
      )}
      {type === "special_options" && (
        <>
          <th scope="col" className="bg-cream">
            Service
          </th>
          <th scope="col" className="bg-cream">
            Note
          </th>
        </>
      )}
      {type === "shop_items" && (
        <th scope="col" className="bg-cream">
          Item
        </th>
      )}
      {type === "subscription" && (
        <>
          <th scope="col" className="bg-cream">
            Channel/Collab
          </th>
          <th scope="col" className="bg-cream">
            Duration
          </th>
        </>
      )}
      <th scope="col" className="bg-cream">
        Amount
      </th>
      <th scope="col" className="bg-cream">
        Net Amount
      </th>
      {type === "shop_items" && <th scope="col" className="bg-cream"></th>}
    </tr>
  </thead>
);

const renderTableRow = (earnings: any, type: string, userId: string) => (
  <tr className="fs-7 table-row" key={earnings._id}>
    <th
      scope="row"
      onClick={() =>
        ModalService.open("TRANSACTION_INFO", {
          OpenType: "Dashbaord",
          transaction: earnings._id,
        })
      }
      className="py-2 bg-body pointer"
    >
      <span className="id-text">PY{earnings._id.slice(0, 6)}</span>
    </th>
    <td className="py-2 bg-body">
      <Link
        href={`/${earnings?.from?.user_type.toLowerCase()}/${
          earnings?.from?.username
        }`}
        className="d-flex gap-2 align-items-center"
      >
        <img
          src={getAssetUrl({
            media: earnings?.from?.avatar && earnings?.from?.avatar[0],
            defaultType: "avatar",
            variation: "thumb",
          })}
          className="rounded-5"
          width={35}
          height={35}
          alt="dashboard-user"
        />
        <div className="d-flex flex-column align-items-start">
          <p className="fs-7 mb-0 text-overflow-ellipsis color-dark">
            {earnings?.from?.display_name}
          </p>
          <p className="fs-8 mb-0 color-medium">
            {formatDate(earnings?.created_at)}
          </p>
        </div>
      </Link>
    </td>

    {type === "shop_items" && (
      <td className="py-2 bg-body">
        <Link href={`/shop/${earnings?._id}/track`} className="d-flex gap-3">
          <img
            src={getAssetUrl({ media: earnings?.order?.product?.media[0] })}
            width={56}
            height={48}
            className="object-fit-cover rounded-3"
            alt=""
          />
          <div>
            <p className="text-overflow-ellipsis color-dark m-0">
              {earnings?.order?.product?.name}
            </p>
            <p
              className={`fs-7 mb-0 color-medium px-2 rounded ${earnings?.order?.status}`}
            >
              {earnings?.order?.status}
            </p>
          </div>
        </Link>
      </td>
    )}

    {type === "subscription" && (
      <td className="py-2 bg-body">
        <Link
          href={
            earnings?.category === "ChannelSubscription"
              ? `/channel/${earnings?.channel?.channel_tagname}`
              : `/collab/${earnings?.group?.channel_tagname}`
          }
          className="d-flex gap-2 align-items-center"
        >
          <img
            src={getAssetUrl({
              media:
                earnings?.category === "ChannelSubscription"
                  ? earnings?.channel?.avatar && earnings?.channel?.avatar[0]
                  : earnings.group?.background &&
                    earnings?.group?.background[0],
              defaultType: "avatar",
            })}
            className="rounded-5"
            width={35}
            height={35}
            alt="dashboard-user"
          />
          <p className="m-0 fw-medium">
            {earnings?.category === "ChannelSubscription"
              ? earnings?.channel?.name
              : earnings?.group?.name}
            <span
              className={`fw-medium px-2 ms-1 py-1 text-white rounded-2 fs-8 ${
                earnings?.category === "ChannelSubscription"
                  ? "bg-green2"
                  : "bg-blue"
              }`}
            >
              {earnings?.category === "ChannelSubscription"
                ? "Channel"
                : "Collab"}
            </span>
          </p>
        </Link>
      </td>
    )}

    {type !== "shop_items" && (
      <td className="py-2 bg-body">
        <div className="fs-7 mb-0 text-overflow-ellipsis color-dark">
          {earnings?.request_id ? (
            <div className="d-flex align-items-center gap-2">
              <div
                className="p-1 rounded-3"
                style={{
                  backgroundColor: ServicesColor[earnings?.request_id?.type],
                  width: 32,
                  height: 32,
                }}
              >
                <Image
                  src={ServicesImg[earnings?.request_id.type]}
                  width={24}
                  height={24}
                  className={
                    "object-fit-cover rounded-2 m-auto d-block img-fluid"
                  }
                  alt=""
                />
              </div>
              <p className="m-0">
                {ServicesTypeLabel[earnings?.request_id?.type]}
              </p>
            </div>
          ) : type === "subscription" ? (
            subscriptionTypeLabels[earnings?.package_type]
          ) : (
            `${premiumType[earnings?.category]}${
              earnings.post && earnings.post.collaborators.length !== 0
                ? " (Collab Post)"
                : ""
            }` || earnings?.category
          )}
        </div>
        {earnings?.category === "PremiumPost" && (
          <Link href={`${process.env.client}/post/${earnings.post._id}`}>
            {process.env.client}/post/{earnings.post._id}
          </Link>
        )}
      </td>
    )}
    {type === "special_options" && (
      <>
        <td className="py-2 bg-body">
          <div className="d-flex align-items-center gap-3">
            <div
              style={{
                backgroundColor: earnings?.request_id?.service?.avatar?.length
                  ? "transparent"
                  : ServicesColor[earnings?.request_id?.service?.type],
                padding: earnings?.request_id?.service?.avatar?.length
                  ? ""
                  : "0.25rem",
                borderRadius: "8px",
              }}
            >
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={
                  earnings?.request_id?.service?.avatar?.length
                    ? getAssetUrl({
                        media: earnings?.request_id?.service?.avatar[0],
                      })
                    : ServicesImg[earnings?.request_id?.service?.type]
                }
                width={earnings?.request_id?.service?.avatar?.length ? 32 : 24}
                height={earnings?.request_id?.service?.avatar?.length ? 32 : 24}
                alt=""
              />
            </div>
            <div>
              <p className="m-0">{earnings?.request_id?.service?.name}</p>
            </div>
          </div>
        </td>
        <td className="py-2 bg-body">
          {earnings?.request_id?.type === "VIDEO" ||
          earnings.request_id?.type === "VOICE" ? (
            <p className="m-0">
              {earnings?.request_id?.extra_data?.duration / 60} Minute
            </p>
          ) : earnings?.request_id?.service?.includes ? (
            <>{earnings?.request_id?.service?.includes}</>
          ) : (
            <p className="m-0">-</p>
          )}
        </td>
      </>
    )}

    <td className="py-2 bg-body">
      <p className="date mb-0 color-dark">
        {formatCurrency(
          earnings?.to.find((res: any) => res.recipient === userId)
            ?.total_amount ||
            earnings?.total_amount ||
            0
        )}
      </p>
    </td>

    <td className="py-2 bg-body">
      <p className="date mb-0 color-dark">
        {formatCurrency(
          earnings?.to.find((res: any) => res.recipient === userId)?.amount ||
            earnings?.amount ||
            0
        )}
      </p>
    </td>

    {type === "shop_items" && (
      <td className="py-2 bg-body">
        <Link href={`/shop/${earnings?._id}/track`}>
          <ActionButton>Order details</ActionButton>
        </Link>
      </td>
    )}
  </tr>
);

export default function AllEarningsTable(tableProps: dashboardTable) {
  return (
    <>
      <table className="table">
        {renderTableHeader(tableProps.type)}
        {!tableProps.empty && (
          <tbody className="bg-body border-0">
            {tableProps.tableValues.map((earnings) =>
              renderTableRow(earnings, tableProps.type, tableProps.userId!)
            )}
          </tbody>
        )}
      </table>
    </>
  );
}
