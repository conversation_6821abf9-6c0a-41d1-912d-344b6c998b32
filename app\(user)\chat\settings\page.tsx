"use client";

import classNames from "classnames";
import { nanoid } from "nanoid";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import MassMessageHistory from "@/components/settings/creator-settings/mass-message-history";
import ScheduledMassMessage from "@/components/settings/creator-settings/scheduled-mass-message";
import AutoMessages from "@/components/settings/creator-settings/sub-components/auto-messages/page";
import MassMessaging from "@/components/settings/creator-settings/sub-components/mass-message";
import { configActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";
import "./index.scss";

const items = {
  _id: nanoid(),
  type: "Chat Settings",
  menuItems: [
    {
      list: {
        icon: "/images/chat/settings/auto-message.svg",
        title: "Auto Messages",
      },
      comp: <AutoMessages />,
      partition: "General",
    },
    {
      list: {
        icon: "/images/chat/settings/chat-fee.svg",
        title: "Setup chat Fee",
      },
      comp: <></>,
      partition: "General",
    },
    {
      list: {
        icon: "/images/chat/settings/mass-message.svg",
        title: "New Mass",
      },
      comp: <MassMessaging />,
      partition: "Mass Message",
    },
    {
      list: {
        icon: "/images/chat/settings/schedule.svg",
        title: "Scheduled Message",
      },
      comp: <ScheduledMassMessage />,
      partition: "Mass Message",
    },
    {
      list: {
        icon: "/images/chat/settings/history.svg",
        title: "Messaging History",
      },
      comp: <MassMessageHistory />,
      partition: "Mass Message",
    },
  ],
};

const partitions = [...new Set(items.menuItems.map((item) => item.partition))];

export default function ChatSettings() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [activeItem, setActiveElement] = useState("");

  const activeComponent = items.menuItems.find(
    (item) => item.list.title === activeItem
  )?.comp;

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (window.innerWidth > 768) {
      const firstMenuItem = items.menuItems[0]?.list.title;
      setActiveElement(firstMenuItem);
    }
  }, []);

  useEffect(() => {
    const type = searchParams.get("type");

    if (type === "mass") {
      setActiveElement("New Mass");
    } else if (type === "history") {
      setActiveElement("Messaging History");
    } else if (type === "scheduled") {
      setActiveElement("Scheduled Message");
    } else if (window.innerWidth > 768) {
      const firstMenuItem = items.menuItems[0]?.list.title;
      setActiveElement(firstMenuItem);
    }
  }, [searchParams.get("type")]);

  useEffect(() => {
    dispatch(configActions.toggleShowCreateWidget());

    return () => {
      dispatch(configActions.toggleShowCreateWidget());
    };
  }, []);

  return (
    <div
      className={classNames("chat-settings-container container mt-md-4 p-0")}
    >
      <div
        className={classNames("menu-container rounded h-fit", {
          "mobile-hidden d-none d-md-block": activeItem,
        })}
      >
        <div
          className={classNames(
            "menu-header p-3 pb-0 d-flex align-items-center gap-2 position-relative"
          )}
        >
          <div
            className={classNames("ms-2 start-0 pointer", {
              "position-absolute": window.innerWidth < 768,
            })}
            style={{
              top: 16,
            }}
            onClick={() => {
              router.replace("/chat");
            }}
          >
            <BackButton />
          </div>
          <div
            className={classNames({
              "text-center w-100": window.innerWidth < 768,
            })}
          >
            {items.type}
          </div>
        </div>
        <hr />
        {partitions.map((p) => {
          const filteredItems = items.menuItems.filter(
            (item) => item.partition === p
          );

          if (
            p.toLowerCase() === "mass message" &&
            process.env.environment === "live"
          )
            return;

          return (
            <div className="partition px-3 pb-3" key={p}>
              <div className={classNames("color-medium")}>{p}</div>
              <div className="menu-items">
                {filteredItems.map((item) => (
                  <div
                    key={item.list.title}
                    className="menu-item"
                    onClick={() => {
                      if (item.list.title === "Setup chat Fee") {
                        router.push("/services");
                        return;
                      }

                      setActiveElement(item.list.title);
                    }}
                  >
                    <Image
                      src={item.list.icon}
                      alt=""
                      width={24}
                      height={24}
                      className={classNames({
                        grayscale: item.list.title !== activeItem,
                      })}
                    />
                    <span
                      style={{
                        color: item.list.title !== activeItem ? "" : "#ac1991",
                      }}
                    >
                      {item.list.title}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      <div
        className={classNames("content-container py-0", {
          "mobile-visible": activeItem,
        })}
      >
        {activeItem && (
          <div className="content">
            <div className="position-relative d-md-none bg-body">
              <div
                className="position-absolute ms-2 start-0 pointer"
                style={{
                  top: 16,
                }}
                onClick={() => setActiveElement("")}
              >
                <BackButton />
              </div>
              <div className="w-100 text-center py-3 fw-semibold fs-5">
                {activeItem}
              </div>
              <hr className="w-100 m-0" />
            </div>
            <div className="content-section">{activeComponent}</div>
          </div>
        )}
      </div>
    </div>
  );
}

export const BackButton = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M28.3301 17.1467C28.61 17.1063 28.8657 16.9677 29.05 16.7565C29.2344 16.5453 29.335 16.2756 29.3334 15.9972C29.3345 15.846 29.3054 15.6961 29.2476 15.5561C29.1899 15.416 29.1047 15.2885 28.9969 15.1808C28.8891 15.0732 28.7609 14.9875 28.6194 14.9286C28.478 14.8698 28.3261 14.8389 28.1726 14.8378H6.64107L12.8521 8.64617L12.9656 8.51627C13.1318 8.29229 13.2121 8.01759 13.1921 7.74092C13.1721 7.46425 13.0531 7.2035 12.8564 7.00494C12.7495 6.89728 12.6219 6.81167 12.4811 6.75314C12.3402 6.69461 12.1889 6.66432 12.036 6.66406C11.8831 6.6638 11.7317 6.69357 11.5906 6.75162C11.4496 6.80968 11.3217 6.89485 11.2145 7.00215L3.02524 15.1591C2.9132 15.267 2.82387 15.3955 2.76237 15.5374C2.70086 15.6793 2.66839 15.8317 2.66681 15.986C2.66523 16.1403 2.69457 16.2933 2.75315 16.4364C2.81173 16.5795 2.8984 16.7098 3.00821 16.8199L11.2131 24.9936L11.345 25.1053C11.5684 25.2697 11.8448 25.3486 12.1229 25.3273C12.4009 25.306 12.6616 25.1859 12.8564 24.9894C13.0741 24.7697 13.1957 24.4748 13.1949 24.1679C13.1941 23.861 13.071 23.5667 12.8521 23.3481L6.64107 17.1579H28.174L28.3301 17.1467Z"
      fill="#131416"
    />
  </svg>
);
