import { useEffect, useState } from "react";
import { formatDistance } from "date-fns";

import { useAppSelector } from "@/redux-store/hooks";
import { Table } from "@/components/common/event/table";
import { User, UserActions } from "@/components/common/event/user";
import { formatCurrency } from "@/utils/formatter";
import { formatDate } from "@/utils/number";

type TipTimelineTableCols =
  | "time"
  | "user"
  | "amount"
  | "actions"
  | "netamount";

export function TipTable({ data }: { data: any[] }) {
  const tips = useAppSelector((state) => state.liveEvent.tips);

  const tipTimelineColumns: { name: string; key: TipTimelineTableCols }[] = [
    { name: "Sent at", key: "time" },
    { name: "User", key: "user" },
    { name: "Amount", key: "amount" },
    { name: "Net Amount", key: "netamount" },
    { name: "Actions", key: "actions" },
  ];
  const [tipTimelineRows, setTipTimelineRows] = useState<
    Record<TipTimelineTableCols, any>[]
  >([]);

  useEffect(() => {
    const r =
      data &&
      data.map((tip) => {
        const t = {
          time: formatDate(tip.created_at),
          user: (
            <User
              display_name={tip.from.display_name}
              username={tip.from.username}
              avatar={tip.from.avatar}
            />
          ),
          amount: formatCurrency(tip.total_amount),
          netamount: formatCurrency(tip.amount),
          actions: (
            <UserActions
              username={tip.from.username}
              usertype={tip.from.user_type.toLocaleLowerCase()}
            />
          ),
        };
        return t;
      });
    setTipTimelineRows(r);
  }, [tips, data]);

  return <Table columns={tipTimelineColumns} rows={tipTimelineRows} />;
}
