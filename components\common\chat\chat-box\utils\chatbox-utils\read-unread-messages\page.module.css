.callContainer {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.callIconContainer {
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mediaContainer {
  width: 100%;
  height: 100%;
  padding: 1rem;
}

.mediaItem {
  max-width: 100%;
  display: block;
  border-radius: 0.5rem;
  cursor: pointer;
  border: 2px solid #ac1991;
  object-fit: cover;
}

.textWhite {
  color: white;
}

.pointer {
  cursor: pointer;
}

.rounded {
  border-radius: 0.5rem;
}
