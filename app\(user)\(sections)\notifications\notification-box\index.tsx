import Link from "next/link";
import { useRouter } from "next/navigation";
import { memo, useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import Swal from "sweetalert2";

import { RespondRequest, RespondTagRequest } from "@/api/group";
import { GetNotification, RequestNotification } from "@/api/requests";
import NotificationMedia from "@/components/NotificationMedia";
import ActionButton from "@/components/common/action-button";
import Shimmer from "@/components/common/shimmer";
import { ModalService } from "@/components/modals";
import { useIsMount } from "@/hooks/useIsMount";
import { notificationActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { store } from "@/redux-store/store";
import type { Media } from "@/types/media";
import type {
  Author,
  Notification,
  OfferInvitationNotification,
  OtherData,
  Thumbnail,
} from "@/types/notification";
import {
  getDeletionReasonMessage,
  NotificationSubType,
  NotificationType,
} from "@/types/notification";
import type { UserRole } from "@/types/user";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";
import { convertTimestampToTimeDifference } from "@/utils/number";
import "../index.scss";

// following function will display notification message according to notification type
interface NotificationMessageParams {
  type: NotificationType;
  subType: NotificationSubType;
  groupName?: string;
  other_data?: OtherData;
  author?: Author;
  post?: string;
  currentPlan?: any;
  offer?: OfferInvitationNotification;
  userRole?: UserRole;
  eventId?: string;
  channelId?: string;
  storyId: string;
  channel_details?: Thumbnail & {
    name: string;
    channel_tagname: string;
  };
  product?: {
    _id: string;
    name: string;
  };
  friendly_name?: string;
  user_id?: string;
  agencyName: string;
  notificationId: string;
}

/**
 * This function takes a notification type and sub type and returns a string which represents the notification message.
 * @param {NotificationMessageParams} params - An object containing the notification type, sub type and any other relevant data.
 * @returns {string} A string representing the notification message.
 */
export const getNotificationMessage = (params: NotificationMessageParams) => {
  const {
    type,
    subType,
    groupName,
    other_data,
    author,
    post,
    currentPlan,
    userRole,
    eventId,
    offer,
    channelId,
    storyId,
    channel_details,
    product,
    user_id,
    friendly_name,
    agencyName,
    notificationId,
  } = params;

  const userId = user_id || store.getState().user.id;

  //TODO: Have a link wrapper which can have links accomodated so this same function can be used for FCM as well
  switch (type) {
    case NotificationType.SUBSCRIPTION:
      switch (subType) {
        case NotificationSubType["CHANNEL-SUBSCRIPTION"]:
          return (
            <>
              Purchased your{" "}
              <Link href={`/channel/${channel_details?.channel_tagname}`}>
                {channel_details?.name}
              </Link>{" "}
              channel subscription.
            </>
          );
        case NotificationSubType["GROUP-SUBSCRIPTION"]:
          return (
            <>
              Purchased your <Link href={`/collab/}`}>{groupName}</Link> collab
              subscription.
            </>
          );
        default:
          return "Purchased your subscription.";
      }

    case NotificationType.PAYMENT:
      switch (subType) {
        case NotificationSubType["TICKET-PURCHASE"]:
          return (
            <>
              Purchased your event ticket.{" "}
              <Link
                href={`events/${eventId}/analysis`}
                className="pointer underline"
              >
                Event Analysis
              </Link>
            </>
          );
        case NotificationSubType["POST-TIP"]:
          return (
            <Link href={`/post/${post}`}>
              Sent{" "}
              {other_data?.amount ? formatCurrency(other_data?.amount) : ""} tip
              on your{" "}
              {other_data?.type_of_post ? other_data?.type_of_post : "post"}.
            </Link>
          );
        case NotificationSubType["CHAT-TIP"]:
          return (
            <Link href={`/chat?user=${author?._id}`}>
              Sent{" "}
              {other_data?.amount ? formatCurrency(other_data?.amount) : ""} tip
              from chat.
            </Link>
          );
        case NotificationSubType["STORY-TIP"]:
          return `Sent ${
            other_data?.amount ? formatCurrency(other_data?.amount) : ""
          } tip on your story.`;
        case NotificationSubType["CHANNEL-TIP"]:
          return (
            <>
              Sent{" "}
              {other_data?.amount ? formatCurrency(other_data?.amount) : ""} tip
              on your channel{" "}
              <Link href={`/channel/${other_data?.channel_tagname}`}>
                {other_data?.channel_name}
              </Link>
            </>
          );
        case NotificationSubType["PROFILE-TIP"]:
          return `Sent ${
            other_data?.amount ? formatCurrency(other_data?.amount) : ""
          } tip on your profile.`;
        case NotificationSubType["PREMIUM-POST-PURCHASE"]:
          return (
            <Link href={`/post/${post}`}>
              Purchased your Pay-To-View{" "}
              {other_data?.type_of_post ? other_data?.type_of_post : "post"}.
            </Link>
          );
        case NotificationSubType["PREMIUM-STORY-PURCHASE"]:
          return "Purchased your Pay-To-View story.";
        case NotificationSubType["AUDIO-MEDIA-PURCHASE"]:
          return "Purchasd your audio chat media.";
        case NotificationSubType["MEDIA-PURCHASE"]:
          return "Purchased your chat media.";
        default:
          return "Paid you money";
      }

    case NotificationType.REQUEST:
      switch (subType) {
        case NotificationSubType["TAGGED_REQUEST"]:
          return <>The creator has requested to tag you in a post/vault</>;
        case NotificationSubType["TAGGED_REQUEST_REJECTED"]:
          return <>The creator has rejected your tag request.</>;
        case NotificationSubType["TAGGED_REQUEST_ACCEPTED"]:
          return <>The creator has accepted your tag request</>;
        case NotificationSubType["TAG_REVOKED"]:
          return (
            <>
              The tag request has been revoked by the creator. If you want to
              tag them again in a post or vault, you&apos;ll need to send a new
              tag request.
            </>
          );
        case NotificationSubType["GROUP-MEMBERSHIP"]:
          return `Invited you to join "${groupName}" collab.`;
        case NotificationSubType["COLLABORATION-REQUEST"]:
          return `Invited you for a collaborative post, with a ${other_data?.percentage}% share of your fee.`;
        case NotificationSubType["RATING"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Sent you a{" "}
              <Link href={`/chat?user=${author?._id}`}>
                <u>rating</u>
              </Link>{" "}
              request.
            </Link>
          );
        case NotificationSubType["VIDEO"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Sent you a{" "}
              <Link href={`/chat?user=${author?._id}`}>
                <u>video call</u>
              </Link>{" "}
              request.
            </Link>
          );
        case NotificationSubType["VOICE"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Sent you a{" "}
              <Link href={`/chat?user=${author?._id}`}>
                <u>voice call</u>
              </Link>{" "}
              request.
            </Link>
          );
        case NotificationSubType["REQUEST-ACCEPTED"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Accepted your{" "}
              <span className="text-lowercase underline">
                {other_data?.request_type}
              </span>{" "}
              request.
            </Link>
          );
        case NotificationSubType["REQUEST-REJECTED"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Rejected your{" "}
              <span className="text-lowercase underline">
                {other_data?.request_type}
              </span>{" "}
              request.
            </Link>
          );
        case NotificationSubType["PROMOTIONAL-REQUEST-ACCEPTED"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Accepted your{" "}
              <span className="text-lowercase underline">
                {other_data?.request_type}
              </span>{" "}
              promotion request.
            </Link>
          );
        case NotificationSubType["PROMOTIONAL-REQUEST-REJECTED"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Rejected your{" "}
              <span className="text-lowercase underline">
                {other_data?.request_type}
              </span>{" "}
              promotion request.
            </Link>
          );
        case NotificationSubType["REQUEST-PARTIALLY-ACCEPTED"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Partially accepted your <u>custom request</u>
            </Link>
          );
        case NotificationSubType["COLLABORATION-REQUEST-ACCEPT"]:
          return (
            <Link href={`/post/${post}`}>
              Accepted your <u>collab request</u>
            </Link>
          );
        case NotificationSubType["COLLABORATION-REQUEST-REJECT"]:
          return (
            <Link href={`/post/${post}`}>
              Rejected your <u>collab request</u>
            </Link>
          );
        case NotificationSubType["COLLABORATION-POST-DELETE"]:
          return "The collaborative post where you were mentioned has been deleted by the author";
        case NotificationSubType["COLLABORATION-REQUEST-ACCEPT-PARTIAL"]:
          return (
            <>
              The collaborative post where you were mentioned accepted by{" "}
              {other_data?.display_name}.
            </>
          );
        case NotificationSubType["GROUP-REQUEST-REJECTED"]:
          return (
            <>
              The creator has rejected a collab request for{" "}
              <strong>{groupName}</strong>.
            </>
          );
        case NotificationSubType["JOIN_AGENCY_REQUEST"]:
        case NotificationSubType["JOIN_AGENCY_INVITE"]:
          return (
            <>
              Sent you a request to join {agencyName} agency profile.{" "}
              <span
                onClick={() =>
                  ModalService.open("AGENCY_COLLAB", {
                    agency_id: notificationId,
                  })
                }
                className="underline pointer"
              >
                View details
              </span>
            </>
          );
        case NotificationSubType["CANCEL_AGENCY_COLLABORATION"]:
          return (
            <>
              You have requested to cancel your collaboration with {agencyName}{" "}
              agency
            </>
          );

        default:
          return "New Request";
      }

    case NotificationType.MATCH:
      switch (subType) {
        case NotificationSubType["LIKED-MATCH-PROFILE"]:
          return (
            <>
              {`${
                userRole === "user"
                  ? currentPlan?.type !== "Free"
                    ? author?.display_name
                    : "Someone"
                  : author?.display_name
              } liked your match profile! `}
              <Link
                className="pointer underline"
                href={"/matchmaker/match?type=like"}
              >
                Let’s check!
              </Link>
            </>
          );
        case NotificationSubType["SUPER-LIKED-MATCH-PROFILE"]:
          return (
            <>
              {`${
                userRole === "user"
                  ? currentPlan?.type !== "Free"
                    ? author?.display_name
                    : "Someone"
                  : author?.display_name
              } superliked your match profile! `}
              <Link
                className="pointer underline"
                href={"/matchmaker/match?type=like"}
              >
                Let’s check!
              </Link>
            </>
          );
        case NotificationSubType["POKE"]:
          return `${
            userRole === "user"
              ? currentPlan?.type !== "Free"
                ? author?.display_name
                : "Someone"
              : author?.display_name
          } poked you!`;
        case NotificationSubType["NEW-PROFILE-MATCH"]:
          return (
            <>
              {"You have a new match. "}
              <Link className="pointer underline" href={"/matchmaker/match"}>
                Let’s check!
              </Link>
            </>
          );

        default:
          return "New Match Notification";
      }

    case NotificationType.PROMOTION:
      switch (subType) {
        case NotificationSubType["RATING"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Sent you a <u>rating</u> request.
            </Link>
          );
        case NotificationSubType["VIDEO"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Sent you a <u>video call</u> request.
            </Link>
          );
        case NotificationSubType["VOICE"]:
          return (
            <Link href={`${author ? `/chat?user=${author?._id}` : "/chat"}`}>
              Sent you a <u>voice call</u> request.
            </Link>
          );

        default:
          return "New Request";
      }

    case NotificationType.INTERACTION:
      switch (subType) {
        case NotificationSubType["POST-LIKE"]:
          return (
            <Link href={`/post/${post}`}>
              Liked your{" "}
              {other_data?.type_of_post ? other_data?.type_of_post : "post"}.
            </Link>
          );
        case NotificationSubType["POST-COMMENT"]:
          return (
            <Link href={`/post/${post}`}>
              Commented on your{" "}
              {other_data?.type_of_post ? other_data?.type_of_post : "post"}.
            </Link>
          );
        case NotificationSubType["FOLLOW"]:
          return (
            <>
              Started following you.{" "}
              <Link
                href={`/${author?.user_type.toLowerCase()}/${author?.username}`}
                className="underline pointer"
              >
                View Profile.
              </Link>
            </>
          );
        case NotificationSubType["STORY-LIKE"]:
          return (
            <Link href={`/fresh?storyId=${storyId}&userId=${userId}`}>
              Liked your story.
            </Link>
          );
        case NotificationSubType["COMMENT-LIKE"]:
          return <Link href={`/post/${post}`}>Liked your comment.</Link>;
        case NotificationSubType["COMMENT-REPLY"]:
          return <Link href={`/post/${post}`}>Replied to your comment.</Link>;
        case NotificationSubType["COMMENT-REPLY-LIKE"]:
          return <Link href={`/post/${post}`}>Liked your reply.</Link>;
        case NotificationSubType["POST-TAGGING"]:
          return (
            <>
              Tagged you in a post.{" "}
              <Link href={`/post/${post}`}>View Details</Link>
            </>
          );
        case NotificationSubType["CONTENT-FROM-MY-SUBSCRIPTION"]:
          return (
            <Link href={`/post/${post}`}>
              {author?.display_name} has posted something on your subscription!
            </Link>
          );
        case NotificationSubType["CONTENT-FROM-MY-FOLLOWING"]:
          return (
            <Link href={`/post/${post}`}>
              {author?.display_name} has posted something new!
            </Link>
          );
        default:
          return "New Notification";
      }

    case NotificationType.SYSTEM:
      switch (subType) {
        case NotificationSubType["MEDIA_DELETION"]:
          return (
            <>
              {`${other_data?.type === "Event" ? other_data?.type : ""} ${
                other_data?.entity !== "Profile" && other_data?.name
                  ? `${other_data.name}'s`
                  : ""
              } ${
                other_data?.media_type
              } was removed due to a violation of content moderation policy${
                other_data?.flags?.length
                  ? ", with the specific reason being: "
                  : ". "
              }
              `}
              {other_data?.flags?.length && (
                <span className="fw-bold">
                  {other_data?.flags?.join(", ")}.{" "}
                </span>
              )}
              <Link
                className="pointer underline text-capitalize"
                href="/articles/content-moderation-and-protection-policy"
                target="_blank"
              >
                Read policy.
              </Link>
            </>
          );

        case NotificationSubType["MODERATION_FAILED"]:
          return (
            <>
              {`Media was removed due to a violation of content moderation policy${
                other_data?.flags?.length
                  ? ", with the specific reason being: "
                  : ". "
              }`}
              {other_data?.flags?.length ? (
                <span className="fw-semibold">
                  {other_data?.flags?.join(", ")}.{" "}
                </span>
              ) : null}
              <Link
                className="pointer underline text-capitalize"
                href="/articles/content-moderation-and-protection-policy"
                target="_blank"
              >
                Read policy.
              </Link>
            </>
          );
        case NotificationSubType["AGE_VERIFICATION_FAILED"]:
          return "Age verification failed. Please review your information and try again.";
        case NotificationSubType["FULL_KYC_FAILED"]:
          return "Full KYC verification failed. Please review your information and try again.";
        case NotificationSubType["AGE_VERIFICATION_COMPLETED"]:
          return "Age verification successful. You can now fully access and use the application.";
        case NotificationSubType["FULL_KYC_COMPLETED"]:
          return "Full KYC verification successful. You can now fully access and use the application.";
        case NotificationSubType["GROUP-ACTIVATED"]:
          return <>Your collab {groupName} is now activated.</>;

        case NotificationSubType["RELEASE_TAG_DECLINATION"]:
          return (
            <>
              The user with friendly name {friendly_name} has declined your
              request for tagging in any of your posts.
            </>
          );

        case NotificationSubType["RELEASE_TAG_CONFIRMATION"]:
          return (
            <>
              The Know-Your-Customer (KYC) verification of the external user
              with friendly name {friendly_name}
              has been successfully completed. You can now tag this user in your
              post.
            </>
          );

        case NotificationSubType["MISSING_TAG_WARNING"]:
          return (
            <>
              The tags are missing in this post. Please add them.{" "}
              <Link href={`/post/${post}`} className="underline">
                View Details
              </Link>
            </>
          );

        case NotificationSubType["RELEASE_TAG_KYC_FAILURE"]:
          return (
            <>
              The Know-Your-Customer (KYC) verification of the external user
              with friendly name {friendly_name}
              has been declined. Please review the information provided and
              resubmit the request.
            </>
          );

        case NotificationSubType["USER_EXISTS_ON_PLATFORM_ERROR"]:
          return (
            <>
              The user with friendly name {friendly_name} already exists on the
              platform. To tag the user, you need to add them to as a
              collaborator.
            </>
          );

        case NotificationSubType["GROUP-DELETION"]:
          return (
            <>
              {author?.display_name} has requested to leave {groupName} collab.
            </>
          );
        case NotificationSubType["GROUP-PERMANENT-DELETION"]:
          return (
            <>
              {groupName} collab has been permanently deleted due to:{" "}
              <span className="text-lowercase fw-bold">
                {other_data?.reason
                  ? getDeletionReasonMessage(other_data.reason, author!)
                  : "No reason provided."}
              </span>
              .
            </>
          );
        case NotificationSubType["OFFER_INVITATION"]:
          return (
            <>
              <p className="m-0">You just received new offer.</p>
              <p className="fw-semibold m-0">{offer?.name}</p>
              <ul className="m-0">
                {offer?.fee && (
                  <li>Platform fee: {offer?.fee?.platform_fee}%</li>
                )}
                {offer?.fee && (
                  <li>Processing fee: {offer?.fee?.processing_fee}%</li>
                )}
                {offer?.pro_creator && (
                  <li>
                    Pro creator: {formatCurrency(offer?.pro_creator?.fee)}
                  </li>
                )}
                {offer?.prime && (
                  <li>Prime: {formatCurrency(offer?.prime?.fee)}</li>
                )}
                {offer?.match_maker_prime && (
                  <li>
                    MatchMaker Prime:{" "}
                    {formatCurrency(offer?.match_maker_prime?.fee)}
                  </li>
                )}
                {}
              </ul>
              <p className="m-0">
                If you want to access this offer, please click “Claim offer”
                button.
              </p>
            </>
          );

        case NotificationSubType["VAULT-MEDIA-COMPLETION"]:
          return (
            <>
              Your media file{" "}
              <span
                className="fw-bold text-overflow-ellipsis vfile-name"
                title={other_data?.vault_media?.name}
              >
                {other_data?.vault_media?.name}
              </span>{" "}
              has been successfully uploaded to vault.
            </>
          );
        case NotificationSubType["POST-MEDIA-COMPLETION"]:
          return <>Your post is now live.</>;
        case NotificationSubType["POST-DELETE-BY-ADMIN-ON-REPORT"]:
          return (
            <>
              This post, with post id{" "}
              <span className="fw-bold">{other_data?.post_id}</span>, has been
              deleted by the system due to:{" "}
              <span className="fw-bold fst-italic">
                &quot;
                {other_data?.admin_remark || "Unknow Reasons"}
                &quot;
              </span>
            </>
          );
        case NotificationSubType["REPORTED-POST-DELETE-ALERT"]:
          return (
            <>Update: The post you reported has been deleted by the system.</>
          );
        case NotificationSubType["POST-DELETE-BY-ADMIN"]:
          return (
            <>
              This post, with post id{" "}
              <span className="fw-bold">{other_data?.post_id}</span>, has been
              deleted by the system due to:{" "}
              <span className="fw-bold fst-italic">
                &quot;
                {other_data?.admin_remark || "Unknow Reasons"}
                &quot;
              </span>
            </>
          );
        default:
          return "You have message from KNKY.";
      }

    case NotificationType.COMPLAINTS:
      switch (subType) {
        case NotificationSubType["COMPLAINT-ACCEPTED"]:
          return "Your complaint has been accepted.";
        case NotificationSubType["COMPLAINT-REJECTED"]:
          return "Sorry! Your compaint has been rejected.";
        case NotificationSubType["COMPLAINT-REPLIED"]:
          return "You have replied to your complaint.";
        case NotificationSubType["COMPLAINT-RESOLVED"]:
          return "Yay! Your complaint has been resolved.";
        default:
          return "New Notification";
      }

    case NotificationType.TAG:
      switch (subType) {
        case NotificationSubType["POST-TAGGING"]:
          return (
            <>
              Tagged you in a post.{" "}
              <Link href={`/post/${post}`} className="underline">
                View Details
              </Link>
            </>
          );
        case NotificationSubType["TAG-ACCEPTED"]:
          return (
            <>
              Congratulations! Your tag{" "}
              {other_data?.tag_id ? `${other_data?.tag_id}` : ""} has been
              accepted.
            </>
          );
        case NotificationSubType["TAG-REJECTED"]:
          return (
            <>
              Sorry! Your tag{" "}
              {other_data?.tag_id ? `#${other_data?.tag_id}` : ""} has been
              rejected.
            </>
          );

        default:
          return "New Notification";
      }

    case NotificationType.SHOP:
      switch (subType) {
        case NotificationSubType["ORDER_STATUS_CHANGE"]:
          return (
            <Link href={`/shop/${other_data?.transaction_id}/track`}>
              Your order <u className="pointer">{product?.name}</u> has been{" "}
              {other_data?.status}.
            </Link>
          );
        case NotificationSubType["PRODUCT-PURCHASE"]:
          return (
            <Link href={`/shop/${other_data?.transaction_id}/track`}>
              Purchased your <u>product.</u>
            </Link>
          );
        default:
          return "New Shop Notification";
      }

    case NotificationType.SUBSCRIPTION_RENEWAL:
      switch (subType) {
        case NotificationSubType["CHANNEL_SUBSCRIPTION_RENEWED"]:
          return (
            <>
              Your <Link href={`/channel/${channelId}`}>channel</Link>{" "}
              subscription has been renewed.
            </>
          );
        case NotificationSubType["GROUP_SUBSCRIPTION_RENEWED"]:
          return "Your group subscription has been renewed.";
        case NotificationSubType["MATCH_SUBSCRIPTION_RENEWED"]:
          return "Your match subscription has been renewed.";
        case NotificationSubType["PRIME_SUBSCRIPTION_RENEWED"]:
          return "Your prime subscription has been renewed.";
        case NotificationSubType["PRO_CREATOR_SUBSCRIPTION_RENEWED"]:
          return "Your pro creator subscription has been renewed.";
        case NotificationSubType["CHANNEL_SUBSCRIPTION_RENEWAL_ATTEMPTED"]:
          return "Your channel subscription renewal has been attempted.";
        case NotificationSubType["GROUP_SUBSCRIPTION_RENEWAL_ATTEMPTED"]:
          return "Your group subscription renewal has been attempted.";
        case NotificationSubType["MATCH_SUBSCRIPTION_RENEWAL_ATTEMPTED"]:
          return "Your match subscription renewal has been attempted.";
        case NotificationSubType["PRIME_SUBSCRIPTION_RENEWAL_ATTEMPTED"]:
          return "Your prime subscription renewal has been attempted.";
        case NotificationSubType["PRO_CREATOR_SUBSCRIPTION_RENEWAL_ATTEMPTED"]:
          return "Your pro creator subscription renewal has been attempted.";
        case NotificationSubType["CHANNEL_SUBSCRIPTION_RENEWAL_FAILED"]:
          return "Your channel subscription renewal has failed.";
        case NotificationSubType["GROUP_SUBSCRIPTION_RENEWAL_FAILED"]:
          return "Your group subscription renewal has failed.";
        case NotificationSubType["MATCH_SUBSCRIPTION_RENEWAL_FAILED"]:
          return "Your match subscription renewal has failed.";
        case NotificationSubType["PRIME_SUBSCRIPTION_RENEWAL_FAILED"]:
          return "Your prime subscription renewal has failed.";
        case NotificationSubType["PRO_CREATOR_SUBSCRIPTION_RENEWAL_FAILED"]:
          return "Your pro creator subscription renewal has failed.";
        default:
          return "New Notification.";
      }
  }
};

/**
 * Marks the given notifications as seen.
 * @param {boolean} skipped - If true, all unseen notifications will be marked as seen.
 * @param {number} currentPage - The current page number.
 * @param {Notification[]} data - The array of notifications to mark as seen.
 * @param {number} itemsPerPage - The number of items per page.
 * @param {any} localNotifsToBeSeen - The local notifications that are to be seen.
 */

function NotificationBoxBase(props: { type: string }) {
  const router = useRouter();
  const isMount = useIsMount();
  const dispatchAction = useAppDispatch();
  const userId = useAppSelector((state) => state.user.id);
  const blockedUsers = useAppSelector((state) => state.block.blockedByMe);
  const blockedMe = useAppSelector((state) => state.block.hasBlockedMe);
  const currentPlan = useAppSelector(
    (state) => state.matchmakerData.currentPlan
  );
  const showUnseen = useAppSelector(
    (s) => s.notification.showUnseenNotifications
  );
  const userRole = useAppSelector((s) => s.user.role);
  const username = useAppSelector((s) => s.user.profile.username);
  const notificationStateData = useAppSelector(
    (state) => state.notification.notifications
  )
    .filter((n) => !blockedUsers[n?.author?._id])
    .filter((n) => !blockedMe[n?.author?._id])
    .filter((n) => {
      const seen = new Set();

      if (!seen.has(n._id)) {
        seen.add(n._id);
        return true;
      }

      return false;
    });

  const page_data = useAppSelector((state) => state?.notification?.page_data);

  const [showShimmer, setShowShimmer] = useState<boolean>(false);
  const itemsPerPage = page_data?.limit;
  const [hasMoreData, setHasMoreData] = useState(true);

  // cleanup -> mark all notifications as seen
  useEffect(() => {
    return () => {
      dispatchAction(
        notificationActions.markSeen(
          new Set(
            store
              .getState()
              .notification.notifications.filter(
                (n) => n.seen === false || n?.seen === undefined
              )
              .map((n) => n._id)
          )
        )
      );
    };
  }, []);

  useEffect(() => {
    if (isMount) return;
    handlePageChange();
  }, [props.type]);

  const handlePageChange = async () => {
    setShowShimmer(true);

    const res = await GetNotification({
      limit: itemsPerPage,
      type: props.type === "all" ? undefined : props.type,
      seen: showUnseen ? "false" : "",
    });

    dispatchAction(
      notificationActions.addNotifications({
        notification: res.data,
        append: false,
      })
    );

    setShowShimmer(false);

    if (res.data.length === 0) {
      setHasMoreData(false);
    }

    if (props.type === "all" && notificationStateData.length) {
      const extraNotificationCount =
        notificationStateData.length % itemsPerPage || 0;
      const newNotifications = res.data
        .slice(0, res.data.length - extraNotificationCount)
        .filter((n: Notification) => !blockedUsers[n?.author?._id])
        .filter((n: Notification) => !blockedMe[n?.author?._id]);

      dispatchAction(
        notificationActions.addNotifications({
          notification: newNotifications,
          append: true,
        })
      );
    }
  };

  // response api accept or decline
  const handleGroupAction = async (
    groupId: string,
    notificationId: string,
    action: string
  ) => {
    const filteredNotifications = notificationStateData.filter(
      (n) => n._id !== notificationId
    );

    const executeAction = () => {
      RespondRequest(groupId, action)
        .then(() => {
          dispatchAction(
            notificationActions.addNotifications({
              notification: filteredNotifications,
              append: false,
            })
          );
          dispatchAction(
            notificationActions.markSeen(new Set([notificationId]))
          );

          if (action === "accept") {
            router.push(`/collab/${groupId}`);
          }
        })
        .catch((error) => console.error("Error making API call: ", error));
    };

    if (action === "decline") {
      Swal.fire({
        title: "Are you sure you want to decline?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, reject it!",
      }).then((result) => {
        if (result.isConfirmed) {
          executeAction();
          router.push("/trending");
        }
      });
    } else {
      executeAction();
    }
  };

  const handleTagAction = async (notificationId: string, action: string) => {
    const filteredNotifications = notificationStateData.filter(
      (n) => n._id !== notificationId
    );

    const executeAction = () => {
      RespondTagRequest({
        notification_id: notificationId,
        action: action === "decline" ? "reject" : "accept",
      })
        .then(() => {
          dispatchAction(
            notificationActions.markSeen(new Set([notificationId]))
          );

          dispatchAction(
            notificationActions.addNotifications({
              notification: filteredNotifications,
              append: false,
            })
          );

          if (action === "accept") {
            router.push("/");
          }
        })
        .catch((error) => console.error("Error making API call: ", error));
    };

    if (action === "decline") {
      Swal.fire({
        title: "Are you sure you want to decline?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, reject it!",
      }).then((result) => {
        if (result.isConfirmed) {
          executeAction();
          router.push("/trending");
        }
      });
    } else {
      executeAction();
    }
  };

  const handleCollabAction = async (
    post_id: string,
    action: string,
    notificationId: string
  ) => {
    const filteredNotifications = notificationStateData.filter(
      (n) => n._id !== notificationId
    );

    const executeAction = () => {
      RequestNotification(post_id, action)
        .then(() => {
          dispatchAction(
            notificationActions.addNotifications({
              notification: filteredNotifications,
              append: false,
            })
          );
          dispatchAction(
            notificationActions.markSeen(new Set([notificationId]))
          );

          if (action === "accept") {
            router.push(`/post/${post_id}`);
          }
        })
        .catch((error) => console.error("Error making API call: ", error));
    };

    if (action === "decline") {
      Swal.fire({
        title: "Are you sure you want to decline?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, reject it!",
      }).then((result) => {
        if (result.isConfirmed) {
          executeAction();
          router.push("/trending");
        }
      });
    } else {
      executeAction();
    }
  };

  const handleOfferAction = async (notificationId: string) => {
    const filteredNotifications = notificationStateData.filter(
      (n) => n._id !== notificationId
    );
    console.log(notificationId);

    const executeAction = () => {
      router.push(`/settings/my-offers`);
      dispatchAction(
        notificationActions.addNotifications({
          notification: filteredNotifications,
          append: false,
        })
      );
      dispatchAction(notificationActions.markSeen(new Set([notificationId])));
    };

    // executeAction();
  };

  // Following function will render button for group and collab request notification
  const responseButtons = (
    type: string,
    subType: string,
    notificationId: string,
    groupId: string,
    postId: string,
    other_data: OtherData
  ) => {
    if (type === "3" && subType === "5") {
      return (
        <div className="d-flex gap-2 align-items-center">
          <ActionButton
            onClick={() =>
              handleCollabAction(postId, "decline", notificationId)
            }
            variant="danger"
            type="outline"
          >
            Decline
          </ActionButton>
          <ActionButton
            onClick={() => handleCollabAction(postId, "accept", notificationId)}
            variant="success"
            type="solid"
          >
            Accept
          </ActionButton>
        </div>
      );
    }

    if (type === "3" && subType === "4") {
      return (
        <div className="d-flex gap-2 align-items-center">
          <ActionButton
            type="outline"
            variant="danger"
            onClick={() =>
              handleGroupAction(groupId, notificationId, "decline")
            }
          >
            Decline
          </ActionButton>
          <ActionButton
            type="solid"
            variant="success"
            onClick={() => handleGroupAction(groupId, notificationId, "accept")}
          >
            Accept
          </ActionButton>
        </div>
      );
    }

    if (type === "6" && subType === "74") {
      return (
        <ActionButton
          variant="primary"
          onClick={(e) => {
            e.preventDefault();
            handleOfferAction(notificationId);
          }}
        >
          Claim offer
        </ActionButton>
      );
    }

    if (type === "3" && subType === "93") {
      return (
        <div className="d-flex gap-2 align-items-center">
          <ActionButton
            onClick={() => handleTagAction(notificationId, "decline")}
            variant="danger"
            type="outline"
          >
            Decline
          </ActionButton>
          <ActionButton
            onClick={() => handleTagAction(notificationId, "accept")}
            variant="success"
            type="solid"
          >
            Accept
          </ActionButton>
        </div>
      );
    }

    if (type === "6" && subType === "37") {
      const failedPostRedirection = () => {
        if (other_data?.type === "Group") {
          router.push(`/collab/${other_data?.name}`);
        } else if (other_data?.type === "Channel") {
          router.push(`/channel/${other_data?.name}`);
        } else {
          router.push(`/${userRole.toLowerCase()}/${username}/`);
        }
      };

      return (
        <ActionButton
          type="outline"
          variant="primary"
          onClick={failedPostRedirection}
        >
          Go to
          {`${
            other_data?.type === "Channel"
              ? " channel"
              : other_data?.type === "Group"
              ? " collab"
              : " profile"
          }`}
        </ActionButton>
      );
    }

    return null;
  };

  const handleFetchMoreData = async () => {
    if (!hasMoreData) return;

    const lastTimestamp =
      notificationStateData[notificationStateData.length - 1].created_at;

    const res = await GetNotification({
      timestamp: lastTimestamp,
      limit: itemsPerPage,
      type: props.type === "all" ? undefined : props.type,
      seen: showUnseen ? "false" : "",
    });

    if (res.data.length === 0 || res.data.length < itemsPerPage) {
      setHasMoreData(false);
      return;
    }

    dispatchAction(
      notificationActions.addNotifications({
        notification: res.data,
        append: true,
      })
    );
  };

  return (
    <div className="container d-flex flex-column gap-3 p-0">
      <div className="d-flex flex-column gap-3">
        {showShimmer ? (
          <Shimmer type="notification" />
        ) : (
          <InfiniteScroll
            dataLength={notificationStateData.length}
            next={handleFetchMoreData}
            hasMore={hasMoreData}
            loader={<Shimmer type="notification" />}
            scrollableTarget="infiniteScrollProfile"
            className="d-flex flex-column gap-3"
            endMessage={
              <h5 className="text-center w-100">
                You&apos;ve reached the end!
              </h5>
            }
          >
            {notificationStateData.map((notification) => {
              const isSpecialSubType = ["12", "13", "23", "24"].includes(
                notification.sub_type
              );
              const isAnotherSubType = ["39", "40", "41"].includes(
                notification.sub_type
              );
              return (
                <div
                  key={notification._id}
                  className="d-flex justify-content-between align-items-center rounded-3 border p-2 gap-3"
                  style={{
                    backgroundColor: !notification?.seen
                      ? "var(--bg-unseen-light)"
                      : "var(--bg-seen-light)",
                  }}
                >
                  <div className="d-flex flex-column gap-3 flex-lg-row flex-md-row align-items-md-center justify-content-between w-100">
                    <div className="d-flex gap-3 align-items-center">
                      <div className="d-flex gap-3 align-items-center">
                        <Link
                          href={
                            notification?.author && notification.type !== "6"
                              ? `/${notification?.author?.user_type?.toLowerCase()}/${
                                  notification?.author?.username
                                }`
                              : ""
                          }
                        >
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={
                              ["98", "99"].includes(notification.sub_type)
                                ? "/images/common/agency.png"
                                : getAssetUrl({
                                    media: isSpecialSubType
                                      ? userRole === "creator" ||
                                        (userRole === "user" &&
                                          currentPlan?.type !== "Free")
                                        ? notification?.author?.avatar?.[0]
                                        : undefined
                                      : !isAnotherSubType
                                      ? notification?.author?.avatar?.[0]
                                      : undefined,
                                    defaultType: "avatar",
                                    variation: "thumb",
                                  })
                            }
                            width={48}
                            height={48}
                            className="rounded-pill object-fit-cover"
                            alt="avatar"
                          />
                        </Link>
                        <div className="d-flex flex-column gap-2">
                          {["98", "99"].includes(notification.sub_type) ? (
                            <div className="d-flex gap-3 align-items-center">
                              <Link href={``} className="fs-6 fw-medium mb-0">
                                {notification.other_data?.agencyName}
                              </Link>
                              <p className="fs-6 color-light mb-0">|</p>
                              <p
                                className="color-medium fs-7 mb-0"
                                style={{
                                  color: !notification?.seen ? "#ac1991" : "",
                                }}
                              >
                                {convertTimestampToTimeDifference(
                                  notification?.created_at
                                )}
                              </p>
                            </div>
                          ) : notification?.author &&
                            !["39", "40", "41"].includes(
                              notification.sub_type
                            ) ? (
                            <div className="d-flex gap-3 align-items-center">
                              <Link
                                href={`/${notification?.author?.user_type?.toLowerCase()}/${
                                  notification?.author?.username
                                }`}
                                className="fs-6 fw-medium mb-0"
                              >
                                {["12", "13", "23", "24"].includes(
                                  notification.sub_type
                                )
                                  ? userRole === "creator" ||
                                    (userRole === "user" &&
                                      currentPlan?.type !== "Free")
                                    ? notification?.author?.display_name
                                    : "KNKY User"
                                  : notification?.author?.display_name}{" "}
                              </Link>
                              <p className="fs-6 color-light mb-0">|</p>
                              <p
                                className="color-medium fs-7 mb-0"
                                style={{
                                  color: !notification?.seen ? "#ac1991" : "",
                                }}
                              >
                                {convertTimestampToTimeDifference(
                                  notification?.created_at
                                )}
                              </p>
                            </div>
                          ) : (
                            <div className="d-flex gap-3 align-items-center">
                              <Link href={``} className="fs-6 fw-medium mb-0">
                                System
                              </Link>
                              <p className="fs-6 color-light mb-0">|</p>
                              <p
                                className="color-medium fs-7 mb-0"
                                style={{
                                  color: !notification?.seen ? "#ac1991" : "",
                                }}
                              >
                                {convertTimestampToTimeDifference(
                                  notification?.created_at
                                )}
                              </p>
                            </div>
                          )}

                          {/* Notification Message */}
                          <p
                            onClick={() =>
                              notification?.post
                                ? router.push(
                                    `/post/${notification?.post?._id}`
                                  )
                                : notification?.channel?._id &&
                                  router.push(
                                    `/channel/${notification?.channel?._id}`
                                  )
                            }
                            className={`fs-6 fw-normal mb-0 ${
                              (notification?.post ||
                                notification?.channel?._id) &&
                              "pointer"
                            }`}
                          >
                            {getNotificationMessage({
                              type: notification.type,
                              subType: notification.sub_type,
                              groupName: notification?.group?.name,
                              other_data: notification?.other_data,
                              author: notification?.author,
                              post: notification?.post?._id,
                              currentPlan: currentPlan,
                              userRole: userRole,
                              offer: notification?.offer,
                              eventId: notification?.event?._id,
                              channelId: notification?.channel?._id,
                              storyId: notification?.story?._id,
                              channel_details: notification.channel,
                              product: notification?.product,
                              friendly_name:
                                notification?.other_data?.display_name,
                              agencyName:
                                notification.other_data?.agencyName || "",
                              notificationId: notification._id,
                            })}
                            &nbsp;
                            {notification?.group?.name &&
                              !["41", "42"].includes(notification.sub_type) && (
                                <Link
                                  className="text-decoration-underline"
                                  href={`/collab/${notification?.group?.tag_name}`}
                                >
                                  View details
                                </Link>
                              )}
                            {notification?.channel?.name &&
                              !["41"].includes(notification.sub_type) && (
                                <Link
                                  className="text-decoration-underline"
                                  href={`/channel/${notification?.channel?.channel_tagname}`}
                                >
                                  View details
                                </Link>
                              )}
                            {notification?.type == "3" &&
                              notification?.sub_type == "5" && (
                                <Link
                                  className="text-decoration-underline"
                                  href={`/post/${notification?.post?._id}`}
                                >
                                  View details
                                </Link>
                              )}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Response Buttons */}
                    {responseButtons(
                      notification.type,
                      notification.sub_type,
                      notification._id,
                      notification?.group?._id,
                      notification?.post?._id,
                      notification?.other_data!
                    )}
                  </div>
                  {/* Notification Media */}
                  {notification?.post?.media_thumbnail &&
                    notification?.post?.media_thumbnail?.length > 0 && (
                      <NotificationMedia
                        id={notification?.post?._id}
                        media_thumbnail={notification.post?.media_thumbnail}
                        thumbnailType="post"
                        redirect={true}
                      />
                    )}
                  {notification?.story?.media_thumbnail &&
                    notification?.story?.media_thumbnail?.length > 0 && (
                      <NotificationMedia
                        id={notification?.story?._id}
                        media_thumbnail={notification.story?.media_thumbnail}
                        redirect={false}
                        onClickHandler={() => {
                          router.push(
                            `/fresh?storyId=${notification?.story?._id}&userId=${userId}`
                          );
                        }}
                        thumbnailType="story"
                      />
                    )}
                  {notification?.channel?.media_thumbnail &&
                    notification?.channel?.media_thumbnail?.length > 0 && (
                      <NotificationMedia
                        id={notification?.channel?._id}
                        media_thumbnail={notification.channel?.media_thumbnail}
                        thumbnailType="channel"
                        redirect={true}
                      />
                    )}
                  {notification?.event?.media_thumbnail &&
                    notification?.event?.media_thumbnail?.length > 0 && (
                      <NotificationMedia
                        id={notification?.event?._id}
                        media_thumbnail={notification.event?.media_thumbnail}
                        redirect={false}
                        thumbnailType="event"
                      />
                    )}
                  {notification?.product?.media_thumbnail &&
                    notification?.product?.media_thumbnail?.length > 0 && (
                      <NotificationMedia
                        id={notification?.product?._id}
                        media_thumbnail={notification.product?.media_thumbnail}
                        onClickHandler={() => {
                          router.push(
                            `/shop/${notification?.other_data?.transaction_id}/track`
                          );
                        }}
                        redirect={false}
                        thumbnailType="product"
                      />
                    )}
                  {notification?.other_data?.vault_media && (
                    <NotificationMedia
                      id={notification?.other_data?.vault_media?._id}
                      media_thumbnail={
                        [
                          notification.other_data?.vault_media?.type === "image"
                            ? notification.other_data?.vault_media!
                            : notification.other_data?.vault_media?.poster!,
                        ] as Media[]
                      }
                      redirect={false}
                      thumbnailType="vault"
                    />
                  )}
                </div>
              );
            })}
          </InfiniteScroll>
        )}
      </div>
    </div>
  );
}

export default memo(NotificationBoxBase);
