import Image from "next/image";
import { useCallback, useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

import { getrequests } from "@/api/dashboard";
import { EventBridge } from "@/app/event-bridge";
import LoadingSpinner from "@/components/common/loading";
import { useAppSelector } from "@/redux-store/hooks";

import ListingTable from "../../utils/table-content/option-requests/listing-table";

const statusOptions = [
  { label: "All", value: null },
  { label: "Pending", value: 0 },
  { label: "Accepted", value: 1 },
  { label: "Completed", value: 4 },
  { label: "Declined", value: 2 },
];

const ChatOptionsListing = ({ type, cb }: { type: string; cb: () => void }) => {
  const [status, setStatus] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [requestData, setRequestData] = useState<any>([]);
  const [pageCount, setPageCount] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const dateFilter = useAppSelector(
    (state) => state.user.creatorDashboardDateFilter
  );

  const getData = useCallback(() => {
    setIsLoading(true);
    getrequests(1, dateFilter, status, type || "").then((data) => {
      setRequestData(data.data.result);
      setCurrentPage(1);
      setPageCount(data.data.pages_available);
      setIsLoading(false);
    });
  }, [dateFilter, status, type]);

  useEffect(() => {
    getData();
  }, [getData]);

  const fetchMoreData = () => {
    const pageNO = currentPage + 1;
    setCurrentPage(pageNO);
    getrequests(pageNO, dateFilter, status, type || "").then((data) => {
      setRequestData((prevData: any) => [...prevData, ...data.data.result]);
      setPageCount(data.data.pages_available);
    });
  };

  useEffect(() => {
    const s = EventBridge.on("Notification", (notif) => {
      if (
        notif.type !== "ChatMessageUpdate" &&
        notif.type === "UserNotification" &&
        notif?.data?.type !== "3"
      )
        return;

      cb();
      getData();
    });

    return () => {
      s.unsubscribe();
    };
  }, []);

  const renderStatusButtons = () => {
    return statusOptions.map(({ label, value }) => {
      // Exclude "Accepted" when type is "RATING"
      if ((type === "RATING" || type === "CUSTOM-SERVICE") && value === 1)
        return null;

      // Handle "Completed" button logic
      if (label === "Completed") {
        return (
          <button
            key={value}
            type="button"
            className={`btn-light border-0 px-3 py-1 rounded-3 fw-medium fs-6 ${
              (status === 4 ||
                ((type === "RATING" || type === "CUSTOM-SERVICE") &&
                  status === 1)) &&
              "active"
            }`}
            onClick={() =>
              setStatus(type === "RATING" || type === "CUSTOM-SERVICE" ? 1 : 4)
            }
          >
            {label}
          </button>
        );
      }

      return (
        <button
          key={value}
          type="button"
          className={`btn-light border-0 px-3 py-1 rounded-3 fw-medium fs-6 ${
            status === value && "active"
          }`}
          onClick={() => setStatus(value)}
        >
          {label}
        </button>
      );
    });
  };

  return (
    <>
      <div className="container d-flex flex-column gap-3 px-0 px-lg-2">
        <div className="z-1">
          <div className="d-flex align-items-center gap-3 flex-nowrap text-nowrap scrollable py-1 px-2">
            {renderStatusButtons()}
          </div>
        </div>
        {isLoading ? (
          <div
            className="d-flex justify-content-center align-items-center"
            style={{ height: "50vh" }}
          >
            <LoadingSpinner />
          </div>
        ) : (
          <div id="cotentScrollable" className="content-wrapper">
            <InfiniteScroll
              dataLength={requestData.length}
              next={() => fetchMoreData()}
              hasMore={currentPage < pageCount}
              scrollableTarget="cotentScrollable"
              loader={
                <>
                  <LoadingSpinner />
                </>
              }
              endMessage={
                requestData.length === 0 ? (
                  <div
                    className="d-flex flex-column align-items-center justify-content-center"
                    style={{ height: "50vh" }}
                  >
                    <Image
                      src={"/images/svg/dashboard-user/empty-table.svg"}
                      width={50}
                      height={50}
                      alt="loader"
                      className=""
                    />
                    <p className="text-center mt-3">No data found</p>
                  </div>
                ) : (
                  <div className="d-flex flex-column align-items-center justify-content-center">
                    <p className="text-center ">You&apos;ve reached the end</p>
                  </div>
                )
              }
            >
              <ListingTable
                tableData={requestData}
                chatType={type}
                getDataAgain={getData}
              />
            </InfiniteScroll>
          </div>
        )}
      </div>
    </>
  );
};

export default ChatOptionsListing;
