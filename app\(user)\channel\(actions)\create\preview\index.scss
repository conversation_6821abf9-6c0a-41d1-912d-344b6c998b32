.avatar-container {
  max-width: 10rem;
  max-height: 10rem;
  background-color: var(--bg-cream);
  border: 0.1rem dashed var(--primary-color);

  .avatar-img {
    object-fit: cover;
  }
}

.background-container {
  min-height: 20rem;
}

.img-container {
  height: 26rem;
}

.change-avatar {
  color: var(--bg-cream) !important;
}

.cropper-wrapper {
  max-width: 30rem;
  max-height: 30rem;
}

.avatar-cropper {
  .cropper-container,
  .cropper-drag-box {
    border-radius: 3px;
  }
  .cropper-crop-box,
  .cropper-view-box {
    border-radius: 50%;
  }
}
