"use client";

import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";

import type { Subscription } from "@/types/event-bridge";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { notification<PERSON>and<PERSON> } from "@/utils/notification-handler";
import { blockActions } from "@/redux-store/actions";

import { EventBridge } from ".";
import { PresenceChecker } from "./presence-checker";

export default function EventBridgeInit() {
  const router = useRouter();
  const token = useAppSelector((state) => state?.user?.token);
  const dispatch = useAppDispatch();
  const subRefs = useRef<Subscription[]>([]);

  useEffect(() => {
    if (token) {
      EventBridge.connect(token);

      let s = EventBridge.on("Notification", notificationHandler);
      subRefs.current.push(s);

      s = EventBridge.on("GotBlocked", ({ from, blocked }) => {
        dispatch(blockActions.gotBlocked({ from, blocked }));
      });
      subRefs.current.push(s);

      s = EventBridge.on("connect", () => {
        PresenceChecker.startProcessing();
      });
      subRefs.current.push(s);

      s = EventBridge.on("disconnect", () => {
        PresenceChecker.stopProcessing();
      });
      subRefs.current.push(s);
    } else {
      subRefs.current.forEach((s) => s.unsubscribe());
      EventBridge.disconnect();
    }
  }, [token]);

  useEffect(() => {
    const messageHandler = (e: MessageEvent) => {
      if (e.origin !== window.location.origin) return;
      if (typeof e.data !== "object") return;
      const { event, data } = e.data;

      if (event === "navigate") {
        router.push(data);
      }
    };

    window.addEventListener("message", messageHandler);

    return () => {
      subRefs.current.forEach((s) => s.unsubscribe());
      EventBridge.disconnect();
      window.removeEventListener("message", messageHandler);
    };
  }, []);

  return <></>;
}
