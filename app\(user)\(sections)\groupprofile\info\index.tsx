import Image from "next/image";

export default function GroupInfo() {
  return (
    <div className="bg-body container d-flex flex-column p-3 rounded-3">
      <p className="fs-5 fw-medium">Collab informations</p>
      <div className="group-details-wrapper">
        <label
          htmlFor="group name"
          className=" align-self-start color-medium fs-7 "
        >
          Collab name
          <span className="color-red">*</span>
        </label>
        <div className="input-group mb-3 w-75 w-sm-100">
          <input
            name="Name"
            type="text"
            placeholder="Name"
            className="color-dark bg-cream border-0 py-2 px-3 shadow-none rounded-3 w-100"
          />
        </div>
        <label
          htmlFor="group username"
          className=" align-self-start color-medium fs-7 "
        >
          Collab username
          <span className="color-red">*</span>
        </label>
        <div className="input-group mb-3 w-75 w-sm-100">
          <input
            name="Username"
            type="text"
            placeholder="Username"
            className="color-dark bg-cream border-0 py-2 px-3 shadow-none rounded-3 w-100"
          />
        </div>
        <label
          htmlFor="group topic"
          className=" align-self-start color-medium fs-7 "
        >
          Group topic
          <span className="color-red">*</span>
        </label>
        <div className="dropdown w-75 w-sm-100">
          <button
            className="btn btn-secondary w-100 d-flex align-items-center justify-content-between bg-cream ps-2 pe-2"
            type="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            <div className="d-flex gap-1 align-items-center">
              <span className="fs-6 fw-medium"></span>
            </div>
            <Image
              src={"/images/svg/chevron-down.svg"}
              width={25}
              height={25}
              alt="dropdown"
              className="svg-icon"
            />
          </button>
          <ul className="dropdown-menu w-100">
            <li className="d-flex pe-2 ps-2 gap-2 align-items-center mb-2">
              <p className="fs-6 mb-0 fw-medium">Topic 1</p>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
