import Image from "next/image";
import { useSearchParams } from "next/navigation";
import React, { memo, useEffect, useRef, useState } from "react";

import TagList from "@/components/common/hashtags/list";
import MentionTextArea from "@/components/common/mention-textarea";
import { type ModalRef } from "@/components/common/modal";
import { userDataActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Media } from "@/types/media";
import { getAssetUrl } from "@/utils/assets";
import "./index.scss";

import Badges from "@/components/common/badges";
import { ModalService } from "@/components/modals";
import type { ExternalTagConsent } from "@/types/post";
import { containsInvalidURL } from "@/utils/formatter";

import { PostVisibilitySelector } from "../../post-visibility";
import VaultMediaRenderer from "../vault-media-renderer";
import Background from "./../background";
import MediaValidation from "./../media-validation";
import EditImage from "./../modal/edit-image";
import { PollBox } from "./../poll/index";
import PromoteItem from "./../promote-item";
import TagAndCollab from "./../tag-collab";
import MediaRenderer from "./mediaRenderer";

const pollInitState = {
  question: "",
  answer_choice_type: "single",
  answer_choices: [
    { option: "", votes: [] },
    { option: "", votes: [] },
  ],
};
const editImageModalRef = { method: {} as ModalRef };

const EnableMap: any = {
  background: {
    media: false,
    poll: false,
    tag: false,
    collab: false,
    promote: false,
    streaming: false,
    event: false,
  },
  promote: {
    media: false,
    poll: false,
    tag: false,
    collab: false,
    background: false,
    streaming: false,
    event: false,
  },
  media: {
    background: false,
    promote: false,
  },
  poll: {
    background: false,
    promote: false,
  },
  premium: {
    poll: false,
    promote: false,
    background: false,
  },
};

const PostContent = ({
  postCaption,
  props,
}: {
  postCaption: Function;
  props: any;
}) => {
  const caption = useAppSelector((state) => state.userData?.post?.caption);
  const groupData = useAppSelector((state) => state.defaults.group_profile);
  const channelprofile = useAppSelector(
    (state) => state?.defaults?.channel_profile
  );
  const searchParams = useSearchParams();
  const isGroup = searchParams?.get("isGroupPost")
    ? !!searchParams?.get("isGroupPost")
    : false;
  const isChannel = searchParams?.get("isChannelPost")
    ? !!searchParams?.get("isChannelPost")
    : false;

  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const dispatchAction = useAppDispatch();
  const channelList = useAppSelector((state) => state?.userData?.channels);
  const groupList = useAppSelector((state) => state?.userData?.groups);
  const user = useAppSelector((state) => state.user);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const isExternalLinkInclude = useAppSelector(
    (s) => s.userData?.isExternalLinkInclude
  );
  const stateCaption = useAppSelector((s) => s.userData.post.caption);
  const tagModalRef = { method: {} as ModalRef };

  const captionLimitExceeds = stateCaption.length > 1000;

  const handleThumbnailUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const selectedThumbnail = event.target.files?.[0];

    if (selectedThumbnail && selectedThumbnail.type.startsWith("image/")) {
      props.setVideoThumbnail((prevThumbnails: string[] | File[]) => {
        const updatedThumbnails = [...prevThumbnails];
        updatedThumbnails[index] = selectedThumbnail;
        return updatedThumbnails;
      });

      props.setThumbnailUrl((prevThumbnails: string[]) => {
        const updatedThumbnails = [...prevThumbnails];
        updatedThumbnails[index] = URL.createObjectURL(
          selectedThumbnail as File
        );
        return updatedThumbnails;
      });
    }
  };

  const removeThumbnail = (index: number) => {
    props.setVideoThumbnail((prevThumbnails: string[] | File[]) => {
      const updatedThumbnails = [...prevThumbnails];
      updatedThumbnails[index] = "";
      return updatedThumbnails;
    });
    props.setThumbnailUrl((prevThumbnails: string[]) => {
      const updatedThumbnails = [...prevThumbnails];
      updatedThumbnails[index] = "";
      return updatedThumbnails;
    });
  };

  const visibilityProps = {
    isGroup,
    isChannel,
    isEdit: props.isEdit,
    visibility: props.visibility,
    setVisibility: props.setVisibility,
    postOf: props.postOf,
    isSubscriptionVisibility:
      props.shareToChannels.length > 0 || props.shareToGroups.length > 0,
    postingAsSubs: props.postingAsSubs,
  };

  const [editableImage, setEditableImage] = useState<string>("");
  const [editableImageIndex, setEditableImageIndex] = useState<number>(-1);

  const editImage = (src: string, i: number) => {
    setEditableImage(src);
    setEditableImageIndex(i);
  };

  useEffect(() => {
    if (editableImage.length) {
      editImageModalRef?.method?.open();
    }
  }, [editableImage]);

  const handleCroppedPostUrl = (url: string, dataUrl: any, index: number) => {
    const blob = dataUrl;
    const file = new File([blob], "fileName", { type: blob.type });
    props.setPostImg((prevPostImg: File[] | any[]) => {
      const updatedImages = [...prevPostImg];
      updatedImages[index] = file;
      return updatedImages;
    });
    props.setPostImgUrls((prevPostImg: string) => {
      const updatedImages = [...prevPostImg];
      updatedImages[index] = url;
      return updatedImages;
    });

    editImageModalRef.method.close();
  };

  function removePost(type: "image" | "video", index: number) {
    if (type === "image") {
      props.setPostImg((prevImages: File[]) =>
        prevImages.filter((_, i) => i !== index)
      );
      props.setPostImgUrls((prevImages: string[]) =>
        prevImages.filter((_, i) => i !== index)
      );

      props.setTagConsent((prev: any) => {
        const updated = [...prev];

        updated.splice(index, 1);
        return updated;
      });
      props.setReleaseUsers((prev: any) => {
        const updated = [...prev];
        updated.splice(index, 1);
        return updated;
      });
      props.setMediaLocation((prev: any) => {
        const updated = [...prev];
        updated.splice(index, 1);
        return updated;
      });
      props.setMediaDOP((prev: any) => {
        const updated = [...prev];
        updated.splice(index, 1);
        return updated;
      });
      props.setMediaTitle((prev: any) => {
        const updated = [...prev];
        updated.splice(index, 1);
        return updated;
      });
      props.setIsAuthorPresent((prev: boolean[]) => {
        const updated = [...prev];
        updated.splice(index, 1);
        return updated;
      });
    } else if (type === "video") {
      const videoIndex = index - props.postImgUrls.length;
      props.setPostVid((prevVideos: File[]) =>
        prevVideos.filter((_, i) => i !== videoIndex)
      );

      props.setPostVidUrls((prevVideos: string[]) =>
        prevVideos.filter((_, i) => i !== videoIndex)
      );
      props.setVideoThumbnail((prevThumbnails: string[] | File[]) => {
        const updatedThumbnails = [...prevThumbnails];
        updatedThumbnails.splice(videoIndex, 1);
        return updatedThumbnails;
      });
      props.setThumbnailUrl((prevThumbnails: string[]) => {
        const updatedThumbnails = [...prevThumbnails];
        updatedThumbnails.splice(videoIndex, 1);
        return updatedThumbnails;
      });

      props.setTagConsent((prev: any) => {
        const updated = [...prev];

        updated.splice(videoIndex, 1);
        return updated;
      });
      props.setReleaseUsers((prev: any) => {
        const updated = [...prev];
        updated.splice(videoIndex, 1);
        return updated;
      });
      props.setMediaLocation((prev: any) => {
        const updated = [...prev];
        updated.splice(videoIndex, 1);
        return updated;
      });
      props.setMediaDOP((prev: any) => {
        const updated = [...prev];
        updated.splice(videoIndex, 1);
        return updated;
      });
      props.setMediaTitle((prev: any) => {
        const updated = [...prev];
        updated.splice(videoIndex, 1);
        return updated;
      });
      props.setIsAuthorPresent((prev: boolean[]) => {
        const updated = [...prev];
        updated.splice(videoIndex, 1);
        return updated;
      });
    }

    props.setCroppedPostUrl("");
  }

  function removeVaultPost(type: "image" | "video", index: number) {
    if (type === "image") {
      props.setVaultImages((prevImages: Media[]) =>
        prevImages.filter((_, i) => i !== index)
      );
    } else if (type === "video") {
      props.setVaultVideos((prevVideos: Media[]) =>
        prevVideos.filter((_, i) => i !== index)
      );

      props.setVaultVideoThumbnail((prevThumbnails: string[] | Media[]) => {
        const updatedThumbnails = [...prevThumbnails];
        updatedThumbnails.splice(index, 1);
        return updatedThumbnails;
      });
    }
  }

  const handleVaultVideoThumbnail = (i: number) => {
    window.KNKY.openVault({
      readonly: true,
      constraints: { type: "image" },
    }).then((res) => {
      // @ts-expect-error Argument of type '(media: Media) => void' is not assignable to parameter of type '(value: VFile, index: number, array: VFile[]) => void'.
      res.medias.map((media: Media) => {
        props.setVaultVideoThumbnail((prevThumbnails: string[] | Media[]) => {
          const updatedThumbnails = [...prevThumbnails];
          updatedThumbnails[i] = media;
          return updatedThumbnails;
        });
      });
    });
  };

  const removeVaultThumbnail = (index: number) => {
    props.setVaultVideoThumbnail((prevThumbnails: string[] | Media[]) => {
      const updatedThumbnails = [...prevThumbnails];
      updatedThumbnails[index] = "";
      return updatedThumbnails;
    });
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
      // Close dropdown if clicking outside
    }
  };

  useEffect(() => {
    // Add click event listener to detect outside clicks
    document.addEventListener("mousedown", handleClickOutside);

    // Cleanup event listener on component unmount
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const [isPaidSubscription, setIsPaidSubscription] = useState(false);
  const profile = {
    _id: isChannel
      ? channelprofile._id
      : isGroup
      ? groupData?._id
      : user.profile._id,
    username: isChannel
      ? channelprofile.username
      : isGroup
      ? groupData?.username
      : user.profile.username,
    avatar: isChannel
      ? channelprofile?.img || channelprofile?.media[0]
      : user.profile.avatar[0],
  };
  const [selectedName, setSelectedName] = useState(profile?.username);
  const [selectedId, setSelectedId] = useState(profile._id);
  const [selectedAvatar, setSelectedAvatar] = useState(profile.avatar);
  const isMobile = window.innerWidth < 768;
  const role = useAppSelector((state) => state.user.role);

  const handleSelectionChange = (
    id: string,
    name: string,
    avatar: Media,
    type: string,
    subscriptionType?: "FREE" | "FREE_TRIAL" | "FEE"
  ) => {
    setSelectedId(id);
    setSelectedName(name);
    setSelectedAvatar(avatar);

    if (type === "channel") {
      props.setShareToChannels([id]);
      props.setShareToGroups([]);
      props.setShareOnProfile(false);
      props.setPostingAsSubs(true);
    } else if (type === "group") {
      props.setShareToGroups([id]);
      props.setShareOnProfile(false);
      props.setShareToChannels([]);
      props.setPostingAsSubs(true);
    } else {
      props.setShareOnProfile(true);
      props.setShareToChannels([]);
      props.setShareToGroups([]);
      props.setPostingAsSubs(false);
    }

    setShowDropdown(false);
    setIsPaidSubscription(subscriptionType === "FEE");
  };

  useEffect(() => {
    if (isChannel) {
      const matchingChannel = channelList.find(
        (channel) => channel._id === channelprofile._id
      );

      const subscriptionType = matchingChannel?.subscription[0]?.offer_type;
      setIsPaidSubscription(subscriptionType === "FEE");
    } else if (isGroup) {
      const matchingGroup = channelList.find(
        (channel) => channel._id === channelprofile._id
      );

      const subscriptionType = matchingGroup?.subscription[0]?.offer_type;
      setIsPaidSubscription(subscriptionType === "FEE");
    }
  }, [isChannel, isGroup]);

  useEffect(() => {
    const hasInvalidUrl = containsInvalidURL(caption);

    dispatchAction(userDataActions.setExternalLinkInclude(hasInvalidUrl));
  }, [caption]);

  const handleOpenTagModal = (index: number) => {
    ModalService.open("RELEASE_FORM_LIST", {
      ids: props.isEdit ? [`${props.editPostMedia[index]?._id}`] : [`${index}`],
      source: "create-post",

      addedInternalTags: props.tagConsent[index],
      addedExternalTags: props.releaseUsers[index],
      addedDate: props.mediaDOP[index],
      addedLocation: props.mediaLocation[index],
      isAuthorPresent: props.isAuthorPresent[index],
      isEditPost: props.isEdit,
      consentId: props.isEdit ? props.editPostMedia?.[index]?.consent?._id : "",
      onSave: (
        newTags: any[],
        newReleases: ExternalTagConsent[],
        applyToAll: boolean,
        date?: string,
        location?: string,
        isAuthorPresent?: boolean
      ) => {
        if (applyToAll) {
          const mediaLength =
            props.postImgUrls.length + props.postVidUrls.length;
          // for replacing tags while apply to all

          const newTagConsent = Array(mediaLength).fill(newTags);
          const newReleaseUsers = Array(mediaLength).fill(newReleases);
          const newMediaDOP = Array(mediaLength).fill(date);
          const newMediaLocation = Array(mediaLength).fill(location);
          const newIsAuthorPresent = Array(mediaLength).fill(isAuthorPresent);

          props.setMediaDOP(newMediaDOP);
          props.setMediaLocation(newMediaLocation);
          props.setTagConsent(newTagConsent);
          props.setReleaseUsers(newReleaseUsers);
          props.setIsAuthorPresent(newIsAuthorPresent);
          // for merging new tags  to existing one
          // const newTagConsent = Array.from({ length: mediaLength }, (_, i) => {
          //   const existingTags = props.tagConsent[i] || [];
          //   const filteredNewTags = newTags.filter(
          //     (newTag) =>
          //       !existingTags.some(
          //         (existingTag: any) => existingTag.username === newTag.username
          //       )
          //   );
          //   return [...existingTags, ...filteredNewTags];
          // });

          // const newReleaseUsers = Array.from(
          //   { length: mediaLength },
          //   (_, i) => {
          //     const existingReleases = props.releaseUsers[i] || [];
          //     const filteredNewReleases = newReleases.filter(
          //       (newRelease) =>
          //         !existingReleases.some(
          //           (existingRelease: any) =>
          //             existingRelease.email === newRelease.email
          //         )
          //     );
          //     return [...existingReleases, ...filteredNewReleases];
          //   }
          // );

          // props.setTagConsent(newTagConsent);
          // props.setReleaseUsers(newReleaseUsers);
        } else {
          props.setTagConsent((prev: any) => {
            const copy = [...prev];
            copy[index] = newTags;
            return copy;
          });

          props.setReleaseUsers((prev: any) => {
            const copy = [...prev];
            copy[index] = newReleases;
            return copy;
          });

          props.setMediaDOP((prev: any) => {
            const copy = [...prev];
            copy[index] = date;
            return copy;
          });
          props.setMediaLocation((prev: any) => {
            const copy = [...prev];
            copy[index] = location;
            return copy;
          });
          props.setIsAuthorPresent((prev: boolean[]) => {
            const copy = [...prev];
            copy[index] = isAuthorPresent || false;
            return copy;
          });
        }
      },
    });
  };

  return (
    <>
      <div className="post-content-display d-flex flex-column gap-2 gap-md-3 bg-body rounded-3 h-100">
        <div className="post-content-user d-flex gap-2 py-1 align-items-center flex-nowrap px-2 px-md-0">
          {props.postSettings && (
            <div className="mb-2">
              <Image
                src={"/images/common/back-arrow.svg"}
                height={20}
                width={20}
                alt="back"
                className="pointer"
                onClick={() => props.setPostSettings(false)}
              />
            </div>
          )}
          {
            //#region
            // {isGroup && (
            //   <>
            //     {" "}
            //     <div className="rounded-pill profile-wrapper ">
            //       {/* eslint-disable-next-line @next/next/no-img-element */}
            //       <img
            //         width={58}
            //         height={58}
            //         className="img-fluid object-fit-cover bg-body rounded-pill h-100"
            //         src={getAssetUrl({
            //           media: user.profile.avatar[0],
            //           defaultType: "avatar",
            //         })}
            //         alt="profile-img"
            //       />
            //     </div>
            //     <div className="d-flex flex-column flex-nowrap">
            //       <p className="fs-5 text-overflow-ellipsis fw-medium mb-0">
            //         {groupData?.name}
            //       </p>
            //       {props.feeling.text.length != 0 && (
            //         <div className="feeling-box rounded-3 bg-cream d-flex flex-nowrap align-items-center m-auto ms-0 ps-1 pe-1">
            //           <span className="fs-7 fw-medium mb-0 p-1">{`is feeling ${props.feeling.emoji} ${props.feeling.text}`}</span>
            //           <Image
            //             onClick={() => {
            //               props.setFeeling({ emoji: "", text: "" });
            //               props.setPostType({ feeling: false });
            //             }}
            //             width={20}
            //             height={20}
            //             className="invert"
            //             src="/images/svg/nav-close.svg"
            //             alt="close-feeling"
            //           />
            //         </div>
            //       )}
            //     </div>
            //   </>
            // )}
            // {isChannel && (
            //   <>
            //     {" "}
            //     <div className="rounded-pill profile-wrapper">
            //       {/* eslint-disable-next-line @next/next/no-img-element */}
            //       <img
            //         width={58}
            //         height={58}
            //         className="img-fluid object-fit-cover bg-body  rounded-pill h-100"
            //         src={
            //           channelprofile?.img ||
            //           getAssetUrl({ media: channelprofile?.media[0] })
            //         }
            //         alt="profile-img"
            //       />
            //     </div>
            //     <div className="d-flex flex-column flex-nowrap">
            //       <p className="fs-5 text-overflow-ellipsis fw-medium mb-0">
            //         {channelprofile?.name}
            //       </p>
            //       {props.feeling.text.length != 0 && (
            //         <div className="feeling-box rounded-3 bg-cream d-flex flex-nowrap align-items-center m-auto ms-0 ps-1 pe-1">
            //           <span className="fs-7 fw-medium mb-0 p-1">{`is feeling ${props.feeling.emoji} ${props.feeling.text}`}</span>
            //           <Image
            //             onClick={() => {
            //               props.setFeeling({ emoji: "", text: "" });
            //               props.setPostType({ feeling: false });
            //             }}
            //             width={20}
            //             height={20}
            //             className="invert"
            //             src="/images/svg/nav-close.svg"
            //             alt="close-feeling"
            //           />
            //         </div>
            //       )}
            //     </div>{" "}
            //   </>
            // )}
            //#endregion
          }
          {
            <>
              <div className="rounded-pill profile-wrapper d-flex align-items-center  ">
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  width={58}
                  height={58}
                  className=" object-fit-cover d-none d-md-block bg-body rounded-circle "
                  src={
                    typeof selectedAvatar === "string"
                      ? selectedAvatar
                      : getAssetUrl({
                          media: selectedAvatar,
                          defaultType: "avatar",
                        })
                  }
                  alt="profile-img"
                />
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  width={42}
                  height={42}
                  className=" object-fit-cover bg-body   d-md-none rounded-circle "
                  src={
                    typeof selectedAvatar === "string"
                      ? selectedAvatar
                      : getAssetUrl({
                          media: selectedAvatar,
                          defaultType: "avatar",
                        })
                  }
                  alt="profile-img"
                />
              </div>
              <div ref={dropdownRef} className="position-relative">
                <div
                  className={`d-flex flex-column flex-nowrap ${
                    isMobile ? "pointer" : ""
                  }`}
                  onClick={() =>
                    role === "creator" &&
                    !(props.postSettings || props.isEdit) &&
                    setShowDropdown(!showDropdown)
                  }
                >
                  {role === "creator" &&
                    !(props.postSettings || props.isEdit) && (
                      <div className="d-flex  gap-1 align-items-center pointer">
                        <span className="fs-9">Posting as</span>
                        <Image
                          src={"/images/post/switch-account.svg"}
                          alt=""
                          width={12}
                          height={12}
                          style={{
                            transform: showDropdown
                              ? "rotate(180deg)"
                              : "rotate(0deg)",
                            transition: "transform 0.5s ease-in-out",
                          }}
                        />
                      </div>
                    )}
                  {/* To make it fs-7 */}
                  <p
                    className={` fs-7 fs-md-5 text-overflow-ellipsis fw-medium mb-0 ${
                      role === "creator" &&
                      !(props.postSettings || props.isEdit) &&
                      "pointer"
                    }`}
                  >
                    {selectedName}
                  </p>
                  {props.feeling.text.length != 0 && (
                    <div className="feeling-box rounded-3 bg-cream d-flex flex-nowrap align-items-center m-auto ms-0 ps-1 pe-1">
                      <span className="fs-7 fw-medium mb-0 p-1">{`is feeling ${props.feeling.emoji} ${props.feeling.text}`}</span>
                      <Image
                        onClick={() => {
                          props.setFeeling({ emoji: "", text: "" });
                          props.setPostType({ feeling: false });
                        }}
                        width={20}
                        height={20}
                        className="invert svg-icon"
                        src="/images/svg/nav-close.svg"
                        alt="close-feeling"
                      />
                    </div>
                  )}
                </div>

                {showDropdown && (
                  <ul
                    className="dropdown-menu switch-dropdown bg-body show position-absolute p-2 py-3 rounded-4"
                    style={{ maxHeight: "25rem", overflow: "scroll" }}
                  >
                    <li>
                      <div
                        className={`d-flex gap-2 align-items-center  ms-1 `}
                        aria-disabled={true}
                        onClick={() =>
                          handleSelectionChange(
                            user.profile._id,
                            user.profile.username,
                            user.profile.avatar[0],
                            "profile"
                          )
                        }
                      >
                        <input
                          type="checkbox"
                          name="selection"
                          className="form-check-input rounded-circle p-2 switch-radio"
                          checked={selectedId === user.profile._id} // Check if it's selected
                          onChange={() =>
                            handleSelectionChange(
                              user.profile._id,
                              user.profile.username,
                              user.profile.avatar[0],

                              "profile"
                            )
                          }
                        />

                        <label className="d-flex align-items-center gap-2 ">
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            src={getAssetUrl({
                              media: user.profile.avatar[0],
                              variation: "thumb",
                            })}
                            alt=""
                            className="rounded-circle bg-body object-fit-cover"
                            width={32}
                            height={32}
                          />

                          <div className="d-flex align-items-center gap-1">
                            <h6
                              className="fw-medium mb-0 text-overflow-ellipsis"
                              style={{ width: "6rem" }}
                            >
                              {" "}
                              {user.profile.username}
                            </h6>
                            <Badges array={user?.profile?.badges} />
                          </div>
                        </label>
                      </div>
                    </li>
                    {channelList.length > 0 &&
                      channelList?.map((channel, index) => {
                        if (channel.marked_as_deleted) {
                          return;
                        }

                        return (
                          <li key={index}>
                            <div
                              key={channel?._id}
                              className={`d-flex gap-2 align-items-center mt-3 ms-1`}
                              aria-disabled={true}
                              onClick={() =>
                                handleSelectionChange(
                                  channel._id,
                                  channel.name,
                                  channel.avatar[0],
                                  "channel",
                                  channel.subscription[0]?.offer_type
                                )
                              }
                            >
                              <input
                                type="checkbox"
                                name="selection"
                                className="form-check-input rounded-circle p-2 switch-radio"
                                checked={selectedId === channel._id}
                                onChange={() =>
                                  handleSelectionChange(
                                    channel._id,
                                    channel.name,
                                    channel.avatar[0],
                                    "channel",
                                    channel.subscription[0]?.offer_type
                                  )
                                }
                              />
                              <label className="d-flex align-items-center gap-2 ">
                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                <img
                                  src={getAssetUrl({
                                    media: channel.avatar[0],
                                    variation: "thumb",
                                  })}
                                  alt=""
                                  className="rounded-circle bg-body object-fit-cover"
                                  width={32}
                                  height={32}
                                />
                                <div className="d-flex align-items-center gap-1">
                                  <h6
                                    className="fw-medium mb-0 text-overflow-ellipsis"
                                    style={{ width: "6rem" }}
                                  >
                                    {channel?.name}
                                  </h6>
                                  <span className=" px-2 py-1 bg-green2  text-white rounded-2 fs-9 fw-normal">
                                    Channel
                                  </span>
                                </div>
                              </label>
                            </div>
                          </li>
                        );
                      })}
                    {groupList.length > 0 &&
                      groupList?.map((group, index) => {
                        if (
                          group.members.some((u) => !u.terms_accepted) ||
                          group.marked_as_deleted
                        ) {
                          return;
                        }

                        return (
                          <li key={index}>
                            <div
                              key={group?._id}
                              className="d-flex gap-2 align-items-center mt-3 ms-1"
                              onClick={() =>
                                handleSelectionChange(
                                  group._id,
                                  group.name,
                                  group?.author?.background?.[0]!,
                                  "group",
                                  group.subscription[0]?.offer_type
                                )
                              }
                            >
                              <input
                                type="checkbox"
                                name="selection"
                                className="form-check-input rounded-circle p-2 switch-radio"
                                checked={selectedId === group._id}
                                onChange={() =>
                                  handleSelectionChange(
                                    group._id,
                                    group.name,
                                    group?.author?.background?.[0]!,
                                    "group",
                                    group.subscription[0]?.offer_type
                                  )
                                }
                              />
                              <label className="d-flex align-items-center gap-2 ">
                                {/* eslint-disable-next-line @next/next/no-img-element */}
                                <img
                                  src={getAssetUrl({
                                    media: group?.author?.background?.[0],
                                    variation: "thumb",
                                  })}
                                  alt=""
                                  className="rounded-circle bg-body object-fit-cover"
                                  width={32}
                                  height={32}
                                />
                                {/* {group.members.slice(1).length > 0 && (
                                <div
                                  style={{
                                    marginLeft: -40,
                                  }}
                                >
                                  <div
                                    className="rounded-pill bg-cream"
                                    style={{
                                      height: 32,
                                      width: 32,
                                    }}
                                  >
                                    <div className="bg-purple rounded-pill h-100 w-100 d-flex justify-content-center align-items-center text-white fw-bold">
                                      <u className="fs-9 fw-light text-wrap">
                                        +{group.members.slice(1).length}
                                      </u>
                                    </div>
                                  </div>
                                </div>
                              )} */}

                                <div className="d-flex align-items-center gap-1">
                                  <h6
                                    className="fw-medium mb-0 text-overflow-ellipsis"
                                    style={{ width: "6rem" }}
                                  >
                                    {group?.name}
                                  </h6>
                                  <span className=" px-2 py-1 bg-blue  text-white rounded-2 fs-9 fw-normal">
                                    Collab
                                  </span>
                                </div>
                              </label>
                            </div>
                          </li>
                        );
                      })}
                  </ul>
                )}
              </div>
            </>
          }
        </div>

        {!props.postSettings && (
          <div className="mobile-post-audience">
            <PostVisibilitySelector props={visibilityProps} onMobile={true} />
          </div>
        )}

        {!props.postSettings && !props.isEdit && (
          <MediaValidation
            images={props.postImg}
            videos={props.postVid}
            vaultImages={props.vaultImages}
            vaultVideos={props.vaultVideos}
            preview={props.preview}
            poll={props.poll}
            bg={props.bg}
            promote={props.shopItems}
            postOf={props.postOf}
            visibility={props.visibility.value!}
            setSubmitPost={props.setSubmitPost}
            isPaidSubscription={isPaidSubscription}
            isSubscriptionVisibility={
              props.shareToChannels.length > 0 || props.shareToGroups.length > 0
            }
          />
        )}

        {!props.postOf.background && (
          // removing overflow-hidden class, to show whole mention dropdown.
          <div
            className="caption-content mx-2 "
            // style={{
            //   // adding the height so suggestion dropdown can flow a little. Temporary fix. Will need to see the changes to be made later
            //   height: "200px",
            // }}
          >
            {props.postSettings ? (
              <div>
                <MentionTextArea
                  inputName="caption"
                  inputId="caption-text"
                  disabled={
                    props.postOf.media &&
                    !props.postSettings &&
                    !props.croppedPostUrl &&
                    !props.postVid
                      ? true
                      : false
                  }
                  placeholder={
                    "Enter a caption for your post (minimum 10 characters) \n Add #hashtags here to boost discovery!"
                  }
                  value={caption}
                  onBlurFn={postCaption}
                />
              </div>
            ) : (
              <>
                {isExternalLinkInclude && (
                  <span className="text-danger fs-7 ms-2 mb-1 d-inline-block">
                    {" "}
                    External links are not supported
                  </span>
                )}
                {captionLimitExceeds && (
                  <span className="text-danger fs-7 ms-2 mb-1 d-inline-block">
                    {" "}
                    Caption can’t exceed 1020 characters.
                  </span>
                )}
                <div className="position-relative">
                  <MentionTextArea
                    inputName="caption"
                    inputId="caption-text2"
                    disabled={
                      props.postOf.media &&
                      !props.postSettings &&
                      !props.croppedPostUrl &&
                      !props.postVid
                        ? true
                        : false
                    }
                    placeholder={
                      "Enter a caption for your post (minimum 10 characters) \nAdd #hashtags here to boost discovery!"
                    }
                    value={caption}
                    onBlurFn={postCaption}
                  />

                  <div className="position-absolute"></div>
                </div>
              </>
            )}

            {/* {!postSettings && <SearchHashtags />} */}
            {props.postSettings && <TagList />}
          </div>
        )}

        <div className="media-content  position-relative justify-content-center px-1 d-flex row  flex-wrap ">
          {(props.postOf?.media || props.isEdit) &&
            props.postImgUrls?.map((src: string, i: number) => (
              <MediaRenderer
                key={i}
                index={i}
                type="image"
                isEdit={props.isEdit}
                src={src}
                title={props.mediaTitle[i]}
                setTitle={props.setMediaTitle}
                postSettings={props.postSettings}
                onEdit={editImage}
                onRemove={removePost}
                tagsCount={
                  props.tagConsent[i]?.length + props.releaseUsers[i]?.length ||
                  0
                }
                openTagModal={() => handleOpenTagModal(i)}
                editPostMedia={props.editPostMedia?.[i]}
              />
            ))}

          {(props.postOf?.media || props.isEdit) &&
            props.postVidUrls.length != 0 &&
            props.postVidUrls.map((src: string, i: number) => {
              const index = i + props.postImgUrls.length;
              return (
                <MediaRenderer
                  key={index}
                  index={index}
                  type="video"
                  isEdit={props.isEdit}
                  src={src}
                  title={props.mediaTitle[index]}
                  setTitle={props.setMediaTitle}
                  postSettings={props.postSettings}
                  onRemove={removePost}
                  thumbnailUrl={props.thumbnailUrl?.[index]}
                  onThumbnailUpload={handleThumbnailUpload}
                  onRemoveThumbnail={removeThumbnail}
                  tagsCount={
                    props.tagConsent[index]?.length +
                      props.releaseUsers[index]?.length || 0
                  }
                  openTagModal={() => handleOpenTagModal(index)}
                  editPostMedia={props.editPostMedia?.[index]}
                />
              );
            })}

          {props.postOf.vault && (
            <VaultMediaRenderer
              images={props.vaultImages}
              videos={props.vaultVideos}
              thumbnails={props.vaultVideoThumbnail}
              selectVideoThumbnail={handleVaultVideoThumbnail}
              isSettings={props.postSettings}
              removeVaultPost={removeVaultPost}
              removeVaultThumbnail={removeVaultThumbnail}
            />
          )}

          {props.postOf.background && (
            <Background
              removeBg={() => {
                props.setSubmitPost(false);
                props.setPostType({ background: false });
                props.setDisabled({
                  ...props.disabled,
                  ...(EnableMap.background || {}),
                });
              }}
              postSettings={props.postSettings}
              backgroundImage={(file: any) => {
                props.setPostImgData(file);
              }}
              setBg={(
                caption: string,
                background: { backgroundColor: string; textColor: string }
              ) => {
                props.setCaption(caption);
                props.setBg(background);
                // props.bg
                //   ? props.setSubmitPost(true)
                //   : props.setSubmitPost(false);
              }}
            />
          )}
        </div>
        {props.postOf.poll && props.visibility.value !== "Premium" && (
          <div>
            {props.postSettings ? (
              <div className="disable">
                <PollBox
                  showElement={false}
                  removePoll={() => {
                    props.setPostType({ poll: false });
                    props.setSubmitPost(false);
                    dispatchAction(userDataActions.setPostPoll(pollInitState));
                    props.setPoll(pollInitState);
                  }}
                  setPollBox={(poll: any) => props.setPoll(poll)}
                />
              </div>
            ) : (
              <div className="poll-content d-flex p-1">
                <PollBox
                  showElement={true}
                  removePoll={() => {
                    props.setPostType({ poll: false });
                    dispatchAction(userDataActions.setPostPoll(pollInitState));
                    props.setPoll(pollInitState);
                    props.setDisabled({
                      ...props.disabled,
                      ...(EnableMap.poll || {}),
                    });
                  }}
                  setPollBox={(poll: any) => {
                    props.setPoll(poll);
                    // props.setSubmitPost(true);
                  }}
                />
              </div>
            )}
          </div>
        )}

        {!props.postSettings && (
          <>
            {(props.postOf.tag || props.postOf.tagAndRelease) && (
              <div className="tag-content d-flex flex-column p-1 ">
                <TagAndCollab
                  getUsers={{
                    tags: props.tagConsent,
                    setTags: props.setTagConsent,

                    collab: props.collabUsers,
                    setCollab: props.setCollabUsers,
                    releaseUsers: props.releaseUsers,
                    setReleaseUsers: props.setReleaseUsers,
                  }}
                  removeTag={() => props.setPostType({ tag: false })}
                  tagAndCollab={true}
                />
              </div>
            )}
          </>
        )}

        {props.postOf.promote && (
          <div className="tag-content d-flex flex-column p-1 ">
            <PromoteItem
              getShopItems={props.shopItems}
              removeTag={() => {
                props.setPostType({ promote: false });
                props.setDisabled({
                  ...props.disabled,
                  ...(EnableMap.promote || {}),
                });
              }}
            />
          </div>
        )}
      </div>
      {editableImage.length ? (
        <EditImage
          setChildRef={editImageModalRef}
          misc={editableImage}
          index={editableImageIndex}
          setCroppedPostUrl={handleCroppedPostUrl}
        />
      ) : (
        <></>
      )}

      {/* <TagCollab
        setChildRef={tagModalRef}
        releaseUsers={props.releaseUsers}
        setReleaseUsers={props.setReleaseUsers}
        tagConsent={props.tagConsent}
        setTagConsent={props.setTagConsent}
        type={"tag"}
        onclose={() => {
          if (
            props.releaseUsers.length === 0 &&
            props.tagConsent.length === 0
          ) {
            props.setPostOf({ ...props.postOf, tagAndRelease: false });
          }
        }}
        showRelease={(value: ReleaseFormsResponse[]) => {
          props.setReleaseUsers(value);
        }}
        showTagConsent={(value: any[]) => {
          props.setTagConsent(value);
        }}
      />         */}
    </>
  );
};

export default memo(PostContent);
