import type { ServicesTypeValue } from "@/types/services";

import API from ".";

export const AddService = async (body: FormData) =>
  API.post(`${API.USERS}/services`, body) as Promise<any>;

export const GetServices = async (userId: string, type?: ServicesTypeValue) =>
  API.get(
    `${API.USERS}/services?user_id=${userId}` + (type ? `&type=${type}` : "")
  ) as Promise<any>;

export const UpdateService = async (body: FormData, id: string) =>
  API.patch(`${API.USERS}/services/${id}`, body) as Promise<any>;

export const DeleteService = async (id: string) =>
  API.delete(`${API.USERS}/services/${id}`) as Promise<any>;

export const GetPublicServices = async (userId: string) =>
  API.get(`${API.USERS}/public-services?user_id=${userId}`) as Promise<any>;

export const RegisterServiceInterest = async (service_id: string) =>
  API.post(`${API.USERS}/service-interest`, {
    service_id,
  }) as Promise<any>;

export const BuyChatFeeService = async (
  service_id: string,
  payment_type: any
) => {
  return API.post(`${API.USERS}/purchase-chat-fee-service`, {
    service_id,
    ...payment_type,
  }) as Promise<any>;
};
