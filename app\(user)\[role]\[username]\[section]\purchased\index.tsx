import { useEffect, useState } from "react";
import "./index.scss";

import type { GetPurchasedBody } from "@/api/settings";
import { GetPurchased } from "@/api/settings";
import { getProductOrders } from "@/api/shop";
import PurchasedPremium from "@/components/common/purchased/premium";
import PurchasedShopItems from "@/components/common/purchased/shop-item";

export default function Purchased() {
  const [tab, setTab] = useState(0);

  const [premiumItems, setPremiumItems] = useState<GetPurchasedBody[]>([]);
  const [shopItems, setShopItems] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    GetPurchased().then((res) => {
      setLoading(false);
      console.log({ res });
      const premiumFilteredData = res.data.filter(
        (post) => post.meta.type == "PremiumPost"
      );
      setPremiumItems(premiumFilteredData);
    });

    getProductOrders().then((res) => {
      console.log(res);
      setShopItems(res.data);
    });
  }, []);
  return (
    <>
      <div className="purchased-section rounded-top-3 bg-cream">
        <div
          className="d-flex gap-4 align-items-center p-md-3 px-3 py-2"
          id="pills-tab"
          role="tablist"
        >
          <button
            className={`btn tab ${tab === 0 ? "active" : ""}`}
            type="button"
            onClick={() => setTab(0)}
          >
            Pay-To-View content
          </button>
          <button
            className={`btn tab ${tab === 1 ? "active" : ""}`}
            type="button"
            onClick={() => setTab(1)}
          >
            {" "}
            Shop item{" "}
          </button>
        </div>
      </div>

      <div className="bg-body  p-3">
        {tab === 0 && (
          <div className="  ">
            <PurchasedPremium data={premiumItems} loading={loading} />
          </div>
        )}

        {tab === 1 && (
          <div className="">
            <PurchasedShopItems data={shopItems} />
          </div>
        )}
      </div>
    </>
  );
}
