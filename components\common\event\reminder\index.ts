import Swal from "sweetalert2";

import { store } from "@/redux-store/store";
import type { LiveStreamReminderNotification } from "@/types/notification";
import { postMessageNavigator } from "@/utils/post-message-navigator";

export async function showLiveStreamReminder(
  data: LiveStreamReminderNotification["data"]
) {
  const myUserId = store.getState().user.id;
  const isMyEvent = myUserId === data.publisher_id;
  const isBlocked =
    store.getState().block.blockedByMe[data.publisher_id] ||
    store.getState().block.hasBlockedMe[data.publisher_id];

  if (isMyEvent && data.remaining_time === 0) return;
  if (isBlocked) return;

  const res = await Swal.fire({
    imageUrl: "/images/post/fill-calendar.svg",
    imageHeight: 60,
    imageAlt: "End Event",
    title: isMyEvent
      ? `Your event ${data.event_name} will start in ${data.remaining_time} minutes.`
      : data.remaining_time > 0
      ? `${data.publisher_username}'s event ${data.event_name} will start in ${data.remaining_time} minutes.`
      : `${data.publisher_username}'s event ${data.event_name} has started!`,
    text: isMyEvent
      ? "Hey, don't forget to give everything a final check before your event. Your fans are waiting for you!"
      : data.remaining_time > 0
      ? `Hey, just a heads up! Your ${data.publisher_username}'s event ${data.event_name} is kicking off in ${data.remaining_time} minutes. We'll ping you again when it starts!`
      : "Hey, join now so you don't miss out!",
    confirmButtonText: isMyEvent
      ? "Go to my Event"
      : data.remaining_time > 0
      ? "OK"
      : "Go to Event",
    showCloseButton: true,
    customClass: {
      image: "mb-0",
      title: "fw-semibold ",
      actions: "w-75",
      confirmButton: "w-100 btn btn-purple py-2",
    },
    buttonsStyling: false,
    timer: data.remaining_time === 5 && isMyEvent ? 5000 : undefined,
    timerProgressBar: data.remaining_time === 5 && isMyEvent,
  });

  if (!res.isConfirmed && !(data.remaining_time === 5 && isMyEvent)) return;

  if (isMyEvent) {
    postMessageNavigator(`/events/${data.event_id}/device-preview`);
  } else {
    if (data.remaining_time === 0) {
      postMessageNavigator(`/events/${data.event_id}/live`);
    }
  }
}
