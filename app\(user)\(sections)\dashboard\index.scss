.subscriber-tab {
  .active {
    border-bottom: 1px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
  }
  width: 40%;

  @media (max-width: 768px) {
    width: 100%;
  }
}
.subscriber-content-wrapper .nav-pills .active {
  color: var(--primary-color) !important;
  background-color: #ffffff !important;
  border-top: var(--primary-color) !important;
}

.subscriber-content-wrapper .nav-pills button {
  border-bottom: 0 !important;
}

.bar-chart-wrapper tspan {
  font-weight: 550;
}

.dashboard-nav {
  top: 0;
  position: sticky;
  backdrop-filter: blur(3rem);

  padding: 0;
  .nav-active {
    background-color: var(--primary-color) !important;
    color: var(--bg-color) !important;
  }
}

.dashboard-nav .active {
  background-color: #ac1191;
  color: white;
}
.table {
  border-spacing: 0 10px;
  border-collapse: separate;
}

.table-row {
  // border-style: hidden !important;
  border-bottom: 1px solid #ebebec;
  .id-text {
    color: #ac1191;
  }
  td,
  th {
    vertical-align: middle;
  }
}

.bg-complete {
  background-color: #27b1ff;
  color: white;
}

.bg-complete:hover {
  background-color: white;
  color: #27b1ff;
}

.table-row .bg-purple {
  color: white;
}

.apexcharts-legend {
  justify-content: space-between !important;
  top: 0 !important;
  height: 100% !important;
}

.apexcharts-legend-series {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.apexcharts-legend-text {
  font-weight: 600 !important;
  font-size: 0.8rem !important;
}

//pagination

.pagination {
  margin: auto;
  margin-top: 0.5rem;
  display: flex;
  gap: 1rem;
}

.page-link {
  color: #000 !important;
  font-family: "SFDisplay";
  font-weight: 600;

  border-radius: 0.4rem;
}
.page-link.active,
.active > .page-link {
  background-color: #ac1991 !important;
  border-color: #ac1991;
  color: #fff !important;
}

.earning-category-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  @media (max-width: 786px) {
    width: 150vw;
  }
}
.earning-header-wrapper {
  background-color: var(--bg-body-color);
  padding: 0.5rem 1rem;
  border-radius: 8px;

  @media (max-width: 786px) {
    width: 150vw;
  }
}
.special-earning-wrapper {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(4, 1fr);

  @media (max-width: 992px) {
    grid-template-columns: repeat(1, 1fr);
    gap: 0;
  }
}

.color-div {
  width: 20px;
  height: 20px;
  border-color: rgb(255, 255, 255);
  border-radius: 5px;
}
.color-1 {
  background-color: #751363;
}
.color-2 {
  background-color: #ac1191;
}
.color-3 {
  background-color: #a24f93;
}
.color-4 {
  background-color: #dcbdd7;
}
.color-5 {
  background-color: #f3e9f2;
}
.color-6 {
  background-color: #a24f93;
}
.color-7 {
  background-color: #dcbdd7;
}
.color-8 {
  background-color: #a24f93;
}

.services-data-header {
  background-color: #f5f5f6;
  padding: 0.5rem 1rem;
}
