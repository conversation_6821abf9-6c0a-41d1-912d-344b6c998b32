import Image from "next/image";

export default function GroupFee() {
  return (
    <div className="bg-body container d-flex flex-column p-3 rounded-3 gap-3">
      <p className="fs-5 fw-medium mb-0">Subscribe Fee</p>
      <div className="subscribe-fee-wrapper d-flex flex-column">
        <p className="fs-6 fw-medium">Subscribe Fee</p>
        <p className="align-self-start color-medium fs-7">
          Applies when people subscribe to you
        </p>
        <div className="d-flex justify-content-between flex-md-row flex-column gap-2">
          <div className="form-check">
            <input
              className="form-check-input"
              type="checkbox"
              value=""
              id="flexCheckDefault"
            />
            <label className="form-check-label" htmlFor="flexCheckDefault">
              Fee
            </label>
          </div>
          <div className="form-check">
            <input
              className="form-check-input"
              type="checkbox"
              value=""
              id="flexCheckDefault"
            />
            <label className="form-check-label" htmlFor="flexCheckDefault">
              Free trial
            </label>
          </div>
          <div className="form-check">
            <input
              className="form-check-input"
              type="checkbox"
              value=""
              id="flexCheckDefault"
            />
            <label className="form-check-label" htmlFor="flexCheckDefault">
              Free
            </label>
          </div>
        </div>
      </div>
      <div className="subscribe-offers-wrapper d-flex flex-column">
        <p className="fs-6 fw-medium">Subscribe offers</p>

        <p className="align-self-start color-medium fs-7">
          You can set your own or use pre-existing suggestions.
        </p>
        <div className="d-flex flex-column">
          <p className="fs-7 fw-medium mb-1">
            Price per month<span className="color-red">*</span>
          </p>
          <div className="input-group mb-2 d-flex bg-cream w-75 w-sm-100 rounded-3">
            <span
              className="input-group-text bg-transparent border-0 color-dark fw-medium py-0"
              id="basic-addon1"
            >
              $
            </span>
            <input
              type="text"
              className="color-dark bg-transparent border-0 py-2 px-2 shadow-none flex-grow-1 fs-6 fw-medium"
              placeholder="0"
              aria-label="Price"
              aria-describedby="basic-addon1"
            />
          </div>
          <p className="align-self-start color-medium fs-7">
            Minimum $5.00 USD
          </p>
        </div>
      </div>
      <div className="offers-wrapper d-flex flex-column">
        <p className="fs-6 fw-medium mb-0">Months offers</p>

        <p className="align-self-start color-medium fs-7">
          Offer several months of subscription as a discounted bundle.
        </p>
        <div className="d-flex gap-3 flex-column ">
          <div className="subscribe-plan-wrapper d-flex gap-2 align-items-center">
            <div className="input-group bg-cream rounded-3">
              <div className="dropdown w-100 d-flex">
                <button
                  className="btn btn-secondary fs-6 bg-transparent d-flex align-items-center w-100 py-2"
                  type="button"
                  id="dropdownMenuButton"
                  data-bs-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  <span className="flex-grow-1">3 Months</span>
                  <Image
                    src={"/images/svg/chevron-down.svg"}
                    width={25}
                    height={25}
                    alt="dropdown"
                    className="svg-icon"
                  />
                </button>
                <div
                  className="dropdown-menu"
                  aria-labelledby="dropdownMenuButton"
                >
                  <a className="dropdown-item fs-6" href="#">
                    Action
                  </a>
                  <a className="dropdown-item fs-6" href="#">
                    Another action
                  </a>
                  <a className="dropdown-item fs-6" href="#">
                    Something else here
                  </a>
                </div>
              </div>
            </div>
            <div className="input-group d-flex bg-cream rounded-3 flex-nowrap text-overflow-ellipsis">
              <span
                className="input-group-text bg-transparent border-0 color-dark fw-medium py-0"
                id="basic-addon1"
              >
                $
              </span>
              <input
                type="text"
                className="color-dark bg-transparent border-0 py-2 px-2 shadow-none flex-grow-1 fs-6 fw-medium"
                placeholder="0"
                aria-label="Price"
                aria-describedby="basic-addon1"
              />
            </div>
            <Image
              width={25}
              height={25}
              alt="remove-social"
              className="invert"
              src="/images/svg/activepoll.svg"
            />
          </div>
          <div className="subscribe-plan-wrapper d-flex gap-2 align-items-center ">
            <div className="input-group bg-cream rounded-3">
              <div className="dropdown w-100 d-flex">
                <button
                  className="btn btn-secondary fs-6 bg-transparent d-flex align-items-center w-100 py-2"
                  type="button"
                  id="dropdownMenuButton"
                  data-bs-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  <span className="flex-grow-1">3 Months</span>
                  <Image
                    src={"/images/svg/chevron-down.svg"}
                    width={25}
                    height={25}
                    className="svg-icon"
                    alt="dropdown"
                  />
                </button>
                <div
                  className="dropdown-menu"
                  aria-labelledby="dropdownMenuButton"
                >
                  <a className="dropdown-item fs-6" href="#">
                    Action
                  </a>
                  <a className="dropdown-item fs-6" href="#">
                    Another action
                  </a>
                  <a className="dropdown-item fs-6" href="#">
                    Something else here
                  </a>
                </div>
              </div>
            </div>
            <div className="input-group d-flex bg-cream rounded-3 flex-nowrap text-overflow-ellipsis">
              <span
                className="input-group-text bg-transparent border-0 color-dark fw-medium py-0"
                id="basic-addon1"
              >
                $
              </span>
              <input
                type="text"
                className="color-dark bg-transparent border-0 py-2 px-2 shadow-none flex-grow-1 fs-6 fw-medium"
                placeholder="0"
                aria-label="Price"
                aria-describedby="basic-addon1"
              />
            </div>
            <Image
              width={25}
              height={25}
              alt="remove-social"
              className="invert"
              src="/images/svg/activepoll.svg"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
