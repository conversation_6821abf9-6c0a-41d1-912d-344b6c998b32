import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useRef } from "react";
import Swal from "sweetalert2";

import type { APIChannel } from "@/api/channel";
import ActionButton from "@/components/common/action-button";
import { SubscriptionButton } from "@/components/common/subscribed";
import { createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { subscriptionTypeLabels } from "@/types/user";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";

export const ChannelSchema = ({
  data,
  uid,
  from,
}: {
  data: APIChannel;
  uid: string;
  from: "post" | "reel";
}) => {
  const user = useAppSelector((state) => state.user);
  const isOwner = user.id === uid;
  const isMobile = window.innerWidth < 768;
  console.log({ data: data?.subscription });
  if (!data?.is_subscribed && data?.marked_as_deleted && !isOwner) return;
  return (
    <div className={from === "reel" ? "col-12" : `col-lg-6 col-12  `}>
      <Link
        className={`channel-view d-flex flex-column rounded-3 overflow-hidden shadow-dark`}
        href={`/channel/${data.channel_tagname}`}
      >
        <div
          className="channel-pic ratio position-relative"
          style={{ aspectRatio: "43.2/12.6" }}
        >
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            src={getAssetUrl({ media: data.background[0] })}
            alt={"channel cover"}
            width={620}
            height={480}
            className="w-100 h-100 object-fit-cover"
          />
        </div>
        <div className="channel-details">
          <div className="d-flex px-5 mb-1">
            <div className="centered-image flex-shrink-0 mt-4 me-5">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={getAssetUrl({
                  media: data.avatar[0],
                  defaultType: "avatar",
                })}
                alt={"channel image"}
                className="rounded-pill object-fit-cover "
                width={from === "reel" ? 48 : 80}
                height={from === "reel" ? 48 : 80}
                style={{ boxShadow: "0 0 0 3pt var(--bg-color)" }}
              />
            </div>
            <div className="d-flex flex-column py-3 gap-1 align-items-start w-100">
              <div
                className="d-flex align-items-center gap-2"
                style={{ width: "80%", minWidth: "12rem" }}
              >
                <span
                  className={`fw-semibold text-overflow-ellipsis  ${
                    from === "reel" ? "fs-6" : "fs-4"
                  }`}
                  style={{ width: "100%" }}
                >
                  {data.name}{" "}
                </span>
              </div>
              <div
                className={`d-flex flex-wrap column-gap-2 overflow-hidden row-gap-0 color-medium align-items-center  ${
                  from === "reel" ? "color-grey fs-8" : ""
                }`}
              >
                <span
                  className="text-overflow-ellipsis"
                  title={data.channel_tagname}
                  style={{ maxWidth: "7.5rem" }}
                >
                  @{data.channel_tagname}
                </span>
                <span className="group-dot"></span>
                <span>
                  {user.profile.username === data.author.username
                    ? data.counter.post_count
                    : data.counter.post_count - data.counter.private_post_count}
                  &nbsp;posts
                </span>
              </div>
              {}
            </div>
          </div>

          {!isOwner &&
            (!data?.is_subscribed && data?.marked_as_deleted ? (
              <div className=" mb-3 mx-3">
                <ActionButton className="   fs-6 fw-500  text-bg-secondary w-100 pe-none">
                  Marked as deleted
                </ActionButton>
              </div>
            ) : (
              <div>
                {data?.is_subscribed ? (
                  <>
                    <SubscriptionButton
                      is_cancelled={data?.my_subscription_data?.is_cancelled}
                      planPrice={data?.subscription?.[0]?.price}
                      planExpiry={data?.my_subscription_data?.expires_on}
                      planType={data.subscription?.[0]?.validity_type}
                    />
                  </>
                ) : (
                  <div className={` mb-3 mx-3 subscibe-btn  `}>
                    <ActionButton className="d-flex justify-content-between  w-100">
                      <p className="m-0">Subscribe </p>
                      <p className="m-0">
                        {data?.subscription?.[0]?.price > 0
                          ? ` ${formatCurrency(
                              data?.subscription?.[0]?.price
                            )} / ${
                              subscriptionTypeLabels[
                                data.subscription?.[0]?.validity_type
                              ] === "1 month"
                                ? "month"
                                : subscriptionTypeLabels[
                                    data.subscription?.[0]?.validity_type
                                  ] === "12 months"
                                ? "year"
                                : subscriptionTypeLabels[
                                    data.subscription?.[0]?.validity_type
                                  ]
                            }`
                          : " for Free"}
                      </p>
                    </ActionButton>
                  </div>
                )}
                {}
              </div>
            ))}
        </div>
      </Link>
    </div>
  );
};

const ChannelList = ({
  uid,
  list,
  from,
}: {
  uid: string;
  list: APIChannel[];
  from: "post" | "reel";
}) => {
  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );
  const user = useAppSelector((state) => state.user);
  const dispatchAction = useAppDispatch();
  const channelRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const isOwner = user.id === uid;

  const addNewChannel = () => {
    if (isKycCompleted) {
      dispatchAction(createActions.resetChannel());
      router.push("/channel/create");
    } else {
      Swal.fire({
        title: " Your channel will not be visible to others. ",
        text: "Get Verified to visible to others.",
        icon: "warning",
        confirmButtonColor: "#3085d6",
        confirmButtonText: "Skip",
        showDenyButton: true,
        denyButtonColor: "#ac1991",
        denyButtonText: "Get verified",
        showCloseButton: true,
      }).then((result) => {
        if (result.isConfirmed) {
          dispatchAction(createActions.resetChannel());
          router.push("/channel/create");
        } else if (result.isDenied) {
          router.push("/settings/kyc");
        }
      });
    }
  };

  const AddNewBtn = ({ onClick }: { onClick: any }) => (
    <div
      className="d-flex gap-2 align-self-end align-items-center me-2 pointer"
      onClick={onClick}
    >
      <Image
        src={"/images/post/line-plus.svg"}
        alt={""}
        width={18}
        height={18}
      />
      <span>Add new Subscription</span>
    </div>
  );

  if (!list?.length && !isOwner)
    return (
      <div className="bg-body p-3 rounded-3 text-center">No channels yet!</div>
    );

  return (
    <div
      ref={channelRef}
      className={`d-flex flex-column gap-3  p-3 rounded-3 scrollable-division ${
        from === "reel" ? "bg-reel" : " bg-body"
      }`}
    >
      {isOwner ? <AddNewBtn onClick={addNewChannel} /> : <></>}
      <div className="container-fluid g-0 g-md-4 g-lg-0 my-lg-0 my-md-4 ">
        <div className="row g-3">
          {list.length > 0 ? (
            list.map((channel) => (
              <ChannelSchema
                key={channel?._id}
                data={channel}
                uid={uid}
                from={from}
              />
            ))
          ) : (
            <div className="bg-body  rounded-3 text-center">
              No channels yet!
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChannelList;
