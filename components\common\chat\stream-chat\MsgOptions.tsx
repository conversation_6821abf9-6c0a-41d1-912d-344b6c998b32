import {
  forwardRef,
  memo,
  type Ref,
  useImperative<PERSON>andle,
  useRef,
  useState,
} from "react";
import Image from "next/image";
import classNames from "classnames";
import Swal from "sweetalert2";

import { DirectPortal } from "@/components/portals/DirectPortal";
import { KickOutUser } from "@/api/event";
import { useAppSelector } from "@/redux-store/hooks";

interface User {
  sid: string;
  name: string;
}

export interface MsgOptionsRef {
  show: (event: React.MouseEvent | React.TouchEvent, user?: User) => void;
}

interface MsgOptionsProps {
  isMobile?: boolean;
}

function MsgOptionsBase(props: MsgOptionsProps, ref: Ref<MsgOptionsRef>) {
  const [show, setShow] = useState<boolean>(false);
  const userRef = useRef<User | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const targetRef = useRef<HTMLElement | null>(null);
  const eventId = useAppSelector((state) => state.liveEvent._id);

  const showMenu = (
    event: React.MouseEvent | React.TouchEvent,
    user?: User
  ) => {
    event.preventDefault();
    setShow(true);

    const el = event.target as HTMLElement;

    if (user?.sid) {
      userRef.current = user;
    } else {
      userRef.current = {
        sid: el.getAttribute("data-user-id") || "",
        name: el.getAttribute("data-user-name") || "",
      };
    }

    const { x, y, height, width } = el.getBoundingClientRect();

    if (menuRef.current) {
      menuRef.current.style.setProperty(
        "top",
        (props.isMobile ? y : y + height) + "px",
        "important"
      );
      menuRef.current.style.setProperty(
        "left",
        props.isMobile ? "50%" : x + width + "px",
        "important"
      );
    }

    targetRef.current = event.target as HTMLElement;
    event.stopPropagation();
  };

  useImperativeHandle(ref, () => ({ show: showMenu }));

  const onKickOut = async () => {
    setShow(false);
    if (!userRef.current) return;

    const res = await Swal.fire({
      icon: "warning",
      title: "Are you sure?",
      html: `You want to kick out <b>${userRef.current?.name}<b/>?`,
      showCancelButton: true,
      cancelButtonText: "No",
      confirmButtonText: "Yes",
    });

    if (!res.isConfirmed) return;

    await KickOutUser(eventId, userRef.current.sid)
      .then(() => {
        Swal.fire({
          icon: "success",
          title: "User has been kicked out",
          showConfirmButton: false,
          timer: 1500,
        });
      })
      .catch((res) => {
        if (res.message === "User has already been kicked out") {
          Swal.fire({
            icon: "info",
            title: "User has already been kicked out!",
            showConfirmButton: false,
            timer: 1500,
          });
        } else {
          Swal.fire({
            icon: "error",
            title: "Failed to kick out user!",
            showConfirmButton: false,
            timer: 1500,
          });
        }
      });
  };

  return (
    <DirectPortal>
      <div
        ref={menuRef}
        className={classNames("stream-msg-option dropdown-menu show", {
          "d-none": !show,
          "w-75 dropdown-menu-dark": props.isMobile,
        })}
        style={{
          position: "absolute",
          transform: props.isMobile
            ? "translate(-50%, calc(-100% - 0.5em))"
            : "translateX(-100%)",
        }}
      >
        <button
          className={classNames(
            "dropdown-item d-flex align-items-center gap-2 fw-semibold"
          )}
          style={{ color: "red" }}
          onClick={onKickOut}
        >
          <Image
            src="/images/svg/delete-group.svg"
            height={28}
            width={24}
            alt="delete"
          />
          Kick Out
        </button>
      </div>

      <div
        className={classNames(
          "streamchat-msg-menu-overlay position-fixed top-0 start-0 w-100 h-100 z-1",
          { "d-none": !show }
        )}
        onClick={() => setShow(false)}
      ></div>
    </DirectPortal>
  );
}

export const MsgOptions = memo(forwardRef(MsgOptionsBase));
