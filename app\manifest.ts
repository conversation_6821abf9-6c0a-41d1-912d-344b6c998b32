import type { MetadataRoute } from "next";

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    short_name: "<PERSON><PERSON><PERSON><PERSON>",
    orientation: "any",
    display: "standalone",
    dir: "auto",
    lang: "en-US",
    scope: process.env.client,
    start_url: "/",
    theme_color: "#fff",
    background_color: "#fff",
    description:
      "Let’s get KNKY. A dedicated platform for knky creators and fans alike to create and share amazing content. ;)",
    icons: [
      {
        purpose: "maskable",
        sizes: "512x512",
        src: "/manifest/icon512_maskable.png",
        type: "image/png",
      },
      {
        purpose: "any",
        sizes: "512x512",
        src: "/manifest/icon512_rounded.png",
        type: "image/png",
      },
    ],
    related_applications: [
      {
        platform: "webapp",
        url: `${process.env.client}/manifest.webmanifest`,
      },
    ],
    screenshots: [
      {
        src: "/manifest/desktop/image1.jpg",
        type: "image/jpg",
        sizes: "1024x592",
        //@ts-expect-error ts error
        form_factor: "wide",
      },
      {
        src: "/manifest/desktop/image2.jpg",
        type: "image/jpg",
        sizes: "1024x592",
        //@ts-expect-error ts error
        form_factor: "wide",
      },
      {
        src: "/manifest/desktop/image3.jpg",
        type: "image/jpg",
        sizes: "1024x592",
        //@ts-expect-error ts error
        form_factor: "wide",
      },
      {
        src: "/manifest/desktop/image4.jpg",
        type: "image/jpg",
        sizes: "1024x592",
        //@ts-expect-error ts error
        form_factor: "wide",
      },
      {
        src: "/manifest/desktop/image5.jpg",
        type: "image/jpg",
        sizes: "1024x592",
        //@ts-expect-error ts error
        form_factor: "wide",
      },
      {
        src: "/manifest/mobile/image1.jpg",
        type: "image/jpg",
        sizes: "1024x592",
        //@ts-expect-error ts error
        form_factor: "narrow",
      },
      {
        src: "/manifest/mobile/image2.jpg",
        type: "image/jpg",
        sizes: "1024x592",
        //@ts-expect-error ts error
        form_factor: "narrow",
      },
      {
        src: "/manifest/mobile/image5.jpg",
        type: "image/jpg",
        sizes: "1024x592",
        //@ts-expect-error ts error
        form_factor: "narrow",
      },
    ],
    prefer_related_applications: true,
  };
}
