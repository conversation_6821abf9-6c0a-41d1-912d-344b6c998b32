import Image from "@/node_modules/next/image";
import { memo, useEffect, useState } from "react";

import type { Media } from "@/types/media";
import { getAssetUrl } from "@/utils/assets";

type ChildComponentProps = {
  // setValue1: React.Dispatch<React.SetStateAction<File | undefined>>;

  setPreview: React.Dispatch<React.SetStateAction<File | undefined>>;
  setVaultPreview: React.Dispatch<React.SetStateAction<Media | undefined>>;
  postOf: any;
  setShowPreviewComponent?: React.Dispatch<
    React.SetStateAction<boolean | undefined>
  >;
};

const ThumbnailPreviewComponent: React.FC<ChildComponentProps> = ({
  setPreview,
  setVaultPreview,
  postOf,
  setShowPreviewComponent,
}) => {
  const [fileName, setFileName] = useState("file");
  const [postImg, setPostImg] = useState("");
  const [vaultImage, setVaultImage] = useState<Media>();
  const isMobile: boolean = window.innerWidth < 768;

  const fileUpload = async (event: any) => {
    const file = event.target.files[0];

    setPreview(event.target.files[0]);

    if (file && file.type.startsWith("image/")) {
      setFileName(file.name.replace(/\s+/g, ""));
      setPostImg("");
      const blobUrl = URL.createObjectURL(file);
      // setValue2(blobUrl);
      console.log("Thumb: ", { blobUrl });
      setPostImg(blobUrl);
    }
  };

  const vaultPreviewUpload = () => {
    window.KNKY.openVault({
      readonly: true,
      constraints: { type: "image" },
    }).then((res) => {
      // @ts-expect-error Argument of type '(media: Media) => void' is not assignable to parameter of type '(value: VFile, index: number, array: VFile[]) => void'.
      res.medias.map((media: Media) => {
        console.log("UNLOCK: ", media);
        setVaultPreview(media);
        setVaultImage(media);
      });
    });
  };

  useEffect(() => {
    console.log({ vaultImage });
  }, [vaultImage]);

  return (
    <>
      <div className={`bg-body ${!isMobile && "rounded-3"}`}>
        <div className=" border-bottom ">
          <div className=" d-flex flex-column   p-3 ">
            <div className="d-flex gap-3 align-items-center">
              {isMobile && (
                <div className="mb-2">
                  <Image
                    src={"/images/common/back-arrow.svg"}
                    height={20}
                    width={20}
                    alt="back"
                    className="pointer"
                    onClick={() => setShowPreviewComponent!(false)}
                  />
                </div>
              )}
              <h5 className="fw-semibold">Unlock thumbnail & preview</h5>
            </div>
            <span className="fw-500 fs-6 color-black">
              This will be visible to your fans until they purchase.
            </span>
            {!(postImg || vaultImage) ? (
              <div
                onClick={() => {
                  postOf.vault ? vaultPreviewUpload() : null;
                }}
                className="d-flex flex-column align-items-center justify-content-center text-center position-relative ratio-9x5 border border-1 border-style-dashed  mt-3 gap-2"
              >
                <Image
                  src={"/images/post/line-plus.svg"}
                  alt=""
                  height={24}
                  width={24}
                />
                <div className="fs-6 fw-500 color-black">Upload Image</div>
                {postOf.media && (
                  <input
                    type="file"
                    className="media-input position-absolute top-0 bottom-0 start-0 end-0 opacity-0 z-3  pointer"
                    accept="image/*"
                    onChange={fileUpload}
                  />
                )}
              </div>
            ) : (
              <div className="d-flex align-items-center justify-content-center p-3 pb-1 position-relative ">
                <img
                  src={
                    postOf.media ? postImg : getAssetUrl({ media: vaultImage })
                  }
                  alt=""
                  height={232}
                  width={232}
                  style={{ aspectRatio: "3/2", objectFit: "contain" }}
                />
              </div>
            )}
          </div>
        </div>
        {(postImg || vaultImage) && (
          <div>
            <div
              onClick={() => {
                postOf.vault ? vaultPreviewUpload() : null;
              }}
              className="d-flex gap-2 align-items-center justify-content-center py-3 position-relative"
            >
              <Image
                src={"/images/post/image-select-icon.svg"}
                alt=""
                height={19}
                width={19}
              />
              <span className="color-primary fs-7 fw-500">Change preview</span>
              {postOf.media && (
                <input
                  type="file"
                  className="media-input position-absolute top-0 bottom-0 start-0 end-0 opacity-0 z-3  pointer"
                  accept="image/*"
                  onChange={fileUpload}
                  // onMouseEnter={() => setBtnHover("media-button")}
                  // onMouseLeave={() => setBtnHover("")}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export const ThumbnailPreview = memo(ThumbnailPreviewComponent);
