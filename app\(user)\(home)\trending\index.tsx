"use client";

import "./index.scss";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

import type { BadgesType, MinSubscription } from "@/api/post";
import { GetFeaturedUserProfiles } from "@/api/user";
import { OnlineDot } from "@/components/common/OnlineDot";
import ActionButton from "@/components/common/action-button";
import Badges from "@/components/common/badges";
import Follow from "@/components/common/buttons/follow";
import TrendingShimmer from "@/components/common/shimmer/all-shimmer/trending";
import { useIsLive } from "@/components/common/useIsLive";
import { Divider } from "@/components/common/wrapper";
import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";
import { subscriptionTypeLabels, type UserProfile } from "@/types/user";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";
import { formatWithCommas } from "@/utils/number";

export interface UserFeatured {
  id: string;
  back: string;
  profile: string;
  subscribed: boolean;
  followed: boolean;
  role: string;
  badges: BadgesType;
  name: string;
  display_name: string;
  username: string;
  price: number;
  followers: number;
  videos: number;
  photos: number;
  minSubscription: MinSubscription;
  is_subscribed?: boolean;
  has_purchasable_shop_item?: boolean;
  f_name?: string;
  follows_you?: boolean;
  followed_by_you?: boolean;
  shop_count?: number;
  totalLikes?: number;
}

function transformData(profiles: UserProfile[]): UserFeatured[] {
  return profiles.map((profile) => ({
    back: getAssetUrl({ media: profile.background[0], variation: "thumb" }),
    profile: getAssetUrl({
      media: profile.avatar[0],
      defaultType: "avatar",
    }),
    //  To Make a mechanism to save all the subscriptions and following peoples for the user
    subscribed: false,
    followed: false,
    role: profile.user_type.toLowerCase(),
    badges: profile?.badges,
    name: `${profile.f_name} ${profile.l_name}`,
    username: profile.username,
    price: profile.chat_fee?.[0]?.price || 0,
    followers: profile.follower_count,
    videos: profile.video_count || 0,
    photos: profile.image_count || 0,
    id: profile._id,
    minSubscription: profile?.min_subscription,
    is_subscribed: profile?.is_subscribed,
    f_name: profile?.f_name,
    display_name: profile?.display_name,
    has_purchasable_shop_item: profile?.has_purchasable_shop_item,
    followed_by_you: profile?.followed_by_you,
    follows_you: profile?.follows_you,
    shop_count: profile?.shop_count,
    totalLikes: profile?.totalLikes,
  }));
}

const UserExtra = ({ icon, count }: { icon: string; count: number }) => {
  return (
    <div className="d-flex flex-column align-items-center">
      <Image
        width={24}
        height={24}
        alt=""
        src={
          icon === "heart"
            ? `/images/svg/${icon}.svg`
            : `/images/home/<USER>
        }
        className={`${icon === "heart" ? "grayscale" : ""}`}
      />
      <div className="fw-medium color-bold">{formatWithCommas(count)}</div>
    </div>
  );
};

export const Featured = ({ user }: { user: UserFeatured }) => {
  const router = useRouter();
  const isGuest = useAppSelector((state) => state?.user?.role) === "guest";
  const route = useRouter();
  const myId = useAppSelector((state) => state?.user?.id);
  const isLive = useIsLive(user.id);
  return (
    <div className="user-featured d-flex flex-column rounded-md-3 bg-body overflow-hidden flex-grow-1 position-relative">
      <Link href={`/${user.role}/${user.username}`} className="user-head w-100">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          width={256}
          height={128}
          className="w-100 h-100 object-fit-cover"
          alt=""
          src={user.back}
        />
      </Link>

      <div className="user-content d-flex flex-column gap-3 align-items-center justify-content-center pb-3 px-3">
        <Link
          href={`/${user.role}/${user.username}`}
          className="user-details w-100"
        >
          <div className="user-info d-flex flex-column align-items-center gap-1">
            <div className="user-image centered-image">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                width={128}
                height={128}
                src={user.profile}
                alt=""
                className={`qwerty object-fit-cover rounded-pill bg-body ${
                  isLive && "is-live"
                }`}
              />
              <OnlineDot
                userId={user.id}
                style={{
                  position: "absolute",
                  bottom: "15%",
                  right: window.innerWidth > 786 ? "-40%" : "-44%",
                }}
              />
              {isLive && (
                <div className="position-absolute bg-red fw-semibold rounded-1 px-2 fs-8 live-badge-trending">
                  <div className="text-white">LIVE</div>
                </div>
              )}
            </div>
            <div
              className={`user-identity d-flex w-100 align-items-center gap-2 justify-content-center  ${
                isLive && "mt-2"
              } `}
            >
              <div className="d-flex justify-content-center align-items-center gap-1 ">
                <div className="user-name w-100 text-center text-overflow-ellipsis fs-4">
                  <Link href={`/${user.role}/${user.username}`}>
                    {user.display_name}
                  </Link>
                </div>
                <Badges array={user.badges} />
              </div>
            </div>
            <div className="user-uname fs-6 fw-medium color-bold w-100 text-center text-overflow-ellipsis">
              <Link href={`/${user.role}/${user.username}`}>
                @{user.username}
              </Link>
            </div>
            <div
              className={`user-price fs-6 color-medium ${
                user?.minSubscription?.price === undefined ? "invisible" : ""
              }`}
            >
              From &nbsp;
              <span className="fw-bold color-bold">
                {formatCurrency(user?.minSubscription?.price)}
              </span>
              &nbsp;/{" "}
              {subscriptionTypeLabels[user?.minSubscription?.validity_type]}
            </div>
          </div>
        </Link>

        <div className="user-extra d-flex gap-3 align-items-center justify-content-around w-100 px-3">
          <UserExtra icon="heart" count={user.totalLikes || 0} />
          <Divider direction="end" />
          <UserExtra icon="image" count={user.photos} />
          <Divider direction="end" />
          <UserExtra icon="video" count={user.videos} />
        </div>

        {user.id !== myId && (
          <div className="user-actions d-flex  gap-3 w-100">
            <Follow
              visible={true}
              author={user.id}
              followed_by_you={user.followed_by_you!}
            />
            <ActionButton
              type={user?.is_subscribed ? "outline" : "solid"}
              variant={user?.is_subscribed ? "dark" : "primary"}
              className={
                user?.minSubscription?.price === undefined ? "d-none " : "w-100"
              }
              onClick={() => {
                isGuest
                  ? ModalService.open("SIGN_IN")
                  : route.push(
                      `/${user.role}/${user.username}/subs?type=channels&scroll=down`
                    );
              }}
            >
              {user?.is_subscribed ? "Subscribed" : "View Subs"}
            </ActionButton>
          </div>
        )}
        {user.id === myId && (
          <>
            <ActionButton
              onClick={() => {
                isGuest
                  ? ModalService.open("SIGN_IN")
                  : route.push(`/${user.role}/${user.username}`);
              }}
              variant="dark"
              type="outline"
              className="w-100"
            >
              View my profile
            </ActionButton>
          </>
        )}
      </div>
      {user?.has_purchasable_shop_item ? (
        <div
          className="position-absolute top-0 start-0 pointer"
          onClick={(e) => {
            e.stopPropagation();
            router.push(`/creator/${user.username}/shop-item?scroll=down`);
          }}
        >
          <div
            className="d-flex justify-content-center align-items-center gap-1 px-3 py-1"
            style={{
              background: "rgba(39, 177, 255, 1)",
              borderBottomRightRadius: "0.5rem",
            }}
          >
            <Image
              src={"/images/common/shop-item.svg"}
              alt="Shop item"
              width={16}
              height={16}
            />
            <span className="fw-bold fs-7 text-white">Shop</span>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default function FeaturedList() {
  const [users, setUsers] = useState<UserFeatured[]>([]);
  const [loading, setLoading] = useState(true);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [page, setPage] = useState(1);

  useEffect(() => {
    GetFeaturedUserProfiles(page)
      .then((response) => {
        setUsers(transformData(response.data));
        setLoading(false);
      })
      .catch((err) => {
        console.error(err);
      });
  }, []);

  const loadMoreData = async () => {
    const newPage = page + 1;
    setPage(newPage);
    const response = await GetFeaturedUserProfiles(newPage);

    const newData = transformData(response.data);

    if (response.data?.length) {
      setUsers((prev) => [...prev, ...newData]);
    } else {
      setHasMoreData(false);
    }

    // setHasMoreData(response.data.length === 20);
    setLoading(false);
  };

  // const { restoreScroll } = useScrollRestore({
  //   key: "trending",
  //   targetScrollable: "#infiniteScrollHome",
  // });

  // useEffect(() => {
  //   if (users.length > 0) {
  //     setTimeout(() => {
  //       restoreScroll();
  //     }, 200);
  //   }
  // }, [users]);

  const blockedByMe = useAppSelector((state) => state?.block?.blockedByMe);
  const hasBlockedMe = useAppSelector((state) => state?.block?.hasBlockedMe);
  // !removing h-100 to avoid big gaps between trending cards on larger height's devices
  return (
    <div className="overflow-y-auto container-fluid g-0 g-md-4 g-lg-0 users-featured-list my-lg-0 my-md-4">
      {loading ? (
        <TrendingShimmer />
      ) : (
        <>
          <InfiniteScroll
            className="d-flex flex-column gap-3 "
            style={{ overflowX: "hidden" }}
            next={() => {
              loadMoreData();
            }}
            dataLength={users.length}
            hasMore={hasMoreData}
            loader={<TrendingShimmer />}
            scrollThreshold={0.6}
            scrollableTarget={"infiniteScrollHome"}
            endMessage={
              <div className="text-center bg-body rounded p-3 ">
                {users.length
                  ? "Yay! You have seen it all."
                  : "No Trending Profiles "}
              </div>
            }
          >
            {" "}
            <div className="row g-3 pb-2 me-0 position-relative">
              {users.map((user) => {
                if (blockedByMe[user.id] || hasBlockedMe[user.id]) {
                  return;
                }

                return (
                  <div
                    key={user.id}
                    className={`${"col-lg-4 col-md-6 col-sm-12 pe-lg-2 pe-0"}`}
                  >
                    <Featured user={user} />
                  </div>
                );
              })}
            </div>
          </InfiniteScroll>
        </>
      )}
    </div>
  );
}
