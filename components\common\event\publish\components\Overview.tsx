import Image from "next/image";
import { useEffect, useRef, useState } from "react";

import { GetChatStats } from "@/api/chat";
import { GetEvent } from "@/api/event";
import { EventBridge } from "@/app/event-bridge";
import { liveEventActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { formatCurrency, formatNumberWithoutCurrency } from "@/utils/formatter";
import { dateDiffInHhMmSs } from "@/utils/number";

import { FinishBtn } from "./FinishBtn";

export function Overview() {
  const dispatch = useAppDispatch();
  const event = useAppSelector((state) => state.liveEvent);
  const [viewers, setViewers] = useState(0);
  const [reactions, setReactions] = useState(0);
  const [msgs, setMsgs] = useState(0);
  const time = useRef(dateDiffInHhMmSs(new Date(event.scheduled_on))).current;

  const details = [
    {
      img: "/images/svg/fill-show-primary.svg",
      count: formatNumberWithoutCurrency(viewers),
      label: "Viewers",
    },
    {
      img: "/images/post/fill-heart.svg",
      count: formatNumberWithoutCurrency(reactions),
      label: "Reactions",
    },
    {
      img: "/images/svg/fill-message.svg",
      count: formatNumberWithoutCurrency(msgs),
      label: "Messages",
    },
    { img: "/images/svg/fill-share.svg", count: 0, label: "Shares" },
    {
      img: "/images/post/fill-money.svg",
      count: "~" + formatCurrency(event.total_tips),
      label: "Tips",
    },
    {
      img: "/images/post/fill-ticket.svg",
      count:
        formatNumberWithoutCurrency(event.sold_ticket_count) +
        " / " +
        (event.quantity_type === "Unlimited" ? "∞" : event.quantity),
      label: `Tickets Sold (${
        event.ticket_type === "Fee" ? formatCurrency(event.price) : "Free"
      })`,
    },
  ];

  useEffect(() => {
    (async () => {
      const _event = (await GetEvent(event._id)).data;
      dispatch(liveEventActions.setLiveEvent(_event));

      const res = await EventBridge.request("LiveStream/Stats", {
        event_id: _event._id,
      });
      setViewers(res.viewers - 1);
      setReactions(res.reactions);

      const messageCount = (
        await GetChatStats(_event.event_converse_channel_id)
      ).data.messageCount;
      setMsgs(messageCount);
    })();
  }, []);

  return (
    <div className="container-fluid h-100 d-flex flex-column-reverse flex-md-column bg-body rounded-md-3 p-3 user-select-none">
      <div className="row fs-4 fw-medium mb-0 mb-md-3">
        <div className="text-center my-3 my-md-0">Total time: {time}</div>
        <FinishBtn className="d-block d-md-none mx-auto" />
      </div>
      <div className="row g-2 g-md-3">
        {details.map((d, i) => (
          <div className="col-6" key={i}>
            <div className="d-flex flex-column align-items-center py-2 border border-1 border-secondary-subtle rounded-3">
              <Image
                src={d.img}
                alt={d.label}
                width={32}
                height={32}
                className="mb-1"
              />
              <span>{d.count}</span>
              <span>{d.label}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
