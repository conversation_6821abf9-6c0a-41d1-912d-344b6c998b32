import classNames from "classnames";

import {
  AudioSent,
  VideoSent,
} from "@/components/matchmaker/match-cards/utils/svg";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";

import ReadMoreContent from "../../../../read-more-content";
import { RatingSent } from "../../rating-request";

const PromotionSender = ({ message }: { message: MessageInterface }) => {
  const isRequestSent = message?.meta?.requestAccept === "sent";

  return (
    <div className="sender d-grid h-100 mt-1 ms-auto w-100 px-2 mt-2 rounded-2 text-break ">
      <div className="d-flex flex-row-reverse position-absolute top-0 end-0 m-2">
        {!isRequestSent && (
          <div
            className={classNames("badge p-2", {
              "fs-10": window.innerWidth < 576,
            })}
            style={{
              color:
                message?.meta?.requestAccept === false
                  ? "rgba(245, 34, 45, 1)"
                  : "rgba(86, 194, 45, 1)",
              border: `1px solid ${
                message?.meta?.requestAccept === false
                  ? "rgba(245, 34, 45, 1)"
                  : "rgba(86, 194, 45, 1)"
              }`,
              backgroundColor:
                message?.meta?.requestAccept === false
                  ? "rgba(245, 34, 45, 0.2)"
                  : "rgba(86, 194, 45, 0.2)",
            }}
          >
            {message?.meta?.requestAccept === false ? "Declined" : "Accepted"}
          </div>
        )}
        {isRequestSent && (
          <div
            className={classNames("badge p-2", {
              "fs-10": window.innerWidth < 576,
            })}
            style={{
              color: "rgba(255, 168, 0, 1)",
              border: "1px solid rgba(255, 168, 0, 1)",
              backgroundColor: "rgba(255, 168, 0, 0.12)",
            }}
          >
            Waiting for fan
          </div>
        )}
      </div>
      <div className="d-flex align-items-center gap-2">
        <div>
          {message?.meta?.type.toLowerCase() === "voice" ? (
            message?.meta?.request_icon &&
            message?.meta?.request_icon?.length > 0 ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={
                  message?.meta?.request_icon?.length! > 0
                    ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                    : ""
                }
                height={72}
                width={72}
                alt="Request Icon"
                className="rounded"
              />
            ) : (
              <AudioSent />
            )
          ) : message?.meta?.type.toLowerCase() === "video" ? (
            message?.meta?.request_icon &&
            message?.meta?.request_icon?.length > 0 ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={
                  message?.meta?.request_icon?.length! > 0
                    ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                    : ""
                }
                height={72}
                width={72}
                alt="Request Icon"
                className="rounded"
              />
            ) : (
              <VideoSent />
            )
          ) : message?.meta?.type === "CUSTOM-SERVICE" ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={
                message?.meta?.request_icon?.length! > 0
                  ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                  : "/images/chat/service-icon.png"
              }
              height={72}
              width={72}
              alt="Request Icon"
              className="rounded"
            />
          ) : message?.meta?.request_icon &&
            message?.meta?.request_icon?.length > 0 ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={
                message?.meta?.request_icon?.length! > 0
                  ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                  : ""
              }
              height={72}
              width={72}
              alt="Request Icon"
              className="rounded"
            />
          ) : (
            <RatingSent />
          )}
        </div>
        <div>
          <div className="fw-bold">
            {message?.meta?.type.toLowerCase() === "voice"
              ? "Voice call"
              : message?.meta?.type.toLowerCase() === "video"
              ? message?.meta?.type === "CUSTOM-SERVICE"
                ? "Request"
                : "Video call"
              : "Ratings"}
          </div>
          <div className="d-flex gap-2">
            <div>
              {["VOICE", "VIDEO"].includes(message?.meta?.type)
                ? `For ${
                    Math.ceil((message?.meta?.duration || 0) / 60) >= 10
                      ? Math.ceil((message?.meta?.duration || 0) / 60)
                      : `${Math.ceil((message?.meta?.duration || 0) / 60)}`
                  } mins`
                : "1 turn"}
            </div>
            <div className="vr"></div>
            <div>
              {formatCurrency(
                (() => {
                  const price = message?.meta?.price;
                  const hasDiscount = message?.meta?.has_discount;
                  const discount = message?.meta?.discount;

                  if (message?.meta?.offered_amount)
                    return message?.meta?.offered_amount;

                  if (
                    price !== undefined &&
                    hasDiscount &&
                    discount?.discount_value !== undefined
                  ) {
                    return discount?.discount_value;
                  }

                  return price ?? 0;
                })()
              )}
            </div>
          </div>
        </div>
      </div>
      <ReadMoreContent
        text={`${message?.meta?.request_note || ""}`}
        textColor="black"
        classes="mt-1 fw-bold"
      />
    </div>
  );
};

export default PromotionSender;
