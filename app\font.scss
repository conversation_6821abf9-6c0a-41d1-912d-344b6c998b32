/* latin-ext */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url(/fonts/SFDisplay-Thin.otf) format("opentype");
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF,
    U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url(/fonts/SFDisplay-Thin.otf) format("opentype");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/fonts/SFDisplay-Light.otf) format("opentype");
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF,
    U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/fonts/SFDisplay-Light.otf) format("opentype");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/fonts/SFDisplay-Regular.otf) format("opentype");
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF,
    U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/fonts/SFDisplay-Regular.otf) format("opentype");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/fonts/SFDisplay-Medium.otf) format("opentype");
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF,
    U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/fonts/SFDisplay-Medium.otf) format("opentype");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/fonts/SFDisplay-Semibold.otf) format("opentype");
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF,
    U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/fonts/SFDisplay-Semibold.otf) format("opentype");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/fonts/SFDisplay-Bold.otf) format("opentype");
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF,
    U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/fonts/SFDisplay-Bold.otf) format("opentype");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(/fonts/SFDisplay-Heavy.otf) format("opentype");
  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF,
    U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "SFDisplay";
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(/fonts/SFDisplay-Heavy.otf) format("opentype");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191,
    U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
