import Link from "next/link";

const SubsInfo = ({ isOwner }: { isOwner: boolean }) => {
  const title = isOwner
    ? "Let’s create your first Sub!"
    : "No Subscriptions Yet";
  const subTitle1 = isOwner
    ? "Create unlimited Channels and Collabs by <PERSON>, <PERSON><PERSON> (niche).. or both!"
    : "This creator hasn’t set up any subscriptions yet.";
  const subTitle2 = isOwner
    ? "Expand your reach, excite your fans and manage engagement like never before."
    : "Stay tuned for exclusive content and exciting perks as soon as they launch their first subscription plan!";

  return (
    <div className="d-flex flex-column gap-3 justify-content-center align-items-center bg-body py-5 rounded-3">
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src="/images/common/subs-info.png"
        width={148}
        height={148}
        alt="no subs"
      />

      <div className="d-flex flex-column gap-2 align-items-center mt-3">
        <div className="d-flex flex-column text-center">
          <h4 className="fw-semibold">{title}</h4>
          <p className="fs-7 color-black m-0 fw-medium">
            {subTitle1} <br />
            {subTitle2}
          </p>
        </div>

        <div className="d-flex flex-column flex-md-row gap-3 align-items-center mt-3">
          {isOwner ? (
            <>
              <Link
                href={"/channel/create"}
                className="btn btn-purple fw-semibold "
                style={{ minWidth: "13rem" }}
              >
                Create channel
              </Link>
              <Link
                href={"/collab/create"}
                className="btn btn-black fw-semibold "
                style={{ minWidth: "13rem" }}
              >
                Create collab profile
              </Link>
            </>
          ) : (
            <Link
              href={"/trending"}
              className="btn btn-purple fw-semibold "
              style={{ minWidth: "13rem" }}
            >
              Explore more creators
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubsInfo;
