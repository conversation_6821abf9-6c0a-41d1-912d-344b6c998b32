import { memo, useEffect, useMemo, useRef, useState } from "react";
import Image from "next/image";
import classNames from "classnames";
import { useAudioPlayback } from "@livekit/components-react";
import screenfull from "screenfull";
import { type Tooltip } from "bootstrap";

import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { liveEventActions } from "@/redux-store/actions";
import { ExitEvent } from "@/components/common/event/ExitEvent";

function PlaybackControlsAndFallbackImgBase(props: {
  videoFallback: undefined | boolean;
  audioFallback: undefined | boolean;
  isMobile: boolean;
  controlsDivClassName?: string;
  fallbackImgClassName?: string;
}) {
  const dispatch = useAppDispatch();
  const expanded = useAppSelector((state) => state.liveEvent.expanded);
  const muted = useAppSelector((state) => state.liveEvent.muted);
  const isFakeFullscreen = useAppSelector((s) => s.liveEvent.isFakeFullscreen);
  const isStreamPortrait = useAppSelector((s) => s.liveEvent.isStreamPortrait);
  const showFSBtn = useMemo(() => {
    return screenfull.isEnabled || (!screenfull.isEnabled && props.isMobile);
  }, [props.isMobile]);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const isScreenOrientationLockPossible = useRef(
    // @ts-expect-error - TS doesn't know about screen orientation lock
    screen.orientation && screen.orientation?.lock
  ).current;
  const lastOrientation = useRef({
    type: screen.orientation?.type,
    angle: screen.orientation?.angle,
    changed: false,
  });
  const tooltipsRef = useRef<Tooltip[]>([]);

  const { canPlayAudio, startAudio } = useAudioPlayback();

  useEffect(() => {
    if (!screenfull.isEnabled) return;

    const onChange = async () => {
      setIsFullScreen(screenfull.isFullscreen);

      if (
        props.isMobile &&
        isScreenOrientationLockPossible &&
        !isStreamPortrait
      ) {
        if (screenfull.isFullscreen) {
          lastOrientation.current = {
            type: screen.orientation.type,
            angle: screen.orientation.angle,
            changed: true,
          };
          // @ts-expect-error - TS doesn't know about screen orientation lock
          await screen.orientation.lock("landscape");
        } else if (lastOrientation.current.changed) {
          const type =
            lastOrientation.current.type === "portrait-primary" &&
            lastOrientation.current.angle === 0
              ? "portrait"
              : lastOrientation.current.type;
          // @ts-expect-error - TS doesn't know about screen orientation lock
          await screen.orientation.lock(type);
          lastOrientation.current.changed = false;
          screen.orientation.unlock();
        }
      }
    };

    screenfull.onchange(onChange);

    return () => {
      screenfull.off("change", onChange);
    };
  }, [isStreamPortrait]);

  //#region <Tooltips>
  useEffect(() => {
    const tooltipTriggerList = document.querySelectorAll(
      '[data-bs-toggle="tooltip-ep"]'
    );
    tooltipsRef.current = [...tooltipTriggerList].map(
      (el) => new window.bootstrap.Tooltip(el)
    );
    return () => tooltipsRef.current.forEach((t) => t.dispose());
  }, []);

  useEffect(() => {
    tooltipsRef.current.forEach((t) => t.hide());
  }, [isFullScreen, expanded]);

  useEffect(() => {
    tooltipsRef.current[0].setContent({
      ".tooltip-inner": expanded ? "Collapse" : "Expand",
    });
  }, [expanded]);
  //#endregion

  const toggleMuted = async () => {
    if (canPlayAudio) {
      dispatch(liveEventActions.toggleMuted());
    } else {
      await startAudio();
    }
  };

  const goFullScreen = async () => {
    let id;
    if (props.isMobile) {
      if (isScreenOrientationLockPossible) id = "stream-video-container";
      else id = "stream-video";
    } else id = "stream-video-container";

    const el = document.getElementById(id);
    if (!el) return;
    await screenfull.toggle(el);
  };

  const goFakeFullScreen = () => {
    setIsFullScreen((b) => !b);
    dispatch(liveEventActions.setFakeFullscreen(!isFullScreen));
  };

  const onFullScreenClick = () => {
    if (screenfull.isEnabled) goFullScreen();
    else goFakeFullScreen();
  };

  const onExitBtnClick = (exit: () => void) => {
    if (isFullScreen || isFakeFullscreen) {
      onFullScreenClick();
      setTimeout(exit, 300);
    } else exit();
  };

  return (
    <>
      <Image
        src="/images/common/default.svg"
        width={72}
        height={72}
        alt="default user logo"
        className={classNames(
          "position-absolute top-50 start-50 translate-middle z-2",
          { invisible: !props.videoFallback },
          props.fallbackImgClassName
        )}
      />

      <div
        className={classNames(
          "position-absolute top-0 end-0 z-2 d-flex flex-column align-items-center gap-2 p-3",
          props.controlsDivClassName
        )}
      >
        <ExitEvent type="playback" onClick={onExitBtnClick} />

        <button
          className="btn bg-black p-2 rounded-3 d-none d-xxl-block"
          style={{ minWidth: "fit-content" }}
          onClick={() => dispatch(liveEventActions.toggleExpanded())}
          data-bs-toggle="tooltip-ep"
          data-bs-title="Expand"
          data-bs-delay='{"show": 650, "hide": 0}'
        >
          <Image
            src={`/images/event/unfold_${expanded ? "less" : "more"}.svg`}
            alt="expand"
            width={28}
            height={28}
            priority
          />
        </button>

        <button
          className={classNames(
            "btn p-2 rounded-3",
            muted || !canPlayAudio ? "bg-danger" : "bg-black"
          )}
          style={{ minWidth: "fit-content" }}
          onClick={toggleMuted}
        >
          <Image
            src={`/images/event/volume_${
              muted || !canPlayAudio ? "off" : "up"
            }.svg`}
            alt="mute"
            width={28}
            height={28}
            priority
          />
        </button>

        {showFSBtn && (
          <button
            className={"btn bg-black p-2 rounded-3"}
            style={{ minWidth: "fit-content" }}
            onClick={onFullScreenClick}
            data-bs-toggle="tooltip-ep"
            data-bs-title="Fullscreen"
            data-bs-delay='{"show": 650, "hide": 0}'
          >
            <Image
              src={`/images/event/fullscreen${isFullScreen ? "_exit" : ""}.svg`}
              alt="fullscreen"
              width={28}
              height={28}
              priority
            />
          </button>
        )}

        <Image
          src="/images/event/mic_off_white.svg"
          width={24}
          height={24}
          alt="speaker mic off"
          className={classNames("bg-danger rounded-circle p-1", {
            invisible: !props.audioFallback,
          })}
          priority
        />
      </div>
    </>
  );
}

export const PlaybackControlsAndFallbackImg = memo(
  PlaybackControlsAndFallbackImgBase
);
