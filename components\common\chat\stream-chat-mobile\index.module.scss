.stream-chat {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100vw;
  padding: 0 !important;

  .stream-comments {
    height: 14em;
    overflow: scroll;
    background-color: rgba(0, 0, 0, 0.25);
    -webkit-mask-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 1) 10%,
      rgba(0, 0, 0, 1) 100%
    );
    mask-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 1) 10%,
      rgba(0, 0, 0, 1) 100%
    );

    .comment:first-child {
      margin-top: auto;
    }
  }

  .send-tip-btn {
    border: none !important;
    padding: 0 !important;

    img {
      scale: 1 !important;
      width: 28px !important;
      height: 28px !important;
    }
  }

  .share-btn {
    padding: 0 !important;
  }
}

.placeholder-white::placeholder {
  color: white;
}
