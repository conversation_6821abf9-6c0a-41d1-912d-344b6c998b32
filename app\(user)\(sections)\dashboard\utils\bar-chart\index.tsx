import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

import { formatCurrency } from "@/utils/formatter";

const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: true,
});

// import ReactApexChart from "react-apexcharts";

interface BarChart {
  series: { name: string; data: number[] }[];
  minTick: string | number;
  maxTick: string | number;
  showCurrency?: boolean;
  colors: string[];
  barWidth: string;
  categories?: string[];
}

export default function BarChart(barProps: BarChart) {
  const [chartOptions, setChartOptions] = useState<any>({});
  const [chartSeries, setChartSeries] = useState<any>([]);
  console.log();
  const [key, setKey] = useState(0);

  useEffect(() => {
    const options: any = {
      chart: {
        type: "bar",
        stacked: false,
        toolbar: { show: false },
      },
      plotOptions: {
        bar: {
          columnWidth: barProps.barWidth, // Adjust the width of the bars (0.5 means 50% of available space)
        },
      },
      xaxis: {
        categories: barProps.categories || [],
      },
      legend: {
        show: false,
      },
      dataLabels: {
        enabled: false,
      },
      yaxis: {
        labels: {
          show: true,
          formatter: function (value: any) {
            return barProps.showCurrency ? formatCurrency(value) : value;
          },
        },
        opposite: true, // Show y-axis on the right side
        axisTicks: {
          show: true, // Show y-axis ticks
        },
        tickAmount: 5, // Number of ticks
        min: barProps.minTick, // Min value for the y-axis
        max: barProps.maxTick, // Max value for the y-axis
      },
      colors: barProps.colors,
    };

    if (barProps.series && barProps.series[0]?.data?.length === 24) {
      options.responsive = [
        {
          breakpoint: 600, // Mobile devices
          options: {
            xaxis: {
              tickAmount: 8,
              padding: 4,
              labels: {
                rotate: -0,
                style: {
                  fontSize: "10px",
                },
              },
            },
          },
        },
      ];
    }

    setChartOptions(options);
    setChartSeries(barProps.series);
    setKey((prevKey) => prevKey + 1);
  }, [barProps.series, barProps.maxTick]);

  return (
    <div className="bar-chart-wrapper w-100 scrollable">
      <ReactApexChart
        key={key}
        options={chartOptions}
        series={chartSeries}
        type="bar"
        height={200}
      />
    </div>
  );
}
