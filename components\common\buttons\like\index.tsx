import "./index.scss";

import { useEffect, useRef, useState } from "react";

import { UserPostLike } from "@/api/post";
import { LikeStory } from "@/api/story";
import { ModalService } from "@/components/modals";
import { connectionsActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

import { PostActionBtnClass } from "../../class";

interface PostAction {
  postId: string;
  cls?: string;
  likeDetails?: any;
  setLikedReel?: any;
  type: string;
  postLiked?: (likeType: any) => void;
  from?: string;
  counter?: number;
}

interface PostLikeBtnState {
  cls: string;
  img: string;
  title: string;
}

const View: {
  [key: string]: PostLikeBtnState;
} = {
  inactive: {
    cls: "",
    img: "new-heart",
    title: "Love",
  },
  active: {
    cls: "active",
    img: "new-fill-heart",
    title: "Loved",
  },
};

const PostActionLike = ({
  postId,
  cls,
  likeDetails,
  setLikedReel,
  type,
  postLiked,
  from = "post",
  counter = 0,
}: PostAction) => {
  const [isLiked, setLiked] = useState(likeDetails);
  const dispatchEvent = useAppDispatch();
  const [counterState, setCounterState] = useState(counter || 0);

  const state = View[isLiked ? "active" : "inactive"];
  const role = useAppSelector((state) => state.user.role);

  const firstRender = useRef(true);

  useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false;
      return;
    }

    setLiked(likeDetails);

    if (likeDetails) {
      setCounterState((counterState) => counterState + 1);
    } else {
      setCounterState((counterState) => counterState - 1);
    }
    // setCounterState(counter);

    console.log("like called");
  }, [likeDetails]);

  // const signInModalRef = { method: {} as ModalRef };

  // const triggers = {
  //   modal: {
  //     signIn: () => signInModalRef,
  //   },
  // };

  const onClick = () => {
    if (isLiked) {
      setLiked(!isLiked);

      if (from === "reel") {
        setLikedReel(false);
      }

      postLiked?.("is_dislike");
      // setCounterState(counterState - 1);
      // counter--;

      if (type === "post") {
        UserPostLike(postId, { reaction_type: "" })
          .then(() => {
            dispatchEvent(connectionsActions.removePostLike(postId));
          })
          .catch(console.error);
      } else {
        LikeStory(postId, { reaction_type: "" });
      }
    } else {
      setLiked(true);

      if (from === "reel") {
        setLikedReel(true);
      }

      postLiked?.("is_like");
      // setCounterState(counterState + 1);
      // counter++;

      if (type === "post") {
        UserPostLike(postId, { reaction_type: "love" })
          .then(() => {})
          .catch(console.error);
      } else {
        LikeStory(postId, { reaction_type: "Love" });
      }
    }
  };

  return (
    <>
      <div
        // #FIXME: refactor and make it more readable
        onClick={
          role === "guest"
            ? () =>
                from === "reel"
                  ? ModalService.open("SIGN_IN")
                  : // signInModalRef.method?.open()   (if some issue when logged out and clicking on posts the remove comment)
                    console.log("")
            : onClick
        }
        onKeyDown={role === "guest" ? () => console.log("") : onClick}
        className={`post-action-like fw-medium rounded-3 ${
          type === "post" && PostActionBtnClass
        } ${cls} ${state.cls}`}
      >
        <div className="d-flex flex-column align-items-center justify-content-center ">
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            width={32}
            height={32}
            className=""
            alt=""
            src={`/images/post/${state.img}.svg`}
            style={{
              filter: from === "reel" && !isLiked ? "brightness(5)" : "",
            }}
          />
          {from === "reel" && (
            <span className="text-white fs-8">{counterState}</span>
          )}
        </div>
        {from !== "reel" && (
          <div
            className={`d-md-block d-sm-none d-none ${state.cls}`}
            style={{ width: "3rem" }}
          >
            {type === "post" && state.title}
          </div>
        )}
      </div>
    </>
  );
};

export default PostActionLike;
