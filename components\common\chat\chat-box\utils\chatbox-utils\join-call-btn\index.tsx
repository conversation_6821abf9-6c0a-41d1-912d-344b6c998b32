import Swal from "sweetalert2";

import { getReqById, UpdateMessageId } from "@/api/chat";
import ActionButton from "@/components/common/action-button";
import {
  AudioSent,
  VideoSent,
} from "@/components/matchmaker/match-cards/utils/svg";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";

export const JoinCallBtn = ({
  message,
}: {
  message: MessageInterface;
  videoConfRef: any;
}) => {
  const dispatch = useAppDispatch();
  const userId = useAppSelector((state) => state.user.id);
  const channelId = useAppSelector((state) => state.chat.channelId);

  const handleOnClick = async () => {
    const getReq = await getReqById(message?.meta?.reqId!);
    const isCompletedFlag = getReq?.data?.[0]?.status !== 1;

    if (!isCompletedFlag) {
      dispatch(chatActions.setCall(true));
      dispatch(chatActions.setReqId(message?.meta?.reqId!));
      dispatch(chatActions.setCallToken(getReq?.data?.[0]?.token?.[userId]));
      dispatch(chatActions.setStartedAt(getReq?.data?.[0]?.started_at));
      UpdateMessageId(
        message?.meta?.reqId!,
        message?.messageId || message?._id,
        message?.createdAt,
        channelId,
        message?.meta
      ).catch((err) => console.error(err));
    }

    if (!isCompletedFlag) {
      const modal = document.getElementById("videoModal");
      if (modal) modal.style.display = "block";
    } else if (isCompletedFlag) {
      Swal.fire({
        icon: "error",
        title: "Uh-oh! The timer for the call has ended",
        confirmButtonText: "Close",
        confirmButtonColor: "#AC1991",
        customClass: {
          confirmButton: "custom-btn",
        },
        showCloseButton: true,
      });
    }
  };

  return (
    <div>
      <div className="d-flex gap-2 p-2 align-items-center">
        <div className="d-flex justify-content-center align-items-center">
          {message?.message === "Voice Call" ? <AudioSent /> : <VideoSent />}
        </div>
        <div className="fw-bold">
          {message?.message === "Voice Call" ? "Voice Call" : "Video Call"}
        </div>
      </div>
      <div className="d-flex justify-content-center px-2 mb-2">
        <ActionButton
          type="solid"
          variant="success"
          className="w-100"
          onClick={handleOnClick}
        >
          Join the call
        </ActionButton>
      </div>
    </div>
  );
};
