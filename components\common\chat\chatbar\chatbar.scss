:root {
  --bg-light: rgba(255, 255, 255, 1);
  --text-light: #c3c3c3;
  --border-light: #cecece3f;
  --dropdown-hover-light: rgba(243, 233, 242, 1);

  --chat-bg-dark: #2c2c2c;
  --text-dark: #b3b3b3;
  --border-dark: #5555553f;
  --dropdown-hover-dark: rgba(80, 70, 90, 1);
  --icon-color-light: #131416;
  --icon-color-dark: #f5f5f5;
}

[data-bs-theme="dark"] {
  --bg-light: var(--chat-bg-dark);
  --text-light: var(--text-dark);
  --border-light: var(--border-dark);
  --dropdown-hover-light: var(--dropdown-hover-dark);
  --icon-color-light: var(--icon-color-dark);
}

svg {
  color: var(--icon-color-light);
}

.gif {
  cursor: pointer;
}

.gif-display-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  height: 100%;
  overflow-y: scroll;
}

.gif-box-container {
  display: flex;
  height: 300px;
  flex-direction: column;
  bottom: 4.3125rem;
  left: 10px;
  z-index: 1;
  width: 50%;
}

.gif-box-container::after {
  content: "";
  position: relative;
  top: 1.8125rem;
  right: 0.4375rem;
  display: block;
  width: 0;
  height: 0;
  border-left: 1.25rem solid transparent;
  border-right: 1.25rem solid transparent;
  border-top: 1.25rem solid var(--border-light);
}

.dropdown-item:hover {
  background-color: var(--dropdown-hover-light);
  color: #ac1991;
}

.translate {
  transform: translate(0, -50px) !important;
}

.rotate {
  transform: rotate(45deg);
  transition: transform 0.2s ease-in-out;
}

.reset-rotate {
  transform: rotate(0deg);
  transition: transform 0.2s ease-in-out;
}

.text {
  display: inline-block;
  position: relative;
  transition: opacity 1s ease-out;
}

.text.zigzag {
  animation: zigzag 1s linear forwards;
}

@keyframes zigzag {
  0% {
    transform: translate(0, 0);
    opacity: 1;
  }
  20% {
    transform: translate(10px, -20px);
    opacity: 0.8;
  }
  40% {
    transform: translate(-10px, -50px);
    opacity: 0.6;
  }
  60% {
    transform: translate(10px, -80px);
    opacity: 0.4;
  }
  80% {
    transform: translate(-10px, -110px);
    opacity: 0.2;
  }
  100% {
    transform: translate(0, -140px);
    opacity: 0;
  }
}

.attachment-box {
  height: 120px;
  padding: 10px;
  border-style: dashed;
  color: var(--text-light);
  background: var(--bg-light);
  width: 100%;
}
