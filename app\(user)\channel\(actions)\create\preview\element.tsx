"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { type ChangeEvent, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import "./index.scss";

import {
  CreateNewChannel,
  UpdateChannel,
  UpdateGroupAvatarBackground,
} from "@/api/channel";
import PostNavbar from "@/app/(user)/(sections)/navbar";
import ImageCropper from "@/components/common/cropper";
import Loader from "@/components/common/loader/loader";
import { type PossibleValues } from "@/components/common/select-array-v2";
import { configActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

interface ChannelPreviewViewParams {
  edit: boolean;
}

export default function CreateChannelPreview(params: ChannelPreviewViewParams) {
  const user = useAppSelector((state) => state.user.profile);
  const Values = useAppSelector((state) => state.create.channel);
  const [initialValues, setInitialValues] = useState(Values);

  const router = useRouter();

  const fileRef = useRef<any>(null);
  const avatarFileRef = useRef<any>(null);
  const [prevImg, setPrevImg] = useState("");
  const [avatarName, setAvatarName] = useState("");
  const [bgImg, setBgImg] = useState("");
  const [bgName, setBgName] = useState("");
  const [cropActive, setCropActive] = useState(false);
  const [fileCroppedURL, setCroppedImg] = useState<any>("");
  const [avatarCroppedURL, setAvatarCroppedImg] = useState<any>("");
  const [showAvatar, setShowAvatar] = useState(true);
  const [isFile, setIsFile] = useState(false);
  const [loading, setLoading] = useState(false);

  const dispatchAction = useAppDispatch();

  useEffect(() => {
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  console.log("In channel create.");

  const croppedAvatarImg = (url: Blob) => {
    setShowAvatar(true);
    setIsFile(true);
    setAvatarCroppedImg(URL.createObjectURL(url));
    const blob = url;
    const file = new File([blob], avatarName, { type: blob.type });
    setInitialValues((prevState) => ({
      ...prevState,
      avatar: file,
    }));
  };

  const croppedBackgroundImg = (url: Blob) => {
    setIsFile(true);
    setCroppedImg(URL.createObjectURL(url));
    setCropActive(false);
    const blob = url;
    const file = new File([blob], bgName, { type: blob.type });
    setInitialValues((prevState) => ({
      ...prevState,
      background: file,
    }));
  };

  const onFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setCropActive(true);
    setBgImg("");
    const file = event.currentTarget.files?.[0];

    if (file) {
      setBgName(file.name.replace(/\s+/g, ""));
      setBgImg(URL.createObjectURL(file));
    }
  };

  const [btnDisable, setBtnDisable] = useState<boolean>(false);

  if (loading) {
    return (
      <div className="container min-vh-100 p-3 d-flex align-items-center justify-content-center">
        <Loader />
      </div>
    );
  }

  return (
    <div className="position-relative">
      <PostNavbar
        icon={"/images/svg/nav-back.svg"}
        buttonText={params.edit ? "Update" : "Create New Channel"}
        text="Back to details"
        showBtn={true}
        btnIsValid={params.edit || (avatarCroppedURL && fileCroppedURL)}
        btnIsDirty={params.edit || (avatarCroppedURL && fileCroppedURL)}
        btnIsSubmitting={false}
        btnDisable={btnDisable}
        submitPost={() => {
          setBtnDisable(true);
          setLoading(true);

          if (params.edit) {
            const avatarBackground = {
              avatar: initialValues.avatar,
              background: initialValues.background,
            };
            let updatedValues: any = {
              name: initialValues.name,
              about: initialValues.description,
              hashtags: initialValues.hashtags,
              social_handles: initialValues.social_media,
            };

            const sub_perks: {
              type: PossibleValues;
              description: string;
              emoji?: string;
            }[] = [];
            initialValues.perks.forEach((s) => {
              const { icon, value, emoji } = s;
              const perk = {
                type: icon,
                description: value,
                ...(emoji && { emoji }),
              };
              sub_perks.push(perk);
            });

            console.log({ initialValues });

            updatedValues = {
              ...updatedValues,
              ...(initialValues.counter.subscriber_count === 0 && {
                perks: sub_perks,
                ...(initialValues?.services
                  ? {
                      services:
                        initialValues.services
                          .map((s) => s._id)
                          .filter((id): id is string => !!id) || [],
                    }
                  : { services: [] }),
                subscriptions: (initialValues?.subscriptions?.array ?? []).map(
                  (sub) => {
                    const { _id, ...rest } = sub;
                    return {
                      ...rest,
                      offer_type: initialValues?.subscriptions?.offer_type,
                      validity_type: sub.subscription_type,
                      price: parseFloat(`${sub.price}`),
                      is_active: true,
                      trial_period: 0,
                    };
                  }
                ),
                is_paid:
                  initialValues.subscriptions?.offer_type === "FEE"
                    ? true
                    : false,
                free_services: initialValues.free_services ?? [],
              }),
            };

            console.log({ updatedValues });

            if (initialValues.from !== "updateAvatar") {
              UpdateChannel(updatedValues, initialValues?.id || "")
                .then(() => {
                  setLoading(false);

                  if (!isFile) {
                    Swal.fire({
                      icon: "success",
                      title: "Channel Updated Successfully",
                      showCloseButton: true,
                    }).then((res) => {
                      router.push(`/channel/${initialValues.username}`);
                    });
                  }
                })
                .catch((error) => {
                  setBtnDisable(false);
                  setLoading(false);

                  if (error.errorCode === 1000) {
                    Swal.fire({
                      icon: "warning",
                      title: "Oops! Some words aren’t allowed.",
                      text: `The word "${error.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
                    });
                  } else {
                    Swal.fire({
                      icon: "error",
                      title: error.message,
                    });
                  }
                });
            }

            if (isFile) {
              UpdateGroupAvatarBackground(
                avatarBackground,
                initialValues?.id || ""
              )
                .then(() => {
                  setLoading(false);

                  Swal.fire({
                    icon: "success",
                    title: "Channel Updated Successfully",
                    showCloseButton: true,
                  }).then((res) => {
                    if (res.isConfirmed) {
                      router.push(
                        `/${user.user_type.toLowerCase()}/${
                          user.username
                        }/subs?type=channels`
                      );
                    }
                  });
                })

                .catch((error) => {
                  setBtnDisable(false);
                  setLoading(false);

                  if (error.errorCode === 1000) {
                    Swal.fire({
                      icon: "warning",
                      title: "Oops! Some words aren’t allowed.",
                      text: `The word "${error.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
                    });
                  } else {
                    Swal.fire({
                      icon: "error",
                      title: error.message,
                    });
                  }
                });
            }
          } else {
            if (avatarCroppedURL && fileCroppedURL) {
              let creation_values = { ...initialValues };
              const sub_perks: {
                type: PossibleValues;
                description: string;
                emoji?: string;
              }[] = [];
              initialValues.perks.forEach((s) => {
                const { icon, value, emoji } = s;
                const perk = {
                  type: icon,
                  description: value,
                  ...(emoji && { emoji }),
                };
                sub_perks.push(perk);
              });
              // @ts-expect-error-interface-error
              creation_values = {
                ...creation_values,
                perks: sub_perks,
                ...(creation_values?.services
                  ? creation_values.services.length > 0 && {
                      services: creation_values.services
                        .map((s) => s._id)
                        .filter((id): id is string => !!id),
                    }
                  : {}),
                free_services: initialValues.free_services,
              };

              CreateNewChannel(creation_values)
                .then(() => {
                  setLoading(false);

                  router.push(
                    `/${user.user_type.toLowerCase()}/${
                      user.username
                    }/subs?type=channels`
                  );
                  Swal.fire({
                    icon: "success",
                    title: "Channel Created Successfully",
                    showCloseButton: true,
                  });
                })

                .catch((error) => {
                  setLoading(false);

                  setBtnDisable(false);

                  if (error.errorCode === 1000) {
                    Swal.fire({
                      icon: "warning",
                      title: "Oops! Some words aren’t allowed.",
                      text: `The word "${error.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
                    });
                  } else {
                    Swal.fire({
                      icon: "error",
                      title: error.message,
                    });
                  }
                });
            }
          }
        }}
      />
      <div className="container min-vh-100 p-3">
        <div className="container bg-body rounded-2 d-flex flex-column p-3 gap-3 channel-preview-container">
          <div className="d-flex flex-column">
            <p className="fs-6 fw-medium">Upload Avatar</p>
            <div className="update-avatar-container d-flex align-items-center justify-content-center overflow-hidden ">
              {prevImg && !showAvatar && (
                <>
                  <div className="cropper-wrapper d-flex align-items-end flex-column justify-content-center">
                    <ImageCropper
                      img={prevImg}
                      cls="z-3 object-fit-contain rounded-3 avatar-cropper"
                      aspectRatio={1}
                      viewMode={1}
                      dragMode="move"
                      guides={false}
                      cropBoxResizable={true}
                      onCropComplete={croppedAvatarImg}
                      cropperHeight={400}
                    />
                  </div>
                </>
              )}
              {showAvatar && (
                <div className="avatar-container ratio-1x1 rounded-pill overflow-hidden position-relative d-flex align-items-center justify-content-center">
                  {(prevImg && avatarCroppedURL) || initialValues.avatar ? (
                    <div className="w-100 h-100 position-absolute">
                      <Image
                        width={1280}
                        height={720}
                        style={{
                          width: "100%",
                          height: "max-content",
                          objectFit: "cover",
                        }}
                        src={avatarCroppedURL || initialValues.avatar}
                        alt="change-avatar"
                        className="avatar-img"
                      />
                      <p className="position-absolute bottom-0 w-100 bg-black bg-opacity-50 pb-2 m-auto text-center fw-medium change-avatar">
                        Change
                      </p>
                    </div>
                  ) : (
                    <div className="d-flex w-100 h-100 flex-column align-items-center gap-3">
                      <Image
                        className="image-icon"
                        width={25}
                        height={25}
                        src="/images/svg/upload-avatar.svg"
                        alt="avatar"
                      />
                      <p className="text-center lh-1 mb-0 fw-medium">
                        Upload Avatar
                      </p>
                    </div>
                  )}
                  <input
                    ref={avatarFileRef}
                    className="opacity-0 position-absolute bottom-0 end-0 w-100 h-100"
                    type="file"
                    accept="image/*"
                    onChange={(event: ChangeEvent<HTMLInputElement> | any) => {
                      const file = event.currentTarget.files[0];

                      if (!file) {
                        return;
                      }

                      setShowAvatar(false);
                      setAvatarName(file.name.replace(/\s+/g, ""));

                      const reader = new FileReader();

                      reader.onload = (event: any) => {
                        setPrevImg(event.target.result);
                      };

                      reader.readAsDataURL(file);
                    }}
                  />
                </div>
              )}
            </div>
          </div>
          <div className="d-flex flex-column">
            <p className="fs-6 fw-medium">Upload Background</p>
            <div className="background-container bg-cream d-flex position-relative">
              {(bgImg || user.background) && (
                <div className="img-container w-100 position-relative overflow-hidden d-flex justify-content-center align-items center">
                  {cropActive ? (
                    <ImageCropper
                      img={bgImg || ""}
                      cls="z3"
                      aspectRatio={12 / 6}
                      guides={false}
                      cropBoxResizable={true}
                      onCropComplete={croppedBackgroundImg}
                      cropperHeight={320}
                    />
                  ) : (
                    <>
                      {!fileCroppedURL && !initialValues.background ? (
                        <Image
                          fill
                          objectFit="cover"
                          src={"/images/common/defaultBack.svg"}
                          alt="background"
                        />
                      ) : (
                        <Image
                          src={fileCroppedURL || initialValues.background}
                          objectFit="contain"
                          fill
                          alt="cropped-file"
                        />
                      )}
                    </>
                  )}
                </div>
              )}
              <div
                className="position-absolute z-3 bottom-0 end-0 p-2 pointer"
                onClick={() => fileRef.current?.click()}
              >
                <input
                  ref={fileRef}
                  className="opacity-0 position-absolute bottom-0 end-0"
                  type="file"
                  accept="image/*"
                  onChange={onFileInputChange}
                />
                <Image
                  src={"/images/creator/edit.svg"}
                  width={25}
                  height={25}
                  alt="edit profile"
                  className="position-relative bottom-0 end-0"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
