import { Author } from "@/types/post";
import API from ".";
import type { MediaInfo } from "./post";
import { Media } from "@/types/media";

interface createProductBodyResponse {
  data: any;
}

export enum ShopItemCategory {
  ALLITEMS = "All Items",
  BONDAGE = "Bondage",
  DILDO = "Dildo",
  NIPPLE_CLAMPS = "Nipple Clamps",
  VIBRATOR = "Vibrator",
  PANTIES = "Panties",
  BRA = "Bra",
  FLOGGER = "Flogger",
  TOE_NAIL_CLIPPINGS = "Toe Nail Clippings",
  HAIR = "Hair",
  USED_PANTIES = "Used Panties",
  BIKINI = "Bikini",
  HAIR_BRUSH = "Hair Brush",
  TOOTH_BRUSH = "Tooth Brush",
  SAUCY_PICS = "Saucy Pics",
  SAUCY_VID = "Saucy Vid",
  WORN_HEELS = "Worn Heels",
  USED_MAGIC_WAND = "Used Magic Wand",
  VIALS_SPIT = "Vials Spit",
  VIALS_SQUIRT = "Vials Squirt",
  USED_NYLON_STOCKINGS = "Used Nylon Stockings",
  FOOT_SHAVINGS = "Foot Shavings",
  USED_BUTT_PLUG = "Used Butt Plug",
  USED_GYM_ATTIRE = "Used Gym Attire",
  OTHERS = "Others",
}

export interface Product {
  name: string;
  media: MediaInfo[];
  brand: string;
  category: string;
  custom_category?: string;
  description: string;
  variations: {
    colors: string[];
    sizes: string[];
  };
  sell_type: string;
  is_public: boolean;
  price: number;
  quantity: number;
  end_date?: string;
}

export type MediaStatus = "thumb" | "compressed" | "high";

export interface SignedUrls {
  [variation: string]: string;
}

export interface MediaItem {
  _id: string;
  path: string;
  type: string;
  variations: string[];
  status: string;
  used_as: string;
  signed_urls: SignedUrls;
}

export interface ShopProduct {
  _id: string;
  author: Author;
  name: string;
  brand: string;
  category: string;
  description: string;
  variations: {
    colors: string[];
    sizes: string[];
  };
  price: number;
  quantity: number;
  sold: number;
  sell_type: string;
  media: Media[];
  media_thumbnail: Media[];
  is_public: boolean;
  is_deleted: boolean;
  media_categories: any[];
  employee_id: string;
  created_at: string;
  updated_at: string;
  __v: number;
  ts: number;
  end_date: string;
}
export interface ShopProductListResponse {
  data: ShopProduct[];
}

export const CreateProduct = async (body: Product) => {
  console.log(body);
  const ShopItemData: Product = {
    name: body.name,
    media: body.media,
    brand: body.brand,
    category: body.category,
    description: body.description,
    variations: {
      colors: body.variations.colors,
      sizes: body.variations.sizes,
    },
    sell_type: body.sell_type,
    is_public: body.is_public,
    price: body.price,
    quantity: body.quantity,
  };

  if (body.category === "Others") {
    ShopItemData.custom_category = body.custom_category;
  }

  return API.post(
    `${API.SHOPS}/create-product`,
    ShopItemData
  ) as Promise<createProductBodyResponse>;
};

export const ProductListing = async (
  userId?: string,
  page?: number,
  limit?: number
) =>
  API.get(
    `${API.SHOPS}/fetch-products${userId ? `?user_id=${userId}` : ""}${
      page ? `&page=${page}` : ""
    }${limit ? `&limit=${limit}` : ""}`
  ) as Promise<createProductBodyResponse>;

export const GetSoldProductListing = async (page: number) =>
  API.get(`${API.SHOPS}/sold-products?page=${page}&limit=20`) as Promise<any>;

export const GetAllProductListing = async (
  page: number,
  filter?: boolean,
  category?: string
) =>
  API.get(
    `${API.SHOPS}/trending-products?page=${page}${
      filter ? "&creatorsubscribed=true" : ""
    }${category !== "All Items" ? `&category=${category}` : ""} `
  ) as Promise<any>;

export interface BuyProductBody {
  product_id: string | number;
  variant_data: {
    color: string;
    size: string;
  };
  qty: number;
  payment_mode: string;
  tokenised_card_id?: string;
  delivery_address_id: string;
}
export const BuyProduct = async (body: BuyProductBody) =>
  API.post(`${API.SHOPS}/buy-product`, body) as Promise<any>;

export const DeleteProduct = async (productId: string | string[]) =>
  API.delete(`${API.SHOPS}/delete-products/${productId}`) as Promise<any>;

export const getSingleProduct = async (productId: string | string[]) =>
  API.get(`${API.SHOPS}/fetch-products/${productId}`) as Promise<any>;

export const getProductOrders = async (trasanctionId?: string | string[]) =>
  API.get(
    `${API.SHOPS}/product-orders${trasanctionId ? `/${trasanctionId}` : ""}`
  ) as Promise<any>;

export const updateProductInfo = async (
  isStatus: boolean,
  body: string,
  trasanctionId: string | string[]
) =>
  API.patch(
    `${API.SHOPS}/product-orders/${trasanctionId}${
      isStatus ? "/update-status" : "/add-comment"
    }`,
    isStatus ? { status: body } : { comment: body }
  ) as Promise<any>;

export const EditShopItemData = (id: string, body: any) =>
  API.patch(`${API.SHOPS}/update-product/${id}`, body);
