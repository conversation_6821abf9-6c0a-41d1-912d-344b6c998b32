"use client";

import classNames from "classnames";
import { debounce } from "lodash";
import throttle from "lodash/throttle";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import { ChatMedia } from "@/api/user";
import { ModalService } from "@/components/modals";
import {
  ATTACHMENT_MAX_DURATION,
  IMAGE_ATTACHMENT_MAX_SIZE,
  VIDEO_ATTACHMENT_MAX_SIZE,
} from "@/global/limits/chat";
import useBrowser from "@/hooks/useBrowser";
import useFileUploader from "@/hooks/useFileUploader";
import { chatActions, configActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { <PERSON><PERSON>, <PERSON>t<PERSON><PERSON> } from "@/redux-store/slices/chat.slice";
import { ChatFeeType } from "@/types/services";
import { getAssetUrl } from "@/utils/assets";
import socketChannel from "@/utils/chatSocket";

import { RemoveFileSelected } from "../chat-box/utils/svg-utils";
import { fetchChatFeeDetails } from "./CreatorChatBar";
import { EditMediaModal } from "./helpers/EditMediaModal";
import ServicesBtn from "./helpers/ServicesBtn";
import { OptionPlusIcon } from "./svg";

const NormalChatBar = ({ chattingFeeRef }: any) => {
  const { uploadFile } = useFileUploader();

  const router = useRouter();
  const browser = useBrowser();
  const chatData = useAppSelector((state) => state.chat);
  const chatList = useAppSelector((s) => s.chat.chatList);
  const user = useAppSelector((state) => state.user);
  const [receiverUserType, setReceiverUserType] = useState<string>("");
  const [isVideo, setIsVideo] = useState<boolean>(false);
  const [_chatEnabledLocal, setChatEnabledLocal] = useState<boolean>(false);
  const [image, setImage] = useState<string>("");
  const messageInputRef = useRef<any>(null);
  const dispatch = useAppDispatch();
  const [file, setFile] = useState<File | undefined>(undefined);
  const hasStartedTyping = useRef(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const replyMessage = useAppSelector((s) => s.chat.replyMessage);
  const [mediaType, setMediaType] = useState<"image" | "video">("image");
  const [channelData, setChannelData] = useState<Chat>();
  const [_dontAsk, setDontAsk] = useState(false);
  const [_targetData, setTargetData] = useState<ChatPerson>();
  const isLoading = useAppSelector((s) => s.chat.isLoaded);

  const targetUser = useAppSelector((s) => s.chat.targetUser);

  useEffect(() => {
    const element = document.getElementById("dropdownMenuButton");

    if (!element) return;

    const onShow = () => {
      element.classList.add("rotate");
      element.classList.remove("reset-rotate");
    };

    const onHide = () => {
      element.classList.remove("rotate");
      element.classList.add("reset-rotate");
    };

    element.addEventListener("shown.bs.dropdown", onShow);
    element.addEventListener("hidden.bs.dropdown", onHide);

    return () => {
      element.removeEventListener("shown.bs.dropdown", onShow);
      element.removeEventListener("hidden.bs.dropdown", onHide);
    };
  }, []);

  const uploadPicture = (file: any) => {
    const selectedFile = file.target.files[0];
    const { type, size } = selectedFile;

    if (
      (type.startsWith("image") && size > IMAGE_ATTACHMENT_MAX_SIZE) ||
      (type.startsWith("video") && size > VIDEO_ATTACHMENT_MAX_SIZE)
    ) {
      Swal.fire({
        title: "File too large",
        icon: "error",
        text: "The maximum file size allowed for video attachments is 1 GB, and for image attachments, it's 40 MB.",
        confirmButtonText: "Okay",
        showCancelButton: false,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        allowOutsideClick: false,
      });
      return;
    }

    const url = URL.createObjectURL(selectedFile);

    if (type.startsWith("video")) {
      setMediaType("video");
      setIsVideo(true);
      const videoElement = document.createElement("video");
      videoElement.src = url;

      videoElement.onloadedmetadata = () => {
        if (videoElement.duration > ATTACHMENT_MAX_DURATION) {
          Swal.fire({
            title: "File length should not be greater than 10 minutes",
            icon: "error",
            confirmButtonText: "Okay",
            showCancelButton: false,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            allowOutsideClick: false,
          });
          URL.revokeObjectURL(url);
        } else {
          setFile(selectedFile);
          setImage(url);
        }
      };
    } else {
      setFile(selectedFile);
      setImage(url);
    }

    dispatch(chatActions.setHasMedia(true));
  };

  const [message, setMessage] = useState<string>("");
  const [_optionsUi, setOptionsUi] = useState<boolean>(false);
  const optionsRef = useRef<any>(null);

  useEffect(() => {
    setMessage("");
  }, [chatData.targetUser]);

  useEffect(() => {
    setOptionsUi(false);
  }, [chatData.channelId]);

  useEffect(() => {
    const targetId = chatData.targetUser;
    const data = chatData.chatList.filter((chat: any) => {
      return (
        chat?.target?._id === targetId || chat?.initiator?._id === targetId
      );
    })?.[0];

    if (!data) return;
    const { target, initiator } = data;
    const isTargetUser = target._id === targetUser;
    const targetData = isTargetUser ? target : initiator;

    setTargetData(targetData);

    setDontAsk(data?.payment_reminder ?? false);

    setChatEnabledLocal(
      data.buyers.some((b) => b.buyer === targetUser || b.buyer === user.id) ||
        !targetData?.latest_chat_fee?.is_active
    );

    if (user.id === data?.target?._id) {
      setReceiverUserType(data?.initiator?.user_type);
      if (data?.initiator?.user_type === "USER") setChatEnabledLocal(true);
    } else if (user.id === data?.initiator?._id) {
      if (data?.target?.user_type === "USER") setChatEnabledLocal(true);
      setReceiverUserType(data?.target?.user_type);
    }

    setChannelData(data);

    return () => {
      setFile(undefined);
      dispatch(chatActions.setHasMedia(false));

      setImage("");
      setMessage("");
    };
  }, [chatData.targetUser]);

  const handleClickOutside = (event: any) => {
    if (optionsRef.current && !optionsRef?.current?.contains(event.target)) {
      setOptionsUi(false);
    }
  };

  useEffect(() => {
    if (user.loggedIn === false) router.push("/fresh");
    dispatch(configActions.showFooter(false));

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const debouncedStopTyping = useCallback(
    debounce(() => {
      socketChannel?.channel?.stopTyping();
    }, 200),
    [socketChannel?.channel]
  );

  const throttledChatMedia = throttle(ChatMedia, 3000);

  const notifyAndSendMessage = (type: "media" | "text" = "text") => {
    if (type === "media") return;
    const msgMeta = {
      chat_list_message: message,
      converseId: chatData.channelId,
      avatar: user.profile.avatar,
      name: user.profile.display_name,
      type: "message",
      id: user.id,
      ...(replyMessage?.message ? { replyMessage } : {}),
    };

    dispatch(chatActions.updateLoadingState(true));
    dispatch(
      chatActions.updateLastMessage({
        channelId: chatData.channelId,
        message: message || (!isVideo ? "Sent an image" : "Sent a video"),
      })
    );

    socketChannel.channel?.sendMessage({
      message: message || "Attachment",
      meta: msgMeta,
    });
  };

  function handleDeduct() {
    if (!chatList.length) return chatList;

    const updatedChatList = chatList.map((chat) => {
      if (chat.converse_channel_id === chatData.channelId) {
        return {
          ...chat,
          converse_consumable: chat.converse_consumable?.map((c) =>
            c.buyer === user.id
              ? {
                  ...c,
                  available_message: Math.max(c.available_message - 1, 0),
                }
              : c
          ),
        };
      }

      return chat;
    });

    return updatedChatList;
  }

  const sendMessage = async () => {
    if (
      !chatData.targetUser ||
      message.length > 2000 ||
      (!message.trim() && !file)
    ) {
      if (message.length > 2000) {
        Swal.fire({
          title: "Message too long",
          icon: "error",
          text: "The maximum message length is 2,000 characters.",
          confirmButtonText: "Okay",
          showCancelButton: false,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          allowOutsideClick: false,
        });
      }

      return;
    }

    const { chat_fee_services } = fetchChatFeeDetails({
      chatList,
      channelId: chatData.channelId,
      targetUser,
    });

    if (
      !channelData?.buyers.some(
        (b) => b.buyer === user.id || b.buyer === targetUser
      ) &&
      chat_fee_services.length > 0 &&
      chat_fee_services.some((c) => c.chat_fee_type !== ChatFeeType.FREE) &&
      (!channelData?.converse_consumable?.some((c) => c.buyer === user.id) ||
        channelData?.converse_consumable?.find((c) => c.buyer === user.id)
          ?.available_message === 0)
    ) {
      ModalService.open("SHOW_SERVICES", {
        authorName:
          channelData?.target?._id === targetUser
            ? channelData?.target?.display_name || ""
            : channelData?.initiator?.display_name || "",
        targetUserId: targetUser,
        filter: {
          type: "CHAT-FEE",
        },
        directMessage: {
          showBtn: true,
          file: file || null,
          message,
          vault_media_ids: [],
          channel_id: chatData.channelId,
          dmCb: () => {
            setFile(undefined);
            dispatch(chatActions.setHasMedia(false));

            setImage("");
            dispatch(chatActions.setNewMessage(message));
            if (messageInputRef.current) messageInputRef.current.rows = 1;
            setMessage("");
            dispatch(chatActions.setReplyMessage({}));
          },
        },
        chattingFeeRef,
      });
      return;
    }

    if (messageInputRef.current) messageInputRef.current.focus();

    const targetUserId = chatData.targetUser;
    const index = chatList.findIndex(
      (chat: any) =>
        chat?.initiator?._id === targetUserId ||
        chat?.target?._id === targetUserId
    );

    if (index !== -1) {
      const updatedChatList = [
        chatList[index],
        ...chatList.slice(0, index),
        ...chatList.slice(index + 1),
      ];
      dispatch(chatActions.setChatUserList(updatedChatList));
    }

    dispatch(chatActions.updateChatActivity(chatData.channelId));

    const shouldDeduct =
      !channelData?.buyers.some(
        (b) => b.buyer === user.id || b.buyer === targetUser
      ) &&
      channelData?.converse_consumable &&
      (channelData?.converse_consumable?.find((c) => c.buyer === user.id)
        ?.available_message ?? 0) > 0;

    if (
      file ||
      channelData?.buyers.some(
        (b) => b.buyer === user.id || b.buyer === targetUser
      ) ||
      chat_fee_services.some((c) => c.chat_fee_type !== ChatFeeType.FREE) ||
      (channelData?.converse_consumable?.find((c) => c.buyer === user.id)
        ?.available_message ?? 0) > 0
    ) {
      try {
        dispatch(
          chatActions.setMediaLoading({
            channel_id: chatData.channelId,
            loading: true,
          })
        );

        setFile(undefined);
        setMessage("");

        let mediaResponse;
        if (file) mediaResponse = await uploadFile(file, "Chat");
        const filePath =
          mediaResponse?.[0]?.uploadURL?.split("/")[
            mediaResponse?.[0]?.uploadURL?.split("/").length - 1
          ];

        const res: any = await throttledChatMedia({
          ...(file &&
            filePath &&
            mediaResponse?.[0]?.type && {
              media: [
                {
                  path: (mediaResponse as any)?.[0]?.s3Multipart?.key,
                  type: mediaResponse[0].type?.split("/")[0] as
                    | "image"
                    | "video",
                },
              ],
            }),
          channel_id: chatData.channelId,
          message_text: message || "Attachment",
          meta: {
            chat_list_message:
              message || (!isVideo ? "Sent an image" : "Sent a video"),
            converseId: chatData.channelId,
            id: user.id,
            type: file ? "message-attachment" : "message",
            ...(replyMessage?.message ? { replyMessage } : {}),
          },
          browser: {
            name: browser.browser.name || "",
            os: browser.os.name || "",
          },
          ...(shouldDeduct ? { deduct_count: true } : {}),
        });

        if (shouldDeduct) {
          const updated = handleDeduct();
          dispatch(chatActions.setChatUserList([...updated]));
        }

        const media = res?.data?.media;
        dispatch(chatActions.setMediaId(media?._id));

        dispatch(chatActions.updateLoadingState(true));
        dispatch(
          chatActions.updateLastMessage({
            channelId: chatData.channelId,
            message: message || (!isVideo ? "Sent an image" : "Sent a video"),
          })
        );
      } catch (err: any) {
        console.error(err);
        Swal.fire({
          icon: "error",
          text: err?.response?.data?.message || "Something went wrong",
          confirmButtonText: "Close",
          confirmButtonColor: "#AC1991",
          customClass: {
            confirmButton: "custom-btn",
          },
        });
      } finally {
        dispatch(
          chatActions.setMediaLoading({
            channel_id: chatData.channelId,
            loading: false,
          })
        );
        // if (image) URL.revokeObjectURL(image);
      }
    } else {
      notifyAndSendMessage();
    }

    // Reset form and state
    setFile(undefined);
    dispatch(chatActions.setHasMedia(false));

    setImage("");
    dispatch(chatActions.setNewMessage(message));
    if (messageInputRef.current) messageInputRef.current.rows = 1;
    setMessage("");
    dispatch(chatActions.setReplyMessage({}));
  };

  const [cropMedia, setCropMedia] = useState(false);

  const isImageAsset = () => {
    if (Array.isArray(replyMessage?.meta?.media)) {
      return replyMessage?.meta?.media[0]?.type?.includes("image");
    } else {
      return replyMessage?.meta?.media?.type?.includes("image");
    }
  };

  return (
    <>
      <div className="position-relative">
        {replyMessage?.message && (
          <div
            className="position-absolute rounded p-3"
            style={{
              background: "rgba(243, 233, 242, 1)",
              width: "100%",
              bottom: messageInputRef?.current?.rows > 1 ? 100 : 72,
            }}
          >
            <div
              className="rounded p-1 position-relative"
              style={{
                background: "rgba(243, 233, 242, 1)",
              }}
            >
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <div className="fw-bold ">{replyMessage?.display_name}</div>
                  <div
                    style={{
                      color: "black",
                    }}
                  >
                    {replyMessage?.message}
                  </div>
                </div>
                {replyMessage?.meta?.media &&
                  "is_unlocked" in replyMessage?.meta &&
                  replyMessage?.meta?.is_unlocked && (
                    <div className="me-4">
                      {isImageAsset() ? (
                        // eslint-disable-next-line @next/next/no-img-element
                        <img
                          src={getAssetUrl({
                            media: Array.isArray(replyMessage?.meta?.media)
                              ? replyMessage?.meta?.media[0]
                              : replyMessage?.meta?.media,
                            defaultType: "avatar",
                            variation:
                              "is_unlocked" in replyMessage?.meta &&
                              !replyMessage?.meta?.is_unlocked
                                ? "blur"
                                : "compressed",
                          })}
                          className="rounded object-fit-cover pointer"
                          width={50}
                          height={50}
                          onClick={() => {
                            if (
                              "is_unlocked" in replyMessage?.meta &&
                              !replyMessage?.meta?.is_unlocked
                            )
                              return;
                            window.KNKY.showFullscreenMedia(
                              "image",
                              getAssetUrl({
                                media: Array.isArray(replyMessage?.meta?.media)
                                  ? replyMessage?.meta?.media[0]
                                  : replyMessage?.meta?.media,
                                defaultType: "avatar",
                                variation: "compressed",
                              })
                            );
                          }}
                          alt=""
                        />
                      ) : (
                        <video
                          src={getAssetUrl({
                            media: Array.isArray(replyMessage?.meta?.media)
                              ? replyMessage?.meta?.media[0]
                              : replyMessage?.meta?.media,
                            variation: "compressed",
                          })}
                          className="rounded object-fit-cover pointer"
                          width={50}
                          height={50}
                          controlsList="nodownload"
                          controls={false}
                          autoPlay
                          loop
                          onClick={() => {
                            window.KNKY.showFullscreenMedia(
                              "video",
                              getAssetUrl({
                                media: Array.isArray(replyMessage?.meta?.media)
                                  ? replyMessage?.meta?.media[0]
                                  : replyMessage?.meta?.media,
                                defaultType: "avatar",
                                variation: "compressed",
                              })
                            );
                          }}
                        />
                      )}
                    </div>
                  )}
              </div>
              <span className="bg-body rounded fs-8 position-absolute top-0 end-0 d-flex justify-content-center w-fit">
                <button
                  className="btn-close"
                  onClick={() => {
                    dispatch(chatActions.setReplyMessage({}));
                  }}
                ></button>
              </span>
            </div>
          </div>
        )}
        {image && file && (
          <div
            className="rounded position-absolute w-100 z-2"
            style={{
              height: "120px",
              padding: 10,
              borderStyle: "dashed",
              backgroundColor: "rgba(255,255,255,1)",
              zIndex: 1,
              bottom: messageInputRef.current.rows > 1 ? 100 : 72,
            }}
          >
            <div
              className="position-absolute d-flex justify-content-center align-items-center"
              style={{
                height: 20,
                width: 20,
                top: 1,
                left: 100,
                cursor: "pointer",
              }}
              onClick={() => {
                setImage("");
                setFile(undefined);
                dispatch(chatActions.setHasMedia(false));
              }}
            >
              <RemoveFileSelected />
            </div>
            {file.type.includes("image") ? (
              <Image
                src={image || ""}
                alt="Attachment"
                height={100}
                width={100}
                style={{ objectFit: "contain" }}
              />
            ) : (
              <video
                src={image}
                height={100}
                width={100}
                muted
                playsInline
                autoPlay
                controlsList="nodownload"
              ></video>
            )}
            {image && file && mediaType === "image" && (
              <div
                className="position-absolute top-0 end-0 p-3 fw-bold pointer color-medium"
                data-bs-toggle="modal"
                onClick={() => {
                  setTimeout(() => {
                    setCropMedia(true);
                  }, 200);
                }}
                data-bs-target="#editMediaModal"
              >
                Crop
              </div>
            )}
          </div>
        )}
        <div className="chat-bar d-flex align-items-center p-3 pb-4 gap-2">
          <div
            className="pointer"
            data-bs-toggle="dropdown"
            id="dropdownMenuButton"
            aria-expanded="false"
            data-bs-auto-close="outside"
          >
            <OptionPlusIcon />
          </div>
          <input
            onChange={uploadPicture}
            type="file"
            onClick={(e) => {
              (e.target as HTMLInputElement).value = "";
            }}
            disabled={
              user.loggedIn === false || !chatData?.targetUser || !isLoading
                ? true
                : false
            }
            name="upload-picture"
            id="upload-picture"
            accept="image/*, video/*"
            hidden
          />
          <ul
            className={classNames("dropdown-menu pointer w-fit translate", {})}
          >
            <label htmlFor="upload-picture" className="dropdown-item pointer">
              <Image
                width={"28"}
                height={"25"}
                src="/images/svg/gallery.svg"
                alt="close-feeling"
              />
              <span className="ms-2">Media</span>
            </label>
          </ul>

          {receiverUserType === "CREATOR" && (
            <ServicesBtn
              target={{
                target_user_display_name:
                  channelData?.target?._id === targetUser
                    ? channelData?.target?.display_name!
                    : channelData?.initiator?.display_name!,
                target_user_id: targetUser,
              }}
            />
          )}
          <textarea
            className="form-control"
            value={message}
            rows={1}
            onChange={(e: any) => {
              setMessage(e.target.value);
              const textarea = e.target;
              textarea.rows = 1;
              const lineHeight = 20;
              const currentRows = Math.min(
                Math.floor(textarea.scrollHeight / lineHeight),
                2
              );
              textarea.rows = currentRows;

              if (!hasStartedTyping.current) {
                socketChannel?.channel?.startTyping();
                hasStartedTyping.current = true;
              }

              if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
              }

              typingTimeoutRef.current = setTimeout(() => {
                debouncedStopTyping();
                hasStartedTyping.current = false;
              }, 200);
            }}
            style={{ resize: "none", overflowY: "hidden" }}
            ref={messageInputRef}
            onKeyDown={(e: any) => {
              if (e.key === "Enter") {
                if (e.shiftKey || /Android|iPhone/i.test(navigator.userAgent)) {
                  e.preventDefault();
                  const textarea = e.target;
                  const cursorPosition = textarea.selectionStart;
                  const value = textarea.value;
                  textarea.value =
                    value.substring(0, cursorPosition) +
                    "\n" +
                    value.substring(cursorPosition);
                  textarea.selectionStart = textarea.selectionEnd =
                    cursorPosition + 1;
                  setMessage(textarea.value);

                  setTimeout(() => {
                    textarea.scrollTop = textarea.scrollHeight;
                  }, 0);
                } else {
                  e.preventDefault();
                  sendMessage();
                }
              }
            }}
            placeholder={
              !user.loggedIn ? "Login to send a message" : "Send a message"
            }
            disabled={!user.loggedIn || isLoading ? false : true}
          />

          <Image
            width={30}
            height={30}
            onTouchEnd={() => {
              sendMessage();
            }}
            onClick={() => sendMessage()}
            className="invert ms-2 pointer"
            src="/images/svg/send-chat.svg"
            alt="close-feeling"
          />
        </div>
      </div>
      <EditMediaModal
        cropMedia={cropMedia}
        currentUrl={image}
        setCroppedPostUrlFunc={(data) => {
          const file = new File([data], "file", { type: data.type });
          setFile(file);
          setImage(URL.createObjectURL(file));
        }}
        resetCropper={setCropMedia}
      />
    </>
  );
};

export default memo(NormalChatBar);
