import { memo } from "react";

import { getReqById } from "@/api/chat";
import ActionButton from "@/components/common/action-button";
import { ModalService } from "@/components/modals";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import socketChannel from "@/utils/chatSocket";
import { formatCurrency } from "@/utils/formatter";

const PromotionActionButtons = ({
  message,
  receiver,
  setCallReqId,
}: {
  message: MessageInterface;
  receiver: any;
  setCallReqId: (value: string) => void;
}) => {
  const dispatch = useAppDispatch();
  const user = useAppSelector((s) => s.user);

  const handleAccept = async () => {
    dispatch(chatActions.setFocusedReq(message));

    if (["VIDEO", "VOICE"].includes(message?.meta?.type)) {
      ModalService.open("BUY_PROMOTION", {
        receiver: { ...user },
        price: message?.meta?.offered_amount || message?.meta?.price,
        has_discount: message?.meta?.has_discount,
        ...(message?.meta?.has_discount && {
          discount: message?.meta?.discount,
        }),
        ...(message?.meta?.offered_amount && {
          offered_amount: message?.meta?.offered_amount,
        }),
        requestId: message?.meta?.reqId,
        sender_id: message?.sender_id || message?.sid,
        onClick: async () => {
          socketChannel?.channel?.editMessage({
            message_id: message._id || message?.messageId,
            data: {
              meta: {
                ...message?.meta,
                requestAccept: true,
              },
            },
          });

          if (message?.meta?.type === "CUSTOM-SERVICE") return;
          getReqById(message?.meta?.reqId!).then((res: any) => {
            dispatch(chatActions.setCallToken(res.data[0].token[user.id]));

            if (message?.meta?.reqId) {
              dispatch(chatActions.setReqId(message?.meta?.reqId));
              setCallReqId(message?.meta?.reqId);
              if (message?.meta?.type !== "RATING")
                socketChannel.channel?.sendMessage({
                  message: `${
                    message?.meta?.type === "VOICE" ? "Voice" : "Video"
                  } Call`,
                  meta: {
                    type: "ACCEPT_CALL",
                    chat_list_message: `${
                      message?.meta?.type === "VOICE" ? "Voice" : "Video"
                    } Call`,
                    token: res.data[0].token,
                    reqId: message?.meta?.reqId,
                  },
                });
            }
          });
        },
      });
    } else if (message?.meta?.type === "CUSTOM-SERVICE") {
      ModalService.open("CUSTOM_REQUEST", {
        service_id: message?.meta?.serviceId || "",
        ...(message?.meta?.offered_amount && {
          offered_amount: message?.meta?.offered_amount,
        }),
        request_id: message?.meta?.reqId || "",
        amount:
          message?.meta?.offered_amount ||
          message?.meta?.price! -
            (message?.meta?.discount?.discount_value ?? 0) ||
          0,
        has_discount: message?.meta?.has_discount ?? false,
        is_flexible: false,
        ...(message?.meta?.has_discount && {
          discount: message?.meta?.discount,
        }),
      });
    } else {
      if (document) {
        const element = document.querySelector("#ratingReqModal");

        if (element) {
          const modal = new window.bootstrap.Modal(element);
          modal.show();
        }
      }

      dispatch(chatActions.setPromotionMessage(message));
    }
  };

  return (
    <ActionButton onClick={handleAccept}>
      Accept for{" "}
      {formatCurrency(
        (() => {
          const price = message?.meta?.price;
          const hasDiscount = message?.meta?.has_discount;
          const discount = message?.meta?.discount;

          if (message?.meta?.offered_amount)
            return message?.meta?.offered_amount;

          if (
            price !== undefined &&
            hasDiscount &&
            discount?.discount_value !== undefined
          ) {
            return discount?.discount_value;
          }

          return price ?? 0;
        })()
      )}
    </ActionButton>
  );
};

export default memo(PromotionActionButtons);
