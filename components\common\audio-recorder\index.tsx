import Image from "next/image";
import { memo, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import WaveAudioPlayer from "@/components/WaveAudioPlayer";

import {
  FinishRecordingIcon,
  MicIconChatbar,
  PauseMicIcon,
} from "../chat/chat-box/utils/svg-utils";
import Visualizer from "../visualizer";

function AudioRecorder({ audioBlob, setAudioBlob, modalClose }: any) {
  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const audioChunks = useRef<Blob[]>([]);
  const [holdingMic, setHoldingMic] = useState<boolean>(false);
  const [audioMsRef, setAudioMsRef] = useState<MediaStream | null>(null);
  const [hasStarted, setHasStarted] = useState<boolean>(false);
  const [time, setTime] = useState(0);

  useEffect(() => {
    if (holdingMic) {
      const interval = setInterval(() => {
        setTime((prev) => prev + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [holdingMic]);

  useEffect(() => {
    if (!audioBlob) {
      setAudioMsRef(null);
      mediaRecorder.current = null;
      audioChunks.current = [];
    }
  }, [audioBlob]);

  useEffect(() => {
    if (modalClose && hasStarted) {
      end();
      setAudioBlob(null);
      setAudioMsRef(null);
      mediaRecorder.current = null;
      audioChunks.current = [];
    }
  }, [modalClose]);

  const toggle = () => {
    if (holdingMic) {
      pause();
    } else {
      resume();
    }
  };

  const formatTimer = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds % 60;
    const formattedMinutes = minutes.toString().padStart(2, "0");
    const formattedSeconds = seconds.toString().padStart(2, "0");
    return `${formattedMinutes}:${formattedSeconds}`;
  };

  const resume = async () => {
    if (audioBlob) {
      Swal.fire({
        title: "You already have an audio.",
        icon: "error",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Okay!",
        customClass: {
          confirmButton: "custom-btn",
          cancelButton: "custom-btn",
        },
      });
      return;
    }

    setHasStarted(true);

    if (!mediaRecorder.current) {
      const ms = await window.navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: {
            ideal: 2,
          },
        },
      });
      setAudioMsRef(ms);

      createMediaRecorder(ms);
      mediaRecorder.current!.start();
    } else {
      mediaRecorder.current.resume();
    }

    setHoldingMic(true);
  };

  const pause = () => {
    setHoldingMic(false);
  };

  const end = () => {
    setHasStarted(false);
    mediaRecorder.current!.stop();

    audioMsRef?.getTracks().forEach((track) => track.stop());
    setHoldingMic(false);
    setAudioMsRef(null);

    setTime(0);
  };

  const createMediaRecorder = (ms: MediaStream) => {
    mediaRecorder.current = new MediaRecorder(ms);

    mediaRecorder.current.ondataavailable = (e) => {
      audioChunks.current.push(e.data);
    };

    mediaRecorder.current.onstop = () => {
      const audioBlob = new Blob(audioChunks.current, {
        type: mediaRecorder.current!.mimeType,
      });
      const audioType = mediaRecorder.current!.mimeType;
      let audioFile = null;

      if (audioBlob) {
        let extension = "";
        if (audioType.includes("webm")) extension = "weba";
        else if (audioType.includes("ogg")) extension = "ogg";
        else if (audioType.includes("mp4")) extension = "mp4";
        audioFile = new File([audioBlob], `audio.${extension}`, {
          type: audioType,
        });
      }

      setAudioBlob(audioFile);
    };
  };

  return (
    <div>
      <div className="d-flex gap-2 align-items-center">
        <span className="mx-1 pointer" onClick={toggle}>
          {holdingMic ? (
            <PauseMicIcon />
          ) : (
            <>
              {!audioBlob && <MicIconChatbar />}
              {!hasStarted && !audioBlob && (
                <button className="ms-3 btn btn-purple">Start recording</button>
              )}
            </>
          )}
        </span>

        {hasStarted && (
          <div
            style={{
              height: "30px",
              maxWidth: "500px",
              border: "1px solid #EBEBEC",
            }}
            className="d-flex w-100 rounded mx-2 position-relative"
          >
            <Visualizer
              mediaStream={holdingMic ? audioMsRef : null}
              color="#AC1991"
              mainClassName="w-100 h-100 me-5"
            />
            <span className="position-absolute end-0 m-1 fs-8 ">
              {formatTimer(time)}
            </span>
          </div>
        )}
        {hasStarted && (
          <span className="pointer" onClick={() => end()}>
            <FinishRecordingIcon />
          </span>
        )}
      </div>

      {audioBlob && (
        <div className="d-flex gap-2 align-items- border p-2 rounded-2">
          <button
            type="button"
            className="border-0 bg-transparent"
            onClick={() => {
              setAudioBlob(null);
              setAudioMsRef(null);
              mediaRecorder.current = null;
              audioChunks.current = [];
            }}
          >
            <Image
              src="/images/common/delete.svg"
              alt=""
              style={{ filter: "brightness(0)" }}
              width={13}
              height={14}
            />
          </button>
          <WaveAudioPlayer
            height={25}
            url={URL.createObjectURL(audioBlob)}
            className="rounded border p-1"
            style={{
              width: "200px",
            }}
            progressColor="#ac1991"
            waveColor="#cecece"
          />
        </div>
      )}
    </div>
  );
}

export default memo(AudioRecorder);
