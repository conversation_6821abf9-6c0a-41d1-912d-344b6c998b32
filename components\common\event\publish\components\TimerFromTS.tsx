import { useEffect, useState } from "react";

import { dateDiffInHhMmSs } from "@/utils/number";

// TODO: Red timer when less then 30 maybe...
export function TimerFromTS(props: { ts: number; reverse: boolean }) {
  const [time, setTime] = useState("...");

  useEffect(() => {
    const loopRef = setInterval(() => {
      if (props.reverse && props.ts - Date.now() < 0) {
        clearInterval(loopRef);
        setTime("00:00:00");
        return;
      }

      setTime(dateDiffInHhMmSs(props.ts));
    }, 1000);

    return () => {
      if (loopRef) clearInterval(loopRef);
    };
  }, [props.ts, props.reverse]);

  return time;
}
