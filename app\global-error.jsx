"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import { useEffect } from "react";

import Error500 from "@/components/misc/500";
import { getTokenData } from "@/utils/jwt";

import { useAppSelector } from "@/redux-store/hooks";

export default function GlobalError({ error }) {
  const token = useAppSelector((s) => s.user.token);
  useEffect(() => {
    const eid = Sentry.captureException(error);

    setTimeout(() => {
      const decodedToken = getTokenData(token);
      Sentry.showReportDialog({
        eventId: eid,
        user: {
          name: decodedToken.id ? decodedToken.username : undefined,
          email: decodedToken.id ? decodedToken.email : undefined,
        },
        onClose() {
          window.location.href = "/";
        },
      });
    }, 300);
  }, [error]);

  return (
    <html>
      <body>
        <Error500 />
      </body>
    </html>
  );
}
