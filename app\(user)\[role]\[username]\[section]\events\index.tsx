import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { memo } from "react";

import PostList from "@/components/common/post/list";
import useScrollIntoView from "@/hooks/useScrollIntoView";
import { PostListEnum } from "@/types/post";

import "./index.scss";

import { LiveCardsList } from "@/app/(user)/(home)/live";

const Events = ({ uid }: { uid: string }) => {
  const searchParams = useSearchParams();
  const scrollIntoView = useScrollIntoView();
  const currentTab =
    searchParams.get("type") === "ended"
      ? 2
      : searchParams.get("type") === "upcoming-events"
      ? 1
      : 0;

  return (
    <>
      <div className="subs-options rounded-top-3 bg-cream">
        <div
          className="d-flex  gap-4 align-items-center  w-100 overflow-auto p-md-3 px-3 py-2"
          id="pills-tab"
          role="tablist"
        >
          <Link
            href={`?type=live`}
            className={`btn tab ${currentTab === 0 ? "active" : ""}`}
            type="button"
          >
            Live
          </Link>
          <Link
            href={`?type=upcoming-events`}
            className={`btn tab ${currentTab === 1 ? "active" : ""}`}
            type="button"
          >
            Upcoming
          </Link>
          <Link
            className={`btn tab ${currentTab === 2 ? "active" : ""}`}
            type="button"
            href={`?type=ended`}
          >
            Ended
          </Link>
        </div>
      </div>
      <div>
        {currentTab === 0 && (
          <div className="bg-body p-lg-3 pe-lg-1 ">
            <LiveCardsList type="profile" uid={uid} />
          </div>
        )}
        {currentTab === 1 && (
          <div>
            <PostList
              uid={uid}
              type={PostListEnum.EVENTS}
              eventStatus="upcoming"
            />{" "}
          </div>
        )}

        {currentTab === 2 && (
          <div>
            <PostList
              uid={uid}
              type={PostListEnum.EVENTS}
              eventStatus="ended"
            />{" "}
          </div>
        )}
      </div>
    </>
  );
};

export default memo(Events);
