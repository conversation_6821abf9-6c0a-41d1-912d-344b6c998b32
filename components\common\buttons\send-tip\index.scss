.tip-wrapper {
  padding: 0 20%;

  @media (max-width: 992px) {
    padding: 0;
  }
}
.payment-mode-section {
  display: flex;
  gap: 1rem;
  justify-content: center;
  border-bottom: 1px solid rgba(235, 235, 236, 1);
}
.payment-mode {
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
}
.active-mode {
  border-bottom: 1px solid var(--primary-color);
  color: var(--primary-color);
}
.active-amount {
  background: var(--primary-color) !important;
  color: #ffffff !important;
}
.payment-amounts {
  padding: 4px 10px 4px 10px;
  border-radius: 8px;
  background: var(--bg-cream);
  color: black;
  cursor: pointer;
  text-align: center;
}
.payment-amount-wrapper {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;

  @media (max-width: 786px) {
    grid-template-columns: repeat(3, 1fr);
  }
}
.wallet-balance {
  color: rgba(39, 177, 255, 1);
}
.swal2-container {
  z-index: 1111 !important;
}

.golden {
  width: 10rem;

  @media (max-width: 786px) {
    width: 8rem;
  }
}

.onlyIcon {
  flex-grow: 0 !important;
  padding: 0 !important;
  box-shadow: none;
  padding: 0.4rem !important;
  min-width: auto !important;
}
.onlyIcon img {
  scale: 1.2;
}

.tip-success-btn {
  width: 15rem !important;
  border-radius: 8px !important;
}
.tip-title {
  color: #29a81e !important;
  font-weight: 500 !important;
}
.tip-deny-btn {
  width: 15rem;
  background: #ffffff !important;
  color: #131416 !important;
  border-radius: 8px !important;
  border: 1px solid #131416 !important;
}

.vertical-btn {
  flex-direction: column !important;
}
.reel {
  padding: 0.3rem !important;
  border: 0 !important;
  background-color: var(--warning-color) !important;
  img {
    scale: initial !important;
  }
}
