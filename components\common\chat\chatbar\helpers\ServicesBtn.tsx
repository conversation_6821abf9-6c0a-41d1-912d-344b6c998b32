/* eslint-disable @next/next/no-img-element */
import { ModalService } from "@/components/modals";

import { ServicesIcon } from "../svg";

/**
 * ServicesBtn component
 *
 * @param {Object} props Component props
 * @prop {Object} target Target user information
 * @prop {string} target.target_user_id Target user id
 * @prop {string} target.target_user_display_name Target user display name
 * @prop {boolean} [is_text_required] Whether text is required or not (default: true)
 * @prop {Function} [onClickHandler] Click handler function
 * @prop {string} [classes] Additional CSS classes
 *
 * @returns {React.ReactElement} ServicesBtn component
 */
const ServicesBtn = (props: {
  target: {
    target_user_id: string;
    target_user_display_name: string;
  };
  style?: React.CSSProperties;
  is_text_required?: boolean;
  onClickHandler?: () => void;
  classes?: string;
  from?: "reel" | "post" | "grid";
}) => {
  const isTextRequired = props.is_text_required ?? true;
  const isGridMobile = props.from === "grid" && window.innerWidth < 768;
  return (
    <>
      {props.from === "reel" ? (
        <>
          <img
            onClick={() => {
              if (props.onClickHandler) {
                props.onClickHandler();
              }

              ModalService.open("SHOW_SERVICES", {
                authorName: props.target.target_user_display_name,
                targetUserId: props.target.target_user_id,
              });
            }}
            src="/images/reels/service.svg"
            alt=""
            width={40}
            height={40}
            className="pointer"
          />
        </>
      ) : (
        <div
          className={
            props.classes ||
            `${`${
              isGridMobile ? " p-1 rounded-1" : "p-2 rounded"
            } rounded d-flex gap-2 justify-content-center align-items-center pointer `} `
          }
          style={
            props.style || {
              backgroundColor: "rgba(233, 247, 255, 1)",
            }
          }
          onClick={() => {
            if (props.onClickHandler) {
              props.onClickHandler();
            }

            ModalService.open("SHOW_SERVICES", {
              authorName: props.target.target_user_display_name,
              targetUserId: props.target.target_user_id,
            });
          }}
        >
          <ServicesIcon res={isGridMobile ? "11" : "24"} />

          {
            <span
              className={
                !isTextRequired ? "d-none" : "fw-semibold d-none d-md-block"
              }
              style={{
                color: "rgba(82, 192, 255, 1)",
              }}
            >
              Services
            </span>
          }
        </div>
      )}
    </>
  );
};

export default ServicesBtn;
