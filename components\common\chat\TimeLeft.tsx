import { useEffect, useState } from "react";

import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { BuyerInterface } from "@/redux-store/slices/chat.slice";

const TimeLeft = ({
  buyDetails,
  targetName,
}: {
  buyDetails: BuyerInterface[];
  targetName: string;
}) => {
  const dispatch = useAppDispatch();

  const userId = useAppSelector((s) => s.user.id);

  const [_t, setT] = useState(Date.now());
  const [me, setMe] = useState<BuyerInterface>();

  const chatList = useAppSelector((s) => s.chat.chatList);
  const channelId = useAppSelector((s) => s.chat.channelId);
  const targetUser = useAppSelector((s) => s.chat.targetUser);

  function filterBuyers() {
    const relevantChatIndex = chatList.findIndex(
      (c) => c.converse_channel_id === channelId
    );

    if (relevantChatIndex !== -1) {
      const updatedChat = {
        ...chatList[relevantChatIndex],
        buyers: chatList[relevantChatIndex].buyers
          .filter((b) => new Date(b.expires_at) > new Date())
          .sort(
            (a, b) =>
              new Date(b.expires_at).getTime() -
              new Date(a.expires_at).getTime()
          ),
      };

      const updatedChatList = [
        ...chatList.slice(0, relevantChatIndex),
        updatedChat,
        ...chatList.slice(relevantChatIndex + 1),
      ];

      dispatch(chatActions.setChatUserList(updatedChatList));
    }
  }

  useEffect(() => {
    const matchingBuyer = buyDetails.find(
      (b) => b.buyer === userId || b.buyer === targetUser
    );
    setMe(matchingBuyer || undefined);
  }, [buyDetails, userId]);

  useEffect(() => {
    const interval = setInterval(() => {
      setT(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const expiresAt = new Date(me?.expires_at || 0);
  const hasExpired = expiresAt < new Date();

  useEffect(() => {
    if (hasExpired) {
      filterBuyers();
    }
  }, [hasExpired]);

  if (hasExpired) return null;

  if (me?.service_id?.chat_fee_type === "OneOff") return null;

  const timeLeft = formatTimeLeft(expiresAt);

  return (
    <div
      className="p-2 fs-7"
      style={{
        backgroundColor: "rgba(245, 245, 246, 1)",
        color: "rgba(77, 80, 83, 1)",
      }}
    >
      {me?.buyer === userId ? "You have" : (targetName ?? "The user") + " has"}{" "}
      paid the chat fee. {me?.buyer === userId ? "Your chat" : "Chat"} fee
      subscription ends in {timeLeft}.
    </div>
  );
};

function formatTimeLeft(endDate: Date): string {
  const now = new Date();
  const diff = Math.max(0, endDate.getTime() - now.getTime()); // Difference in milliseconds

  const seconds = Math.floor(diff / 1000) % 60;
  const minutes = Math.floor(diff / (1000 * 60)) % 60;
  const hours = Math.floor(diff / (1000 * 60 * 60)) % 24;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24)) % 30; // Approximate 30 days
  const months = Math.floor(diff / (1000 * 60 * 60 * 24 * 30)) % 12;
  const years = Math.floor(diff / (1000 * 60 * 60 * 24 * 365)); // Approximate year

  const pad = (num: number) => (num < 10 ? "0" + num : num);

  if (years > 0) {
    return `${years} year${years > 1 ? "s" : ""}, ${months} month${
      months > 1 ? "s" : ""
    }`;
  }

  if (months > 0) {
    return `${pad(months)} month${months > 1 ? "s" : ""}, ${pad(days)} day${
      days > 1 ? "s" : ""
    }`;
  }

  if (days > 0) {
    return `${pad(days)} day${days > 1 ? "s" : ""}, ${pad(hours)} hour${
      hours > 1 ? "s" : ""
    }`;
  }

  if (hours > 0) {
    return `${pad(hours)} hour${hours > 1 ? "s" : ""}, ${pad(minutes)} minute${
      minutes > 1 ? "s" : ""
    }`;
  }

  if (minutes > 0) {
    return `${pad(minutes)} minute${minutes > 1 ? "s" : ""}, ${pad(
      seconds
    )} second${seconds > 1 ? "s" : ""}`;
  }

  return `${pad(seconds)} second${seconds > 1 ? "s" : ""}`;
}

export default TimeLeft;
