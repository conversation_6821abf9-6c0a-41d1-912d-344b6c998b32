import { type PossibleValues } from "@/components/common/select-array-v2";
import type { Media } from "@/types/media";
import type { Author } from "@/types/post";
import type {
  Group,
  Member,
  PostMember,
  SocialMedia,
  SubscriptionData,
} from "@/types/profile";

import API from ".";
import type { SubscriptionSchema } from "./channel";

interface GroupSubmitResponse {
  data: { group_id: string };
}

const GroupToAPIGroupTransformer = (body: Group) => {
  const form = new FormData();

  form.append("name", body.name);
  form.append("tag_name", body.username);
  form.append("topic", body.topic);
  form.append("social_handles", JSON.stringify(body.social_media));
  form.append("about", body.description);
  form.append("hashtags", JSON.stringify(body.hashtags));

  const members: PostMember[] = [];
  body.members.forEach((mem) => {
    members.push({
      member_id: mem?.member_id?._id,
      member_type: mem?.member_type,
      earning: mem?.earning || 1,
    });
  });
  form.append("members", JSON.stringify(members));
  form.append("background", body.cover);
  const subscriptions: SubscriptionSchema[] = [];

  body.subscribe.array.forEach((sub) => {
    subscriptions.push({
      offer_type: body.subscribe.offer_type,
      trial_period: parseInt(body.subscribe.trial_period) || 0,
      validity_type: sub.subscription_type,
      price: sub.price!,
      is_active: true,
    });
  });

  form.append("subscription", JSON.stringify(subscriptions));

  const sub_perks: { type: string; description: string; emoji?: string }[] = [];
  body.subscription_perks.forEach((s) => {
    const { icon, value, emoji } = s;
    const perk = {
      type: icon,
      description: value,
      ...(emoji && { emoji }),
    };
    sub_perks.push(perk);
  });

  form.append("perks", JSON.stringify(sub_perks));

  // FileHandler.isLocalFile(body.avatar) &&
  //   form.append("avatar", FileHandler.get(body.avatar));
  // FileHandler.isLocalFile(body.cover) &&
  //   form.append("background", FileHandler.get(body.cover));

  return form;
};

export function CreateNewGroup(body: Group) {
  const form = GroupToAPIGroupTransformer(body);
  return API.post(API.GROUP, form) as Promise<GroupSubmitResponse>;
}

interface GroupSubscription {
  offer_type: string;
  subscription_type: string;
  price: number;
  is_active: boolean;
}

interface UpdateGroupBody {
  about?: string;
  hashtags?: string[];
  social_handles?: SocialMedia[];
  name?: string;
  members?: Member[];
  perks: {
    icon: PossibleValues;
    value: string;
    emoji?: string;
  }[];
}

export function CreateGroupSubscription(body: GroupSubscription, id: string) {
  return API.post(`${API.GROUP}/${id}/subscriptions`, body) as Promise<any>;
}

const UpdateGroupToAPIGroupTransformer = (body: UpdateGroupBody) => {
  let data = {
    social_handles: body.social_handles,
    about: body.about,
    name: body.name,
    hashtags: body.hashtags,
  };

  const sub_perks: {
    type: PossibleValues;
    description: string;
    emoji?: string;
  }[] = [];
  body.perks.forEach((s) => {
    const { icon, value, emoji } = s;
    const perk = {
      type: icon,
      description: value,
      ...(emoji && { emoji }),
    };
    sub_perks.push(perk);
  });
  data = {
    ...data,
    perks: sub_perks,
  };

  return data;
};

export function UpdateGroup(body: UpdateGroupBody, groupId: any) {
  const data = UpdateGroupToAPIGroupTransformer(body);

  return API.patch(
    `${API.GROUP}/${groupId}`,
    data
  ) as Promise<GroupSubmitResponse>;
}

interface UpdateBackgroundBody {
  cover: File | string;
}

const BackgroungImgAPIGroupTransformer = (body: UpdateBackgroundBody) => {
  const form = new FormData();
  form.append("background", body.cover);
  // FileHandler.isLocalFile(body.cover) &&
  //   form.append("background", FileHandler.get(body.cover));

  return form;
};

export function UpdateGroupBackground(
  body: UpdateBackgroundBody,
  groupId: any
) {
  const form = BackgroungImgAPIGroupTransformer(body);
  return API.patch(
    `${API.GROUP}/${groupId}/background`,
    form
  ) as Promise<GroupSubmitResponse>;
}

interface CounterData {
  post_count: number;
  scheduled_post_count: number;

  media_count: {
    image_count: number;
    video_count: number;
    private_image_count: number;
    private_video_count: number;
    scheduled_image_count: number;
    scheduled_video_count: number;
  };
  subscriber_count: number;
  follower_count: number;
  private_post_count: number;
  private_media_count: number;
}
export interface APIGroup {
  _id: string;
  name: string;
  topic: string;
  tag_name: string;
  author?: Author;
  about: string;
  social_handles: SocialMedia[];
  hashtags: string[];
  is_public: boolean;
  marked_as_deleted?: boolean;
  is_deleted: boolean;
  is_subscribed: boolean;
  my_subscription_data: SubscriptionData;
  avatar: Media[];
  background: Media[];

  subscription: SubscriptionSchema[];
  members: Member[];
  created_at: string;
  updated_at: string;
  __v: number;
  counter?: CounterData;
  totalLikes?: number;
  is_activated: boolean;
  deletion_request?: {
    requested_by: string;
    deletion_scheduled_on: string;
  }[];
  scheduled_deletion_date?: string;
  perks: { type: PossibleValues; description: string; emoji?: string }[];
}

interface GetGroupListResponse {
  data: APIGroup[];
}

export interface GetGroup {
  name: string;
  _id: string;
  avatar: Media[];
  background: Media[];
  tag_name: string;
  marked_as_deleted?: boolean;
  deletion_request?: {
    requested_by: string;
    deletion_scheduled_on: string;
  }[];
  members: Member[];
  is_paid?: boolean;
  counter?: any;
  totalLikes?: number;
}
export const GetGroupList = async (
  userId?: string,
  page?: number,
  limit?: number
) =>
  API.get(
    `${API.GROUP}${userId ? `?target_user=${userId}` : ""}${
      page ? `&page=${page}` : ""
    }${limit ? `&limit=${limit}` : ""}`
  ) as Promise<GetGroupListResponse>;

interface GetGroupByUsernameResponse {
  data: APIGroup;
}

export const GetGroupByUsername = async (groupId: string) =>
  API.get(`${API.GROUP}/${groupId}`) as Promise<GetGroupByUsernameResponse>;

export const RespondRequest = async (
  groupId: string,
  action: string,
  notificationId?: string
) => {
  const url = notificationId
    ? `${API.GROUP}/${groupId}/membership_request/${notificationId}`
    : `${API.GROUP}/${groupId}/membership_request`;

  return API.put(url, {
    action,
  }) as Promise<any>;
};

export const RespondTagRequest = async (params: {
  notification_id: string;
  action: "accept" | "reject";
}) => {
  return API.patch(
    `${API.USERS}/${params.notification_id}/tagged-request/${params.action}`,
    {}
  ) as Promise<any>;
};

export interface AddMembersBody {
  member_id: string;
  earning: number;
  member_type: string;
}

export const AddGroupMembers = async (groupId: any, body: AddMembersBody) => {
  API.post(`${API.GROUP}/${groupId}/request`, body) as Promise<any>;
};

export const getGroupFollowers = async (groupId: string) =>
  API.get(`${API.GROUP}/${groupId}/followers`);

export const GetGroupSubscribers = async (groupId: string) =>
  API.get(`${API.GROUP}/${groupId}/subscribers`);

export const EditGroupTags = async (channelId: string, body: any) =>
  API.patch(`${API.GROUP}/${channelId}/update-tags`, body) as Promise<any>;

export const LeaveGroup = async (collab_id: string) => {
  API.post(`${API.GROUP}/${collab_id}/exit`, {}) as Promise<any>;
};

export const CancelLeaveGroup = async (collab_id: string) => {
  API.patch(`${API.GROUP}/${collab_id}/exit/cancel`, {}) as Promise<any>;
};
