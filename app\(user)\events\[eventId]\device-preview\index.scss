.source-icon {
  color: var(--primary-color) !important;
  box-shadow: 0 0 0 1pt var(--primary-color) !important;
  width: max-content;

  .btn-check:checked + & {
    color: var(--bg-color) !important;
    background-color: var(--primary-color) !important;
    box-shadow: 0 0 0 1px var(--bg-color) !important;

    > img {
      filter: brightness(0) invert(1) contrast(100);
    }
  }
}

.visualizer-wrapper {
  width: 100%;
  height: 2.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 1.5rem;
  margin-top: -0.5rem;
  background-color: var(--bg-cream);

  span {
    white-space: nowrap;
    margin-right: 1rem;
  }

  .visualizer {
    flex-grow: 1;
    height: 70%;
  }
}
