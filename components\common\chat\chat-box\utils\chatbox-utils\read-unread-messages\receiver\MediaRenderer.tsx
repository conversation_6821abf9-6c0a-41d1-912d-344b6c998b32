import classNames from "classnames";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Swal from "sweetalert2";

import { PurchaseMedia } from "@/api/chat";
import { GetSignedUrl } from "@/api/user";
import WaveAudioPlayer from "@/components/WaveAudioPlayer";
import ActionButton from "@/components/common/action-button";
import {
  InformationIcon,
  RatingReceiverSent,
} from "@/components/matchmaker/match-cards/utils/svg";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import type { Variation } from "@/types/media";
import { getAssetUrl } from "@/utils/assets";
import socketChannel from "@/utils/chatSocket";
import {
  convertSeconds,
  formatCurrency,
  getResolution,
} from "@/utils/formatter";

import { isAudioMedia, isImageMedia } from "..";
import ReadMoreContent from "../../../../read-more-content";
import { ProcessingLoader } from "../../../svg-utils";

const MediaRenderer = ({
  message,
  setImageMessage,
}: {
  message: MessageInterface;
  setImageMessage: (value: any) => void;
}) => {
  const router = useRouter();
  const media = message?.meta?.media;
  const mediaFee = message?.meta?.media_fee;
  const mediaStatus = Array.isArray(media) ? media[0]?.status : media?.status;
  const mediaType = Array.isArray(media) ? media[0]?.type : media?.type;

  const [customMedialUrl, setCustomMediaUrl] = useState("");

  useEffect(() => {
    if (mediaStatus !== "Completed" || !media) return;

    const [name, ext] = String(
      Array.isArray(media) ? media?.[0]?.path : media?.path
    )?.split(".");

    if (!name || !ext) return;

    GetSignedUrl({
      assets_path: [`${name}_compressed.${ext}`],
      type: "signedFullUrl",
    }).then((response) => {
      setCustomMediaUrl(response.result[`${name}_compressed.${ext}`]);
    });
  }, [media]);

  const mediaUrl = (
    variation: Variation = "compressed",
    is_poster_required: boolean = false
  ) => {
    return Array.isArray(media)
      ? getAssetUrl({
          media: media[0],
          variation,
          ...(is_poster_required
            ? { poster: media?.[0]?.poster ? true : false }
            : {}),
          defaultType: "background",
        })
      : getAssetUrl({
          media,
          variation,
          ...(is_poster_required
            ? { poster: media?.poster ? true : false }
            : {}),
          defaultType: "background",
        });
  };

  const unlockAudio = (message: MessageInterface) => {
    if ("is_unlocked" in message.meta && message?.meta?.is_unlocked) {
      socketChannel.channel?.editMessage({
        message_id: message._id || message?.messageId,
        data: {
          ...message,
          meta: {
            ...message?.meta,
            is_unlocked: true,
          },
        },
      });
      return;
    }

    Swal.fire({
      text: "Do you want to proceed with this payment?",
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "No",
      confirmButtonColor: "#AC1991",
      confirmButtonText: "Yes",

      showCloseButton: true,
    }).then((result) => {
      if (result.isConfirmed) {
        PurchaseMedia({
          chat_media_id: !Array.isArray(message?.meta?.media)
            ? message?.meta?.media?._id!
            : message?.meta?.media?.[0]?._id,
          payment_mode: "wallet",
          seller_id: message?.sender_id || message?.sid || "",
        })
          .then(() => {
            socketChannel.channel?.editMessage({
              message_id: message._id || message?.messageId,
              data: {
                ...message,
                meta: {
                  ...message?.meta,
                  is_unlocked: true,
                },
              },
            });
          })
          .then(() => {
            Swal.fire({
              title: "Unlocked",
              text: "Your message has been unlocked.",
              icon: "success",
              confirmButtonText: "Got it!",
              confirmButtonColor: "#AC1991",
              customClass: {
                confirmButton: "custom-btn",
                cancelButton: "custom-btn",
              },
              showCloseButton: true,
            });
          })
          .catch((error) => {
            Swal.fire({
              text: error.message,
              icon: "error",
              cancelButtonText: "Cancel",
              confirmButtonText: "Finish",
              confirmButtonColor: "#AC1991",
              customClass: {
                confirmButton: "custom-btn",
                cancelButton: "custom-btn",
              },
              showCloseButton: true,
            });
          });
      }
    });
  };

  const handleImageClick = () => {
    if (message?.meta?.address) return;
    if (message.meta.media_fee > 0 && !message.meta.is_unlocked) return;
    setImageMessage(message);

    if (!media) return;

    const { path } = Array.isArray(media) ? media?.[0] : media;

    if (["video", "image"].includes(mediaType!)) {
      window.KNKY.showFullscreenMedia(
        mediaType === "audio" ? "image" : mediaType!,
        path.startsWith("vault") ? customMedialUrl : mediaUrl()
      );
    }
  };

  const renderMediaBadge = (need_absolute: boolean = true) =>
    mediaFee > 0 && (
      <div
        className={classNames(
          "m-2 top-0 end-0",
          {
            "badge text-bg-warning": !message?.meta?.is_unlocked,
            "badge text-bg-success": message?.meta?.is_unlocked,
          },
          need_absolute ? "position-absolute" : "position-relative"
        )}
      >
        {!message?.meta?.is_unlocked ? "Pending" : "Paid"}{" "}
        {formatCurrency(mediaFee)}
      </div>
    );
  const renderMediaType = () => (
    <div className="position-absolute top-0 start-0 d-flex justify-content-center align-items-center gap-2">
      <div
        className="m-2 me-0 p-1 rounded"
        style={{
          backgroundColor: "rgba(0,0,0,0.4)",
        }}
      >
        <Image
          src={`/images/home/<USER>
            Array.isArray(message?.meta?.media)
              ? message?.meta?.media[0]?.type
              : message?.meta?.media?.type
          }.svg`}
          width={22}
          height={22}
          alt="video"
          style={{
            filter: "brightness(2)",
            marginTop: "-2px",
          }}
        />
      </div>
      {(Array.isArray(message?.meta?.media)
        ? message?.meta?.media[0]?.resolution
        : message?.meta?.media?.resolution) &&
        getResolution(
          Array.isArray(message?.meta?.media)
            ? message?.meta?.media[0]?.resolution!
            : message?.meta?.media?.resolution!
        ) && (
          <div
            className="p-1 rounded text-white fw-bold fs-8"
            style={{
              backgroundColor: "rgba(0,0,0,0.4)",
            }}
          >
            {getResolution(
              Array.isArray(message?.meta?.media)
                ? message?.meta?.media[0]?.resolution!
                : message?.meta?.media?.resolution!
            )}
          </div>
        )}
    </div>
  );

  const renderVideoLength = () => {
    return (
      <div className="position-absolute bottom-0 start-0 m-2">
        {(Array.isArray(message?.meta?.media)
          ? message?.meta?.media[0]?.duration
          : message?.meta?.media?.duration) && (
          <div
            className="p-1 rounded text-white fw-bold fs-8"
            style={{
              backgroundColor: "rgba(0,0,0,0.4)",
            }}
          >
            {convertSeconds(
              Array.isArray(message?.meta?.media)
                ? message?.meta?.media[0]?.duration!
                : message?.meta?.media?.duration!
            )}
          </div>
        )}
      </div>
    );
  };

  const renderMediaContent = () => {
    if (message?.meta?.type === "story-reply") return;

    if (isImageMedia(media) && mediaStatus === "Completed") {
      if (!media) return;

      const { path } = Array.isArray(media) ? media?.[0] : media;

      return (
        <>
          {"is_unlocked" in message.meta &&
            !message?.meta?.is_unlocked &&
            renderMediaType()}
          {renderMediaBadge()}
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            width={350}
            height={150}
            className="object-fit-cover mw-100 d-block rounded pointer"
            alt=""
            src={
              "is_unlocked" in message.meta &&
              message?.meta?.is_unlocked &&
              path.startsWith("vault")
                ? customMedialUrl
                : mediaUrl(
                    "is_unlocked" in message.meta && !message?.meta?.is_unlocked
                      ? "blur"
                      : "compressed"
                  )
            }
            onClick={handleImageClick}
          />
          {"is_unlocked" in message.meta && !message?.meta?.is_unlocked && (
            <ActionButton
              className="position-absolute top-50 start-50 translate-middle w-75"
              variant="white"
              type="solid"
              onClick={() => unlockAudio(message)}
            >
              Unlock for {formatCurrency(message?.meta?.media_fee)}
            </ActionButton>
          )}
        </>
      );
    }

    if (isAudioMedia(media) && mediaStatus === "Completed") {
      return (
        <div>
          <div className="text-end w-100">{renderMediaBadge(false)}</div>
          <WaveAudioPlayer
            height={25}
            disable={
              "is_unlocked" in message.meta && !message?.meta?.is_unlocked
            }
            url={mediaUrl()}
            className="mb-2 mt-2"
            progressColor="#ac1991"
            waveColor="#000"
          />
          {"is_unlocked" in message.meta && !message?.meta?.is_unlocked && (
            <ActionButton
              className="ms-2 my-2 d-block"
              onClick={() => unlockAudio(message)}
            >
              Pay {formatCurrency(message?.meta?.media_fee)} to hear
            </ActionButton>
          )}
        </div>
      );
    }

    if (mediaType === "video" && mediaStatus === "Completed") {
      return (
        <div>
          {"is_unlocked" in message.meta &&
            !message?.meta?.is_unlocked &&
            renderMediaType()}
          {renderMediaBadge()}
          {renderVideoLength()}
          {message.meta.is_unlocked || !("is_unlocked" in message.meta) ? (
            <video
              src={`${mediaUrl()}#t=0.1`}
              className="object-fit-cover mw-100 d-block rounded-1 pointer"
              width={350}
              height={150}
              playsInline
              controls
              poster={mediaUrl("compressed", true)}
              controlsList="nodownload"
            ></video>
          ) : (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={mediaUrl("compressed", true)}
              width={350}
              height={150}
              className="object-fit-cover mw-100 d-block rounded-1 pointer"
              alt=""
            />
          )}
          {"is_unlocked" in message.meta && !message?.meta?.is_unlocked && (
            <ActionButton
              className="position-absolute top-50 start-50 translate-middle w-75"
              onClick={() => unlockAudio(message)}
            >
              Unlock for {formatCurrency(message?.meta?.media_fee)}
            </ActionButton>
          )}
        </div>
      );
    }

    if (["Failed", "Optimizing", "Moderating"].includes(mediaStatus!)) {
      return (
        <div className="p-2 opacity-75">
          <InformationIcon />
          <span className="ms-2 color-medium">
            {mediaStatus === "Failed"
              ? "The moderation of the media has failed due to our content moderation policy."
              : "Processing"}
            {mediaStatus === "Failed" && (
              <Link
                className="pointer underline text-capitalize"
                href="/articles/content-moderation-and-protection-policy"
                target="_blank"
              >
                Read policy.
              </Link>
            )}
            {mediaStatus !== "Failed" && <ProcessingLoader color="#000" />}
          </span>
        </div>
      );
    }

    return null;
  };

  const renderRatingStars = () =>
    message?.meta?.stars && (
      <div className="text-center">
        <RatingReceiverSent starCount={message?.meta?.stars} />
      </div>
    );

  const renderMessageContent = () => {
    return (
      message.message &&
      message?.message !== "Attachment" && (
        <div className="d-flex align-items-center">
          <ReadMoreContent
            text={
              "meta" in message &&
              message.meta.type === "ACCEPT_CALL" &&
              message?.meta?.isCompleted
                ? message?.message === "Video Call"
                  ? "Video Call ended"
                  : "Voice Call ended"
                : message?.meta?.stars
                ? `You rated the user with ${message?.meta?.stars} stars.`
                : message.message
            }
            characterCount={700}
            classes="px-2 pt-1"
            textColor="black"
          />
        </div>
      )
    );
  };

  const renderAddress = () =>
    message?.meta?.address && (
      <div className="ms-2">
        Address:{" "}
        {`${message?.meta?.address.street}, ${message?.meta?.address.city}, ${
          message?.meta?.address.state
        }, ${message?.meta?.address.zip_code}, ${
          message?.meta?.address?.country?.split(":")?.[1]
        }`}
        .<br />
        <Link
          href={`/shop/${message.meta.transaction_id}/track`}
          className="mt-2 underline fw-bold"
        >
          Go to item.
        </Link>
      </div>
    );

  const renderEntityMessage = () =>
    message?.meta?.entity_type && (
      <div className="ms-2">
        <span className="fw-bold">
          {message?.meta?.entity_type.charAt(0).toUpperCase() +
            message?.meta?.entity_type.slice(1)}
        </span>
        : {message?.meta?.channel_name}
        <div>
          <span className="fw-bold">Subscription Plan:</span>{" "}
          {message?.meta?.subscription_type}
        </div>
        <Link
          href={`/channel/${message?.meta?.tag_name}`}
          className="pointer underline"
        >
          Visit the channel
        </Link>
      </div>
    );

  return (
    <div
      className={`position-relative ${
        mediaType === "audio" ? "w-100 h-100 p-1" : ""
      }`}
      onClick={() => {
        if (message?.meta?.address && message.meta.address.transaction_id)
          router.push(`/shop/${message.meta.address.transaction_id}/track`);
      }}
    >
      {renderMediaContent()}
      {renderRatingStars()}
      {(message?.meta?.type !== "ACCEPT_CALL" ||
        !["Failed", "Optimizing", "Moderating"].includes(mediaStatus!)) &&
        renderMessageContent()}
      {renderAddress()}
      {renderEntityMessage()}
    </div>
  );
};

export default MediaRenderer;
