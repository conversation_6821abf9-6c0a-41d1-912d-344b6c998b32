import "./index.scss";

import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

import useIsMobile from "@/hooks/useIsMobile";

// import ReactApexChart from "react-apexcharts";

const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

interface DonutChartProps {
  data: {
    labels: string[];
    series: number[];
  };
}

const DonutChart = ({ data }: DonutChartProps) => {
  const [chartData, setChartData] = useState({
    options: {
      labels: data.labels,
      plotOptions: {
        pie: {
          donut: {
            size: "50%", // Adjust the size of the donut hole
          },
        },
      },

      legend: {
        formatter: function (seriesName: any, opts: any) {
          return `${seriesName}  <span class="percentage">${
            opts.w.globals.series[opts.seriesIndex]
          }</span>`;
        },
        markers: {
          width: 20,
          height: 20,
          radius: 5,
        },
        // show: false,
        // fontSize: 14,
      },
      responsive: [
        {
          breakpoint: 992,
          options: {
            legend: {
              // position: "bottom",
              offsetY: 0,
            },
          },
        },
        {
          breakpoint: 786,
          options: {
            legend: {
              show: false,
              offsetY: 0,
            },
          },
        },
      ],
      dataLabels: {
        enabled: false,
      },
      colors: [
        "#751363",
        "#AC1191",
        "#A24F93",
        "#DCBDD7",
        "#F3E9F2",
        "#A24F93",
        "#DCBDD7",
        "#A24F93",
      ],
    },
    series: data.series,
  });
  const isMobile = useIsMobile();
  useEffect(() => {
    setChartData((prevChartData) => ({
      ...prevChartData,
      options: {
        ...prevChartData.options,
        labels: data.labels,
      },
      series: data.series,
      legend: {
        formatter: function (seriesName: any, opts: any) {
          return `${seriesName}  <span class="percentage">${
            opts.w.globals.series[opts.seriesIndex]
          }%</span>`;
        },
        width: "100%",
        offsetX: -10,
        offsetY: -20,
        fontSize: "16px",
        fontWeight: "600",
        itemMargin: {
          horizontal: 10,
          vertical: 10,
        },
        markers: {
          borderRadius: 8,
          width: 24,
          height: 24,
          offsetY: 0,
        },
      },
    }));
  }, [data]);

  return (
    <div className="donutChart">
      <ReactApexChart
        options={chartData.options}
        series={chartData.series}
        type="donut"
        width={isMobile ? 350 : 600}
      />
    </div>
  );
};

export default DonutChart;
