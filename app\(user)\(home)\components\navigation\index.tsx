import "./index.scss";

import { useWindowSize } from "@uidotdev/usehooks";
import classNames from "classnames";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { defaultActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

const HomeNavigation = () => {
  let route = usePathname();
  const userRole = useAppSelector((s) => s.user.role);

  if (route == "/") {
    route = "/trending";
  }

  const initialHashtag = useAppSelector((e) => e.connections.initialHashtag);
  // To Change Navigation Items as they are different and draggable for specified users
  // Enable it if any routing related issue happens in for you
  // if (route == "/interested") {
  //   route = `/interested?hashtag=${initialHashtag}`;
  // }

  const NavItems = [
    {
      href: "/fresh",
      title: "Fresh",
    },
    {
      href: "/trending",
      title: "Trending",
    },

    {
      href: "/subscribed",
      title: "Subscribed",
    },

    {
      href: "/following",
      title: "Following",
    },
    {
      href: "/premium",
      title: "Pay-To-View",
    },
    {
      // add ${initialHashtag} if required
      href: `/interested`,
      title: "For you",
    },
    {
      href: "/live",
      title: "Live",
    },
    {
      href: "/shop",
      title: "Shop",
    },
  ];

  const { width } = useWindowSize();

  return (
    <div className="home-nav bg-cream home-nav-height d-flex justify-content-between position-relative">
      <div
        className={classNames(
          "w-100 container-xxl d-flex justify-content-between",
          width && width < 768 && "px-0"
        )}
      >
        <div className="home-nav-content scrollable bd-gutter d-flex gap-3 p-2 w-100">
          {NavItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={` home-nav-item py-2 px-3 bg-body fw-semibold rounded-3 scroll-item ${
                item.href === route ? " nav-active" : ""
              }`}
            >
              {item.title}
            </Link>
          ))}
        </div>
        {userRole !== "guest" && <HomeFilters />}
      </div>
    </div>
  );
};

export default HomeNavigation;

const GenderOptions = [
  {
    value: "all",
    label: "All",
  },
  {
    value: "male",
    label: "Male",
  },
  {
    value: "female",
    label: "Female",
  },
  {
    value: "others",
    label: "Trans",
  },
];

const AuthorOptions = [
  {
    value: "all",
    label: "All",
  },
  {
    value: "creators",
    label: "Creators",
  },
  {
    value: "users",
    label: "Users",
  },
];

const HomeFilters = () => {
  const dispatch = useAppDispatch();
  const { homeFeedFilters } = useAppSelector((state) => state.defaults);
  const userRole = useAppSelector((s) => s.user.role);

  const handleGenderChange = (e: React.ChangeEvent<HTMLSelectElement>) =>
    dispatch(
      defaultActions.setHomeFeedFilters({
        gender: e.target.value as "all" | "male" | "female" | "others",
      })
    );

  const handleAuthorChange = (e: React.ChangeEvent<HTMLSelectElement>) =>
    dispatch(
      defaultActions.setHomeFeedFilters({
        posts_of: e.target.value as "all" | "users" | "creators",
      })
    );

  const handleViewChange = (e: "list" | "grid") => {
    dispatch(
      defaultActions.setHomeFeedFilters({
        view: e,
      })
    );
  };

  const route = usePathname();
  const hideViewToggle: boolean =
    route.includes("trending") || route.includes("shop");
  return (
    <div className={classNames("d-flex gap-3 px-2 align-items-center")}>
      {!hideViewToggle && (
        <div
          className={`${
            process.env.environment === "live" ? "d-none" : "d-flex"
          }   dropdown border border-1 pointer rounded-3`}
        >
          <div
            className={classNames(
              {
                "bg-body color-dark": homeFeedFilters.view === "list",
              },
              "px-2 py-1 rounded-3 fw-medium color-black"
            )}
            onClick={() => handleViewChange("list")}
          >
            List
          </div>
          <div
            className={classNames(
              {
                "bg-body color-dark": homeFeedFilters.view === "grid",
              },
              "px-2 py-1 rounded-3 fw-medium color-black"
            )}
            onClick={() => handleViewChange("grid")}
          >
            Grid
          </div>
        </div>
      )}
      <FiltersIcon
        fill={
          homeFeedFilters.gender !== "all" || homeFeedFilters.posts_of !== "all"
            ? "#ac1991"
            : ""
        }
      />
      <ul
        className="dropdown-menu border-0 custom-transform"
        style={{
          boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
        }}
      >
        <li className="fw-bold ms-3 mt-2">Feed Options</li>
        <li
          className={classNames(
            "ms-3 mt-3 mb-2 d-flex justify-content-between align-items-center"
          )}
        >
          <span className="w-100">Gender</span>
          <select
            className="form-select mx-2 border-0"
            aria-label="Gender Select"
            value={homeFeedFilters.gender}
            onChange={handleGenderChange}
          >
            {GenderOptions.map((item, key) => (
              <option value={item.value} key={key}>
                {item.label}
              </option>
            ))}
          </select>
        </li>
        <li
          className={classNames(
            "ms-3 mb-3 d-flex justify-content-between align-items-center",
            {
              "d-none": userRole !== "creator",
            }
          )}
        >
          <span className="w-100">Author</span>
          <select
            className="form-select mx-2 border-0"
            aria-label="Author Select"
            value={homeFeedFilters.posts_of}
            onChange={handleAuthorChange}
          >
            {AuthorOptions.map((item, key) => (
              <option value={item.value} key={key}>
                {item.label}
              </option>
            ))}
          </select>
        </li>
      </ul>
    </div>
  );
};

export const FiltersIcon = ({ fill }: { fill?: string }) => (
  <svg
    width="20"
    height="16"
    viewBox="0 0 28 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="pointer"
    data-bs-toggle="dropdown"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.34602 4.952C8.34602 3.50267 7.15268 2.32267 5.68202 2.32267C4.21535 2.32267 3.01935 3.50267 3.01935 4.952C3.01935 6.40133 4.21402 7.58133 5.68202 7.58133C7.15268 7.58133 8.34602 6.40133 8.34602 4.952ZM10.6993 4.952C10.6993 7.68133 8.45002 9.904 5.68202 9.904C2.91668 9.904 0.666016 7.68 0.666016 4.952C0.666016 2.22133 2.91668 0 5.68202 0C8.45002 0 10.6993 2.22133 10.6993 4.952ZM27.3327 4.952C27.3306 4.6421 27.2055 4.34572 26.985 4.12797C26.7645 3.91021 26.4666 3.78889 26.1567 3.79067H16.51C16.3565 3.78961 16.2042 3.81882 16.0619 3.87661C15.9196 3.9344 15.7901 4.01965 15.6808 4.12749C15.5715 4.23533 15.4845 4.36365 15.4247 4.50512C15.365 4.64659 15.3337 4.79844 15.3327 4.952C15.3327 5.592 15.8594 6.112 16.51 6.112H26.1567C26.806 6.11333 27.3327 5.592 27.3327 4.952ZM19.6527 19.048C19.6527 20.4973 20.846 21.6773 22.3153 21.6773C23.786 21.6773 24.9794 20.4973 24.9794 19.048C24.9794 17.5973 23.786 16.4187 22.3153 16.4187C20.846 16.4187 19.6527 17.5973 19.6527 19.048ZM17.2994 19.048C17.2994 16.3173 19.5487 14.096 22.3153 14.096C25.0833 14.096 27.3327 16.3173 27.3327 19.048C27.3327 21.7787 25.0833 24 22.3153 24C19.5487 24 17.2994 21.7787 17.2994 19.048ZM1.84335 17.8867H11.4887C12.138 17.8867 12.6647 18.4067 12.6647 19.0467C12.6647 19.6893 12.138 20.2093 11.4887 20.2093H1.84335C1.53345 20.2111 1.23552 20.0898 1.01502 19.872C0.794511 19.6543 0.669464 19.3579 0.667349 19.048C0.667349 18.408 1.19402 17.8867 1.84335 17.8867Z"
      fill={fill ? fill : "#808386"}
    />
  </svg>
);
