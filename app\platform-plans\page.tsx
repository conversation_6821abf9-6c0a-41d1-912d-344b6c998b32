"use client";

import { useEffect } from "react";

import CardProcessing from "@/components/CardProcessing";
import Footer from "@/components/common/footer/desktop-footer";
import Header from "@/components/common/header";
import PremiumPlans from "@/components/premium-feature";
import IntroPage from "@/components/premium-feature/intro";
import { useAppSelector } from "@/redux-store/hooks";

const PremiumFetures = () => {
  const cardProcessing = useAppSelector((s) => s.config.cardProcessing);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const element = document.getElementById("cardProcessingModal");
      if (!element) return;

      const modal = new window.bootstrap.Modal(element);

      if (cardProcessing) {
        if (element) {
          modal.show();
        }
      } else {
        if (element) {
          modal.hide();
        }
      }

      return () => {
        if (element) {
          modal.hide();
        }
      };
    }, 300);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [cardProcessing, document, window.bootstrap]);

  return (
    <>
      <div className="bg-body w-100 platform-plan-container overflow-scroll">
        <Header showHeader={true} cls="position-sticky top-0 z-3" />
        <hr className="color-medium" />
        <IntroPage />
        <div>
          <PremiumPlans />
        </div>

        <div style={{ paddingTop: "6%" }}>
          <Footer />
        </div>
      </div>
      <CardProcessing />
    </>
  );
};

export default PremiumFetures;
