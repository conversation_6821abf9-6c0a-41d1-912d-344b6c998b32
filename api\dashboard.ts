import API from ".";

interface RequestResponse {
  status: number;
  is_promotion: boolean;
}
export const getrequests = async (
  page: number,
  date: string,
  status?: number | null,
  type?: string
) =>
  API.get(
    API.REQUESTS +
      `/calls-and-ratings?user_type=receiver&${date}${
        status !== null && status !== undefined ? `&status=${status}` : ""
      }&page=${page}${
        type
          ? type === "PROMOTION"
            ? "&is_promotion=true"
            : `&type=${type}`
          : ""
      }`
  );

export const getPromotionRequest = async (
  page: number,
  date: string,
  status?: number | null
) =>
  API.get(
    API.REQUESTS +
      `/calls-and-ratings?user_type=receiver&${date}${
        status !== null && status !== undefined ? `&status=${status}` : ""
      }&page=${page}&is_promotion=true`
  );
export const requestOverView = async (date: string) =>
  API.get(API.REQUESTS + `/overview?${date}`);

export const handleRequest = async (id: string, body: RequestResponse) =>
  API.put(API.REQUESTS + `/respond-to-request/${id}`, body);

export const earningsOverview = async (date: string) =>
  API.get(API.USERS + `/monetisation/overview?${date}`);

export const earningsList = async (type: string, page: number, date: string) =>
  API.get(
    API.USERS + `/monetisation/listing?${date}&type=${type}&page=${page}`
  );
export const earningsAnalysis = async (group: string, date: string) =>
  API.get(API.USERS + `/monetisation/analysis?${date}&groupBy=${group}`);

export const subsciberAnalysis = async (
  type: string,
  group: string,
  date: string
) =>
  API.get(
    API.USERS + `/subscribers/analysis?${date}&type=${type}&groupBy=${group}`
  );

export const subsciberOverview = async (type: string, date: string) =>
  API.get(API.USERS + `/subscribers/overview?${date}&type=${type}`);

export const UserOverView = async (date: string, range?: string) =>
  API.get(API.USERS + `/overview?${date}${range ? `&queryRange=${range}` : ""}`);

export const getDroppedSubscribers = async (date: string) =>
  API.get(API.USERS + `/dropped-subscribers?${date}`);
