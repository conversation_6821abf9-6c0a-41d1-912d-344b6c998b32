import "./index.scss";

import { useRenderCount } from "@uidotdev/usehooks";
import debounce from "lodash/debounce";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  type FunctionComponent,
  memo,
  useEffect,
  useRef,
  useState,
} from "react";
import Swal from "sweetalert2";
import Swiper from "swiper";
import "swiper/css";
import "swiper/css/navigation";
import { Autoplay, Navigation } from "swiper/modules";

import { GetUserMatchProfile } from "@/api/matchmaker/matchmaker.api";
import { GetUserFollowing } from "@/api/user";
import Badges from "@/components/common/badges";
import getHeightByClassName from "@/components/common/chat/chat-box/utils/helper/get-div-height";
import { OnlineDot } from "@/components/common/OnlineDot";
import { Divider } from "@/components/common/wrapper";
import { ModalService } from "@/components/modals";
import { matchMakerActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Chat } from "@/redux-store/slices/chat.slice";
import { getAssetUrl } from "@/utils/assets";

const HomeSidebar: FunctionComponent = () => {
  const dispatch = useAppDispatch();
  const role = useAppSelector((state) => state?.user?.role);
  const matchProfileExits = useAppSelector(
    (state) => state.matchmakerData.profileExists
  );
  const isProfileCompleted = useAppSelector(
    (state) => state.user.profile.is_profile_completed
  );
  const isMatchDataFetched = useAppSelector(
    (state) => state.matchmakerData.isFetched
  );
  const [height, setHeight] = useState(0);
  const userRole = useAppSelector((state) => state.user.role);
  const isVerified = useAppSelector((state) => state.user.isVerified);

  console.log({ userRole, isVerified: !isVerified });

  const Skeleton = ({ content, cls, parentCls }: any) => (
    <div
      className={`ske-container featured-container d-flex p-3 gap-3 rounded-3 mb-2 ${
        parentCls ? parentCls : ""
      }`}
    >
      <div className={`ske-content d-flex gap-2 align-items-center ${cls}`}>
        {content}
      </div>
    </div>
  );

  useEffect(() => {
    const mainHeaderHeight = getHeightByClassName("header-height");
    const homeNavHeight = getHeightByClassName("home-nav-height");

    setHeight(mainHeaderHeight + homeNavHeight);
  }, []);

  useEffect(() => {
    if (role !== "guest" && !isMatchDataFetched)
      GetUserMatchProfile()
        .then((result: any) => {
          dispatch(
            matchMakerActions.setIsProfileActivated(
              result?.data?.[0]?.is_activated
            )
          );

          if (!result?.data?.[0]?._id) {
            dispatch(matchMakerActions.setProfileExists(false));
            dispatch(matchMakerActions.setUserMatchProfile(result.data));
            dispatch(
              matchMakerActions.setCurrentPlan(result?.data?.[0]?.active_plan)
            );
          } else {
            dispatch(matchMakerActions.setUserMatchProfile(result.data));
            dispatch(
              matchMakerActions.setCurrentPlan(result?.data?.[0]?.active_plan)
            );
            dispatch(matchMakerActions.setProfileExists(true));
          }
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          dispatch(matchMakerActions.setIsFetched(true));
        });
  }, [role]);

  const router = useRouter();

  const KnkyMatch = () => (
    <Skeleton
      content={
        <>
          <Image
            className="ske-icon mt-3"
            width={64}
            height={64}
            alt=""
            src="/images/home/<USER>"
          />
          <div
            className="d-flex flex-column gap-1 pointer"
            onClick={() => {
              if (role === "guest") {
                ModalService.open("SIGN_IN");
              }

              if (userRole === "creator" && !isVerified) {
                Swal.fire({
                  icon: "info",
                  title: "Complete your KYC to get started",
                  showCloseButton: true,
                  confirmButtonColor: "#ac1991",
                  confirmButtonText: "Complete KYC",
                  customClass: {
                    confirmButton: "custom-btn",
                  },
                }).then((res) => {
                  if (res.isConfirmed) {
                    router.push("/settings/kyc");
                  }
                });
              }

              if (role !== "guest" && !isProfileCompleted) {
                Swal.fire({
                  title: "Please complete your profile",
                  icon: "warning",
                  confirmButtonColor: "#3085d6",
                  confirmButtonText: "Okay",
                  showCloseButton: true,
                  customClass: {
                    confirmButton: "custom-btn",
                  },
                }).then((result) => {
                  if (result.isConfirmed) {
                    ModalService.open("COMPLETE_PROFILE");
                  }
                });
                return;
              }

              router.push(
                role === "guest" ||
                  !isProfileCompleted ||
                  (userRole === "creator" && !isVerified)
                  ? ""
                  : matchProfileExits
                  ? "/matchmaker/match"
                  : "/matchmaker"
              );
            }}
          >
            <div className="ske-title fs-5 fw-bold">KNKY Match</div>
            <div className="ske-desc fs-6">
              Match with people who love what you love!
            </div>
            <Link
              className="ske-btn"
              href={
                role === "guest" ||
                !isProfileCompleted ||
                (userRole === "creator" && !isVerified)
                  ? ""
                  : matchProfileExits
                  ? "/matchmaker/match"
                  : "/matchmaker"
              }
              onClick={() => {
                if (userRole === "creator" && !isVerified) {
                  Swal.fire({
                    icon: "info",
                    title: "Complete your KYC to get started",
                    showCloseButton: true,
                    confirmButtonColor: "#ac1991",
                    confirmButtonText: "Complete KYC",
                    customClass: {
                      confirmButton: "custom-btn",
                    },
                  }).then((res) => {
                    if (res.isConfirmed) {
                      router.push("/settings/kyc");
                    }
                  });
                }
              }}
            >
              <span className="ske-text">
                {matchProfileExits ? "Explore More" : "Get started"}
              </span>
            </Link>
          </div>
        </>
      }
      parentCls={"ske-match"}
    />
  );

  const UpgradeToUnlimited = () => {
    const initializeSwiper = () => {
      new Swiper(`.plans-swiper`, {
        direction: "horizontal",
        effect: "slide",
        slidesPerView: 1,
        spaceBetween: 100,
        modules: [Navigation, Autoplay],

        autoplay: {
          delay: 2000,
          disableOnInteraction: true,
        },
        loop: true,
        grabCursor: true,
        followFinger: true,
        allowTouchMove: true,
        autoHeight: false,
      });
    };

    useEffect(() => {
      setTimeout(() => {
        initializeSwiper();
      }, 0);
    }, []);
    const role = useAppSelector((s) => s.user.role);
    return (
      <>
        <div className=" ske-container featured-container  p-3 gap-3 rounded-3 mb-2">
          <div className="plans-swiper overflow-hidden">
            <div className="swiper-wrapper">
              {/* <Link href={"/platform-plans"} className="swiper-slide">
                <div className={` ske-content d-flex gap-3 align-items-center`}>
                  <div className="ske-icon mt-3 unlimited-icon rounded-2">
                    <Image
                      width={64}
                      height={64}
                      alt=""
                      src="/images/home/<USER>"
                    />
                  </div>
                  <div className="d-flex flex-column gap-1">
                    <div className="ske-title fs-5 fw-bold ske-highlight-gold">
                      Unlimited
                    </div>
                    <div className="ske-desc fs-6">
                      A truly unlimited service offering unrestricted access to
                      every Creator channel in the KNKY universe. The option to
                      fully immerse yourself - for those that want it all!
                    </div>
                    <span className="ske-btn">More details</span>
                  </div>
                </div>
              </Link> */}
              <Link href={"/platform-plans"} className="swiper-slide">
                <div className={` ske-content d-flex gap-3 align-items-center`}>
                  <div className="ske-icon mt-3 prime-icon rounded-2">
                    <Image
                      width={64}
                      height={64}
                      alt=""
                      src="/images/home/<USER>"
                    />
                  </div>
                  <div className="d-flex flex-column gap-1">
                    <div className="ske-title fs-5 fw-bold ske-highlight-blue">
                      Prime
                    </div>
                    <div className="ske-desc fs-6">
                      Unlock full access to every Creator and discover your
                      favourites and explore their content before subscribing.
                      Enjoy daily fresh content and new creators!
                    </div>
                    <span className="ske-btn">More details</span>
                  </div>
                </div>
              </Link>
              {!(role === "user") && (
                <Link href={"/platform-plans"} className="swiper-slide">
                  <div
                    className={` ske-content d-flex gap-3 align-items-center`}
                  >
                    <div className="ske-icon mt-3  rounded-2">
                      <Image
                        width={64}
                        height={64}
                        alt=""
                        src="/images/badges/creator-verified-official.svg"
                      />
                    </div>
                    <div className="d-flex flex-column gap-1">
                      <div className="ske-title fs-5 fw-bold color-sky-blue">
                        Pro Creator
                      </div>
                      <div className="ske-desc fs-6">
                        Elevate your Creator journey with our premium tools,
                        tailor-made to help you maximise revenue and expand your
                        fan base on the KNKY platform.
                      </div>
                      <span className="ske-btn">More details</span>
                    </div>
                  </div>
                </Link>
              )}
            </div>
          </div>
        </div>
      </>
    );
  };

  const FooterWidget = () => {
    const links = [
      { title: "About us", href: "https://lander.knky.co/" },

      { title: "", href: "" },
      { title: "T&Cs", href: "/articles/terms-of-service" },

      { title: "", href: "" },

      {
        title: "Help",
        href: "https://help.knky.co/",
      },
      { title: "", href: "" },
      {
        title: "Premium Plans",
        href: "/platform-plans",
      },
    ];

    return (
      <Skeleton
        cls="flex-wrap"
        content={
          <>
            {links.map(({ href, title }, i) => {
              return title ? (
                <Link key={i} target="_blank" href={href}>
                  {title}
                </Link>
              ) : (
                <Divider key={i} direction="end" />
              );
            })}
            <p className=" fs-10 color-medium m-0">
              © {new Date().getFullYear()} KNKY® | Social Commerce Ltd | 71-75
              Shelton Street - London - WC2H 9JQ
            </p>
          </>
        }
      />
    );
  };

  const renders = useRenderCount();

  useEffect(() => {
    console.log({ renders });
  }, []);

  return (
    <div
      className="home-sidebar d-lg-block d-none"
      style={{ height: `calc(100vh - ${height}px - 1.5em)`, padding: 2 }}
    >
      <div className="home-wrapper d-lg-flex d-none flex-column gap-3">
        <KnkyMatch />
        <UpgradeToUnlimited />
        <ChatSection />
        <FooterWidget />
      </div>
    </div>
  );
};

export default HomeSidebar;

// eslint-disable-next-line react/display-name
const ChatSection = memo(() => {
  const userId = useAppSelector((state) => state.user.id);
  const userRole = useAppSelector((state) => state.user.role);
  const [searchTerm, setSearchTerm] = useState("");
  const [userList, setUserList] = useState<any[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [activeChat, _setActiveChat] = useState<any>();
  const chatList = useAppSelector((state) => state.chat.chatList);
  const router = useRouter();
  const [width, setWidth] = useState(0);

  const handleBlur = () => {
    // Hide the dropdown when the input is out of focus
    setTimeout(() => {
      setShowDropdown(false);
      setSearchTerm("");
    }, 500);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const debouncedHandleSearch = useRef(
    debounce(async (term: string) => {
      if (!term) {
        setShowDropdown(false);
        return;
      }

      try {
        const arr = await GetUserFollowing(userId);
        const filteredUsers = arr.data.filter((user: any) => {
          const searchTermLower = term.toLowerCase();

          return (
            user?.display_name.toLowerCase().includes(searchTermLower) ||
            user?.username.toLowerCase().includes(searchTermLower)
          );
        });
        const filteredChatList = chatList
          .filter((chat) => {
            const searchTermLower = term.toLowerCase();

            if (chat.initiator._id === userId) {
              return (
                chat?.target?.display_name
                  .toLowerCase()
                  .includes(searchTermLower) ||
                chat?.target?.username.toLowerCase().includes(searchTermLower)
              );
            } else {
              return (
                chat?.initiator?.display_name
                  .toLowerCase()
                  .includes(searchTermLower) ||
                chat?.initiator?.username
                  .toLowerCase()
                  .includes(searchTermLower)
              );
            }
          })
          .map((chat) => {
            if (chat.initiator._id === userId) {
              return chat.target;
            } else {
              return chat.initiator;
            }
          });
        const userSet = new Set();
        const uniqueUsers = [...filteredUsers, ...filteredChatList].filter(
          (user) => {
            if (userSet.has(user._id)) {
              return false;
            } else {
              userSet.add(user._id);
              return true;
            }
          }
        );

        setUserList(uniqueUsers);
        setShowDropdown(true);
      } catch (error) {
        console.error("Error fetching user data", error);
      }
    }, 600)
  ).current;

  useEffect(() => {
    debouncedHandleSearch(searchTerm);
  }, [searchTerm]);

  useEffect(() => {
    setWidth(document.querySelector(".header-search-box")?.clientWidth || 0);
  }, [document]);

  const SubscribeCreator = () => (
    <>
      <div className="py-4">
        <div className="d-flex align-items-center  flex-column gap-3 p-4  overflow-hidden ">
          <Image
            height={66}
            width={66}
            className="img-fluid"
            src={"/images/common/empty.svg"}
            alt="image"
          />
          <div className="text-center">
            <p className=" fw-semibold fs-5 m-0 ">
              Chat directly with your favorite creators!
            </p>
          </div>
        </div>
      </div>
    </>
  );
  return (
    <>
      <div className=" d-flex position-relative flex-column">
        <div className="chat-sidebar rounded-md-3 bg-body   h-100">
          <div className="chat-header-info d-flex p-3 pb-0 justify-content-between align-items-center mb-2">
            <div className="fs-5 fw-bold d-none d-md-block">Chat</div>

            <div
              className={`header-search-box bg-cream d-md-flex d-none gap-2 align-items-center   rounded-4 ${
                userRole === "guest" ? "opacity-50" : ""
              }`}
              style={{ top: ".5rem", padding: ".25rem" }}
            >
              <div className="d-flex align-items-center gap-2">
                <Image
                  src="/images/creator/search.svg"
                  alt="Search Logo"
                  className="brand-logo"
                  width={24}
                  height={24}
                  priority
                />
                <form>
                  <input
                    type="search"
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    disabled={userRole === "guest"}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                  />
                </form>
              </div>

              {showDropdown && (
                <>
                  <ul
                    className="chat-custom-menu position-absolute"
                    style={{
                      padding: userList.length > 0 ? "1% 1%" : "",
                      marginBottom: 0,
                      width,
                      top: 53,
                    }}
                  >
                    {userList.length > 0 ? (
                      userList.map((user: any, index) => (
                        <>
                          <li key={user._id}>
                            <div
                              onClick={() =>
                                router.push("/chat?user=" + user?._id)
                              }
                              className="user-wrapper"
                            >
                              {/* eslint-disable-next-line @next/next/no-img-element */}
                              <img
                                src={getAssetUrl({
                                  media: user?.avatar[0],
                                  defaultType: "avatar",
                                  variation: "thumb",
                                })}
                                className="rounded-circle object-fit-cover"
                                width={38}
                                height={38}
                                alt="no-image"
                              />
                              <span className="mx-2">{user.username}</span>
                            </div>
                          </li>
                          {index < userList.length - 1 && <hr />}
                        </>
                      ))
                    ) : (
                      <li>No users found</li>
                    )}
                  </ul>
                </>
              )}
            </div>
          </div>
          <hr className=" text-dark text-opacity-50 m-0" />
          <div>
            {userId ? (
              <div className="chat-home-list">
                {chatList.length ? (
                  <>
                    {chatList.map((chat: Chat) => {
                      return (
                        <div key={chat?.converse_channel_id}>
                          <div
                            onClick={() =>
                              router.push(
                                `/chat?user=${
                                  chat?.target?._id === userId
                                    ? chat?.initiator?._id
                                    : chat?.target?._id
                                }`
                              )
                            }
                          >
                            <div
                              key={chat?.converse_channel_id}
                              className={
                                (chat?.target?.display_name &&
                                  chat?.target?._id == activeChat) ||
                                chat?.initiator?._id == activeChat
                                  ? "chat-person d-flex my-2 align-items-center justify-content-between  pointer active"
                                  : chat?.target?.display_name
                                  ? "chat-person d-flex my-2 align-items-center justify-content-between pointer inactive"
                                  : "d-none"
                              }
                              style={{
                                position: "relative",
                              }}
                            >
                              <div className="d-flex">
                                <div className="position-relative">
                                  {/* eslint-disable-next-line @next/next/no-img-element */}
                                  <img
                                    width={40}
                                    height={40}
                                    className="rounded-pill object-fit-cover"
                                    alt=""
                                    src={getAssetUrl({
                                      media: (userId ==
                                      (chat as any).target?._id
                                        ? (chat as any).initiator
                                        : (chat as any).target
                                      )?.avatar?.[0],
                                      defaultType: "avatar",
                                      variation: "thumb",
                                    })}
                                  />
                                  <OnlineDot
                                    userId={
                                      userId == chat?.target?._id
                                        ? chat?.initiator?._id
                                        : chat?.target?._id
                                    }
                                    style={{
                                      width: "12px",
                                      height: "12px",
                                      position: "absolute",
                                      bottom: "9%",
                                      right: "0%",
                                    }}
                                  />
                                </div>
                                <div className="person-details ms-2">
                                  <p className="mb-0 d-flex gap-1 color-dark profile-last-message">
                                    {
                                      (userId == chat?.target?._id
                                        ? chat?.initiator
                                        : chat?.target
                                      )?.display_name
                                    }{" "}
                                    <span>
                                      <Badges
                                        array={
                                          userId === chat?.target?._id
                                            ? chat?.initiator?.badges
                                            : chat?.target?.badges
                                        }
                                      />
                                    </span>
                                  </p>
                                  <div
                                    className={`profile-last-message fs-7  ${
                                      chat.unread_count ? "fw-bold" : ""
                                    }`}
                                  >
                                    {chat?.message?.meta?.chat_list_message ||
                                      (typeof chat.message === "string" &&
                                        chat.message) ||
                                      (typeof chat.lastmessage === "string" &&
                                        chat.lastmessage) ||
                                      (typeof chat.lastmessage === "object" &&
                                        chat.lastmessage?.message) ||
                                      (typeof chat.message === "object" &&
                                        chat.message?.message)}
                                  </div>
                                </div>
                              </div>
                              <div className="unreads me-3 fs-8 background-light p-1 rounded-pill position-absolute end-0 top-0">
                                {chat.unread_count
                                  ? chat.unread_count > 9
                                    ? "9+"
                                    : chat.unread_count
                                  : null}
                              </div>
                            </div>
                          </div>
                          <hr className="mx-3 text-dark text-opacity-50" />
                        </div>
                      );
                    })}
                  </>
                ) : (
                  <>
                    <SubscribeCreator />
                  </>
                )}
              </div>
            ) : (
              <>
                <SubscribeCreator />
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
});
