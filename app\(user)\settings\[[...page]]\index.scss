/* settings css */
.setting-sideBar .nav-link.active,
.horizontalTabs .nav-link.active {
  background-color: transparent !important;
  color: var(--primary-color) !important;
  font-weight: 600;
  border-radius: 0;
}
.setting-sideBar .nav-link.active {
  border-left: 5px solid var(--primary-color);
}

.horizontalTabs .nav-link.active {
  border-bottom: 1.2px solid var(--primary-color);
}

@media screen and (max-width: 767px) {
  .setting-sideBar .nav-link.active,
  .setting-sideBar .nav-link {
    border-left: 0 !important;
  }
  .settings-container .row {
    margin-right: 0 !important;
  }
  .tab-content {
    padding-right: 0 !important
    ;
  }
  .menuVisible {
    display: none;
  }
}
.setting-sideBar .nav-link.active svg path {
  fill: #ac1991;
}
.extra-svg-review svg path {
  fill: #fbde4a !important;
}
.extra-svg-rejected svg path {
  fill: #f11e11 !important;
}
.setting-sideBar .nav-link img {
  filter: grayscale(1);
}
.setting-sideBar .nav-link.active img {
  filter: grayscale(0);
}

.setting-sideBar .nav-link,
.horizontalTabs .nav-link {
  font-weight: 600;
  font-family: "SFDisplay";
  height: 3rem;
  border-radius: 0 !important;
}
.horizontalTabs {
  border-bottom: 1.2px solid #ebebec;
}
.setting-sideBar .nav-link {
  border-left: 5px solid var(--bg-color);
  text-align: left;
}
.nav-link span {
  display: block;
}
.nav-link div {
  gap: 4%;
}

/* radio switch button css */
.form-check-input:checked {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.form-check-input:focus {
  border-color: var(--primary-color) !important;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(253, 13, 209, 0.25) !important;
}
.largeSwitch {
  width: 3em !important;
  height: 1.6em !important;
}
.form-check-label {
  font-family: "SFDisplay";
  font-weight: 500;
  color: var(--fg-color);
  font-size: 1.1rem;
}

/* modal css */

.modal-header {
  border-bottom: 0 !important;
  display: flex;
  justify-content: space-between;
}
.modal-footer {
  border-top: 0 !important;
}
button .close {
  padding: 0;
  background-color: transparent;
  border: 0;
  appearance: none;
  -webkit-appearance: none;
}

.close {
  all: unset;
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}
table {
  white-space: nowrap;
  overflow-x: auto;
}
tr {
  border-color: #ebebec !important;
}
td {
  min-width: 7rem;
}

.revenueTableDiv .card {
  border-radius: 0.375rem 0.375rem 0 0 !important;
}
.revenueTableDiv table {
  margin-bottom: 0 !important;
}
.revenueTabs {
  flex-wrap: nowrap !important;
  white-space: nowrap;
  overflow-x: auto;
}

// apex chart css

.apexcharts-legend {
  position: absolute;
  left: auto;
  top: 0px;
  width: 53%;
  right: 0;
}
.apexcharts-legend-series {
  display: flex;
  align-items: center;
  line-height: normal;
}
.apexcharts-legend-text {
  display: flex;
  width: 100%;
}
// .apexcharts-legend-marker {
//   border-radius: 8px !important;
//   margin-right: 1rem !important;
// }

.percentage {
  font-size: 1rem;
  display: inline-flex;
  width: max-content;
  text-align: right;
  flex-grow: 1;
  justify-content: flex-end;
}

// pagination css

.pagination {
  margin: auto;
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
}

.page-link {
  color: #000 !important;
  font-family: "SFDisplay";
  font-weight: 600;

  border-radius: 0.4rem;
}
.page-link.active,
.active > .page-link {
  background-color: #ac1991 !important;
  border-color: #ac1991;
  color: #fff !important;
}

// form css

.info {
  margin-bottom: 5%;
}

.question {
  color: #4d5053;
  font-size: 0.8rem;
  display: block;
  margin-bottom: 0.7%;
}
.required:after {
  content: " *";
  color: red;
}

.info .input {
  border: 0;
  background-color: #f5f5f6;
  border-radius: 6px;
  padding: 1.2%;
  width: 100%;
}

.add-address-modal {
  @media (max-width: 768px) {
    scale: 0.9;
  }

  @media (max-width: 568px) and (max-height: 700px) {
    scale: 0.8;
  }
}

.menu-container {
  background: white;
  transition: transform 0.3s ease-in-out;
}

.content-container {
  flex-grow: 1;
  padding: 1rem;
  overflow: auto;
}

.wallet-balance-container {
  background: var(--wallet-gradient);
}

@media (min-width: 576px) {
  .chat-settings-container {
    flex-direction: row;
  }

  .menu-container {
    flex: 1;
    transform: translateX(0);
  }

  .content-container {
    flex: 2;
  }

  .mobile-visible,
  .mobile-hidden {
    transform: none;
  }
}

@media (max-width: 575px) {
  .menu-header {
    text-align: start;
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .menu-container {
    transform: translateX(0);
    transition: transform 0.3s ease-in-out;
  }

  .content-container {
    display: none;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
  }

  .mobile-visible {
    display: block;
    transform: translateX(0);
  }

  .mobile-hidden {
    transform: translateX(-100%);
  }
  .content-container {
    flex-grow: 1;
    padding: 0rem;
    overflow: auto;
  }
}

@media screen and (min-width: 768px) {
  .mobileHeader {
    display: none;
  }
}
