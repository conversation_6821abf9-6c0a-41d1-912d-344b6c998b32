.sideBar {
  background-color: white;
  padding-right: 0 !important;
  padding-bottom: 2%;
  /* border-radius: 8px !important; */
}
.sideBar-slide {
  left: 0 !important;
}

.sideBar h5 {
  /* margin-left: 6%; */
  margin-bottom: 7%;
  font-weight: 600;
}

.privacy,
.revenue {
  border-bottom: 1px solid #afb1b3;
}

@media screen and (min-width: 768px) {
  .mobileHeader {
    display: none;
  }
}

.input {
  color: #4d5053;
  font-weight: 500;
}

.payment {
  background-color: white;
  padding: 3% 2%;

  border-radius: 8px;
}

@media screen and (max-width: 767px) {
  .section {
    margin: 0 !important;
  }

  .sideBar {
    position: absolute;
    left: -100%;
    transition: 0.45s ease;
    bottom: 0;
    top: 0;
    right: 0;
    z-index: 2;
    width: 100%;
    flex-grow: 1;
    overflow-y: auto;
  }
  .sideBarSlide {
    left: 0 !important;
  }
  .privacy,
  .revenue {
    border-bottom: 1px solid #ebebec;
  }
  .sideBar h5 {
    padding-bottom: 4%;
    margin-bottom: 1%;
    margin-left: 0;
    padding-left: 6%;
    border-bottom: 1px solid #ebebec;
  }
  .payment {
    border-radius: 0;
  }
}
