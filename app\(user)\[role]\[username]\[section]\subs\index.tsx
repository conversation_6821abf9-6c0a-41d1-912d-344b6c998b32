import "./index.scss";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { memo, useEffect, useState } from "react";

import { type APIChannel, GetChannelList } from "@/api/channel";
import { type APIGroup, GetGroupList } from "@/api/group";
import Loader from "@/components/common/loader/loader";
import useScrollIntoView from "@/hooks/useScrollIntoView";
import { userDataActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

import SubsInfo from "./subsInfo";
import AllSubs from "./types/all";
import ChannelList from "./types/channel";
import GroupList from "./types/group";

const Subs = ({
  uid,
  from = "post",
}: {
  uid: string;
  from: "post" | "reel";
}) => {
  const searchParams = useSearchParams();
  const scrollIntoView = useScrollIntoView();
  const [channelList, setChannelList] = useState([] as APIChannel[]);
  const [error, setError] = useState("");
  const [groupList, setGroupList] = useState([] as APIGroup[]);
  const user = useAppSelector((state) => state.user);
  const dispatchAction = useAppDispatch();
  const isOwner = user.id === uid;
  const [showLoader, setShowLoader] = useState<boolean>(true);
  const [tab, setTab] = useState(0);

  useEffect(() => {
    GetChannelList(uid)
      .then((response) => {
        setShowLoader(false);
        setChannelList(response.data.channels);

        if (uid === user.id) {
          dispatchAction(userDataActions.setChannels(response?.data?.channels));
        }
      })
      .catch(() => {
        setShowLoader(false);

        setError("No Channel found!");
      });

    GetGroupList(uid)
      .then((response) => {
        setShowLoader(false);

        setGroupList(
          response?.data?.sort((a, b) => {
            const allTermsAcceptedA = a.members.every(
              (member) => member.terms_accepted !== false
            );
            const allTermsAcceptedB = b.members.every(
              (member) => member.terms_accepted !== false
            );

            if (allTermsAcceptedA && !allTermsAcceptedB) return -1;
            if (!allTermsAcceptedA && allTermsAcceptedB) return 1;
            return 0;
          })
        );

        if (uid === user.id) {
          dispatchAction(userDataActions.setGroups(response?.data));
        }
      })
      .catch(() => {
        setShowLoader(false);

        setError("No Collab found!");
      });

    setTimeout(() => {
      scrollIntoView(".profile-sections-container");
    }, 500);
  }, []);

  const currentTab =
    searchParams.get("type") === "collabs"
      ? 2
      : searchParams.get("type") === "channels"
      ? 1
      : 0;

  if (!(channelList.length || groupList.length) && !showLoader) {
    return <SubsInfo isOwner={isOwner} />;
  }

  return (
    <>
      <div
        className={`${
          from === "reel" ? "" : "subs-options"
        } rounded-top-3 bg-cream`}
        style={{ background: from === "reel" ? "#1d1d1d" : "" }}
      >
        {from === "reel" ? (
          <div
            className="d-flex  gap-3 align-items-center  w-100 overflow-auto  px-3 py-2 reel-comment-bg"
            id="pills-tab"
            role="tablist"
          >
            {/* <div
              onClick={() => setTab(0)}
              className={`p-1 px-3 rounded-3 tab text-white fs-7 ${
                tab === 0 ? "active" : "reel-comment-bg"
              }`}
            >
              All
            </div> */}
            <div
              onClick={() => setTab(1)}
              className={`p-1 px-3 rounded-3 tab text-white fs-7 ${
                tab === 1 ? "active" : "reel-comment-bg"
              }`}
            >
              Channels
            </div>
            <div
              onClick={() => setTab(2)}
              className={`p-1 px-3 rounded-3 tab text-white fs-7 ${
                tab === 2 ? "active" : "reel-comment-bg"
              }`}
            >
              {" "}
              Collabs{" "}
            </div>
          </div>
        ) : (
          <div
            className="d-flex  gap-4 align-items-center  w-100 overflow-auto p-md-3 px-3 py-2"
            id="pills-tab"
            role="tablist"
          >
            {/* <Link
              href={`?type=all`}
              className={`btn tab ${currentTab === 0 ? "active" : ""}`}
              type="button"
            >
              All
            </Link> */}
            <Link
              href={`?type=channels`}
              className={`btn tab ${currentTab === 1 ? "active" : ""}`}
              type="button"
            >
              My Channels
            </Link>
            <Link
              className={`btn tab ${currentTab === 2 ? "active" : ""}`}
              type="button"
              href={`?type=collabs`}
            >
              {" "}
              My Collabs{" "}
            </Link>
          </div>
        )}
      </div>

      <div className={`${from === "reel" ? "bg-reel" : " bg-body"}  `}>
        {showLoader ? (
          <div className="container-xxl bd-gutter g-lg-4 g-0 d-flex align-items-center justify-content-center vh-100">
            <Loader />
          </div>
        ) : (
          <>
            {(from === "post" ? currentTab === 0 : tab === 0) && (
              <div>
                <AllSubs
                  uid={uid}
                  channelList={channelList}
                  groupList={groupList}
                  from={from}
                />
              </div>
            )}
            {(from === "post" ? currentTab === 1 : tab === 1) && (
              <div>
                <ChannelList uid={uid} list={channelList} from={from} />
              </div>
            )}

            {(from === "post" ? currentTab === 2 : tab === 2) && (
              <div>
                <GroupList uid={uid} list={groupList} from={from} />
              </div>
            )}
          </>
        )}
      </div>
    </>
  );
};

export default memo(Subs);
