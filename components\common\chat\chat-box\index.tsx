import classNames from "classnames";
import { memo, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import { UpdateMessageId } from "@/api/chat";
import { getConsumablePlans } from "@/api/subscriptions";
import { UnblockUser } from "@/api/user";
import type { PlanInfo } from "@/app/(user)/(home)/components/sidebar/modal";
import { useDeepCompareEffect } from "@/hooks/useDeepCompareEffect";
import {
  blockActions,
  chatActions,
  configActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import type { Media } from "@/types/media";
import socketChannel from "@/utils/chatSocket";

import ActionButton from "../../action-button";
import { GoBackToPositionIcon } from "../../go-up-widget/util/svg";
import JoinUs from "../../join-us";
import LoadingSpinner from "../../loading";
import type { ModalRef } from "../../modal";
import ChatShimmer from "../../shimmer/chat-shimmer";
import ImageModal from "../chat-modals/image-modal";
import ReadUnreadMessages from "./utils/chatbox-utils/read-unread-messages";
import { ProcessingLoader } from "./utils/svg-utils";

const ChatBox = ({
  ratingReqref,
  rateRef,
  imageModalRef,
  videoConfRef,
  setDemand,
}: {
  ratingReqref: any;
  chatBoxDisable: boolean;
  rateRef: any;
  imageModalRef: any;
  videoConfRef: any;
  directMessageRef: any;
  chattingFeeRef: any;
  demand: PlanInfo | undefined;
  setDemand: (value: PlanInfo | undefined) => void;
}) => {
  const userId = useAppSelector((state) => state.user.id);
  const targetUser = useAppSelector((state) => state.chat.targetUser);
  const isLoaded = useAppSelector((state) => state.chat.isLoaded);
  const channelId = useAppSelector((state) => state.chat.channelId);
  const prevMessages = useAppSelector((state) => state.chat.prevMessages);
  const currentMessages = useAppSelector((state) => state.chat.currentMessages);
  const [channelData, setChannelData] = useState<{
    me: {
      display_name: string;
      id: string;
      avatar: Media | undefined;
      chat_fee: "free" | "paid";
    };
    target: {
      display_name: string;
      id: string;
      avatar: Media | undefined;
      chat_fee: "free" | "paid";
    };
  }>({
    me: { display_name: "", id: "", avatar: undefined, chat_fee: "paid" },
    target: { display_name: "", id: "", avatar: undefined, chat_fee: "paid" },
  });
  const dispatch = useAppDispatch();

  const [receiver, setReceiver] = useState<any>({});
  const [callReqId, setCallReqId] = useState<string>("");
  const [showBuyTicketModal, setShowBuyTicketModal] = useState<boolean>(false);
  const [imageMessage, setImageMessage] = useState<Record<string, any>>({});
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const buyTicketModalRef = { method: {} as ModalRef };
  const mediaLoading = useAppSelector((state) => state.chat.mediaLoading);
  const chatList = useAppSelector((s) => s.chat.chatList);
  const [completeMessages, setCompleteMessages] = useState<any[]>([]);
  const [internalLoading, setInternalLoading] = useState(false);
  const [stopScroll, setStopScroll] = useState(false);
  const [isScrolledUp, setIsScrolledUp] = useState(false);
  const replyMessage = useAppSelector((s) => s.chat.replyMessage);
  const has_chat_media = useAppSelector((s) => s.chat.has_chat_media);

  const isTyping = useAppSelector((s) => s.chat.isTargetTyping);

  const blockedUser = useAppSelector((state) => state.block.blockedByMe);
  const divRef = useRef<any>(null);

  const scrollIntoView = () => {
    if (divRef.current) {
      divRef.current.scrollTo(0, divRef.current.scrollHeight);
    }
  };

  useEffect(() => {
    scrollIntoView();
  }, [divRef, mediaLoading, targetUser, isLoaded, completeMessages]);

  // can be moved to chatSocket.ts
  useEffect(() => {
    const messageReq = currentMessages?.find(
      (message: MessageInterface) =>
        message?.meta?.reqId === callReqId &&
        message?.meta?.type === "ACCEPT_CALL"
    );

    if (messageReq) {
      UpdateMessageId(
        messageReq?.meta?.reqId,
        messageReq?.messageId || messageReq?._id,
        messageReq?.createdAt || messageReq?.createdAt,
        channelId,
        messageReq?.meta
      )
        .then(() => {})
        .catch((err: any) => {
          console.error(err, "Error updating message id");
        });
    }
  }, [currentMessages]);

  function displayUnread() {
    const isFirstMessage =
      userId !== currentMessages?.[0]?.sid &&
      userId !== currentMessages?.[0]?.sender_id;
    const unreadMessageCount =
      currentMessages?.length > 1 ? "new messages" : "a new message";

    if (isFirstMessage) {
      return (
        <div className="d-flex text-muted justify-content-center align-items-center my-2">
          <hr style={{ width: "30px" }} />
          &nbsp; You have {unreadMessageCount} &nbsp;
          <hr style={{ width: "30px" }} />
        </div>
      );
    }
  }

  const [_chatEnabled, setChatEnabled] = useState(true);

  useEffect(() => {
    getConsumablePlans().then((result) => {
      setDemand(result.data?.[1]);
    });

    return () => {
      const obj = { ...mediaLoading };

      for (const i in obj) {
        dispatch(
          chatActions.setMediaLoading({ channel_id: i, loading: false })
        );
      }
    };
  }, []);

  useEffect(() => {
    const data = chatList.find((chat: any) =>
      [chat?.target?._id, chat?.initiator?._id].includes(targetUser)
    );

    if (!data) return;

    const { target, initiator } = data;
    const isTargetUser = target._id === targetUser;
    const myData = isTargetUser ? initiator : target;
    const targetData = isTargetUser ? target : initiator;

    setChatEnabled(
      data.buyers.some((b) => b.buyer === targetUser || b.buyer === userId) ||
        !targetData?.latest_chat_fee?.is_active
    );

    const getChatFeeStatus = (user: any) =>
      user?.latest_chat_fee?.is_active ? "paid" : "free";

    if (myData && targetData) {
      setChannelData({
        me: {
          display_name: myData.display_name,
          id: myData._id,
          avatar: myData.avatar?.[0],
          chat_fee: getChatFeeStatus(myData),
        },
        target: {
          display_name: targetData.display_name,
          id: targetData._id,
          avatar: targetData.avatar?.[0],
          chat_fee: getChatFeeStatus(targetData),
        },
      });
    }
  }, [chatList, targetUser]);

  useEffect(() => {
    return () => {
      if (window.innerWidth < 768) dispatch(configActions.showFooter(true));
    };
  }, []);

  useEffect(() => {
    setStopScroll(false);
  }, [targetUser]);

  useDeepCompareEffect(() => {
    setCompleteMessages([
      ...(prevMessages ? prevMessages : []),
      ...(currentMessages ? currentMessages : []),
    ]);
    dispatch(
      chatActions.setCompleteMessages({
        messages: [
          ...(prevMessages ? prevMessages : []),
          ...(currentMessages ? currentMessages : []),
        ],
        channelId,
      })
    );
  }, [currentMessages, prevMessages]);

  if ((targetUser === "" && chatList.length) || !isLoaded) {
    return (
      <div
        className="chat-content-body mb-2 p-3 position-relative overflow-y-auto"
        ref={divRef}
      >
        <ChatShimmer />
      </div>
    );
  }

  const handleUnblockUser = () => {
    Swal.fire({
      text: "Are you sure you want to unblock?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, unblock",
      customClass: {
        confirmButton: "custom-btn",
        cancelButton: "custom-btn",
      },
    }).then((res) => {
      if (res.isConfirmed) {
        UnblockUser(targetUser)
          .then(() => {
            dispatch(blockActions.removeBlockedUser(targetUser));
          })
          .catch((err) => {
            Swal.fire({
              title: `${err.message}`,
              icon: "error",

              confirmButtonColor: "#ac1991",
            });
          });
      }
    });
  };

  const handleScroll = async () => {
    const buffer = 100;

    if (
      divRef.current.scrollHeight - divRef.current.scrollTop - buffer >
      divRef.current.clientHeight
    ) {
      setIsScrolledUp(true);
    } else {
      setIsScrolledUp(false);
    }

    if (
      divRef.current.scrollTop === 0 &&
      divRef.current.scrollHeight > divRef.current.clientHeight &&
      !stopScroll
    ) {
      setInternalLoading(true);
      const currentScrollHeight = divRef.current.scrollHeight;

      const newMessages = await socketChannel?.channel?.getMessages({
        time: completeMessages[0]?.createdAt,
      });

      if (newMessages && newMessages.msgs.read.length > 0) {
        dispatch(
          chatActions.setPrevChat([...newMessages.msgs.read, ...prevMessages])
        );
        dispatch(
          chatActions.setCompleteMessages({
            messages: [...newMessages.msgs.read, ...prevMessages],
            channelId,
          })
        );
      } else {
        setStopScroll(true);
      }

      requestAnimationFrame(() => {
        const newScrollHeight = divRef.current.scrollHeight;

        divRef.current.scrollTop = newScrollHeight - currentScrollHeight;
        setInternalLoading(false);
      });
    }
  };

  return (
    <>
      <div
        className="chat-content-body p-3 pb-0 position-relative flex-grow-1 overflow-y-auto h-100"
        ref={divRef}
        onScroll={handleScroll}
      >
        {isLoaded && targetUser ? (
          !prevMessages?.length && !currentMessages?.length ? (
            <div className="text-center text-md h-100 d-flex justify-content-center align-items-center">
              This is the start of something knky ;)
            </div>
          ) : (
            <>
              {userId ? (
                <>
                  {internalLoading && (
                    <div className="d-flex justify-content-center w-100 my-1">
                      <LoadingSpinner />
                    </div>
                  )}
                  {currentMessages?.length > 0 && displayUnread()}
                  <ReadUnreadMessages
                    usedAs="previous"
                    messageArray={completeMessages}
                    videoConfRef={videoConfRef}
                    rateRef={rateRef}
                    setCallReqId={setCallReqId}
                    imageModalRef={imageModalRef}
                    setImageMessage={setImageMessage}
                    showBuyTicketModal={showBuyTicketModal}
                    buyTicketModalRef={buyTicketModalRef}
                    receiver={receiver}
                    setShowBuyTicketModal={setShowBuyTicketModal}
                    setReceiver={setReceiver}
                    ratingReqref={ratingReqref}
                  />
                  {mediaLoading[channelId] && (
                    <div className="sender w-max-35 bg-chat-sender ms-auto d-block w-fit px-3 mt-1 py-2 rounded-3">
                      <i className="">
                        Sending <ProcessingLoader />
                      </i>
                    </div>
                  )}
                  <div ref={chatContainerRef}></div>
                  <div
                    className={classNames("color-medium fs-8", {
                      visible: isTyping,
                      invisible: !isTyping,
                    })}
                  >
                    <span className="text-capitalize">
                      {channelData.target.display_name}
                    </span>{" "}
                    is typing <ProcessingLoader />
                  </div>
                </>
              ) : (
                <JoinUs />
              )}
            </>
          )
        ) : null}
        <ImageModal setChildRef={imageModalRef} misc={imageMessage} />
        {(!isLoaded || !targetUser) &&
          !prevMessages.length &&
          !currentMessages.length && (
            <div className="text-center text-md h-100 d-flex justify-content-center align-items-center">
              This is the start of something knky ;)
            </div>
          )}
        {isScrolledUp && (
          <div
            className={classNames(
              "position-sticky end-100 w-fit rounded p-1 m-2 text-white pointer z-3"
            )}
            style={{
              bottom: replyMessage?._id || has_chat_media ? 120 : 0,
            }}
            onClick={scrollIntoView}
          >
            <GoBackToPositionIcon />
          </div>
        )}
      </div>

      {blockedUser[targetUser] && (
        <div className="d-flex flex-column align-items-center justify-content-center gap-3 pb-5">
          <div className="d-flex w-100 justify-content-between align-items-center gap-1">
            <hr className="color-medium w-100" style={{ height: "2px" }} />
            <div className="w-100 text-center fw-light">
              {`Today, ${new Date().getDate()} ${new Date().toLocaleDateString(
                "en-GB",
                {
                  month: "short",
                }
              )} ${new Date().getFullYear()}`}
            </div>
            <hr className="color-medium w-100" style={{ height: "2px" }} />
          </div>
          <ActionButton onClick={handleUnblockUser}>Unblock</ActionButton>
        </div>
      )}
    </>
  );
};

export default memo(ChatBox);

export const PerChatOption: Record<
  "Minute" | "Hour" | "Day" | "Month" | "PerMessage",
  string
> = {
  Minute: "minute",
  Hour: "hour",
  Day: "day",
  Month: "month",
  PerMessage: "message",
};
