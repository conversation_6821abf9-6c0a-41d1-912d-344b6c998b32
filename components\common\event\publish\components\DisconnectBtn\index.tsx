import styles from "./index.module.scss";

import { useRoomContext } from "@livekit/components-react";
import classNames from "classnames";
import type { Room } from "livekit-client";
import { forwardRef, memo, type Ref, useImperativeHandle } from "react";
import Swal from "sweetalert2";

function DisconnectBtnBase(props: { onClick: () => void }, ref: Ref<Room>) {
  const roomContext = useRoomContext();

  useImperativeHandle(ref, () => roomContext);

  const confirmEnd = () => {
    Swal.fire({
      imageUrl: "/images/event/stop-recording.svg",
      imageHeight: 50,
      imageAlt: "End Event",
      title: "Are you sure you want to end this event?",
      confirmButtonText: "End This Event",
      cancelButtonText: "Cancel",
      showCancelButton: true,
      showCloseButton: true,
      customClass: {
        image: "mb-0",
        title: "fw-semibold ",
        actions: "flex-wrap flex-md-nowrap w-75 gap-3",
        confirmButton: classNames(
          "w-100 w-md-50 btn btn-outline-danger fw-medium",
          styles["border-1"]
        ),
        cancelButton: "w-100 w-md-50 btn btn-purple fw-medium",
      },
      buttonsStyling: false,
    }).then((result) => {
      if (result.isConfirmed) {
        roomContext.disconnect(true);
        props.onClick();
      }
    });
  };

  return (
    <button
      className="btn btn-danger float-right fw-normal"
      onClick={confirmEnd}
    >
      End Stream
    </button>
  );
}

export const DisconnectBtn = memo(forwardRef(DisconnectBtnBase));
