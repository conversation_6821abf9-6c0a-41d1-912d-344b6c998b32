"use client";

import classNames from "classnames";
import { endOfDay, startOfDay } from "date-fns";
import Image from "next/image";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";

import type { TicketResponse } from "@/api/event";
import {
  GetEvent,
  GetEventAnalytics,
  GetEventLatestTips,
  GetTicketsSold,
  GetTotalTipsEarned,
} from "@/api/event";
import Loading from "@/app/(user)/(home)/loading";
import BarChart from "@/app/(user)/(sections)/dashboard/utils/bar-chart";
import { DayWiseCategory } from "@/app/(user)/(sections)/dashboard/utils/bar-chart/category";
import { TicketSoldTable } from "@/components/common/event/analysis";
import { TipTable } from "@/components/common/event/analysis/tip-table";
import type { TabChangeEvent, TabData } from "@/components/common/event/tab";
import { Tabs } from "@/components/common/event/tab";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import { formatCurrency } from "@/utils/formatter";

import style from "./page.module.scss";

export default function EventInsights() {
  const params = useParams();
  const [activeMainTabIdx, setActiveMainTabIdx] = useState(0);
  const [eventInfo, setEventInfo] = useState<Record<string, any>>({});
  const [showTab, setShowTab] = useState(true);
  const [totalTips, setTotalTips] = useState(0);
  const [ticketsSold, setTicketsSold] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [tipData, setTipData] = useState<any>([]);
  const [ticketsData, setTicketsData] = useState<TicketResponse[]>([]);
  const [pageCount, setPageCount] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [dataDump, setDataDump] = useState<number[]>([]);
  const eventId: any = params.eventId;

  const handlePageChange = ({ selected }: { selected: number }) => {
    setCurrentPage(selected + 1);

    if (activeMainTabIdx === 0) {
      GetTicketsSold(eventId, selected + 1).then((res) => {
        setTicketsData(res.data);
      });
    } else {
      GetEventLatestTips(eventId, selected + 1).then((res) => {
        setTipData(res.data);
        const pageCount = Math.ceil(res.page_data.total_result / 10);
        setPageCount(pageCount);
      });
    }
  };

  useEffect(() => {
    const eventId: any = params.eventId;

    const fetchEventInfo = GetEvent(eventId);
    const fetchTotalTips = GetTotalTipsEarned(eventId);
    const fetchTicketsSold = GetTicketsSold(eventId, currentPage);

    fetchAnalytics({ type: "sold_tickets", filter: true });
    Promise.all([fetchEventInfo, fetchTotalTips, fetchTicketsSold])
      .then(([eventResult, tipsResult, ticketsResult]) => {
        setEventInfo(eventResult.data);
        setTotalTips(tipsResult?.data?.earning_excludiing_platform_fee);
        setTicketsData(ticketsResult?.data);
        setTicketsSold(ticketsResult?.data?.length);
        const pageCount = Math.ceil(ticketsResult.page_data.total_result / 10);
        setPageCount(pageCount);
        setIsLoading(false);
      })
      .catch((err) => console.log(err));
  }, [params.eventId]);

  const updateTrafficSeries = (hourlyData: any[], tabIndex: number) => {
    const ans = Array.from({ length: 24 }, () => 0);
    hourlyData.forEach((data: any) => {
      const index = data.hour;

      if (index !== -1) {
        if (!isNaN(ans[index])) {
          ans[index] += tabIndex === 0 ? data.count : data.total_amount;
        } else {
          ans[index] = tabIndex === 0 ? data.count : data.total_amount;
        }
      }
    });
    setDataDump(ans);
  };

  const trafficSeries = [
    {
      name: activeMainTabIdx === 0 ? "Tickets" : "Tips",
      data: dataDump,
    },
  ];
  const trafficColors = ["#000000"];
  const overViewData = [
    { title: "Tickets sold", count: ticketsSold },
    {
      title: "Ticket sold value",
      count: formatCurrency(ticketsSold * eventInfo?.price),
    },
    {
      title: "Total tips",
      count: formatCurrency(totalTips),
    },
    // TODO: Need to save the view count of the event.
    { title: "Total view", count: ticketsSold },
    {
      title: "Total value",
      count: formatCurrency(ticketsSold * eventInfo?.price + totalTips),
    },
  ];
  const [mainTabs] = useState<TabData[]>([
    { name: "Tickets sold", key: "tickets" },
    { name: "Tips during streaming", key: "tipTracking" },
  ]);
  const navigateBack = useNavigateBack();

  function fetchAnalytics({
    type,
    filter = false,
    index = 0,
  }: {
    type: "sold_tickets" | "tips";
    filter?: boolean;
    index?: number;
  }) {
    const eventId: any = params.eventId;

    const data: any = {
      eventId,
      groupBy: "hour",
      type,
    };

    if (filter) {
      data.start_time = startOfDay(new Date()).toISOString();
      data.end_time = endOfDay(new Date()).toISOString();
    }

    GetEventAnalytics(data)
      .then((result: any) => updateTrafficSeries(result.data, index))
      .catch((err) => console.log(err));
  }

  const mainTabChangeHandler = (e: TabChangeEvent) => {
    const newTabIdx = e.index;
    setActiveMainTabIdx(newTabIdx);
    setShowTab(true);
    const type = newTabIdx === 0 ? "sold_tickets" : "tips";

    if (type === "tips") {
      setCurrentPage(1);
      GetEventLatestTips(eventId, currentPage).then((res) => {
        setTipData(res.data);
        const pageCount = Math.ceil(res.page_data.total_result / 10);
        setPageCount(pageCount);
      });
    } else {
      setCurrentPage(1);
      GetTicketsSold(eventId, currentPage).then((res) => {
        setTicketsData(res.data);
        const pageCount = Math.ceil(res.page_data.total_result / 10);
        setPageCount(pageCount);
      });
    }

    fetchAnalytics({
      type,
      index: newTabIdx,
      filter: newTabIdx === 0 ? true : false,
    });
  };

  return (
    <>
      {isLoading ? (
        <Loading />
      ) : (
        <div className="container mt-3">
          <div className="bg-body d-flex flex-column gap-3  rounded-3 p-3">
            <div className="d-flex align-items-center gap-3">
              <Image
                src={"/images/svg/back.svg"}
                width={50}
                className="pointer"
                height={50}
                onClick={() => navigateBack()}
                alt="back"
              />
              <h5 className="fw-semibold m-0">Overview</h5>{" "}
              <div
                className={classNames(
                  "d-flex gap-2 align-items-center ",
                  style.endEventWrapper
                )}
              >
                <Image
                  src={"/images/post/fill-calendar.svg"}
                  width={20}
                  height={20}
                  style={{ filter: "grayscale(1)" }}
                  alt="calender"
                />
                <h6 className="m-0">
                  {eventInfo?.has_ended ? "Ended Event" : ""}
                </h6>
              </div>
            </div>
            <div>
              <div className={classNames(style.overviewCard)}>
                {overViewData.map((data, index) => (
                  <OverviewCount
                    key={index}
                    title={data.title}
                    count={data.count}
                  />
                ))}
              </div>
            </div>
            <div>
              <Tabs
                tabs={mainTabs}
                type="base"
                className="border-bottom border-1 border-secondary-subtle my-2"
                onChange={mainTabChangeHandler}
                itemClassName="pb-3 analysis-tabs"
              />

              <nav>
                <div
                  className={classNames(
                    "nav nav-tabs",
                    style.navTab,
                    activeMainTabIdx !== 0 && "w-25"
                  )}
                  id="nav-tab"
                  role="tablist"
                >
                  {activeMainTabIdx === 0 && (
                    <button
                      className={classNames(
                        "nav-link ",
                        style.navLink,
                        showTab ? style.active : "",
                        showTab ? "active" : ""
                      )}
                      id="nav-home-tab"
                      data-bs-toggle="tab"
                      data-bs-target="#nav-home"
                      type="button"
                      role="tab"
                      aria-controls="nav-home"
                      aria-selected="true"
                      onClick={() => {
                        fetchAnalytics(
                          activeMainTabIdx === 0
                            ? {
                                type: "sold_tickets",
                                filter: true,
                                index: activeMainTabIdx,
                              }
                            : {
                                type: "tips",
                                filter: true,
                                index: activeMainTabIdx,
                              }
                        );
                        setShowTab(true);
                      }}
                    >
                      <p
                        className={classNames(
                          "m-0",
                          showTab ? "color-primary" : ""
                        )}
                      >
                        Today
                      </p>
                    </button>
                  )}

                  <button
                    className={classNames(
                      "nav-link",
                      style.navLink,
                      showTab ? "" : style.active,
                      showTab ? "" : "active",
                      activeMainTabIdx !== 0 && "active",
                      activeMainTabIdx !== 0 && style.active
                    )}
                    id="nav-profile-tab"
                    data-bs-toggle="tab"
                    data-bs-target="#nav-profile"
                    type="button"
                    role="tab"
                    aria-controls="nav-profile"
                    aria-selected="false"
                    onClick={() => {
                      fetchAnalytics(
                        activeMainTabIdx === 0
                          ? {
                              type: "sold_tickets",
                              filter: false,
                              index: activeMainTabIdx,
                            }
                          : {
                              type: "tips",
                              filter: false,
                              index: activeMainTabIdx,
                            }
                      );
                      setShowTab(false);
                    }}
                  >
                    <p
                      className={classNames(
                        "m-0",
                        showTab ? "" : "color-primary",
                        activeMainTabIdx !== 0 && "color-primary"
                      )}
                    >
                      Total
                    </p>
                    {/* <p>{totalTicketsForToday()}</p> */}
                  </button>
                </div>
              </nav>
              <div className="tab-content" id="nav-tabContent">
                <div>
                  <BarChart
                    series={trafficSeries}
                    minTick={0}
                    maxTick={activeMainTabIdx === 0 ? ticketsSold : totalTips}
                    showCurrency={activeMainTabIdx === 0 ? false : true}
                    colors={trafficColors}
                    barWidth="20%"
                    categories={DayWiseCategory}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="bg-body mt-3  p-3 rounded-3">
            <h5 className="fw-semibold m-0">Ticket sold</h5>
            <div className="scrollable">
              {activeMainTabIdx === 0 && (
                <TicketSoldTable
                  data={ticketsData}
                  ticket_amount={eventInfo?.price}
                />
              )}
              {activeMainTabIdx === 1 && <TipTable data={tipData} />}
            </div>
            <div className="d-flex justify-content-center">
              <ReactPaginate
                previousLabel={"Previous"}
                nextLabel={"Next"}
                breakLabel={"..."}
                pageCount={pageCount}
                marginPagesDisplayed={2}
                pageRangeDisplayed={5}
                onPageChange={handlePageChange}
                containerClassName={"pagination mt-0"}
                activeClassName={"active"}
                pageClassName="page-item"
                pageLinkClassName="page-link"
                previousClassName="page-item"
                previousLinkClassName="page-link"
                nextClassName="page-item"
                nextLinkClassName="page-link"
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}

const OverviewCount = ({
  title,
  count,
}: {
  title: string;
  count: number | string;
}) => (
  <>
    <div className="card flex-grow-1 p-3">
      <p className=" fw-medium ">{title}</p>
      <h4 className="m-0">{count}</h4>
    </div>
  </>
);
