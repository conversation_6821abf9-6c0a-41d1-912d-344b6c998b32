"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import "./index.scss";

import {
  chatActions,
  matchMakerActions,
  userActions,
  userDataActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { getAssetUrl } from "@/utils/assets";
import socketChannel from "@/utils/chatSocket";
import { auth } from "@/utils/firebase";

export default function Menu() {
  const user = useAppSelector((state) => state.user);
  const chatSocket = socketChannel;
  const dispatchAction = useAppDispatch();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [avatar, setAvatar] = useState("/images/common/default.svg");
  const [username, setUsername] = useState("User");

  useEffect(() => {
    setAvatar(
      getAssetUrl({ media: user.profile.avatar[0], defaultType: "avatar" })
    );
    setUsername(user.profile.f_name);
  }, [user]);

  useEffect(() => {
    if (isDarkMode) {
      document.body.classList.add("dark");
    } else {
      document.body.classList.remove("dark");
    }

    return () => {
      document.body.classList.remove("dark");
    };
  }, [isDarkMode]);

  const handleToggleSwitch = () => {
    setIsDarkMode(!isDarkMode);
  };

  return (
    <div className="menu-wrapper bg-body fade show border">
      <div className="container">
        <div className="d-flex flex-column justify-content-between gap-3 menu-profile-wrapper py-3">
          <Link
            href={`/${user?.role?.toLowerCase()}/${user.profile.username}`}
            className="d-flex user-row justify-content-between align-items-center"
          >
            <div className="user-wrapper d-flex gap-3">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={avatar}
                width={50}
                height={50}
                alt="profile-icon"
                className="rounded-pill"
              />
              <div className="d-flex flex-column align-items-start">
                <p className="fs-5 mb-0 fw-medium">{username}</p>
                <p className="fs-7 mb-0 color-light">
                  Click to see your profile
                </p>
              </div>
            </div>
            <Image
              src={"/images/svg/chevron-right.svg"}
              width={30}
              height={30}
              alt="profile-chevron"
            />
          </Link>

          <Link
            className="color-bold py-2 d-flex gap-2 align-items-center"
            href="/settings/account-information"
          >
            <Image
              className="invert"
              src={"/header/accountInfoIcon.svg"}
              alt=""
              width={25}
              height={25}
            />
            Account Information
          </Link>
          <Link
            className="color-bold py-2 d-flex gap-2 align-items-center"
            href="/settings/settings-and-privacy"
          >
            <Image
              className="invert"
              src={"/settings/Setting.svg"}
              alt=""
              width={25}
              height={25}
            />
            Settings & Privacy
          </Link>
          <Link
            className="color-bold py-2 d-flex gap-2 align-items-center"
            href="/settings/addCard"
          >
            <Image
              className="invert"
              src={"/header/card.svg"}
              alt=""
              width={25}
              height={25}
            />
            Card
          </Link>
          <Link
            className="color-bold py-2 d-flex gap-2 align-items-center"
            href="/settings/wallet"
          >
            <Image
              className="invert"
              src={"/header/walletIcon.svg"}
              alt=""
              width={25}
              height={21}
            />
            Wallet
          </Link>
          <Link
            className="color-bold py-2 d-flex gap-2 align-items-center"
            href="/settings"
          >
            <Image
              className="invert"
              src={"/header/revenueIcon.svg"}
              alt=""
              width={25}
              height={25}
            />
            Revenue Statistics
          </Link>
          <Link
            className="color-bold py-2 d-flex gap-2 align-items-center"
            href="/settings"
          >
            <div className="d-flex justify-content-between align-items-center w-100">
              <div className="d-flex gap-2">
                <Image
                  className="invert"
                  src={"/header/data-storage.svg"}
                  alt=""
                  width={25}
                  height={25}
                />
                Data & storage
              </div>
              <p className="color-light fs-6 mb-0 fw-semibold">56%</p>
            </div>
          </Link>

          <Link
            className="color-bold py-2 d-flex gap-2 align-items-center"
            href="/settings/help-and-support"
          >
            <Image
              className="invert"
              src={"/header/helpSupport.svg"}
              alt=""
              width={25}
              height={20}
            />
            Help & Support
          </Link>

          <div className="d-none justify-content-between   align-items-center">
            <div className="d-flex flex-grow-1 gap-2 align-items-center">
              <Image
                src="/settings/systemMode.svg"
                alt=""
                width={25}
                height={19}
              />

              <span>System mode</span>
            </div>

            <div className="d-flex justify-content-center">
              <div className="form-check form-switch">
                <input
                  type="checkbox"
                  className="form-check-input dark-mode-toggle"
                  checked={isDarkMode}
                  onChange={handleToggleSwitch}
                  id="darkModeToggle"
                />
              </div>
            </div>
          </div>
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex gap-2 align-items-center">
              <Image
                src="/settings/language.svg"
                alt=""
                width={25}
                height={22}
              />

              <span>Language</span>
            </div>
            <select
              className="border-0 bg-cream shadow-dark rounded-3 px-2 py-1 color-dark"
              name=""
              id=""
            >
              <option value="" className="fs-8 p-0 mx-2 my-1 bg-body">
                English
              </option>
              <option value="" className="fs-8 p-0 mx-2 my-1 bg-body">
                Spanish
              </option>
              <option value="" className="fs-8 p-0 mx-2 my-1 bg-body">
                French
              </option>
            </select>
          </div>
          <Link
            href="/"
            className="color-bold d-flex gap-2 align-items-center py-2 pointer"
            onClick={() => {
              localStorage.setItem("first-walk", "false");
              dispatchAction(userActions.setConverseToken(""));
              dispatchAction(userDataActions.logOut());
              dispatchAction(chatActions.resetChatState());
              dispatchAction(userActions.setFetched(false));
              dispatchAction(userActions.resetUserProfile());
              dispatchAction(userActions.setUserToken(""));
              dispatchAction(matchMakerActions.resetProfile());
              dispatchAction(userActions.setUserIsVerified(undefined));

              auth.signOut();

              chatSocket.closeSocket();
            }}
          >
            <Image
              className="invert"
              src={"/settings/logOut.svg"}
              alt=""
              width={25}
              height={22}
            />
            Log Out
          </Link>
          <ul className="d-flex nav justify-content-start justify-content-lg-center justify-content-md-center mb-1 mb-lg-3 fs-6 mb-5 ">
            <li className="nav-item fw-medium border-end px-3 py-1">
              <Link href={"#"}>About us</Link>
            </li>
            <li className="nav-item fw-medium border-end px-3 py-1">
              <Link href={"#"}>Blog</Link>
            </li>
            <li className="nav-item fw-medium border-end px-3 py-1">
              <Link href={"#"}>FAQs</Link>
            </li>
            <li className="nav-item fw-medium border-end px-3 py-1">
              <Link href={"#"}>Privacy Policy</Link>
            </li>
            <li className="nav-item fw-medium px-3 py-1 mb-3">
              <Link href={"#"}>Terms and conditions</Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
