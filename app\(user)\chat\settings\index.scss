:root {
  --background-light: white;
  --background-dark: #1e1e1e;
  --text-light: black;
  --text-dark: white;
}

[data-bs-theme="dark"] {
  --background-light: var(--background-dark);
  --text-light: var(--text-dark);
}

.chat-settings-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.menu-container {
  background: var(--background-light);
  transition: transform 0.3s ease-in-out;
}

.menu-header {
  font-weight: bold;
  text-align: start;
  font-size: 1.6rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.partition-header {
  font-weight: bold;
  margin-top: 1rem;
  color: var(--text-light);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  padding: 0.5rem 0;
  color: var(--text-light);
}

.menu-item span {
  font-weight: bold;
}

.menu-items {
  display: flex;
  flex-direction: column;
}

.content-container {
  flex-grow: 1;
  padding: 1rem;
  overflow: auto;
  color: var(--text-light);
}

.btn-back {
  display: block;
  margin-bottom: 1rem;
  color: var(--text-light);
}

@media (min-width: 576px) {
  .chat-settings-container {
    flex-direction: row;
  }

  .menu-container {
    flex: 1;
    transform: translateX(0);
  }

  .content-container {
    flex: 2;
  }

  .mobile-visible,
  .mobile-hidden {
    transform: none;
  }
}

@media (max-width: 575px) {
  .menu-header {
    text-align: start;
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .menu-container {
    transform: translateX(0);
    transition: transform 0.3s ease-in-out;
  }

  .content-container {
    display: none;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
  }

  .mobile-visible {
    display: block;
    transform: translateX(0);
  }

  .mobile-hidden {
    transform: translateX(-100%);
  }

  .content-container {
    flex-grow: 1;
    padding: 0rem;
    overflow: auto;
  }
}
