import { useCallback } from "react";

import {
  getChannelId,
  getChatUserList,
  GetUserChatFeeServices,
} from "@/api/chat";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";
import type { Chat, MessageInterface } from "@/redux-store/slices/chat.slice";
import { store } from "@/redux-store/store";
import socketChannel from "@/utils/chatSocket";

import { sortAndFilterChatList } from "../chatList";

/**
 * Custom hook for showing chats based on the target user.
 *
 * @param targetUser - The ID of the target user
 * @param setMenuVisible - Function to set the visibility of the menu
 * @param setActiveChat - Function to set the active chat
 * @param setChatBoxDisable - Function to disable the chat box
 *
 * @returns {Object} - Object containing the function to show chats
 */
const useShowChat = ({
  setMenuVisible,
  setActiveChat,
  setChatBoxDisable,
}: {
  targetUser: string;
  setMenuVisible: any;
  setActiveChat: any;
  setChatBoxDisable: any;
}) => {
  const dispatch = useAppDispatch();

  const showChats = useCallback(
    async (userId: string) => {
      try {
        let latestChatList = store.getState().chat.chatList;
        store.dispatch(chatActions.setCurrentChat([]));
        store.dispatch(chatActions.setPrevChat([]));

        let channelId = latestChatList.find(
          (chat: Chat) =>
            chat?.initiator?._id === userId || chat?.target?._id === userId
        )?.converse_channel_id;

        let existingIndex = latestChatList.findIndex(
          (c) => c.converse_channel_id === channelId
        );

        if (
          latestChatList[existingIndex]?.complete_messages &&
          Array.isArray(latestChatList[existingIndex]?.complete_messages)
        ) {
          store.dispatch(
            chatActions.setPrevChat(
              latestChatList[existingIndex].complete_messages
            )
          );
        } else {
          dispatch(chatActions.updateLoadingState(false));
        }

        setMenuVisible(false);
        setChatBoxDisable(true);
        dispatch(chatActions.setTargetUser(userId));

        if (!channelId) {
          const {
            data: { converse_channel_id },
          } = await getChannelId(userId);
          channelId = converse_channel_id;
          const { data } = await getChatUserList();
          dispatch(
            chatActions.setChatUserList(sortAndFilterChatList(data, userId))
          );
          latestChatList = sortAndFilterChatList(data, userId);
          existingIndex = latestChatList.findIndex(
            (c) => c.converse_channel_id === channelId
          );
        }

        const response = await GetUserChatFeeServices({
          user_id: userId,
        });

        setActiveChat(userId);
        dispatch(chatActions.setChannelId(channelId || ""));
        socketChannel.closeChannel();

        if (channelId) {
          const channel = await socketChannel.updateChannel(channelId);

          if (channel) {
            if (
              existingIndex !== -1 &&
              latestChatList[existingIndex]?.complete_messages?.length
            ) {
              await socketChannel.channel?.checkMoreMessage({
                time: latestChatList[existingIndex].complete_messages?.[
                  latestChatList[existingIndex].complete_messages.length - 1
                ]?.createdAt,
              });
              dispatch(
                chatActions.setCompleteMessages({
                  messages: latestChatList[existingIndex].complete_messages,
                  channelId,
                })
              );
            } else {
              const { msgs } = await channel.getMessages({});
              dispatch(chatActions.setPrevChat([...msgs.read, ...msgs.unread]));
              dispatch(
                chatActions.setCompleteMessages({
                  messages: [
                    ...msgs.read,
                    ...msgs.unread,
                  ] as MessageInterface[],
                  channelId,
                })
              );
            }

            if (response.data) {
              const activeChatFee = response.data.filter((c) => c.is_active);

              const targetChat = store.getState().chat.chatList[existingIndex];

              let updatedList;

              if (targetChat?.initiator._id === userId) {
                updatedList = latestChatList.map((chat, idx) => {
                  if (idx !== existingIndex) return chat;

                  return {
                    ...chat,
                    initiator: {
                      ...chat?.initiator,
                      chat_fee_services: activeChatFee,
                    },
                  };
                });
              } else {
                updatedList = latestChatList.map((chat, idx) => {
                  if (idx !== existingIndex) return chat;

                  return {
                    ...chat,
                    target: {
                      ...chat?.target,
                      chat_fee_services: activeChatFee,
                    },
                  };
                });
              }

              dispatch(chatActions.setChatUserList(updatedList));
              latestChatList = updatedList;
            }
          }

          try {
            dispatch(
              chatActions.updateUnreadCount({ id: channelId, count: 0 })
            );
          } catch (err) {
            console.error(err, "Error when fetching message options array");
          }
        }

        dispatch(chatActions.updateLoadingState(true));
      } catch (error) {
        console.error("Error fetching chats:", error);
      }
    },
    [store.getState().chat.targetUser]
  );

  return { showChats };
};

export default useShowChat;
