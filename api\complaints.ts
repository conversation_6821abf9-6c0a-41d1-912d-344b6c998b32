import API from ".";

interface complaint {
  issue_area: string;
  description: string;
  media: File | null;
}
export const createComplaint = (body: complaint) => {
  const formData = new FormData();
  formData.append("description", body.description);
  formData.append("issue_area", body.issue_area);
  if (body.media) formData.append("media", body.media);

  return API.post(`${API.COMPLAINTS}/create`, formData) as Promise<any>;
};

export const getComplaints = async () =>
  API.get(`${API.COMPLAINTS}/users/list`);

export const getSingleComplaint = async (id: string) =>
  API.get(`${API.COMPLAINTS}/${id}`);

export const complaintResolved = async (id: string) =>
  API.patch(`${API.COMPLAINTS}/update?complaint_id=${id}&resolved=true`, {});
