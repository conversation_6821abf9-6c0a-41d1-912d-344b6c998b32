/* eslint-disable prefer-const */
"use client";

import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { type ReactNode, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import "./index.scss";

import { format } from "date-fns";
import { toast } from "sonner";

import type { MediaInfo, PostSubmitBody } from "@/api/post";
import { EditPost, PostSubmit } from "@/api/post";
import type { ReleaseFormsResponse } from "@/api/user";
import PostNavbar from "@/app/(user)/(sections)/navbar";
import KYCwarning from "@/components/common/KYC-warning";
import Loader from "@/components/common/loader/loader";
import Modal, {
  type ModalImportProps,
  type ModalRef,
} from "@/components/common/modal";
import Spinner from "@/components/common/spinner";
import { PostVisibility } from "@/global/constants";
import { IMAGE_SIZE_MAX } from "@/global/limits/post";
import useBrowser from "@/hooks/useBrowser";
import useFileUploader from "@/hooks/useFileUploader";
import useIsPWA from "@/hooks/useIsPWA";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import {
  configActions,
  createActions,
  userDataActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { store } from "@/redux-store/store";
import type { Media } from "@/types/media";
import type { Collaborator } from "@/types/post";
import type { VFile } from "@/types/vault";
import { getAssetUrl } from "@/utils/assets";

import { PostVisibilitySelector } from "../post-visibility";
import Feeling from "./modal/feeling";
import Promote from "./modal/promote";
import TagCollab from "./modal/tag-collab";
import PostContent from "./post-content";
import PostSettings from "./post-settings";
import { ThumbnailPreview } from "./unlock-thumbnail-preview";
import UserWarning from "./user-only";

interface Post {
  tag: boolean;
  vault: boolean;
  collab: boolean;
  poll: boolean;
  event: boolean;
  media: boolean;
  feeling: boolean;
  promote: boolean;
  streaming: boolean;
  background: boolean;
  tagAndRelease: boolean;
}

const DisableMap: any = {
  background: {
    media: true,
    vault: true,
    poll: true,
    tag: true,
    collab: true,
    promote: true,
    streaming: true,
    event: true,
  },
  promote: {
    media: true,
    vault: true,
    poll: true,
    tag: true,
    collab: true,
    background: true,
    streaming: true,
    event: true,
  },
  media: {
    background: true,
    promote: true,
    vault: true,
  },
  vault: {
    media: true,
    background: true,
    promote: true,
  },
  poll: {
    background: true,
    promote: true,
  },
  premium: {
    poll: true,
    promote: true,
    background: true,
  },
  prime: {
    promote: true,
    background: true,
  },
  subscription: {
    tag: true,
    collab: true,
    poll: true,
    promote: true,
    background: true,
  },
};

const EnableMap: any = {
  background: {
    media: false,
    vault: false,

    poll: false,
    tag: false,
    collab: false,
    promote: false,
    streaming: false,
    event: false,
  },
  promote: {
    media: false,
    vault: false,

    poll: false,
    tag: false,
    collab: false,
    background: false,
    streaming: false,
    event: false,
  },
  media: {
    background: false,
    promote: false,
    vault: false,
  },
  vault: {
    media: false,
    background: false,
    promote: false,
  },
  poll: {
    background: false,
    promote: false,
  },
  premium: {
    poll: false,
    promote: false,
    background: false,
  },
  prime: {
    promote: false,
    background: false,
  },
  subscription: {
    collab: false,
    tag: false,

    poll: false,
    promote: false,
    background: false,
  },
};

const ViewMap: any = {
  creator: {
    media: true,
    poll: true,
    tag: true,
    collab: true,
    promote: true,
    event: true,
    streaming: true,
    background: true,
    feeling: true,
    vault: true,
    tagAndRelease: true,
  },
  user: {
    poll: true,
    media: false,
  },
  guest: {
    poll: false,
    media: false,
  },
};

const SvgMap = {
  tag: "tag",
  collab: "collab",
  tagAndRelease: "tag",
  poll: "poll",
  event: "event",
  media: "upload-media",
  feeling: "feeling",
  promote: "promote",
  streaming: "streaming",
  background: "background",
  vault: "vault",
};

enum FeelingMap {
  happy = "happy",
  loved = "loved",
  lovely = "lovely",
  excited = "excited",
  sexy = "sexy",
  love = "in love",
  crazy = "crazy",
  hot = "hot",
  fantastic = "fantastic",
  cool = "cool",
  chill = "chill",
  sick = "sick",
  lucky = "lucky",
  fresh = "fresh",
  silly = "silly",
}

const pollInitState = {
  question: "",
  answer_choice_type: "single",
  answer_choices: [
    { option: "", votes: [] },
    { option: "", votes: [] },
  ],
};
const feelingModalRef = { method: {} as ModalRef };
const tagModalRef = { method: {} as ModalRef };
const promoteModalRef = { method: {} as ModalRef };
const mobileOptionsModalRef = { method: {} as ModalRef };

export default function CreatePost({
  isEdit = false,
  postId = "",
  media,
}: {
  isEdit?: boolean;
  postId?: string;
  media?: Media[];
}) {
  const isStandAlone = useIsPWA();
  const browser = useBrowser().os;
  const searchParams = useSearchParams();
  const navigateBack = useNavigateBack();
  const dispatchAction = useAppDispatch();
  const groupData = useAppSelector((state) => state.defaults.group_profile);
  const channelprofile = useAppSelector(
    (state) => state?.defaults?.channel_profile
  );
  const user = useAppSelector((state) => state.user);
  const initial_visibility = useAppSelector(
    (s) => s.userData.post.initial_visibility
  );
  const scheduled_date = useAppSelector((s) => s.userData.post.scheduled_on);
  const expiry_date = useAppSelector((s) => s.userData.post.expired_on);

  const [loading, setLoading] = useState(false);
  const [postImgData, setPostImgData] = useState<File[] | any[]>([]);
  const [postSettings, setPostSettings] = useState<boolean>();
  const initialDateTime = new Date().toISOString();
  const [scheduleDateTime, setScheduleDateTime] =
    useState<string>(initialDateTime);
  const [isScheduled, setIsScheduled] = useState<boolean>(false);
  const [hasExpiry, setHasExpiry] = useState<boolean>(false);
  const [expiryTime, setExpiryTime] = useState<string>(
    new Date(new Date(initialDateTime).getTime() + 3600000).toISOString()
  );

  const [donePost, setSubmitPost] = useState(false);
  const [tagModalType, setTagModalType] = useState<"collab" | "tag">("tag");

  const [shareToStory, setShareToStory] = useState(true);
  const [shareOnProfile, setShareOnProfile] = useState(true);
  const [btnHover, _setBtnHover] = useState("");
  const [postImg, setPostImg] = useState<File[]>([]);
  const [postImgUrls, setPostImgUrls] = useState<string[]>([]);
  const [caption, setCaption] = useState("");
  const [videoThumbnail, setVideoThumbnail] = useState<any[] | string[]>([]);
  const [preview, setPreview] = useState<File>();
  const [showPreviewComponent, setShowPreviewComponent] = useState<boolean>();
  const [thumbnailUrl, setThumbnailUrl] = useState<string[]>([]);
  const [_hashtags, setHashtags] = useState<string[]>([]);
  const [croppedPostUrl, setCroppedPostUrl] = useState("");
  const [postVid, setPostVid] = useState<File[]>([]);
  const [postVidUrls, setPostVidUrls] = useState<string[]>([]);
  const [vaultImages, setVaultImages] = useState<Media[]>([]);
  const [vaultVideos, setVaultVideos] = useState<Media[]>([]);
  const [vaultVideoThumbnail, setVaultVideoThumbnail] = useState<
    any[] | string[]
  >([]);
  const [vaultPreview, setVaultPreview] = useState<Media>();
  const [poll, setPoll] = useState(pollInitState);
  const [feeling, setFeeling] = useState({ emoji: "", text: "" });
  const [_selectedFeeling, setSelectedFeeling] = useState("");
  const [postingAsSubs, setPostingAsSubs] = useState(false);

  const [postOf, setPostOf]: [Post, Function] = useState({
    caption: false,
    media: false,
    vault: false,
    poll: false,
    background: false,
    tag: false,
    collab: false,
    feeling: false,
    promote: false,
    event: false,
    streaming: false,
    tagAndRelease: false,
  });
  const [disabled, setDisabled]: [Post, Function] = useState({
    media: false,
    poll: false,
    background: false,
    tag: false,
    collab: false,
    feeling: false,
    promote: false, //change later once implemented
    event: false,
    streaming: false,
    vault: false,
    tagAndRelease: false,
  });
  const [tagUsers, setTagUsers] = useState([] as string[]);
  const [tagConsent, setTagConsent] = useState<any[]>([]);
  const [collabUsers, setCollabUsers] = useState([] as string[] | any[]);

  const [collbedUsers, setCollaborators] = useState([] as string[]);
  const [releaseUsers, setReleaseUsers] = useState<any[]>([]);

  const [mediaLocation, setMediaLocation] = useState<string[]>([]);
  const [mediaDOP, setMediaDOP] = useState<string[]>([]);
  const [mediaTitle, setMediaTitle] = useState<string[]>([]);
  const [isAuthorPresent, setIsAuthorPresent] = useState<boolean[]>([]);
  const [shopItems, setShopItems] = useState([] as any[]);
  const [shopItemId, setShopItemId] = useState([] as string[]);

  const [shareToChannels, setShareToChannels] = useState<string[]>([]);
  const [shareToGroups, setShareToGroups] = useState<string[]>([]);
  const [editPostMedia, setEditPostMedia] = useState<Media[]>(media!);
  const isMobile: boolean = window.innerWidth < 768;

  const openTagModal = (type: "tag" | "collab") => {
    setTagModalType(type);
    tagModalRef.method.open();
  };

  useEffect(() => {
    if (isEdit) {
      media?.map((media: Media, index: number) => {
        console.log({ media });

        if (media.type === "image") {
          setPostImgUrls((prevPostImg) => [
            ...prevPostImg,
            getAssetUrl({ media: media }),
          ]);
          setReleaseUsers((prev: any) => {
            const copy = [...prev];
            copy[index] = media.consent?.external_tag_consent ?? [];
            return copy;
          });
          setTagConsent((prev: any) => {
            const copy = [...prev];
            copy[index] = media.consent?.internal_tag_consent ?? [];
            return copy;
          });
          setMediaDOP((prev: any) => {
            const copy = [...prev];
            copy[index] = media?.date ?? "";
            return copy;
          });
          setMediaLocation((prev: any) => {
            const copy = [...prev];
            copy[index] = media?.location ?? "";
            return copy;
          });

          setMediaTitle((prev: any) => {
            const copy = [...prev];
            copy[index] = media?.title ?? "";
            return copy;
          });
          setIsAuthorPresent((prev: any) => {
            const copy = [...prev];
            copy[index] = media?.is_author_present ?? true;
            return copy;
          });
        } else if (media.type === "video") {
          setPostVidUrls((prevPostVid) => [
            ...prevPostVid,
            getAssetUrl({ media: media }),
          ]);
          setReleaseUsers((prev: any) => {
            const copy = [...prev];
            copy[index] = media.consent?.external_tag_consent ?? [];
            return copy;
          });
          setTagConsent((prev: any) => {
            const copy = [...prev];
            copy[index] = media.consent?.internal_tag_consent ?? [];
            return copy;
          });
          setMediaDOP((prev: any) => {
            const copy = [...prev];
            copy[index] = media?.date ?? "";
            return copy;
          });
          setMediaLocation((prev: any) => {
            const copy = [...prev];
            copy[index] = media?.location ?? "";
            return copy;
          });
          setMediaTitle((prev: any) => {
            const copy = [...prev];
            copy[index] = media?.title ?? "";
            return copy;
          });
          setIsAuthorPresent((prev: any) => {
            const copy = [...prev];
            copy[index] = media?.is_author_present ?? true;
            return copy;
          });
        }
      });

      if (media?.length) {
        setEditPostMedia(media);
      }
    }
  }, [media]);

  useEffect(() => {
    if (collabUsers.length === 0) {
      setPostOf({ ...postOf, tag: false, collab: false });
    }
  }, [collabUsers]);

  useEffect(() => {
    if (tagConsent.length === 0 && releaseUsers.length === 0) {
      setPostOf({ ...postOf, tagAndRelease: false });
    }
  }, [tagConsent, releaseUsers]);

  const [bg, setBg] = useState({
    backgroundColor: "",
    textColor: "",
  });

  // const fileInputRef = useRef<HTMLInputElement>(null);
  const isGroup = searchParams?.get("isGroupPost")
    ? !!searchParams?.get("isGroupPost")
    : false;
  const isChannel = searchParams?.get("isChannelPost")
    ? !!searchParams?.get("isChannelPost")
    : false;
  const isKycCompleted: boolean = useAppSelector(
    (state) => state.user.profile.kyc.full_kyc_completed
  );
  const { uploadFile, fileInputRef, files: uploadingFiles } = useFileUploader();
  const [visibility, setVisibility] = useState(
    isEdit
      ? {
          text: initial_visibility,
          value: initial_visibility,
          icon:
            initial_visibility === "OnlyMe"
              ? "/images/post/fill-private.svg"
              : `/images/post/fill-${initial_visibility?.toLowerCase()}.svg`,
        }
      : user.role === "user"
      ? {
          text: "Public",
          value: "Public",
          icon: "/images/post/fill-public.svg",
        }
      : PostVisibility[0]
  );

  useEffect(() => {
    if (isEdit) {
      setVisibility({
        text: initial_visibility,
        value: initial_visibility,
        icon:
          initial_visibility === "OnlyMe"
            ? "/images/post/fill-private.svg"
            : `/images/post/fill-${initial_visibility?.toLowerCase()}.svg`,
      });
    }
  }, [initial_visibility]);

  useEffect(() => {
    if (isEdit) {
      if (scheduled_date) {
        setIsScheduled(true);
        const date = format(new Date(scheduled_date), "yyyy-MM-dd'T'HH:mm");
        setScheduleDateTime(date);
      }

      if (expiry_date) {
        setHasExpiry(true);
        const date = format(new Date(expiry_date), "yyyy-MM-dd'T'HH:mm");
        setExpiryTime(date);
      }
    }
  }, [scheduled_date, expiry_date]);

  useEffect(() => {
    if (visibility.value === "Premium") {
      setDisabled({
        ...disabled,
        ...(DisableMap.premium || {}),
      });
    } else {
      setDisabled({
        ...disabled,
        ...(EnableMap.premium || {}),
      });
    }
  }, [visibility]);

  useEffect(() => {
    setTimeout(() => {
      if (visibility.value === "Subscription") {
        setDisabled({
          ...disabled,
          ...(DisableMap.subscription || {}),
        });
        setTimeout(() => {
          setCollabUsers([]);
        }, 2000);
      } else if (visibility.value !== "Premium") {
        setDisabled({
          ...disabled,
          ...(EnableMap.subscription || {}),
        });
      }
    }, 200);
  }, [visibility]);
  useEffect(() => {
    if (isChannel) {
      setShareToChannels([channelprofile?._id]);
      setShareOnProfile(false);
      setPostingAsSubs(true);
    } else if (isGroup) {
      setShareToGroups([groupData?._id]);
      setShareOnProfile(false);
      setPostingAsSubs(true);
    }
  }, [isChannel, isGroup]);

  const setPostType = (value: object) => {
    Object.entries(value).forEach(([key, value]) => {
      setPostOf({
        ...postOf,
        [key]: value,
      });

      setDisabled({
        ...disabled,
        ...(DisableMap[key] || {}),
      });
    });
  };

  const [btnDisable, setBtnDisable] = useState<boolean>();

  useEffect(() => {
    if (!(shareToChannels.length || shareToGroups.length || shareOnProfile)) {
      setSubmitPost(false);
    } else if (postSettings) {
      setSubmitPost(true);
    }
  }, [shareToChannels, shareToGroups, shareOnProfile, visibility.value]);

  useEffect(() => {
    const feelingValues = Object.values(FeelingMap);

    if (feeling.text == "@!~") {
      setSelectedFeeling("silly");
    }

    // FIXME: handle all ts-expect-error
    //@ts-expect-error to be handle later
    if (feelingValues.includes(feeling.text)) {
      setSelectedFeeling(feeling.text);
    }
  }, [feeling]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const selectedParam = params.get("type");

    const handleMobileModal = () => {
      if (window.innerWidth < 767) {
        setTimeout(() => {
          setPostType({
            [selectedParam!]: true,
          });

          if (selectedParam == "tag") {
            openTagModal("collab");
          } else if (selectedParam == "feeling") {
            feelingModalRef.method.open();
          } else if (selectedParam == "promote") {
            promoteModalRef.method.open();
          } else if (selectedParam == "media") {
            if (fileInputRef.current) {
              fileInputRef.current.click();
            }
          }
        }, 200);
      }
    };

    handleMobileModal();

    // window.addEventListener("resize", handleMobileModal);
    // return () => window.removeEventListener("resize", handleMobileModal);
  }, []);

  useEffect(() => {
    dispatchAction(userDataActions.setPostCaption(""));
    dispatchAction(userDataActions.setPostPoll(pollInitState));
    dispatchAction(userDataActions.setHashtagsArray([]));
    dispatchAction(
      userDataActions.setPostBackground({
        ...bg,
        backgroundColor: "",
        textColor: "",
      })
    );
  }, []);

  // removing the Go up widget from create post page.
  useEffect(() => {
    dispatchAction(configActions.toggleShowPositionWidget());
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowPositionWidget());
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);
  const ErrorToast = Swal.mixin({
    toast: true,
    position: "top-end",

    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
  });

  const fileUpload = async (event: any) => {
    const files = event.target.files;

    const validImages: File[] = [];
    const validVideos: File[] = [];
    const imageErrors: string[] = [];
    const videoErrors: string[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (file) {
        if (file.type.startsWith("image/")) {
          if (file.size <= IMAGE_SIZE_MAX) {
            validImages.push(file);
          } else {
            imageErrors.push(file.name);
            ErrorToast.fire({
              icon: "error",
              title: `The maximum allowed image file size is 40MB. File: ${file.name}`,
            });
          }
        } else if (file.type.startsWith("video/")) {
          try {
            const isValidVideo = true;

            if (isValidVideo) {
              validVideos.push(file);
            } else {
              videoErrors.push(file.name);
            }
          } catch (err) {
            videoErrors.push(file.name);
            console.error("Error checking video validity:", err);
          }
        }
      }
    }

    if (validImages.length > 0) {
      setPostImg((prevPostImg) => [...prevPostImg, ...validImages]);

      setPostImgUrls((prevPostImg) => [
        ...prevPostImg,
        ...validImages.map((f) => URL.createObjectURL(f)),
      ]);
      setSubmitPost(true);
      setPostType({ media: true });
    }

    setTimeout(() => {
      if (validVideos.length > 0) {
        setPostVid((prevPostVid) => [...prevPostVid, ...validVideos]);
        setSubmitPost(true);
        setPostVidUrls((prevPostVid) => [
          ...prevPostVid,
          ...validVideos.map((f) => URL.createObjectURL(f)),
        ]);

        setVideoThumbnail((prevPostVid) => [
          ...prevPostVid,
          ...validVideos.map(() => ""),
        ]);
        setThumbnailUrl((prevPostVid) => [
          ...prevPostVid,
          ...validVideos.map(() => ""),
        ]);

        setPostType({ media: true });
      }
    }, 1000);

    setIsAuthorPresent((prev) => [
      ...prev,
      ...Array(validImages.length + validVideos.length).fill(true),
    ]);

    if (imageErrors.length > 0 || videoErrors.length > 0) {
      ErrorToast.fire({
        icon: "error",
        title: `Some files were not uploaded due to size or duration limitations.`,
      });
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  useEffect(() => {
    if (postImg.length || postVid.length) {
      setSubmitPost(true);
    } else {
      setPostType({ media: false });
      setSubmitPost(false);
      setDisabled({
        ...disabled,
        ...(EnableMap?.media || {}),
      });
    }
  }, [postImg, postVid]);
  useEffect(() => {
    if (vaultImages.length || vaultVideos.length) {
      setSubmitPost(true);
    } else {
      setPostType({ vault: false });
      setSubmitPost(false);
      setDisabled({
        ...disabled,
        ...(EnableMap?.vault || {}),
      });
    }
  }, [vaultImages, vaultVideos]);

  const Button = ({
    name,
    label,
    content,
    onClick,
    id,
  }: {
    name: keyof Post;
    label: string;
    content?: ReactNode;
    onClick?: Function;
    id?: string;
  }) => {
    if (!ViewMap[user?.role][name]) {
      return <></>;
    }

    return (
      <div className="post-type-btn-container position-relative col-lg-5 p-0 flex-grow-0 flex-md-grow-1 flex-lg-grow-1 min-vw-25 ">
        <button
          className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-btn rounded-3 shadow-dark hover-active-hollow w-100 color-bold py-3 py-md-2 py-lg-2  ${
            postOf[name] == true && "active-post-btn"
          }`}
          onClick={(evt) => {
            if (name !== "vault") {
              setPostType({
                [name]: true,
              });
            }

            onClick?.(evt);
          }}
          disabled={disabled[name]}
          id={id}
        >
          <Image
            width={28}
            height={28}
            src={`/images/svg/${SvgMap[name]}.svg`}
            alt={name}
            className={`d-none d-lg-block d-md-block ${
              name === "vault" || name === "tag" ? "svg-icon" : ""
            }`}
          />
          <Image
            width={40}
            height={40}
            src={`/images/svg/${SvgMap[name]}.svg`}
            alt={name}
            className={`d-lg-none d-md-none ${
              name === "vault" || name === "tag" ? "svg-icon" : ""
            }`}
          />
          <span className="text-overflow-ellipsis fw-medium d-none d-lg-block d-md-block">
            {label}
          </span>
          <span className="text-overflow-ellipsis mt-1  fw-medium d-lg-none d-md-none">
            {label}
          </span>
        </button>
        {content}
      </div>
    );
  };

  const PostTypeSelection = () => {
    const inputRef = useRef(null);

    // ?: Following useEffect is important because synthetic event (onchange) was not calling when caption change and then when try to upload file.
    useEffect(() => {
      // @ts-expect-error sss
      if (inputRef.current) inputRef.current.onchange = fileUpload;
    }, [caption]);

    {
      return (
        <>
          <div className="post-type-selection d-flex flex-column gap-3 bg-body p-3 rounded-3 ">
            <div className="post-selection-title h5 d-none d-md-block fw-semibold">
              Add to your post
            </div>
            <div className="post-type-options d-flex flex-wrap gap-3   justify-content-around justify-content-sm-between justify-content-md-start  ">
              <Button
                name="media"
                label="Photo or Video"
                id={btnHover}
                content={
                  <input
                    type="file"
                    className={`media-input position-absolute top-0 bottom-0 start-0 end-0 opacity-0 z-3  ${
                      !disabled["media"] ? "pointer" : "cursor-not-allowed"
                    }`}
                    ref={inputRef}
                    accept="image/*,video/*,.mkv"
                    disabled={disabled["media"]}
                    multiple={true}
                  />
                }
              />
              <Button
                name="vault"
                label="Vault"
                onClick={() => openVaultSelector()}
              />
              <Button name="poll" label="Poll" />

              <Button name="background" label="Background" />

              <Button
                name="tag"
                label="Collab"
                onClick={() => openTagModal("collab")}
              />

              <Button
                name="feeling"
                label="Feeling"
                onClick={() => {
                  feelingModalRef.method.open();
                }}
              />

              <Button
                name="promote"
                label="Promote Item"
                onClick={() => {
                  promoteModalRef.method.open();
                }}
              />
              {/* <Button
                name="tagAndRelease"
                label="Tag & Release"
                onClick={() => openTagModal("tag")}
              /> */}
            </div>
          </div>

          {user.role === "user" && <UserWarning />}
          {user.role === "creator" && !isKycCompleted && (
            <KYCwarning type="posts" />
          )}
        </>
      );
    }
  };

  const mediaFiles = [...postImg, postVid];

  const extractText = (htmlString: string): string => {
    if (typeof window === "undefined") return ""; // Server-side safety
    const doc = new DOMParser().parseFromString(htmlString, "text/html");
    return doc.body.textContent || "";
  };

  const openVaultSelector = () => {
    window.KNKY.openVault({ readonly: true }).then((res) => {
      console.log({ res });

      if (
        res.medias[0]?.importedMediaPostData?.description?.length ||
        res.medias[0]?.importedMediaPostData?.tags?.length
      ) {
        const caption = extractText(
          res.medias[0]?.importedMediaPostData?.description
        );
        const tags = res.medias[0]?.importedMediaPostData?.tags;

        if (tags?.length) {
          const formattedTags = tags
            .map(
              (tag: string) =>
                `@[#${tag.replace(/\s+/g, "")}](#${tag.replace(/\s+/g, "")})`
            )
            .join(" ");

          dispatchAction(
            userDataActions.setPostCaption(
              `${stateCaption} ${caption} ${formattedTags} `
            )
          );
        } else {
          dispatchAction(
            userDataActions.setPostCaption(`${stateCaption} ${caption} `)
          );
        }
      } else if (!res.medias[0]?.importedMediaPostData) {
        // Check all medias for tags and collect them
        const allTags: string[] = [];
        res.medias.forEach((media: any) => {
          if (media?.tags?.length) {
            allTags.push(...media.tags);
          }
        });

        if (allTags.length) {
          const formattedTags = allTags
            .map(
              (tag: string) =>
                `@[#${tag.replace(/\s+/g, "")}](#${tag.replace(/\s+/g, "")})`
            )
            .join(" ");
          dispatchAction(
            userDataActions.setPostCaption(`${stateCaption} ${formattedTags} `)
          );
        }
      }

      // @ts-expect-error Argument of type '(media: Media) => void' is not assignable to parameter of type '(value: VFile, index: number, array: VFile[]) => void'.
      res.medias.map((media: Media) => {
        if (media.type === "image") {
          setVaultImages((prevImgs) => [...prevImgs, media]);
          setPostType({ vault: true });
        } else if (media.type === "video") {
          setVaultVideos((prevVideo) => [...prevVideo, media]);

          setVaultVideoThumbnail((prevPostVid) => [
            ...prevPostVid,
            ...vaultVideos.map(() => ""),
            setPostType({ vault: true }),
          ]);
        }

        if (media.consent?.external_tag_consent?.length) {
          setReleaseUsers((prevUsers) => [
            ...prevUsers,
            ...(media.consent?.external_tag_consent ?? []),
          ]);
        }
      });

      res.medias.map((media: VFile) => {
        console.log({ media });
        const temp = media?.collaborators?.map((u: Collaborator) => {
          return {
            ...u.user,
            percentage: u.percentage || 0,
          };
        });

        if (temp) {
          setCollabUsers(temp);
        }
      });
    });
  };

  const postContentProps = {
    videoThumbnail,
    setVideoThumbnail,
    preview,
    thumbnailUrl,
    setThumbnailUrl,
    postImg,
    setPostImg,
    postImgUrls,
    setPostImgUrls,
    postVid,
    setPostVid,
    postVidUrls,
    setPostVidUrls,
    vaultImages,
    setVaultImages,
    shareOnProfile,
    setShareOnProfile,
    shareToChannels,
    setShareToChannels,
    shareToGroups,
    setShareToGroups,
    vaultVideos,
    setVaultVideos,
    vaultVideoThumbnail,
    setVaultVideoThumbnail,
    croppedPostUrl,
    setCroppedPostUrl,
    tagUsers,
    setTagUsers,
    tagConsent,
    setTagConsent,
    collabUsers,
    setCollabUsers,
    releaseUsers,
    setReleaseUsers,
    mediaLocation,
    setMediaLocation,
    mediaDOP,
    setMediaDOP,
    mediaTitle,
    setMediaTitle,
    isAuthorPresent,
    setIsAuthorPresent,
    feeling,
    setFeeling,
    postingAsSubs,
    setPostingAsSubs,

    setCaption,
    bg,
    setBg,
    poll,
    setPoll,
    caption,
    shopItems,
    postSettings,
    setPostSettings,
    setPostType,
    setSubmitPost,
    setDisabled,
    setPostImgData,
    postOf,
    setPostOf,
    disabled,
    visibility,
    setVisibility,
    isEdit,
    setBtnDisable,
    openTagModal,
    editPostMedia,
  };

  const isSubscriptionVisibility: boolean =
    shareToChannels.length > 0 || shareToGroups.length > 0;

  // to set the shared entities
  const postSettingsProps = {
    visibility,
    setVisibility,
    shareToChannels,
    setShareToChannels,
    shareToGroups,
    setShareToGroups,
    shareToStory,
    setShareToStory,
    shareOnProfile,
    setShareOnProfile,
    isGroup,
    isChannel,
    isEdit,
    bg,
    mediaFiles,
    postOf,
    postSettings,
    tagUsers,
    tagConsent,
    collabUsers,
    releaseUsers,
    setReleaseUsers,
    setTagConsent,
    setPostType,
    setPostSettings,
    setScheduleDateTime,
    setIsScheduled,
    setHasExpiry,
    setExpiryTime,
    isScheduled,
    hasExpiry,
    scheduleDateTime,
    expiryTime,
    isSubscriptionVisibility,
    postingAsSubs,
  };

  const PostOptionsMobile = ({ setChildRef }: ModalImportProps) => {
    return (
      <Modal
        title="Create new post"
        subtitle={[""]}
        setChildRef={setChildRef}
        render={"direct"}
        cls="modal-fullscreen"
      >
        <PostTypeSelection />
      </Modal>
    );
  };

  useEffect(() => {
    if (feeling.emoji !== "") {
      setPostType({ feeling: true });
    }
  }, [feeling.emoji]);

  useEffect(() => {
    if (shopItemId.length) {
      setPostType({ promote: true });
    }
  }, [shopItemId]);

  useEffect(() => {
    if (collabUsers.length) {
      setPostType({ tag: true });
    }
  }, [collabUsers]);
  useEffect(() => {
    if (tagConsent.length || releaseUsers.length) {
      setPostType({ tagAndRelease: true });
    }
  }, [tagConsent, releaseUsers]);

  const [submitProgress, setSubmitProgress] = useState<number>(0);

  const onProgress = (id: string, progress: number) => {
    setSubmitProgress(progress);
    dispatchAction(createActions.updateProgress({ id, progress }));
  };

  const isExternalLinkInclude = useAppSelector(
    (s) => s.userData?.isExternalLinkInclude
  );
  const stateCaption = useAppSelector((s) => s.userData.post.caption);

  const validCaption = !isExternalLinkInclude && stateCaption.length <= 1020;

  function extractHashtags(input: string): string[] {
    const matches = [...input.matchAll(/@\[#(.*?)\]\(#\1\)/g)];
    return matches.map((match) => `#${match[1]}`);
  }

  function formatMentions(input: string): string {
    const cleanedInput = input.replace(/[\u200B-\u200D\uFEFF]/g, "");

    return cleanedInput.replace(/@\[(#|@)(.*?)\]\([^)]*\)/g, "$1$2");
  }

  return (
    <>
      <PostNavbar
        btnIsValid={isEdit ? isEdit : donePost && validCaption}
        btnIsDirty={isEdit ? isEdit : donePost && validCaption}
        showBtn={true}
        btnDisable={btnDisable}
        submitPost={
          isEdit
            ? () => {
                const tempCaption = formatMentions(
                  store.getState().userData.post.caption
                );
                const tempHashTags = extractHashtags(
                  store.getState().userData.post.caption
                );

                setPostSettings(true);

                // setLoading(true);
                if (postSettings) {
                  setBtnDisable(true);
                  const editBody: any = {
                    caption: tempCaption,
                    hashtags: tempHashTags || [],
                  };

                  if (visibility.value !== "Subscription") {
                    editBody.visibility = visibility.value;
                  }

                  if (isScheduled) {
                    editBody.scheduled_on = new Date(
                      scheduleDateTime
                    ).toISOString();
                  }

                  if (hasExpiry) {
                    editBody.expired_on = new Date(expiryTime).toISOString();
                  }

                  EditPost(postId, editBody)
                    .then(() => {
                      setBtnDisable(false);
                      navigateBack();
                      toast.success("Post Edited Successfully!", {
                        onAutoClose: (t) => {
                          dispatchAction(
                            userDataActions.setInitialVisibility(undefined)
                          );
                          dispatchAction(
                            userDataActions.setScheduleDateTime("")
                          );
                          dispatchAction(userDataActions.setExpireDateTime(""));
                          dispatchAction(userDataActions.setPostCaption(""));
                          dispatchAction(userDataActions.setHashtagsArray([]));
                        },
                      });
                    })
                    .catch((err) => {
                      setLoading(false);
                      dispatchAction(
                        userDataActions.setInitialVisibility(undefined)
                      );
                      setBtnDisable(false);
                      dispatchAction(
                        userDataActions.setInitialVisibility(undefined)
                      );

                      dispatchAction(userDataActions.setPostCaption(""));
                      dispatchAction(userDataActions.setHashtagsArray([]));

                      Swal.fire({
                        icon: "error",
                        title:
                          err.message === "banned tags are not allowed"
                            ? `Oops! Please avoid using the word "${err.data}"`
                            : err.message,
                        confirmButtonText: "Finish",
                        confirmButtonColor: "#AC1991",
                        customClass: { htmlContainer: "custom-swal-popup" },
                        showCloseButton: true,
                      }).then(() => {
                        // navigateBack();
                      });
                    });

                  // const tagAndReleaseBody = editPostMedia.map(
                  //   (media, index) => {
                  //     const internalTagConsent = tagConsent[index]
                  //       ?.filter((user: any) => user.status === "")
                  //       .map((user: any) => user.user._id);

                  //     const externalTagConsent = releaseUsers[index]
                  //       ?.filter((user: any) => user.status === "")
                  //       .map((user: any) => user.email);

                  //     if (
                  //       (internalTagConsent && internalTagConsent.length > 0) ||
                  //       (externalTagConsent && externalTagConsent.length > 0)
                  //     ) {
                  //       return {
                  //         ...(internalTagConsent?.length > 0 && {
                  //           internal_tag_consent: internalTagConsent,
                  //         }),
                  //         ...(externalTagConsent?.length > 0 && {
                  //           external_tag_consent: externalTagConsent,
                  //         }),
                  //         media_id: media._id,

                  //         ...(mediaDOP[index] && { date: mediaDOP[index] }),
                  //         ...(!mediaLocation[index].includes("undefined") && {
                  //           location: mediaLocation[index],
                  //         }),
                  //         is_author_present: isAuthorPresent[index],
                  //       };
                  //     } else if (isAuthorPresent[index]) {
                  //       return {
                  //         media_id: media._id,
                  //         is_author_present: isAuthorPresent[index],
                  //       };
                  //     }

                  //     return null;
                  //   }
                  // );
                  // const body = {
                  //   media_consent_tag: tagAndReleaseBody.filter(
                  //     (item) => item !== null
                  //   ),
                  // };

                  // if (tagAndReleaseBody.length) {
                  //   AddConsentTags(body).then(() => {
                  //     toast.success("Tags Updated Successfully!");
                  //   });
                  // }
                }
              }
            : postSettings
            ? !shareToStory
              ? async () => {
                  setBtnDisable(true);
                  const premiumValue =
                    store.getState().userData.post.premiumValue;
                  // setLoading(true);
                  navigateBack();

                  try {
                    let mediaInfo: MediaInfo[] | null = [];
                    let videoThumbnailInfo: MediaInfo[] | null = [];
                    let previewInfo: MediaInfo[] | null = [];
                    const mediaArray = [...postImg, ...postVid];

                    if (mediaArray.length) {
                      for (let i = 0; i < mediaArray.length; i++) {
                        const res = await uploadFile(mediaArray[i], "Post");
                        console.log({ res });

                        mediaInfo.push({
                          path: (res as any)?.[0]?.s3Multipart?.key,
                          type: res?.[0]?.type.split("/")[0] as
                            | "image"
                            | "video",
                          original_name: res?.[0]?.name,
                          // internal_tag_consent: tagConsent?.[i]?.map(
                          //   (user: any) => user.user._id
                          // ),
                          // external_tag_consent: releaseUsers?.[i]?.map(
                          //   (user: any) => user.email
                          // ),
                          // is_author_present: isAuthorPresent?.[i],
                          // location: mediaLocation?.[i]?.includes("undefined")
                          //   ? undefined
                          //   : mediaLocation?.[i],
                          // date: mediaDOP?.[i] || undefined,
                          // title: mediaTitle?.[i] || "",
                        });
                      }
                    }

                    if (videoThumbnail.some((item) => item instanceof File)) {
                      for (let i = 0; i < videoThumbnail.length; i++) {
                        const file = videoThumbnail[i];

                        if (file instanceof File) {
                          const res = await uploadFile(
                            videoThumbnail[i],
                            "Post"
                          );

                          videoThumbnailInfo.push({
                            path: (res as any)?.[0]?.s3Multipart?.key,
                            type: res?.[0]?.type.split("/")[0] as
                              | "image"
                              | "video",
                          });
                        }
                      }
                    }

                    if (preview) {
                      const res = await uploadFile(preview, "Post");

                      previewInfo.push({
                        path: (res as any)?.[0]?.s3Multipart?.key,
                        type: res?.[0]?.type.split("/")[0] as "image" | "video",
                      });
                    }

                    const tempCaption = formatMentions(
                      store.getState().userData.post.caption
                    );
                    const tempHashTags = extractHashtags(
                      store.getState().userData.post.caption
                    );
                    const payload: PostSubmitBody = {
                      caption: tempCaption,
                      hashtags: tempHashTags,
                      backgroundColor: bg.backgroundColor,
                      textColor: bg.textColor,
                      poll: poll,
                      feeling: feeling.text,
                      tagged_users: [
                        ...(caption
                          ?.match(/\u200D@\w+/g)
                          ?.map((u) => u.slice(2)) || []),
                        ...tagUsers.map((user: any) => user.username),
                      ],
                      tag_consent: tagConsent.map((user: any) => ({
                        username: user.username,
                      })),
                      tagged_channels: [
                        ...(caption
                          .match(/\u200B@\w+/g)
                          ?.map((c) => c.slice(2)) || []),
                      ],
                      tagged_groups: [
                        ...(caption
                          .match(/\u200C@\w+/g)
                          ?.map((c) => c.slice(2)) || []),
                      ],
                      collaborators: collabUsers.map((user: any) => {
                        return {
                          username: user.username,
                          percentage: user.percentage,
                        };
                      }),
                      release_form_users: releaseUsers.map((user) => user._id),
                      media: !postOf.background ? mediaInfo : postImgData,
                      poster: videoThumbnailInfo,
                      preview: previewInfo,
                      video_poster_mapper: videoThumbnail.map((thumbnail) =>
                        thumbnail ? 1 : 0
                      ),

                      tip_setting: {
                        is_enabled: false,
                        minimum: 0,
                      },
                      share_on_story: !shareToStory,
                      share_on_profile:
                        visibility.text === "Subscribers"
                          ? false
                          : shareOnProfile,
                      type: postOf.poll
                        ? "Poll"
                        : shopItemId[0]
                        ? "Product"
                        : "Normal",
                      // @ts-expect-error skdjakd
                      visibility: visibility.value!,
                      channels: shareToChannels,
                      groups: shareToGroups,
                      pay_and_watch_rate: premiumValue,
                      products: shopItemId,
                      isGroup: isGroup || false,
                      isChannel: isChannel || false,
                      scheduled_on:
                        (isScheduled &&
                          new Date(scheduleDateTime).toISOString()) ||
                        "",
                      expired_on:
                        (hasExpiry && new Date(expiryTime).toISOString()) || "",
                      vault_media_ids: [
                        ...vaultImages.map((item) => item._id),
                        ...vaultVideos.map((item) => item._id),
                      ],
                      vault_poster_ids: Array.isArray(vaultVideoThumbnail)
                        ? vaultVideoThumbnail
                            .filter(
                              (item): item is { _id: string } =>
                                typeof item === "object" &&
                                item !== null &&
                                "_id" in item
                            ) // Keep only valid media objects
                            .map((media) => media._id)
                        : [],
                      video_vault_poster_mapper: vaultVideoThumbnail.map(
                        (thumbnail) => (thumbnail ? 1 : 0)
                      ),
                      vault_preview_ids: vaultPreview?._id
                        ? [vaultPreview?._id!]
                        : [],
                    };
                    await PostSubmit(payload, onProgress)
                      .then(() => {
                        setBtnDisable(false);

                        toast.success("Post and story uploaded successfully!");
                        dispatchAction(userDataActions.setPostCaption(""));
                        dispatchAction(
                          userDataActions.setPostPoll(pollInitState)
                        );
                        dispatchAction(
                          userDataActions.setPostBackground({
                            ...bg,
                            backgroundColor: "",
                            textColor: "",
                          })
                        );
                        dispatchAction(userDataActions.setHashtagsArray([]));
                        dispatchAction(
                          userDataActions.setInitialVisibility(undefined)
                        );
                        dispatchAction(userDataActions.setPremiumValue(1));
                      })
                      .catch((err) => {
                        console.log("ERROR: ", err);
                        setBtnDisable(false);
                        setLoading(false);

                        dispatchAction(userDataActions.setPostCaption(""));
                        dispatchAction(userDataActions.setHashtagsArray([]));
                        dispatchAction(
                          userDataActions.setPostPoll(pollInitState)
                        );
                        dispatchAction(
                          userDataActions.setPostBackground({
                            ...bg,
                            backgroundColor: "",
                            textColor: "",
                          })
                        );
                        dispatchAction(
                          userDataActions.setInitialVisibility(undefined)
                        );
                        dispatchAction(userDataActions.setPremiumValue(1));
                        Swal.fire({
                          icon: "error",
                          title:
                            err.message === "banned tags are not allowed"
                              ? `Oops! Please avoid using the word "${err.data}"`
                              : err.message,
                          confirmButtonText: "Finish",
                          confirmButtonColor: "#AC1991",
                          customClass: { htmlContainer: "custom-swal-popup" },

                          showCloseButton: true,
                        });
                      });
                  } catch (error) {
                    console.error("Error in post submission flow:", error);
                  }
                }
              : async () => {
                  const premiumValue =
                    store.getState().userData.post.premiumValue;

                  // setLoading(true);
                  setBtnDisable(true);
                  navigateBack();

                  try {
                    let mediaInfo: MediaInfo[] | null = [];
                    const mediaArray = [...postImg, ...postVid];
                    let videoThumbnailInfo: MediaInfo[] | null = [];
                    let previewInfo: MediaInfo[] | null = [];

                    if (mediaArray.length) {
                      for (let i = 0; i < mediaArray.length; i++) {
                        const res = await uploadFile(mediaArray[i], "Post");
                        console.log({ res });
                        mediaInfo.push({
                          path: (res as any)?.[0]?.s3Multipart?.key,
                          type: res?.[0]?.type.split("/")[0] as
                            | "image"
                            | "video",
                          original_name: res?.[0]?.name,
                          // internal_tag_consent: tagConsent?.[i]?.map(
                          //   (user: any) => user.user._id
                          // ),
                          // external_tag_consent: releaseUsers?.[i]?.map(
                          //   (user: any) => user.email
                          // ),
                          // location: mediaLocation?.[i]?.includes("undefined")
                          //   ? undefined
                          //   : mediaLocation?.[i],
                          // date: mediaDOP?.[i] || undefined,
                          // is_author_present: isAuthorPresent?.[i],
                          // title: mediaTitle?.[i] || "",
                        });
                      }
                    }

                    if (videoThumbnail.some((item) => item instanceof File)) {
                      for (let i = 0; i < videoThumbnail.length; i++) {
                        const file = videoThumbnail[i];

                        if (file instanceof File) {
                          const res = await uploadFile(
                            videoThumbnail[i],
                            "Post"
                          );

                          videoThumbnailInfo.push({
                            path: (res as any)?.[0]?.s3Multipart?.key,
                            type: res?.[0]?.type.split("/")[0] as
                              | "image"
                              | "video",
                          });
                        }
                      }
                    }

                    if (preview) {
                      const res = await uploadFile(preview, "Post");

                      previewInfo.push({
                        path: (res as any)?.[0]?.s3Multipart?.key,
                        type: res?.[0]?.type.split("/")[0] as "image" | "video",
                      });
                    }

                    const tempCaption = formatMentions(
                      store.getState().userData.post.caption
                    );
                    const tempHashTags = extractHashtags(
                      store.getState().userData.post.caption
                    );
                    await PostSubmit(
                      {
                        caption: tempCaption,
                        hashtags: tempHashTags,
                        backgroundColor: bg.backgroundColor,
                        textColor: bg.textColor,
                        poll: poll,
                        feeling:
                          feeling.text === "@!~" ? "silly" : feeling.text,
                        tagged_users: [
                          ...(caption
                            ?.match(/\u200D@\w+/g)
                            ?.map((u) => u.slice(2)) || []),
                          ...tagUsers.map((user: any) => user.username),
                        ],
                        tag_consent: tagConsent?.map((user: any) => ({
                          username: user.username,
                        })),
                        tagged_channels: [
                          ...(caption
                            .match(/\u200B@\w+/g)
                            ?.map((c) => c.slice(2)) || []),
                        ],
                        tagged_groups: [
                          ...(caption
                            .match(/\u200C@\w+/g)
                            ?.map((c) => c.slice(2)) || []),
                        ],
                        collaborators: collabUsers.map((user: any) => {
                          return {
                            username: user.username,
                            percentage: user.percentage,
                          };
                        }),
                        release_form_users: releaseUsers?.map(
                          (user) => user._id
                        ),
                        media: !postOf.background ? mediaInfo : postImgData,
                        poster: videoThumbnailInfo,
                        preview: previewInfo,
                        video_poster_mapper: videoThumbnail.map((thumbnail) =>
                          thumbnail ? 1 : 0
                        ),
                        tip_setting: {
                          is_enabled: false,
                          minimum: 0,
                        },
                        share_on_story: !shareToStory,
                        share_on_profile:
                          visibility.text === "Subscribers"
                            ? false
                            : shareOnProfile,
                        type: postOf.poll
                          ? "Poll"
                          : shopItemId[0]
                          ? "Product"
                          : "Normal",
                        // @ts-expect-error jdns
                        visibility: visibility.value!,
                        channels: shareToChannels,
                        groups: shareToGroups,
                        pay_and_watch_rate: premiumValue,
                        products: shopItemId,
                        isChannel: isChannel,
                        isGroup: isGroup,
                        vault_media_ids: [
                          ...vaultImages.map((item) => item._id),
                          ...vaultVideos.map((item) => item._id),
                        ],
                        vault_poster_ids: Array.isArray(vaultVideoThumbnail)
                          ? vaultVideoThumbnail
                              .filter(
                                (item): item is { _id: string } =>
                                  typeof item === "object" &&
                                  item !== null &&
                                  "_id" in item
                              ) // Keep only valid media objects
                              .map((media) => media._id)
                          : [],
                        video_vault_poster_mapper: vaultVideoThumbnail.map(
                          (thumbnail) => (thumbnail ? 1 : 0)
                        ),
                        vault_preview_ids: vaultPreview?._id
                          ? [vaultPreview?._id!]
                          : [],

                        scheduled_on:
                          (isScheduled &&
                            new Date(scheduleDateTime).toISOString()) ||
                          "",
                        expired_on:
                          (hasExpiry && new Date(expiryTime).toISOString()) ||
                          "",
                      },
                      onProgress
                    )
                      .then(() => {
                        setBtnDisable(false);

                        toast.success("Post Submitted Successfully!");
                        dispatchAction(userDataActions.setPostCaption(""));
                        dispatchAction(
                          userDataActions.setPostPoll(pollInitState)
                        );
                        dispatchAction(
                          userDataActions.setPostBackground({
                            ...bg,
                            backgroundColor: "",
                            textColor: "",
                          })
                        );
                        dispatchAction(userDataActions.setHashtagsArray([]));
                        dispatchAction(
                          userDataActions.setInitialVisibility(undefined)
                        );
                        dispatchAction(userDataActions.setPremiumValue(1));
                      })
                      .catch((err) => {
                        setBtnDisable(false);
                        setLoading(false);
                        console.log("ERRORRRR: ", err);
                        // dispatchAction(userDataActions.setPostCaption(""));
                        dispatchAction(userDataActions.setHashtagsArray([]));
                        dispatchAction(
                          userDataActions.setPostPoll(pollInitState)
                        );
                        dispatchAction(
                          userDataActions.setPostBackground({
                            ...bg,
                            backgroundColor: "",
                            textColor: "",
                          })
                        );
                        dispatchAction(
                          userDataActions.setInitialVisibility(undefined)
                        );
                        dispatchAction(userDataActions.setPremiumValue(1));

                        Swal.fire({
                          icon: "error",
                          title:
                            err.message === "Banned word/words detected."
                              ? `Oops! Please avoid using the word "${err.data.banned_words.join(
                                  ", "
                                )}"`
                              : err.message,
                          confirmButtonText: "Finish",
                          confirmButtonColor: "#AC1991",
                          customClass: { htmlContainer: "custom-swal-popup" },

                          showCloseButton: true,
                        });
                      });
                  } catch (error) {
                    console.error("Error in post submission flow:", error);
                  }
                }
            : () => {
                if (
                  (postOf.media || postOf.vault) &&
                  isMobile &&
                  !showPreviewComponent &&
                  (visibility.value === "Premium" ||
                    visibility.value === "Prime" ||
                    visibility.value === "Subscription")
                ) {
                  setShowPreviewComponent(true);
                } else {
                  setPostSettings(true);
                }
              }
        }
        buttonText={postSettings ? "Submit Post" : "Next"}
        text={isEdit ? "Edit Post" : "Create new post"}
        icon="/images/svg/nav-close.svg"
        postOf={postOf}
        isPremiumPost={visibility.value! === "Premium"}
      />

      <div
        className={`container-xxl  d-flex  py-3 gap-3 flex-column flex-md-row flex-lg-row create-post-container   mb-4 mb-md-0 ${
          isMobile && "flex-grow-1 overflow-x-hidden"
        }`}
      >
        {loading && (
          <div className="d-flex align-items-center justify-content-center flex-grow-1 position-fixed min-vh-100 top-0 min-vw-100 start-0 post-loader">
            {browser?.name === "iOS" ? (
              <Loader />
            ) : (
              <Spinner progress={submitProgress!} />
            )}
          </div>
        )}
        <div
          className={`post-type-wrapper col-12 col-md-4 col-lg-4 d-flex flex-column gap-3 flex-grow-1 ${
            postSettings ? "d-none" : ""
          }`}
        >
          {!isEdit && <PostTypeSelection />}
          {<PostVisibilitySelector props={postSettingsProps} />}

          <div
            className={`${
              (postOf.media || postOf.vault) &&
              (visibility.value === "Premium" ||
                visibility.value === "Prime" ||
                visibility.value === "Subscription")
                ? ""
                : "d-none"
            }`}
          >
            <ThumbnailPreview
              setPreview={setPreview}
              setVaultPreview={setVaultPreview}
              postOf={postOf}
            />
          </div>
        </div>
        <div
          className={`create-post-content flex-grow-1 bg-body rounded-0  rounded-md-3 px-2 py-2 ${
            isMobile &&
            user.role === "creator" &&
            (postSettings || showPreviewComponent) &&
            !(isGroup || isChannel) &&
            "d-none"
          } ${
            postSettings && !isMobile
              ? "col-12 col-md-4 col-lg-4"
              : "col-12 col-md-7 col-lg-7 post-content-height"
          }`}
          style={{ minHeight: "25rem" }}
        >
          <PostContent
            postCaption={(caption: string) => {
              setCaption(caption);

              setHashtags(caption.match(/#\w+/g) || []);
            }}
            props={postContentProps}
          />
        </div>
        {
          <div
            className={`post-content-settings col-12 col-md-7 col-lg-7 flex-grow-1 bg-body p-3 rounded-0 rounded-md-3  ${
              postSettings && user.role === "creator" ? "" : "d-none"
            } `}
          >
            <PostSettings props={postSettingsProps} />
          </div>
        }

        {/* //* doing display none and not conditionally rendering the Thumbnail element because removing the component from dom when conditions change will remove the local preview image which is initiate inside the thumbnail component. On directly passing the preview to show image will take time to load the image (because 'preview' is file and createObjectURL taking fraction of second to load)  */}
        <div
          className={`${
            showPreviewComponent && !postSettings ? " " : "d-none"
          }`}
        >
          <ThumbnailPreview
            setPreview={setPreview}
            setVaultPreview={setVaultPreview}
            postOf={postOf}
            setShowPreviewComponent={setShowPreviewComponent}
          />
        </div>

        <Feeling
          setChildRef={feelingModalRef}
          onclose={() => setPostType({ feeling: false })}
          showFeeling={(emoji: string, text: string) => {
            setFeeling({ emoji, text });
          }}
        />
        <TagCollab
          setChildRef={tagModalRef}
          collabUsers={collabUsers}
          setCollabUsers={setCollabUsers}
          releaseUsers={releaseUsers}
          setReleaseUsers={setReleaseUsers}
          tagConsent={tagConsent}
          setTagConsent={setTagConsent}
          type={tagModalType}
          onclose={() => {
            if (collabUsers.length === 0 && tagModalType === "collab") {
              setPostOf({ ...postOf, tag: false, collab: false });
            }

            if (
              releaseUsers.length === 0 &&
              tagConsent.length === 0 &&
              tagModalType === "tag"
            ) {
              setPostOf({ ...postOf, tagAndRelease: false });
            }
          }}
          showCollab={(value: any) => {
            setCollabUsers(value);
            const collaborators: string[] = value.map(
              (user: any) => user.username
            );
            setCollaborators(collaborators);
          }}
          showRelease={(value: ReleaseFormsResponse[]) => {
            setReleaseUsers(value);
          }}
          showTagConsent={(value: any[]) => {
            setTagConsent(value);
          }}
        />
        <Promote
          setChildRef={promoteModalRef}
          chosenItems={(shop: any) => {
            const productId: string[0] = shop._id;
            setShopItemId([productId]);
            setShopItems([shop]);
          }}
          onclose={() => {
            setPostType({ promote: false });
            setDisabled({
              ...disabled,
              ...(EnableMap.promote || {}),
            });
          }}
        />
        <PostOptionsMobile setChildRef={mobileOptionsModalRef} />
      </div>
      {!postSettings && !showPreviewComponent && !isEdit && (
        <div
          className={` d-flex align-items-center gap-2 post-footer  ps-3 bg-body pb-2 pt-1  mt-3 ${
            isStandAlone && browser && browser?.name === "iOS" ? "pb-3" : ""
          }`}
        >
          <span className="fw-medium " style={{ whiteSpace: "nowrap" }}>
            Add:
          </span>
          {user.role === "creator" && (
            <div className="d-flex overflow-scroll gap-2 py-2 px-1">
              <button
                className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-footer-btn hover-active-hollow py-1 w-100 color-bold   ${
                  postOf["media"] == true && "active-post-btn"
                }`}
                disabled={disabled["media"]}
                id={btnHover}
              >
                <Image
                  width={28}
                  height={28}
                  src={`/images/svg/${SvgMap["media"]}.svg`}
                  alt={"media"}
                  className="d-none d-lg-block d-md-block"
                />
                <Image
                  width={30}
                  height={30}
                  src={`/images/svg/${SvgMap["media"]}.svg`}
                  alt={"media"}
                  className="d-lg-none d-md-none"
                />
                <input
                  ref={fileInputRef}
                  type="file"
                  className="media-input position-absolute top-0 bottom-0 start-0 end-0 opacity-0 z-3  pointer"
                  accept="image/*,video/*,.mkv"
                  onChange={fileUpload}
                  disabled={disabled["media"]}
                  multiple={true}
                />
              </button>

              <button
                className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-footer-btn  hover-active-hollow w-100 color-bold py-1 py-md-2 py-lg-2  ${
                  postOf["vault"] == true && "active-post-btn"
                }`}
                onClick={() => {
                  openVaultSelector();
                }}
                disabled={disabled["vault"]}
              >
                <Image
                  width={28}
                  height={28}
                  src={`/images/svg/${SvgMap["vault"]}.svg`}
                  alt={"vault"}
                  className="d-none d-lg-block d-md-block svg-icon"
                />
                <Image
                  width={30}
                  height={30}
                  src={`/images/svg/${SvgMap["vault"]}.svg`}
                  alt={"vault"}
                  className="d-lg-none d-md-none svg-icon"
                />
              </button>

              <button
                className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-footer-btn  hover-active-hollow w-100 color-bold py-1 py-md-2 py-lg-2  ${
                  postOf["poll"] == true && "active-post-btn"
                }`}
                onClick={() => {
                  setPostType({
                    ["poll"]: true,
                  });
                }}
                disabled={disabled["poll"]}
              >
                <Image
                  width={28}
                  height={28}
                  src={`/images/svg/${SvgMap["poll"]}.svg`}
                  alt={"poll"}
                  className="d-none d-lg-block d-md-block"
                />
                <Image
                  width={30}
                  height={30}
                  src={`/images/svg/${SvgMap["poll"]}.svg`}
                  alt={"poll"}
                  className="d-lg-none d-md-none"
                />
              </button>

              <button
                className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-footer-btn  hover-active-hollow w-100 color-bold py-1 py-md-2 py-lg-2  ${
                  postOf["background"] == true && "active-post-btn"
                }`}
                onClick={() => {
                  setPostType({
                    ["background"]: true,
                  });
                }}
                disabled={disabled["background"]}
              >
                <Image
                  width={28}
                  height={28}
                  src={`/images/svg/${SvgMap["background"]}.svg`}
                  alt={"background"}
                  className="d-none d-lg-block d-md-block"
                />
                <Image
                  width={30}
                  height={30}
                  src={`/images/svg/${SvgMap["background"]}.svg`}
                  alt={"background"}
                  className="d-lg-none d-md-none"
                />
              </button>

              <button
                className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-footer-btn  hover-active-hollow w-100 color-bold py-1 py-md-2 py-lg-2  ${
                  postOf["tag"] == true && "active-post-btn"
                }`}
                onClick={() => {
                  setPostType({
                    ["tag"]: true,
                  });
                  openTagModal("collab");
                }}
                disabled={disabled["tag"]}
              >
                <Image
                  width={30}
                  height={30}
                  src={`/images/svg/${SvgMap["tag"]}.svg`}
                  alt={"tag"}
                  className="d-lg-none d-md-none svg-icon"
                />
              </button>

              <button
                className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-footer-btn  hover-active-hollow w-100 color-bold py-1 py-md-2 py-lg-2  ${
                  postOf["feeling"] == true && "active-post-btn"
                }`}
                onClick={() => {
                  setPostType({
                    ["feeling"]: true,
                  });
                  feelingModalRef.method.open();
                }}
                disabled={disabled["feeling"]}
              >
                <Image
                  width={30}
                  height={30}
                  src={`/images/svg/${SvgMap["feeling"]}.svg`}
                  alt={"feeling"}
                  className="d-lg-none d-md-none"
                />
              </button>

              <button
                className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-footer-btn  hover-active-hollow w-100 color-bold py-1 py-md-2 py-lg-2  ${
                  postOf["promote"] == true && "active-post-btn"
                }`}
                onClick={() => {
                  setPostType({
                    ["promote"]: true,
                  });
                  promoteModalRef.method.open();
                }}
                disabled={disabled["promote"]}
              >
                <Image
                  width={30}
                  height={30}
                  src={`/images/svg/${SvgMap["promote"]}.svg`}
                  alt={"promote"}
                  className="d-lg-none d-md-none"
                />
              </button>
            </div>
          )}
          {user.role === "user" && (
            <div className="d-flex overflow-scroll gap-2 py-2 px-1">
              <button
                className={`btn position-relative z-3 d-flex flex-column flex-lg-row flex-md-row align-items-center gap-2 post-type-footer-btn  hover-active-hollow w-100 color-bold py-1 py-md-2 py-lg-2  ${
                  postOf["poll"] == true && "active-post-btn"
                }`}
                onClick={() => {
                  setPostType({
                    ["poll"]: true,
                  });
                }}
                disabled={disabled["poll"]}
              >
                <Image
                  width={28}
                  height={28}
                  src={`/images/svg/${SvgMap["poll"]}.svg`}
                  alt={"poll"}
                  className="d-none d-lg-block d-md-block"
                />
                <Image
                  width={30}
                  height={30}
                  src={`/images/svg/${SvgMap["poll"]}.svg`}
                  alt={"poll"}
                  className="d-lg-none d-md-none"
                />
              </button>
            </div>
          )}
        </div>
      )}
    </>
  );
}
