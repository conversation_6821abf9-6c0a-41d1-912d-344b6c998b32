"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { GetUserMatchProfile } from "@/api/matchmaker/matchmaker.api";
import Loader from "@/components/common/loader/loader";
import LocationDeniedPage from "@/components/matchmaker/location-denied";
import {
  configActions,
  defaultActions,
  matchMakerActions,
  notificationActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Children } from "@/types/utils";

export default function HomeLayout({ children }: Children) {
  const user = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const router = useRouter();
  const [userExists, setUserExists] = useState<boolean>(false);
  const locationAllowed = useAppSelector(
    (state) => state.defaults.locationAllowed
  );
  const queryParam = useSearchParams();

  useEffect(() => {
    dispatch(configActions.toggleShowPositionWidget());
    dispatch(configActions.toggleShowCreateWidget());
    const type = queryParam.get("type");

    if (!type || (type !== "like" && type !== "you-like" && type !== "match")) {
      router.replace("/matchmaker/match?type=match");
    }

    return () => {
      dispatch(configActions.toggleShowPositionWidget());
      dispatch(configActions.toggleShowCreateWidget());

      dispatch(matchMakerActions.setShowProfile({}));
    };
  }, []);

  useEffect(() => {
    if (window.innerWidth > 992) dispatch(configActions.showFooter(false));

    return () => {
      dispatch(configActions.showFooter(true));
    };
  }, []);

  if (user?.role === "guest") {
    router.push("/");
  }

  if (user.role === "creator" && !user.isVerified) {
    router.push("/settings/kyc");
  }

  useEffect(() => {
    const checkGeolocationPermission = async () => {
      if (navigator.geolocation) {
        try {
          const permissionStatus = await navigator.permissions.query({
            name: "geolocation",
          });

          if (permissionStatus.state === "granted") {
            dispatch(defaultActions.setLocationAllowed(true));

            navigator.geolocation.getCurrentPosition(
              (position) => {
                sessionStorage.setItem(
                  "lat",
                  position.coords.latitude.toString()
                );
                sessionStorage.setItem(
                  "long",
                  position.coords.longitude.toString()
                );
              },
              (error) => {
                console.error("Error getting location:", error);
              }
            );
          } else if (permissionStatus.state === "denied") {
            // Permission denied
            dispatch(defaultActions.setLocationAllowed(false));
          } else if (permissionStatus.state === "prompt") {
            // Permission prompt
            dispatch(defaultActions.setLocationAllowed(false));

            navigator.geolocation.getCurrentPosition(
              (position) => {
                // User granted permission after prompt
                console.log("Location:", position.coords);
                dispatch(defaultActions.setLocationAllowed(true));
              },
              (error) => {
                console.error("Error getting location:", error);
                dispatch(defaultActions.setLocationAllowed(false));
              }
            );
          }
        } catch (error) {
          console.error("Error checking geolocation permission:", error);
          dispatch(defaultActions.setLocationAllowed(false));
        }
      } else {
        dispatch(defaultActions.setLocationAllowed(false));
      }
    };

    const fetchUserProfile = async () => {
      setLoading(true);

      try {
        const result = await GetUserMatchProfile();

        if (result && result.data && result.data[0]?._id) {
          dispatch(
            matchMakerActions.setIsProfileActivated(
              result?.data?.[0]?.is_activated
            )
          );
          dispatch(matchMakerActions.setProfileExists(true));
          dispatch(matchMakerActions.setCurrentProfile(result.data[0]));
          dispatch(matchMakerActions.setUserMatchProfile(result.data));
          dispatch(
            matchMakerActions.setCurrentPlan(result.data[0]?.active_plan)
          );
          dispatch(matchMakerActions.setIsFetched(true));
          dispatch(
            notificationActions.setSpecificCount({ type: "match", count: 0 })
          );
          setUserExists(true);
        } else {
          dispatch(matchMakerActions.setProfileExists(false));
          setUserExists(false);
          router.push("/matchmaker");
        }
      } catch (error) {
        dispatch(matchMakerActions.setProfileExists(false));
        router.push("/matchmaker");
        setUserExists(false);
      } finally {
        setLoading(false);
      }
    };

    checkGeolocationPermission();
    fetchUserProfile();
  }, []);

  if (!locationAllowed) {
    return <LocationDeniedPage />;
  }

  return user?.role !== "guest" ? (
    !loading && userExists ? (
      <div className="d-flex flex-column h-100">{children}</div>
    ) : (
      <div className="container-xxl bd-gutter g-lg-4 g-0 d-flex align-items-center vh-100 justify-content-center">
        <Loader />
      </div>
    )
  ) : null;
}
