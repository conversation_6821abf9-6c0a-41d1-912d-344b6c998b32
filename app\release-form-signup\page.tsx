"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";

import { PreVerifiedSignUp } from "@/api/user";
import ActionButton from "@/components/common/action-button";

const ReleaseFormSignup = () => {
  const params = useSearchParams();
  const router = useRouter();

  const handleClick = async () => {
    try {
      const id = params.get("reference_id");

      if (!id) {
        router.push("/");
        return;
      }

      const res = await PreVerifiedSignUp({ kyc_id: id ?? "" });
      router.push(`/fresh?ot=${res.ot}&id=${id}`);
    } catch (error) {
      console.log({ error });
      toast.error("Something went wrong!");
      router.push("/");
    }
  };

  return (
    <div
      className="d-flex flex-column justify-content-center align-items-center w-100 gap-2 text-white text-center"
      style={{
        height: "100vh",
        background:
          "linear-gradient(180deg, rgba(48,6,89,1) 0%, rgba(70,21,119,1) 55%, rgba(97,38,159,1) 100%)",
      }}
    >
      <div className="fs-2 fw-bold">Welcome to KNKY!</div>
      <div className="fs-5">
        We have everything we need to set up your account. Let’s get you signed
        up!
      </div>
      <ActionButton className="mt-5" onClick={handleClick}>
        Proceed
      </ActionButton>
    </div>
  );
};

export default ReleaseFormSignup;
