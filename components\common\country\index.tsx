import classNames from "classnames";
import React, { useEffect, useRef, useState } from "react";

import { Getcountries, GetUserLocation } from "@/api/user";
import "./index.scss";

import { clearLocalStorage } from "@/utils/local-storage";

import LoadingSpinner from "../loading";

import Swal from "sweetalert2";

interface CountrySelectionProps {
  onCountryChange: (_selectedCountry: string) => void;
  currentCountry: any;
  flagRequired?: boolean;
  disabled?: boolean;
}

const CountrySelector: React.FC<CountrySelectionProps> = ({
  onCountryChange,
  currentCountry,
  flagRequired = true,
  disabled = false,
}) => {
  const [selectedCountry, setSelectedCountry] = useState("US:United States");
  const [countries, setCountries] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleCountryChange = (country: string) => {
    setSelectedCountry(country);
    setDropdownOpen(false);
    onCountryChange(country);
  };

  useEffect(() => {
    const getCountry = async () => {
      try {
        const countries = await Getcountries();
        setCountries(countries.data);

        if (currentCountry) {
          const country = countries.data.filter(
            (res: any) => res.iso2 === currentCountry.split(":")[0]
          )[0];
          setSelectedCountry(`${country.iso2}:${country.name}`);
        } else {
          const location = await GetUserLocation();
          setSelectedCountry(
            `${location.data.countryCode}:${location.data.country}`
          );
          onCountryChange(
            `${location.data.countryCode}:${location.data.country}`
          );
        }
      } catch (error: any) {
        Swal.fire({
          icon: "error",
          title: error.message,
          confirmButtonText: "OK",
          confirmButtonColor: "#AC1991",
        });

        clearLocalStorage();
      } finally {
        setLoading(false);
      }
    };

    getCountry();
  }, []);

  // Handle closing the dropdown when clicking outside of it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      {loading ? (
        <LoadingSpinner />
      ) : (
        <div
          className={classNames(
            "country-selection bg-cream",
            disabled && "bg-disabled"
          )}
          ref={dropdownRef}
        >
          {selectedCountry && flagRequired && (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              width={50}
              height={25}
              src={`https://flagcdn.com/w80/${selectedCountry
                ?.split(":")[0]
                .toLowerCase()}.png`}
              alt={selectedCountry ? selectedCountry.split(":")[1] : ""}
              className={
                window.location.pathname === "/matchmaker" ? "p-1" : ""
              }
            />
          )}

          {/* Custom Dropdown */}
          <div
            className={`custom-dropdown ms-2 ${
              disabled ? "cursor-not-allowed bg-disabled" : ""
            }`}
            onClick={() => !disabled && setDropdownOpen(!dropdownOpen)}
          >
            <div className="custom-dropdown-header">
              {selectedCountry
                ? selectedCountry.split(":")[1]
                : "Select Country"}
            </div>

            {dropdownOpen && (
              <div className="custom-dropdown-options bg-cream">
                {countries.map((country) => (
                  <div
                    key={country.iso2}
                    className="custom-dropdown-option"
                    onClick={() =>
                      handleCountryChange(`${country.iso2}:${country.name}`)
                    }
                  >
                    {country.name}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export const CountrySelection = React.memo(CountrySelector);
