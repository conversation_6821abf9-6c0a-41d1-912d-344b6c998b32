import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import { BuyChatFeeService } from "@/api/services";
import "@/components/common/buttons/send-tip/index.scss";
import { useAsyncEffect } from "@/hooks/useAsyncEffect";
import { useRegisterModal } from "@/hooks/useRegisterModal";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Chat } from "@/redux-store/slices/chat.slice";
import { fetchWalletBalance } from "@/redux-store/slices/wallet.slice";
import type { GetServiceResponse } from "@/types/services";
import socketChannel from "@/utils/chatSocket";
import { formatCurrency } from "@/utils/formatter";
import { waitForBootstrap } from "@/utils/wait-for-bootstrap";

import { PaymentMethod } from "../../payment-method";

function calculateEndsOn(
  type: "Minute" | "Hour" | "Day" | "Month" | "PerMessage" | "OneOff",
  duration: number
) {
  const currentTime = new Date().getTime();

  switch (type) {
    case "Minute":
      return currentTime + duration * 60 * 1000;
    case "Hour":
      return currentTime + duration * 60 * 60 * 1000;
    case "Day":
      return currentTime + duration * 24 * 60 * 60 * 1000;

    case "Month": {
      const month = new Date(currentTime).getMonth();
      const year = new Date(currentTime).getFullYear();
      const daysInMonth =
        month === 1
          ? year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0)
            ? 29
            : 28
          : [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month];

      return currentTime + duration * daysInMonth * 24 * 60 * 60 * 1000;
    }

    default:
      return 0;
  }
}

export default function ChattingFeeModal() {
  const router = useRouter();
  const modalRef = useRef<any>(null);
  const dispatch = useAppDispatch();
  const displayName = useAppSelector((s) => s.user.profile.display_name);
  const userId = useAppSelector((s) => s.user.id);

  const [duration, setDuration] = useState(1);

  const [openSwal, setOpenSwal] = useState(false);
  const [modalData, setModalData] = useState<{
    targetUser: string;
    converse_channel_id: string;
    service: GetServiceResponse;
  }>();
  const chatList = useAppSelector((s) => s.chat.chatList);

  useAsyncEffect(async () => {
    await waitForBootstrap();

    const initializeModal = () => {
      const myModal = new window.bootstrap.Modal(
        modalRef.current as HTMLElement,
        {
          backdrop: "static",
          keyboard: false,
        }
      );
      modalRef.current!.modalInstance = myModal;
    };

    setTimeout(initializeModal, 300);
  }, []);

  useRegisterModal(
    "BUY_CHAT_FEE_SERVICE",
    (data) => {
      modalRef?.current?.modalInstance?.show();
      setModalData(data);
    },
    () => {
      modalRef.current.modalInstance.hide();
    }
  );

  const onSubmit = (values: any) => {
    const cardTip = {
      tokenised_card_id: values?.payment_id,
      payment_mode: values.payment_type,
      service_id: modalData?.service._id,
    };
    const walletTip = {
      payment_mode: values.payment_type,
      service_id: modalData?.service._id,
    };
    const tipObject: any =
      values.payment_type === "wallet" ? walletTip : cardTip;

    modalRef.current.modalInstance.hide();
    Swal.fire({
      title: `Let's proceed with paying the chat fee?`,
      icon: "warning",
      showCancelButton: true,
      cancelButtonText: "No",
      confirmButtonColor: "#AC1991",
      confirmButtonText: "Yes",
      customClass: {
        confirmButton: "custom-btn",
        cancelButton: "custom-btn",
      },
      showCloseButton: true,
    }).then((result) => {
      if (result.isConfirmed) {
        setOpenSwal(true);

        BuyChatFeeService(modalData?.service._id!, tipObject)
          .then(async (response) => {
            setOpenSwal(false);
            dispatch(fetchWalletBalance());

            if (
              !modalData?.converse_channel_id &&
              !response?.data?.converse_channel_id
            )
              return;

            const concentrateChatIndex = chatList.findIndex(
              (el: Chat) =>
                el.converse_channel_id === modalData?.converse_channel_id ||
                el.converse_channel_id === response?.data?.converse_channel_id
            );

            if (modalData?.service?.chat_fee_type !== "PerMessage") {
              socketChannel.channel?.sendMessage({
                message: "Chat fee paid",
                meta: {
                  type: "chat-unlock",
                  chat_list_message: "Chat fee paid",
                  converseId:
                    modalData?.converse_channel_id ||
                    response.converse_channel_id,
                  displayName: displayName,
                  paid_by: userId,
                  buyers: {
                    buyer: userId,
                    bought_at: new Date().toISOString(),
                    expires_at: new Date(
                      calculateEndsOn(
                        modalData?.service?.chat_fee_type as any,
                        duration
                      )
                    ).toISOString(),
                    service_id: {
                      _id: modalData?.service._id || "",
                      user: modalData?.targetUser || "",
                      type: "CHAT-FEE",
                      chat_fee_type: modalData?.service?.chat_fee_type as any,
                      is_active: modalData?.service?.is_active || true,
                      price: modalData?.service?.price || 0,
                    },
                  },
                },
              });
            }

            console.log({ concentrateChatIndex });

            if (concentrateChatIndex === -1) return;

            const updatedChatList = [...chatList];
            updatedChatList[concentrateChatIndex] = {
              ...updatedChatList[concentrateChatIndex],
              buyers: [
                ...updatedChatList?.[concentrateChatIndex]?.buyers,
                ...response?.data?.buyers,
              ],
              converse_consumable: response?.data?.converse_consumable || [],
            };

            dispatch(chatActions.setChatUserList(updatedChatList));
          })
          .then(() => {
            Swal.fire({
              icon: "success",
              title: "Chat fee paid successfully",
              confirmButtonText: "Go to Chat",
              showCancelButton: true,
              cancelButtonText: "Finish",
              confirmButtonColor: "#AC1991",
              customClass: {
                confirmButton: "custom-btn",
                cancelButton: "custom-btn",
              },
            }).then((res) => {
              if (res.isConfirmed) {
                if (modalData?.targetUser)
                  router.push(`/chat?user=${modalData?.targetUser}`);
                else router.push("/chat");
              }
            });
          })
          .catch((error) => {
            console.log({ error });
            setOpenSwal(false);

            Swal.fire({
              icon: "error",
              title:
                error.message === "Insufficient balance"
                  ? "Insufficient balance"
                  : "Error paying chat fee. Please try again later!",
              customClass: {
                confirmButton: "custom-btn",
              },
              showCloseButton: true,
              confirmButtonText:
                error.message === "Insufficient balance"
                  ? "Add Balance"
                  : "Finish",
            }).then((res) => {
              if (res.isConfirmed && error.message === "Insufficient balance") {
                router.push("/settings/wallet");
              }
            });
          });
      }
    });
  };

  return (
    <div
      className="modal fade"
      id="chattingFeeModal"
      tabIndex={-1}
      aria-labelledby="chattingFeeModalLabel"
      aria-hidden="true"
      ref={modalRef}
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header">
            <h1 className="modal-title fs-5" id="chattingFeeModalLabel">
              Buy Chat Fee Service
            </h1>
            <button
              type="button"
              className="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div className="modal-body">
            <>
              <div className="color-medium w-100 text-center mb-2">
                The fee is {formatCurrency(modalData?.service?.price ?? 0)}/
                {modalData?.service.chat_fee_type.toLowerCase()}
              </div>
              {/* {modalData?.service?.chat_fee_type !== "OneOff" &&
                modalData?.service?.chat_fee_type !== "PerMessage" && (
                  <div className="tip-wrapper">
                    <div className="color-black fs-7">
                      How long you want to chat?
                    </div>
                    <input
                      type="number"
                      placeholder="Enter duration"
                      className="bg-cream color-dark w-100 rounded-3 border-0 p-2 fs-7"
                      value={duration}
                      min={1}
                      disabled={modalData?.service?.chat_fee_type === "Month"}
                      onChange={(e) => {
                        const maxDuration =
                          modalData?.service?.chat_fee_type === "Minute"
                            ? MaxChatFeeDuration * 12 * 30 * 24 * 60
                            : modalData?.service?.chat_fee_type === "Hour"
                            ? MaxChatFeeDuration * 12 * 30 * 24
                            : modalData?.service?.chat_fee_type === "Day"
                            ? MaxChatFeeDuration * 12 * 30
                            : 1;

                        if (+e.target.value > maxDuration) {
                          e.target.value = `${maxDuration}`;
                        }

                        setDuration(+e.target.value);
                      }}
                    />
                  </div>
                )} */}
              <div className="tip-wrapper">
                <PaymentMethod
                  type="ChatFee"
                  onSubmit={onSubmit}
                  onClose={() => modalRef.current.modalInstance.hide()}
                  amount={(modalData?.service?.price ?? 0) * duration}
                  isBtnValid={
                    duration >= 1 ||
                    modalData?.service?.chat_fee_type === "OneOff" ||
                    modalData?.service?.chat_fee_type === "Month"
                  }
                />
              </div>
              <CardProcessingLocal openSwal={openSwal} />
            </>
          </div>
        </div>
      </div>
    </div>
  );
}

const CardProcessingLocal = ({ openSwal }: { openSwal: boolean }) => {
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  const modalContainerRef = useRef<HTMLDivElement | null>(null);

  const showProcessingModal = () => {
    Swal.fire({
      html: `
        <div id="react-swal"></div>
        <div class="w-100 my-3">
          <div class="text-center w-100 fs-2 fw-bold">
            Processing Payment...
          </div>
          <div class="fs-6 text-center w-100" style="color: rgba(77, 80, 83, 1);">
            Please wait while we securely process your transaction.
          </div>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      showCloseButton: true,
      backdrop: true,
      customClass: {
        popup: "rounded-3",
      },
      didOpen: () => {
        const container = document.getElementById("react-swal");

        if (container && iframeRef.current) {
          container.appendChild(iframeRef.current);
        }
      },
      didClose: () => {
        if (iframeRef.current && modalContainerRef.current) {
          modalContainerRef.current.appendChild(iframeRef.current);
        }
      },
    });
  };

  const hideProcessingModal = () => {
    Swal.close(); // Close the modal explicitly
  };

  useEffect(() => {
    // Open or close the modal based on `openSwal`
    if (openSwal) {
      showProcessingModal();

      // Auto-close the modal after 5 seconds
      const timer = setTimeout(() => {
        hideProcessingModal();
      }, 5000);

      return () => clearTimeout(timer); // Cleanup the timer
    } else {
      hideProcessingModal(); // Close the modal when `openSwal` becomes false
    }
  }, [openSwal]);

  useEffect(() => {
    const iframe = document.createElement("iframe");
    iframe.id = "payment-loader";
    iframe.src =
      "https://lottie.host/embed/62070a52-0472-4a8c-8112-472c9dec69fa/TXkeg9WkCy.json";
    iframe.style.height = "100%";
    iframe.style.width = "100%";
    iframe.style.scale = "2.5";
    iframe.style.pointerEvents = "none";
    iframe.loading = "eager";

    iframeRef.current = iframe;

    const modalContainer = document.createElement("div");
    modalContainer.style.display = "none";
    document.body.appendChild(modalContainer);
    modalContainerRef.current = modalContainer;

    modalContainer.appendChild(iframe);
  }, []);

  return null;
};
