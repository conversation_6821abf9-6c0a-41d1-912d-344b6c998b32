"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { GetGroupByUsername } from "@/api/group";
import { transformSubscriptions } from "@/components/common/buttons/channel-group-btns";
import { createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { type Params } from "@/types/utils";
import { getAssetUrl } from "@/utils/assets";

import GroupView from "../../element";

export default function EditGroup({ params }: Params) {
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  const userId = useAppSelector((state) => state.user.profile._id);
  const groupId = useAppSelector((state) => state.create.group.id);

  useEffect(() => {
    // Info: Check if group was edited then came back then no fetching of data
    if (groupId === params.id) return;

    GetGroupByUsername(params.id)
      .then((response) => {
        const group = response.data;
        group.members.forEach((member) => {
          member.is_accepted = true;
        });

        if (
          !group.members.map(({ member_id }) => member_id._id).includes(userId)
        )
          throw new Error("User not have sufficient permissions");

        dispatchAction(
          createActions.setGroup({
            id: group._id,
            name: group.name,
            username: group.tag_name,
            topic: group.topic,
            subscribe: transformSubscriptions(group.subscription)?.[0] || {
              array: [],
            },
            // avatar: getAssetUrl({
            //   media: group.avatar[0],
            //   defaultType: "avatar",
            // }),
            cover: getAssetUrl({ media: group.background[0] }),
            description: group.about,
            hashtags: group.hashtags,
            social_media: group.social_handles,
            members: group.members,
            from: "",
            saved: false,
            nextBtnText: "",
            subscription_perks: group.perks.map((p) => {
              const { type, description, emoji } = p;
              return {
                icon: type,
                value: description,
                ...(emoji && { emoji: emoji }),
              };
            }),
          })
        );
      })
      .catch(() => {
        router.push("/");
      });
  }, [params]);

  return GroupView({
    edit: true,
    from: `/collab/${params.id}/edit`,
    fromTitle: "Edit my collab",
    fromBtnText: "Next",
    nextBtnText: "Save Changes",
    navigateTo: `/collab/${params.id}/edit/preview`,
  });
}
