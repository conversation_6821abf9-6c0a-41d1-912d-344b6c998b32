import type { ChatFeeOptions } from "@/components/settings/creator-settings/sub-components/chatting-fee/page";
import type { Media } from "@/types/media";
import type { Author } from "@/types/notification";

import API from ".";

export const getChannelId = async (userId: string) =>
  API.get(`${API.USERS}/get-converse-channel/${userId}`) as Promise<any>;

export const createReq = async (formData: FormData) => {
  return API.post(
    `${API.REQUESTS}/calls-and-ratings`,
    formData
  ) as Promise<any>;
};

export const getReq = async (userType: string) =>
  API.get(
    `${API.REQUESTS}/calls-and-ratings?user_type=${userType}`
  ) as Promise<any>;

export const getReqById = async (reqId: string) =>
  API.get(`${API.REQUESTS}/calls-and-ratings/${reqId}`) as Promise<any>;

export const getChatUserList = async (page: number = 1, limit: number = 200) =>
  API.get(
    `${API.USERS}/get-converse-channel?page=${page}&limit=${limit}`
  ) as Promise<any>;

export const respondReq = async (reqId: any, reqData: any) => {
  const formData = new FormData();

  for (const key in reqData) {
    const value = reqData[key];

    if (key === "media" && Array.isArray(value)) {
      value.forEach((mediaItem) => {
        formData.append("media", mediaItem);
      });
    } else {
      formData.append(key, value);
    }
  }

  return API.put(
    `${API.REQUESTS}/respond-to-request/${reqId}`,
    formData
  ) as Promise<any>;
};

export const PromotionalRespondReq = async (reqId: any, reqData: any) => {
  const formData = new FormData();

  for (const key in reqData) {
    const value = reqData[key];

    if (key === "media" && Array.isArray(value)) {
      value.forEach((mediaItem) => {
        formData.append("media", mediaItem);
      });
    } else {
      formData.append(key, value);
    }
  }

  return API.put(
    `${API.REQUESTS}/respond-to-promotional-request/${reqId}`,
    formData
  ) as Promise<any>;
};

export const UpdateMessageId = async (
  reqId: string,
  messageId: string,
  creation_time: string,
  channelId: string,
  meta: any
) =>
  API.patch(`${API.REQUESTS}/calls-and-ratings/${reqId}`, {
    message_id: messageId,
    creation_time: creation_time,
    channel_id: channelId,
    meta: meta,
  }) as Promise<any>;

export const updateChatActivityApi = async (converse_channel_id: string) => {
  API.patch(`${API.CHAT_ACTIVITY}`, { converse_channel_id }) as Promise<any>;
};

export const StartTimerApi = async (requestId: string) => {
  return API.get(
    `${
      API.REQUESTS
    }/start-timer?request_id=${requestId}&start_time=${Date.now()}`
  ) as Promise<any>;
};

export const GetChatStats = async (channelId: string) => {
  return API.get(
    `${API.USERS}/converse-channel/${channelId}/stats`
  ) as Promise<{ data: { memberCount: number; messageCount: number } }>;
};

interface PurchaseMediaBody {
  seller_id: string;
  chat_media_id: string;
  payment_mode: "wallet" | "card";
  tokenised_card_id?: string;
}

export const PurchaseMedia = (body: PurchaseMediaBody) =>
  API.post(`${API.USERS}/purchase-media`, body) as Promise<any>;

export const DeleteChat = (channelId: string) =>
  API.delete(`${API.USERS}/clear-userchat/${channelId}`) as Promise<any>;

export const RequestConverseToken = () =>
  API.get(`${API.USERS}/request-converse-token`) as Promise<any>;

export const VerifyConverseToken = (body: {
  projectId: string;
  token: string;
}) => API.post(`${API.USERS}/verify-converse-token`, body) as Promise<any>;

export const GetChatFees = () =>
  API.get(`${API.USERS}/chatting-fee`) as Promise<{
    data: {
      _id: string;
      is_active: boolean;
      type: ChatFeeOptions;
      price: number;
    };
  }>;

export enum ChatFeeType {
  ONE_OFF = "OneOff",
  MINUTE = "Minute",
  DAY = "Day",
  HOUR = "Hour",
  WEEK = "Week",
  MONTH = "Month",
  PER_MESSAGE = "PerMessage",
  FREE = "Free",
}

export const UpdateChatFee = (body: {
  is_active: boolean;
  type?: ChatFeeType;
  price?: number;
}) =>
  API.put(`${API.USERS}/chatting-fee`, body) as Promise<{
    data: {
      user: string;
      is_active: boolean;
      type: ChatFeeType;
      price: number;
      _id: string;
    };
  }>;

export interface ChatFeeResponse {
  _id: string;
  user: string;
  type: string;
  name: string;
  chat_fee_type: ChatFeeType;
  note: string;
  avatar: any[];
  is_active: boolean;
  price: number;
  fixed_price: boolean;
  has_discount: boolean;
  is_deleted: boolean;
  is_default: boolean;
  created_at: string;
  includes?: string;
  discount?: {
    discount_value: number;
    discount_type: "percentage" | "amount";
    expires_on: string;
  };
}

export const GetUserChatFeeServices = ({
  user_id,
  chat_fee_type,
}: {
  user_id: string;
  chat_fee_type?: ChatFeeType;
}) => {
  const params = new URLSearchParams();
  params.append("user_id", user_id);
  if (chat_fee_type) params.append("chat_fee_type", chat_fee_type);
  params.append("type", "CHAT-FEE");
  return API.get(`${API.USERS}/services?${params.toString()}`) as Promise<{
    data: ChatFeeResponse[];
  }>;
};

export const UtiliseFreeService = (body: {
  free_service_id: string;
  unit: number;
  extra_data?: Record<string, any>;
  request_note?: string;
  media?: File | File[];
}) => {
  const formData = new FormData();

  for (const key in body) {
    const value = body[key as keyof typeof body];

    if (value !== undefined && value !== null) {
      if (key === "media") {
        const mediaFiles = Array.isArray(value) ? value : [value];
        mediaFiles.forEach((file) => {
          formData.append("media", file);
        });
      } else if (key === "extra_data" && typeof value === "object") {
        formData.append(key, JSON.stringify(value));
      } else {
        formData.append(key, String(value));
      }
    }
  }

  return API.post(
    API.REQUESTS + "/free-calls-and-ratings",
    formData
  ) as Promise<any>;
};

export function ShareEntity(body: {
  target_ids: string[];
  post_id?: string;
  channel_id?: string;
  group_id?: string;
  product_id?: string;
  story_id?: string;
  offer_price?: number;
}) {
  return API.post(API.USERS + "/share-entities", body) as Promise<any>;
}

export enum MassMessageTargetType {
  FOLLOWERS = "Followers",
  SUBSCRIBERS = "Subscribers",
  EXPIRED_SUBSCRIBERS = "ExpiredSubscribers",
}

export const ScheduleMassMessage = (body: {
  message: string;
  target: MassMessageTargetType[];
  type: "Direct" | "Scheduled";
  scheduled_at?: string;
  channels?: string[];
}) => {
  return API.post(API.USERS + "/mass-message/create", body);
};

export interface MassMessage {
  _id: string;
  user: string;
  message: string;
  target: MassMessageTargetType[];
  type: "Direct" | "Scheduled";
  channels: string[];
  created_at: string;
  scheduled_at?: string;
}

export const GetMassMessageHistory = ({
  check_scheduled,
  page = 1,
  limit = 10,
}: {
  check_scheduled?: boolean;
  page?: number;
  limit?: number;
}) => {
  const params = new URLSearchParams();
  if (check_scheduled) params.append("type", "Scheduled");
  params.append("page", String(page));
  params.append("limit", String(limit));

  return API.get(
    API.USERS + `/mass-message/history?${params.toString()}`
  ) as Promise<{
    data: MassMessage[];
  }>;
};

export const DeleteMassMessage = (message_id: string) => {
  return API.delete(
    API.USERS + `/mass-message/delete/${message_id}`
  ) as Promise<any>;
};

export const EditMassMessage = (body: {
  message_id: string;
  message?: string;
  scheduled_at?: string;
  channels?: string[];
}) => {
  const { message_id, ...restBody } = body;
  return API.patch(
    API.USERS + "/mass-message/update" + `/${message_id}`,
    restBody
  ) as Promise<any>;
};

export enum ConsentStatus {
  PENDING = "Pending",
  REJECTED = "Rejected",
  APPROVED = "Approved",
  // Important status in case of post creation
  NEWLYADDED = "",
}

export const ApprovalConsent = (
  body: {
    media_id: string;
    status: ConsentStatus;
  }[]
) => {
  return API.post(API.USERS + `/media-consent`, body) as Promise<any>;
};

export interface GetTagDetailsInterface {
  media: Media[];
  author: Author;
}

export const GetTagDetails = (
  entity_id: string,
  entity_type: "POST" | "VAULT",
  target_user?: string
) => {
  const params = new URLSearchParams({
    source_type: entity_type,
  });

  if (target_user) {
    params.append("target_user", target_user);
  }

  return API.get(
    `${API.USERS}/media-consent/${entity_id}/preview?${params.toString()}`
  ) as Promise<{
    data: GetTagDetailsInterface;
  }>;
};
