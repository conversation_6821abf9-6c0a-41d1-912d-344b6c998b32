.active-post-btn {
  box-shadow: 0 0 0 1pt var(--primary-color);
  color: var(--primary-color) !important;
}

.post-loader {
  z-index: 5;
  background: var(--bg-dark);
}

.story-button-wrapper {
  .btn {
    width: 10rem !important;
    height: 12rem;
  }
  @media (max-width: 768px) {
    .btn {
      width: 100%;
      height: unset;

      img {
        width: 20%;
        height: 20%;
      }
    }
  }
}

.media-content > div {
  height: auto;
  width: 100%;

  @media (max-width: 768px) {
    height: auto;
  }
}

.media-content img {
  max-height: 100%;
}
.media-remove-btn {
  width: fit-content;
  background-color: #00000038;
  right: 1%;
  top: 1%;
  z-index: 9;
}
.text-story-container {
  display: flex;
  justify-content: center;
  flex-direction: row-reverse;
}
.text-box-area {
  min-height: 32rem;
  min-width: 21rem;
  border: 1px solid #ebebec;
  position: relative;
}
.text-box-area textarea {
  box-sizing: border-box;
  border: none;
  outline: none;
  padding: 8px;
  resize: none;
  overflow-y: hidden;
  border-radius: 8px;
  font-weight: 600;
  background: transparent;
}

.color-box {
  min-height: 1.5rem;
  min-width: 1.5rem;
  border-radius: 50%;
  border: 2px solid #ebebec;
  cursor: pointer;
}
.color-box.active {
  border-color: #ac1991;
}
.done-btn {
  position: absolute;
  right: 0;
  bottom: -18%;
  z-index: 9;
}

.promote-wrapper {
  display: flex;
  width: 50%;
  flex-direction: column;
  gap: 0.5rem;
  border: 0.66px solid #afb1b3;
  border-radius: 8px;
  padding: 3%;

  @media (max-width: 992px) {
    width: 100%;
  }
}

.text-story-background {
  display: grid;
  grid-template-columns: 0.5fr 1fr;
  justify-items: center;

  @media (max-width: 767px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}
