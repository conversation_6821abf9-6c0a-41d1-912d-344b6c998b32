:root {
  --chat-bg-light: rgba(245, 245, 246, 1);
  --chat-text-light: rgba(77, 80, 83, 1);
  --border-light: #e0e0e0;
  --scrollbar-thumb-light: #cecece;

  // --bg-dark: #2c2c2c7a;
  --text-dark: rgba(200, 200, 205, 1);
  --border-dark: #444;
  --scrollbar-thumb-dark: #555;
}

[data-bs-theme="dark"] {
  --bg-light: var(--bg-dark);
  --text-light: var(--text-dark);
  --border-light: var(--border-dark);
  --scrollbar-thumb-light: var(--scrollbar-thumb-dark);
}

.chat-bar {
  border-top: 1px solid var(--border-light);
}

.fullfilled {
  opacity: 0.5;
  pointer-events: none;
}

.chat-header-info .header-search-box {
  z-index: 2;
  display: flex;
  flex-direction: column;
}

.chat-custom-menu {
  background-color: var(--bg-light);
  border: none;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  width: 100%;
  border-top: 0 !important;
  border-radius: 1rem;
  display: flex;
  flex-direction: column;
  max-height: 19rem;
  overflow: auto;
  padding: 1%;
  margin-bottom: 0px;
  top: 63px;
  z-index: 3;
  li {
    list-style: none;
    z-index: 10;
  }
}

.chat-custom-menu::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.chat-custom-menu {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.chat-custom-menu::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb-light);
}

.unreads {
  background-color: #ac1991;
  color: white;
  width: 1.7rem;
  height: 1.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unreads:empty {
  display: none;
}

.user-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

@media screen and (max-width: 767px) {
  .profile-user-name {
    text-wrap: nowrap;
    text-overflow: ellipsis;
    max-width: 10ch;
    overflow: hidden;
  }

  .chat-custom-menu {
    width: 100%;
  }

  .slider {
    position: absolute;
    left: -100%;
    transition: 0.4s ease-in-out;
    bottom: 0;
    top: 0;
    right: 0;
    z-index: 3;
    background-color: var(--bg-light);
    padding: 0 !important;
    margin: 0 !important;
  }

  .chat-custom-menu {
    top: 3.8rem;
  }

  .chat-wrapper {
    margin: 0 !important;
    padding: 0 !important;
  }

  .slider-move {
    left: 0 !important;
  }
}

.last-seen-slide-in {
  height: 1rem;
  transition: height 5s ease-out, opacity 5s ease-out;
  opacity: 1;
}

.chat-fee-detail {
  background-color: var(--chat-bg-light);
  color: var(--chat-text-light);
}
