import { format, isToday, isYesterday, parseISO } from "date-fns";

export default function DateFormatter({
  dateString,
  formatType,
  isChat,
}: {
  dateString: string;
  formatType: string;
  isChat?: boolean;
}) {
  const date = parseISO(dateString);

  if (isChat && isYesterday(date)) {
    return <time dateTime={dateString}>Yesterday</time>;
  }

  if (isChat && isToday(date)) {
    return <time dateTime={dateString}>Today</time>;
  }

  return <time dateTime={dateString}>{format(date, formatType)}</time>;
}
