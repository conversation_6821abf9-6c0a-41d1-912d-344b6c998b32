/* eslint-disable @next/next/no-img-element */
"use client";

import classNames from "classnames";
import "./index.scss";

import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";
import type { Author } from "@/types/post";
import { checkVerification } from "@/utils/check-verification";

import ActionButton from "../../action-button";
import { PostActionBtnClass } from "../../class";

interface PostAction {
  postId: string;
  author: Author;
  cls?: string;
  type: string;
  onlyIcon?: boolean;
  source?: "chat" | "profile";
  from?: "reel" | "post" | "grid";
}

interface PostSendTipBtnState {
  cls: string;
  img: string;
  title: string;
}

interface SendTipBody {
  author: Author;
  type: string;
  postId: string;
}

const View: {
  [key: string]: PostSendTipBtnState;
} = {
  inactive: {
    cls: "",
    img: "fill-money",
    title: "Send Tip",
  },
  active: {
    cls: "active",
    img: "line-money",
    title: "Already Tipped",
  },
};

const PostActionSendTip = ({
  postId,
  author,
  cls,
  type,
  onlyIcon,
  source = "profile",
  from = "post",
}: PostAction) => {
  const state = View.inactive;
  const isCardExist = useAppSelector(
    (state) => state?.userData?.cardDetails[0]
  );
  const sendTipDetails: SendTipBody = {
    author: author,
    type: type,
    postId: postId,
    ...(source === "chat" ? { source: "chat" } : {}),
  };

  const role = useAppSelector((state) => state?.user?.role);
  const isGridMobile = from === "grid" && window.innerWidth < 768;

  const handleClick = async () => {
    const verified = await checkVerification("tip");

    if (verified) {
      if (role === "guest") {
        ModalService.open("SIGN_IN");
      } else if (isCardExist?.payment_method_id) {
        ModalService.open("SEND_TIP", sendTipDetails);
      } else {
        ModalService.open("ADD_CARD", { type: "Send Tip" });
      }
    }
  };

  return (
    <>
      {from === "reel" || from === "grid" ? (
        <>
          <img
            onClick={handleClick}
            src={
              from === "grid"
                ? "/images/common/grid-tip.svg"
                : "/images/reels/tip.svg"
            }
            alt=""
            width={isGridMobile ? 18 : 40}
            height={isGridMobile ? 18 : 40}
            className="pointer"
          />
        </>
      ) : (
        <ActionButton
          type="outline"
          variant="warning"
          onClick={handleClick}
          className={classNames(
            " px-1 py-2 text-nowrap",
            PostActionBtnClass,
            cls,
            state.cls,
            { onlyIcon: onlyIcon }
          )}
        >
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            width={22}
            height={22}
            className=""
            alt="send-tip"
            src={`/images/post/${state.img}.svg`}
          />
          {!onlyIcon && <div>{state.title}</div>}
        </ActionButton>
      )}
    </>
  );
};

export default PostActionSendTip;
