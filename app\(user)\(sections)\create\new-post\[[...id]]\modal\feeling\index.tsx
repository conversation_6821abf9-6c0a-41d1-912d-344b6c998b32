import Modal, { type ModalImportProps } from "@/components/common/modal";

export const feelings = [
  { emoji: "😁", text: "happy" },
  { emoji: "❤️", text: "loved" },
  { emoji: "😘", text: "lovely" },
  { emoji: "🤩", text: "excited" },
  { emoji: "🫦", text: "sexy" },
  { emoji: "🥰", text: "in love" },
  { emoji: "😵‍💫", text: "crazy" },
  { emoji: "🥵", text: "hot" },
  { emoji: "😋", text: "fantastic" },
  { emoji: "😎", text: "cool" },
  { emoji: "😇", text: "chill" },
  { emoji: "🤒", text: "sick" },
  { emoji: "☘️", text: "lucky" },
  { emoji: "😉", text: "fresh" },
  { emoji: "😈", text: "horny" },
];

export default function Feeling({
  setChildRef,
  showFeeling,
  onclose,
}: ModalImportProps & { showFeeling: Function } & { onclose: () => void }) {
  return (
    <Modal
      title="Feeling"
      subtitle={[""]}
      setChildRef={setChildRef}
      onClose={() => onclose()}
    >
      <div className="container feeling-container">
        <div className="gender-row row">
          <div className="container card-container">
            <div className="d-flex flex-wrap align-items-center gap-3 justify-content-center ">
              {feelings.map((feeling) => (
                <button
                  className="btn position-relative z-3 d-flex align-items-center gap-2 post-type-btn rounded-3 col-3 flex-grow-1  shadow-dark hover-active-hollow color-bold py-2"
                  key={feeling.text}
                  onClick={() => {
                    showFeeling(feeling.emoji, feeling.text);
                    setChildRef.method.close();
                  }}
                >
                  <span className="card-emoji">{feeling.emoji}</span>
                  {feeling.text}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
}
