#profile-container {
  .profile-header {
    .profile-image {
      position: relative;
      padding-top: 37.037%;

      img {
        position: absolute;
        top: 0;
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  }

  .profile-content {
    .profile-details {
      flex-basis: 250px;

      @media (min-width: 992px) {
        min-width: 20rem;
        max-width: 24rem;
      }
    }

    .profile-sections-container {
      // flex-basis: 35rem;
    }
  }
}

.profile-view {
  position: relative;
  background: var(--bg-color);
  padding-top: 0;

  .profile-view-image {
    --h: clamp(3rem, calc((100vw - 256px) / 10), 6rem);

    @media (max-width: 992px) {
      margin-left: calc(var(--h) / 2);
    }

    img {
      box-shadow: 0 0 0 2pt var(--bg-color);
    }
  }
}

.tag {
  padding: 0.5rem 1rem;
  border: 1pt solid var(--bg-dark);
}

.featured-container {
  position: relative;

  .featured-content {
    overflow: hidden;
    overflow-x: auto;
  }

  .featured-item {
    position: relative;
    height: 14rem;
    width: 10rem;
    min-width: 10rem;
    border-radius: var(--border-radius);
    overflow: hidden;

    .featured-title {
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: 2;
      color: var(--bg-color);
    }

    .featured-image {
      position: absolute;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
}
