import { useAppSelector } from "@/redux-store/hooks";
import { MessageInterface } from "@/redux-store/slices/chat.slice";
import { formatCurrency } from "@/utils/formatter";

const SentTip = ({ message }: { message: MessageInterface }) => {
  const userId = useAppSelector((s) => s.user.id);

  return (
    <div className="py-1">
      <div className="d-flex align-items-center gap-2">
        <div>
          <TipIcon />
        </div>
        <div className="d-flex flex-column">
          <div className="fw-bold">
            Tipped {formatCurrency(Number(message?.meta?.amount) || 0)}
          </div>
          {message?.meta?.details &&
          `Tipped you $${message?.meta?.amount}` !==
            message?.meta?.chat_list_message ? (
            <div>{message?.meta?.details}</div>
          ) : userId === message?.sender_id ? (
            <div>
              You tipped {formatCurrency(Number(message?.meta?.amount || 0))}
            </div>
          ) : (
            <div>
              Tipped you {formatCurrency(Number(message?.meta?.amount || 0))}
            </div>
          )}
        </div>
      </div>
      <div className="position-absolute bottom-0 end-0">
        <SentTipIconBg />
      </div>
    </div>
  );
};

export default SentTip;

const SentTipIconBg = () => (
  <svg
    width="97"
    height="81"
    viewBox="0 0 117 101"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g opacity="0.4">
      <path
        d="M68.3384 64.6622C61.5704 57.8942 52.5717 54.167 43.0003 54.167C33.4289 54.167 24.4303 57.8942 17.6622 64.6622C10.8942 71.4303 7.16699 80.4289 7.16699 90.0003C7.16699 99.5717 10.8944 108.57 17.6622 115.338C24.4301 122.107 33.4289 125.834 43.0003 125.834C52.5717 125.834 61.5704 122.106 68.3384 115.338C75.1064 108.57 78.8337 99.5717 78.8337 90.0003C78.8337 80.4289 75.1063 71.4304 68.3384 64.6622ZM43.0003 87.7607C48.5574 87.7607 53.0785 92.2818 53.0785 97.8389C53.0785 102.441 49.9771 106.33 45.7553 107.531C45.452 107.617 45.2399 107.89 45.2399 108.206V110.093C45.2399 111.299 44.3131 112.337 43.1085 112.394C41.8223 112.454 40.7607 111.429 40.7607 110.157V108.205C40.7607 107.891 40.5499 107.618 40.2475 107.532C36.0438 106.336 32.9503 102.477 32.9223 97.9014C32.9149 96.6802 33.8751 95.6353 35.096 95.6003C36.3629 95.5639 37.4014 96.5801 37.4014 97.8389C37.4014 101.059 40.1332 103.652 43.4005 103.424C46.1586 103.231 48.3927 100.997 48.5853 98.2388C48.8134 94.9714 46.2199 92.2399 43.0003 92.2399C37.4432 92.2399 32.9222 87.7189 32.9222 82.1618C32.9222 77.5599 36.0236 73.6711 40.2454 72.4696C40.5487 72.3832 40.7607 72.1104 40.7607 71.795V69.9073C40.7607 68.7013 41.6875 67.6637 42.8921 67.607C44.1783 67.5465 45.2399 68.5713 45.2399 69.8441V71.7955C45.2399 72.11 45.4507 72.3829 45.7532 72.469C49.9569 73.6644 53.0503 77.5239 53.0783 82.0992C53.0857 83.3205 52.1255 84.3654 50.9047 84.4004C49.6378 84.4368 48.5993 83.4206 48.5993 82.1618C48.5993 78.9421 45.8674 76.3484 42.6001 76.5768C39.8421 76.7696 37.608 79.0038 37.4154 81.7619C37.1872 85.0293 39.7808 87.7607 43.0003 87.7607Z"
        fill="#FFB800"
      />
    </g>
    <g opacity="0.4">
      <path
        d="M107.758 29.2417C104.217 25.7003 99.5083 23.75 94.5 23.75C89.4917 23.75 84.7831 25.7003 81.2417 29.2417C77.7003 32.7831 75.75 37.4917 75.75 42.5C75.75 47.5083 77.7004 52.2168 81.2417 55.7583C84.783 59.2998 89.4917 61.25 94.5 61.25C99.5083 61.25 104.217 59.2997 107.758 55.7583C111.3 52.2169 113.25 47.5083 113.25 42.5C113.25 37.4917 111.3 32.7832 107.758 29.2417ZM94.5 41.3281C97.4078 41.3281 99.7734 43.6938 99.7734 46.6016C99.7734 49.0095 98.1506 51.0444 95.9416 51.6731C95.7828 51.7183 95.6719 51.861 95.6719 52.026V53.0138C95.6719 53.6448 95.1869 54.1878 94.5566 54.2174C93.8836 54.2491 93.3281 53.7129 93.3281 53.0469V52.0258C93.3281 51.8612 93.2178 51.7184 93.0595 51.6734C90.8599 51.0479 89.2413 49.0284 89.2266 46.6343C89.2228 45.9953 89.7252 45.4485 90.364 45.4302C91.0269 45.4112 91.5703 45.9429 91.5703 46.6016C91.5703 48.2863 92.9998 49.6435 94.7094 49.5239C96.1526 49.4231 97.3216 48.254 97.4224 46.8108C97.5417 45.1011 96.1846 43.6719 94.5 43.6719C91.5922 43.6719 89.2266 41.3062 89.2266 38.3984C89.2266 35.9905 90.8494 33.9556 93.0584 33.3269C93.2172 33.2817 93.3281 33.139 93.3281 32.974V31.9862C93.3281 31.3552 93.8131 30.8122 94.4434 30.7826C95.1164 30.7509 95.6719 31.2871 95.6719 31.9531V32.9742C95.6719 33.1388 95.7822 33.2816 95.9405 33.3266C98.1401 33.9521 99.7587 35.9716 99.7734 38.3657C99.7772 39.0047 99.2748 39.5515 98.636 39.5698C97.9731 39.5888 97.4297 39.0571 97.4297 38.3984C97.4297 36.7137 96.0002 35.3565 94.2906 35.4761C92.8474 35.5769 91.6784 36.746 91.5776 38.1892C91.4583 39.8989 92.8154 41.3281 94.5 41.3281Z"
        fill="#FFB800"
      />
    </g>
    <g opacity="0.4">
      <path
        d="M61.0441 5.9556C58.7618 3.67336 55.7274 2.4165 52.4998 2.4165C49.2723 2.4165 46.2378 3.67336 43.9556 5.9556C41.6734 8.23784 40.4165 11.2723 40.4165 14.4998C40.4165 17.7274 41.6734 20.7618 43.9556 23.0441C46.2378 25.3264 49.2723 26.5832 52.4998 26.5832C55.7274 26.5832 58.7618 25.3263 61.0441 23.0441C63.3263 20.7618 64.5832 17.7274 64.5832 14.4998C64.5832 11.2723 63.3263 8.23789 61.0441 5.9556ZM52.4998 13.7446C54.3737 13.7446 55.8983 15.2692 55.8983 17.1431C55.8983 18.6949 54.8525 20.0062 53.4288 20.4114C53.3266 20.4405 53.255 20.5325 53.255 20.6388V21.2754C53.255 21.6821 52.9425 22.032 52.5363 22.0511C52.1026 22.0715 51.7446 21.7259 51.7446 21.2967V20.6387C51.7446 20.5326 51.6735 20.4406 51.5715 20.4116C50.154 20.0085 49.1109 18.707 49.1014 17.1642C49.0989 16.7523 49.4227 16.4 49.8344 16.3882C50.2616 16.3759 50.6118 16.7186 50.6118 17.1431C50.6118 18.2288 51.533 19.1034 52.6348 19.0264C53.5648 18.9614 54.3182 18.208 54.3831 17.2779C54.4601 16.1761 53.5855 15.255 52.4998 15.255C50.6259 15.255 49.1014 13.7305 49.1014 11.8566C49.1014 10.3048 50.1472 8.99347 51.5708 8.5883C51.6731 8.55918 51.7446 8.46719 51.7446 8.36084V7.7243C51.7446 7.31762 52.0571 6.96772 52.4634 6.9486C52.8971 6.92821 53.255 7.27377 53.255 7.70296V8.36099C53.255 8.46704 53.3261 8.55909 53.4281 8.58811C54.8457 8.99121 55.8888 10.2927 55.8982 11.8355C55.9007 12.2473 55.5769 12.5997 55.1653 12.6115C54.738 12.6238 54.3879 12.2811 54.3879 11.8566C54.3879 10.7709 53.4666 9.89628 52.3649 9.97331C51.4349 10.0383 50.6815 10.7917 50.6165 11.7218C50.5396 12.8236 51.4142 13.7446 52.4998 13.7446Z"
        fill="#FFB800"
      />
    </g>
  </svg>
);

const TipIcon = () => (
  <svg
    width="62"
    height="62"
    viewBox="0 0 72 72"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M57.2133 14.7867C51.547 9.12047 44.0133 6 36 6C27.9867 6 20.453 9.12047 14.7867 14.7867C9.12047 20.453 6 27.9867 6 36C6 44.0133 9.12059 51.5469 14.7867 57.2133C20.4529 62.8796 27.9867 66 36 66C44.0133 66 51.547 62.8795 57.2133 57.2133C62.8795 51.547 66 44.0133 66 36C66 27.9867 62.8794 20.4531 57.2133 14.7867ZM36 34.125C40.6525 34.125 44.4375 37.91 44.4375 42.5625C44.4375 46.4153 41.841 49.671 38.3065 50.6769C38.0525 50.7492 37.875 50.9776 37.875 51.2416V52.822C37.875 53.8317 37.0991 54.7004 36.0906 54.7479C35.0138 54.7985 34.125 53.9406 34.125 52.875V51.2413C34.125 50.978 33.9485 50.7495 33.6953 50.6774C30.1759 49.6766 27.5861 46.4454 27.5626 42.6149C27.5564 41.5924 28.3603 40.7176 29.3824 40.6883C30.4431 40.6578 31.3125 41.5086 31.3125 42.5625C31.3125 45.258 33.5996 47.4295 36.335 47.2383C38.6441 47.0769 40.5145 45.2064 40.6758 42.8973C40.8668 40.1618 38.6954 37.875 36 37.875C31.3475 37.875 27.5625 34.09 27.5625 29.4375C27.5625 25.5847 30.159 22.329 33.6935 21.3231C33.9475 21.2508 34.125 21.0224 34.125 20.7584V19.178C34.125 18.1683 34.9009 17.2996 35.9094 17.2521C36.9862 17.2015 37.875 18.0594 37.875 19.125V20.7587C37.875 21.022 38.0515 21.2505 38.3047 21.3226C41.8241 22.3234 44.4139 25.5546 44.4374 29.3851C44.4436 30.4076 43.6397 31.2824 42.6176 31.3117C41.5569 31.3421 40.6875 30.4914 40.6875 29.4375C40.6875 26.742 38.4004 24.5705 35.665 24.7617C33.3559 24.9231 31.4855 26.7936 31.3242 29.1027C31.1332 31.8382 33.3046 34.125 36 34.125Z"
      fill="#FFB800"
    />
  </svg>
);
