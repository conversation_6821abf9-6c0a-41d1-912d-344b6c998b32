import debounce from "lodash/debounce";
import React, { useEffect, useMemo, useState } from "react";

import { Getcountries, GetUserLocation } from "@/api/user";

import styles from "./index.module.css";

interface Country {
  name: string;
  dial_code: string;
  iso2: string;
}

interface CountryCode {
  onChangeCountryCode: (countryCode: string, countryName: string) => void;
  countryDialCode?: string;
  countryFlagName?: string;
}

const CountryCodeSelection = ({
  onChangeCountryCode,
  countryDialCode,
  countryFlagName,
}: CountryCode) => {
  const [countryValues, setCountryValues] = useState({
    countryCode: "",
    countryName: "",
  });
  const [countryList, setCountryList] = useState<Country[]>([]);
  const [filteredOptions, setFilteredOptions] = useState<Country[]>([]);
  const [inputValue, setInputValue] = useState(countryDialCode || "");
  const [error, setError] = useState("");

  useEffect(() => {
    const getCountryInfo = async () => {
      const { data: countries } = await Getcountries();
      setCountryList(countries);

      if (countryDialCode && countryFlagName) {
        setCountryValues({
          countryCode: countryDialCode,
          countryName: countryFlagName,
        });
      } else {
        const { data: location } = await GetUserLocation();
        const country = countries.find(
          (res: any) => res.iso2 === location.countryCode
        );

        if (country) {
          setCountryValues({
            countryName: location.countryCode,
            countryCode: country.dial_code,
          });
          setInputValue(country.dial_code);
          onChangeCountryCode(country.dial_code, location.countryCode);
        }
      }
    };

    getCountryInfo();
  }, []);

  const debouncedFilterCountries = useMemo(
    () =>
      debounce((value: string) => {
        const filtered = countryList.filter(
          (country) =>
            country.name.toLowerCase().includes(value.toLowerCase()) ||
            country.dial_code.includes(value)
        );

        const isValid = filtered.some(
          (country: any) => country.dial_code === value
        );

        if (value === "+44") {
          const country: any = filtered.find((res: any) => res.iso2 === "GB");
          setCountryValues({
            countryCode: country.dial_code,
            countryName: country.iso2,
          });
          setFilteredOptions(filtered.reverse());
          onChangeCountryCode(country.dial_code, country.iso2);
        } else if (value === "+1") {
          const country: any = filtered.find((res: any) => res.iso2 === "US");
          setCountryValues({
            countryCode: country.dial_code,
            countryName: country.iso2,
          });
          setFilteredOptions(filtered.reverse());
          onChangeCountryCode(country.dial_code, country.iso2);
        } else {
          if (isValid) {
            const country: any = filtered[0];
            setCountryValues({
              countryCode: country.dial_code,
              countryName: country.iso2,
            });
            setFilteredOptions(filtered);
            onChangeCountryCode(country.dial_code, country.iso2);
          }
        }

        // setError(isValid ? "" : "Invalid code");
      }, 300),
    [countryList]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    if (!value.startsWith("+")) {
      value = `+${value}`;
    }

    setInputValue(value);

    if (value) {
      debouncedFilterCountries(value);
    } else {
      setFilteredOptions([]);
      setError("");
    }
  };

  const handleOptionClick = (country: Country) => {
    setCountryValues({
      countryCode: country.dial_code,
      countryName: country.iso2,
    });
    setInputValue(country.dial_code);
    setFilteredOptions([]);
    setError("");
    onChangeCountryCode(country.dial_code, country.iso2);
  };

  const handleOnSelect = () => {
    setFilteredOptions(countryList);
  };

  return (
    <div
      className="d-flex align-items-center bg-cream color-dark border-0 p-2 pe-0 gap-2 fs-7"
      style={{ borderRadius: "8px 0 0 8px" }}
    >
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src={`https://flagcdn.com/24x18/${countryValues.countryName.toLowerCase()}.png`}
        alt=""
        id="c-flag"
      />
      <div className={styles.dropdown}>
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          style={{ width: `${inputValue.length}ch` }}
          className="border-0 bg-cream"
          placeholder=""
          maxLength={5}
          onFocus={handleOnSelect}
          onBlur={() => {
            setTimeout(() => {
              setFilteredOptions([]);
            }, 200);
          }}
        />
        {filteredOptions.length > 0 && (
          <ul>
            {filteredOptions.map((country, index) => (
              <li key={index} onClick={() => handleOptionClick(country)}>
                <p className="m-0">{country.name}</p>
                <p className="m-0 fs-8 color-medium">{country.dial_code}</p>
              </li>
            ))}
          </ul>
        )}
      </div>
      {error && <span className={styles.errorMsg}>{error}</span>}
    </div>
  );
};

export default CountryCodeSelection;
