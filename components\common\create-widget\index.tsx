/* eslint-disable @next/next/no-img-element */
import { usePathname, useRouter } from "next/navigation";
import { memo, useState } from "react";

import { useAppSelector } from "@/redux-store/hooks";

import CreatePostModal from "../header/create-post-modal";
import PostTypeSelection from "../header/post-type-selection";
import SliderDiv from "../slider-div";
import styles from "./index.module.scss";

const CreateWidget = () => {
  const role = useAppSelector((state) => state.user.role);
  const isMobile = window.innerWidth < 767;
  const [isVisible, setIsVisible] = useState(false);
  const params = usePathname();
  const router = useRouter();

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  const handleClick = () => {
    if (isMobile && role === "creator") {
      toggleVisibility();
    } else if (role === "user") {
      router.push("/create/new-post");
    }
  };

  if (role === "guest" || params.includes("new-story")) return null;

  return (
    <div className="container-xxl d-flex justify-content-end">
      <div
        className={`position-fixed bg-blue text-white p-2  w-fit rounded-5 d-flex align-items-center gap-1 pointer ${
          styles.createWidget
        } ${isMobile ? "" : "pe-3"}`}
        data-bs-toggle="modal"
        data-bs-target={role === "creator" && !isMobile ? "#createPost" : ""}
        data-toggle="tooltip"
        onClick={() => handleClick()}
      >
        <img
          src="/images/header-footer/plus-white.svg"
          alt=""
          width={32}
          height={32}
        />
        {!isMobile && <span className="fw-semibold">Create</span>}
      </div>

      <CreatePostModal />
      <SliderDiv
        title="Create Content"
        isVisible={isVisible}
        toggleVisibility={toggleVisibility}
        child={<PostTypeSelection toggleVisibility={toggleVisibility} />}
      />
    </div>
  );
};

export default memo(CreateWidget);
