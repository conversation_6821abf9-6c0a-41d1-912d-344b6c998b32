import type { PossibleValues } from "@/components/common/select-array-v2";
import { type Media } from "@/types/media";
import { type Author } from "@/types/post";
import type {
  Channel,
  SocialMedia,
  SubscriptionData,
  SubscriptionPerks,
} from "@/types/profile";
import type { GetServiceResponse } from "@/types/services";

import API from ".";

interface ChannelSubmitResponse {
  data: "success";
}

export interface SubscriptionSchema {
  _id?: string;

  offer_type: "FEE" | "FREE_TRIAL" | "FREE";
  trial_period: number;
  validity_type: "YEARLY" | "QUARTERLY" | "HALF_YEARLY" | "MONTHLY";
  price: number;
  is_active: boolean;
  is_subscribed?: boolean;
}

const ChannelToAPIChannelTransformer = (body: Channel) => {
  const form = new FormData();

  form.append("name", body.name);
  form.append("channel_tagname", body.username.trim());
  form.append("channel_type", body.type);
  form.append("social_handles", JSON.stringify(body.social_media));
  form.append("about", body.description);
  form.append("hashtags", JSON.stringify(body.hashtags));
  form.append("avatar", body.avatar);
  form.append("background", body.background);
  body.services && form.append("services", JSON.stringify(body.services));
  body.free_services &&
    form.append("free_services", JSON.stringify(body.free_services));
  // Fix Its only for now will be changed in the future
  const subscriptions: SubscriptionSchema[] = [];

  body.subscriptions.array.forEach((sub: any) => {
    subscriptions.push({
      offer_type: body.subscriptions.offer_type,
      trial_period: parseInt(body.subscriptions.trial_period),
      validity_type: sub.subscription_type,
      price: parseFloat(`${sub.price}`),
      is_active: true,
    });
  });

  form.append("subscription", JSON.stringify(subscriptions));

  form.append("perks", JSON.stringify(body.perks));

  return form;
};

export function CreateNewChannel(body: Channel) {
  const form = ChannelToAPIChannelTransformer(body);

  return API.post(API.CHANNEL, form) as Promise<ChannelSubmitResponse>;
}

export interface AvatarBackground {
  avatar: File | string;
  background: File | string;
}

const AvatarAndBgTransformer = (body: AvatarBackground) => {
  const form = new FormData();

  form.append("avatar", body.avatar);
  form.append("background", body.background);

  return form;
};

export function UpdateGroupAvatarBackground(
  body: AvatarBackground,
  id: string
) {
  const form = AvatarAndBgTransformer(body);
  return API.patch(
    `${API.CHANNEL}/${id}/avatar-and-background`,
    form
  ) as Promise<ChannelSubmitResponse>;
}

export interface UpdateChannelBody {
  about: string;
  hashtags: string[];
  social_handles: SocialMedia[];
  perks: { type: PossibleValues; description: string; emoji?: string }[];
  services?: string[];
}

// FIXME: Add typings for the body
export function UpdateChannel(body: any, id: string) {
  return API.put(
    `${API.CHANNEL}/${id}`,
    body
  ) as Promise<ChannelSubmitResponse>;
}

export interface APIChannel {
  _id: string;
  author: Author;
  owner: string;
  name: string;
  channel_tagname: string;
  channel_type: string;
  about: string;
  social_handles: SocialMedia[];
  hashtags: string[];
  is_public: boolean;
  is_deleted: boolean;
  is_subscribed: boolean;

  avatar: Media[];
  background: Media[];
  my_subscription_data: SubscriptionData;

  counter: {
    post_count: number;
    private_post_count: number;
    scheduled_post_count: number;
    media_count: {
      image_count: number;
      video_count: number;
      private_image_count: number;
      private_video_count: number;
      scheduled_image_count: number;
      scheduled_video_count: number;
    };
    subscriber_count: number;
    follower_count: number;

    private_media_count: number;
  };
  subscription: SubscriptionSchema[];
  perks: SubscriptionPerks[];
  created_at: string;
  updated_at: string;
  __v: number;
  marked_as_deleted?: boolean;
  services?: GetServiceResponse[];
  free_services?: any[];
  scheduled_deletion_date?: string;
  totalLikes: number;
}

interface GetChannelListResponse {
  data: { channels: APIChannel[] };
}

export const GetChannelList = async (
  userId?: string,
  page?: number,
  limit?: number
) =>
  API.get(
    API.CHANNEL +
      (userId ? `?target_user=${userId}` : "") +
      (page ? `&page=${page}` : "") +
      (limit ? `&limit=${limit}` : "")
  ) as Promise<GetChannelListResponse>;

interface GetChannelByUsernameResponse {
  data: APIChannel;
}

export const GetChannelByUsername = async (channelId: string) =>
  API.get(
    `${API.CHANNEL}/${channelId}`
  ) as Promise<GetChannelByUsernameResponse>;

export const GetChannelSubscibers = async (channelId: string) =>
  API.get(`${API.CHANNEL}/${channelId}/subscribers`) as Promise<any>;

export const EditChannelTags = async (channelId: string, body: any) =>
  API.patch(`${API.CHANNEL}/${channelId}/update-tags`, body) as Promise<any>;

export const DeleteChannel = async (channelId: string) =>
  API.delete(`${API.CHANNEL}/${channelId}`) as Promise<any>;
