"use client";

import "../verify/index.scss";
import styles from "./page.module.css";

import { useState } from "react";

import * as Yup from "yup";
import { Field, Form, Formik } from "formik";

import { UserBecomeCreator, UserBecomeCreatorBody } from "@/api/user";

import Input from "@/components/common/input";
import Button from "@/components/common/button";
import SetupYourChatfee from "@/components/settings/become-creator-components/setup-your-chatfee";


export default function BecomeACreator() {
  const [isFormFilled, setIsFormFilled] = useState(false);

  const [tab, setTab] = useState(false);

  const handleYourInfoClick = () => {
    setTimeout(() => {
      setTab(!tab);
    }, 500);
  };

  const initialValues = {
    type: "BECOME_CREATOR",
    body: {
      country: "",
      tax_number: "",
      insurance_number: "",
      social_handles: [{ platform: "facebook", handle: "john@123" }],
      reason: "",
    },

    tnc_accepted: true,
  };

  const validationSchema = Yup.object().shape({
    country: Yup.string()
      .min(3, "Country must be at least 3 characters")
      .required("Country is required"),
    tax_number: Yup.string()
      .min(8, "Tax number must be at least 8 characters")
      .required("Tax number is required"),
    insurance_number: Yup.string()
      .min(8, "Insurance number must be at least 8 characters")
      .required("Insurance number is required"),

    reason: Yup.string().min(4, "reason must be at least 4 characters"),
  });

  const onSubmit = (
    values: UserBecomeCreatorBody["body"],
    { setSubmitting, setErrors }: any
  ) => {
    UserBecomeCreator({
      type: "BECOME_CREATOR",
      body: values,
      tnc_accepted: true,
    })
      .then(() => {
        setSubmitting(true);
      })
      .catch((err) => {
        setSubmitting(false);
        setErrors({ response: err.message });
      });
    setIsFormFilled(true);
  };

  return (
    <section className={`container-xxl bd-gutter g-lg-4 g-0 my-3 `}>
      <div className="d-flex row align-items-start">
        <div
          className={
            "nav flex-column col-3 nav-pills setting-sideBar bg-body py-4 pr-3 rounded-3 "
          }
          id="v-pills-tab"
          role="tablist"
          aria-orientation="vertical"
        >
          <h5 className="ms-4 mb-4 fw-semibold">Become a Creator</h5>

          <button
            className={
              tab ? "nav-link color-bold" : "nav-link color-bold  active"
            }
            id="v-pills-home-tab"
            data-bs-toggle="pill"
            data-bs-target="#v-pills-home"
            type="button"
            role="tab"
            aria-controls="v-pills-home"
            aria-selected={tab ? false : true}
          >
            <div className="d-flex">
              <span>Step 1: </span>
              <span>Your tax Informations</span>
            </div>
          </button>
          {/* <button
            className={
              tab ? "nav-link color-bold active" : "nav-link color-bold"
            }
            id="v-pills-profile-tab"
            data-bs-toggle="pill"
            data-bs-target="#v-pills-profile"
            type="button"
            role="tab"
            aria-controls="v-pills-profile"
            aria-selected={tab ? true : false}
          >
            <div className="d-flex">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M12.6319 1.50001H11.3643C10.65 1.50001 9.9664 1.78561 9.46244 2.29276C9.01836 2.73826 8.7446 3.33152 8.6906 3.96541L8.68137 4.16911C8.67731 4.48483 8.55136 4.7861 8.33101 5.0072C8.11066 5.2283 7.81378 5.35127 7.50513 5.34931C7.3546 5.34685 7.20615 5.3129 7.06892 5.24956L6.93138 5.17606C6.31451 4.82705 5.58916 4.73709 4.90864 4.9252C4.22813 5.1133 3.64576 5.56472 3.28465 6.18406L2.6175 7.28236C2.27701 7.86843 2.16866 8.56557 2.31466 9.23077C2.46067 9.89598 2.84996 10.4788 3.40268 10.8597L3.56177 10.9626C3.95385 11.1894 4.1776 11.579 4.1776 12C4.1776 12.3875 3.98875 12.7497 3.67159 12.9692L3.56382 13.0374C2.99148 13.3696 2.56395 13.9119 2.36785 14.5545C2.17175 15.1971 2.22173 15.8919 2.50768 16.4982L2.59492 16.6662L3.24872 17.7981C3.60029 18.4242 4.17951 18.8828 4.86014 19.0739C5.47187 19.2471 6.12259 19.1999 6.70147 18.9384L6.88417 18.8471C7.19209 18.6686 7.50924 18.6266 7.80791 18.7064C8.10557 18.7861 8.35908 18.9825 8.51304 19.2513C8.5972 19.3941 8.64647 19.5411 8.66494 19.6944L8.67316 19.8488C8.67213 21.2925 9.8771 22.5 11.3643 22.5H12.6319C14.037 22.5 15.202 21.4185 15.3159 20.0231L15.3241 19.8435C15.3205 19.6843 15.3483 19.5261 15.4058 19.3782C15.4634 19.2303 15.5495 19.0957 15.659 18.9826C15.7685 18.8695 15.8992 18.7801 16.0432 18.7199C16.1872 18.6596 16.3417 18.6296 16.4973 18.6318C16.641 18.636 16.798 18.6717 16.9437 18.7368L17.0833 18.8093C18.2616 19.4918 19.8063 19.1411 20.6018 17.9945L20.7044 17.8349L21.3808 16.704C21.5602 16.3984 21.6774 16.0588 21.7254 15.7057C21.7733 15.3526 21.7511 14.9932 21.66 14.6492C21.4947 14.0334 21.1207 13.4978 20.6049 13.1382L20.4334 13.028C20.2948 12.952 20.1726 12.8482 20.074 12.7228C19.9754 12.5974 19.9025 12.453 19.8597 12.2982C19.8201 12.1494 19.8102 11.9939 19.8307 11.8411C19.8512 11.6883 19.9015 11.5413 19.9788 11.4089C20.065 11.2577 20.181 11.1317 20.3185 11.0351L20.5823 10.8681C21.1039 10.5129 21.4828 9.97712 21.6494 9.35917C21.8161 8.74122 21.7593 8.08251 21.4896 7.50391L21.4311 7.38736C21.4183 7.35664 21.4036 7.32682 21.387 7.29811L20.7578 6.20296C20.0578 4.98496 18.5439 4.51246 17.2814 5.10781L17.1131 5.19391C16.8372 5.36442 16.5065 5.41614 16.1935 5.33776C15.8954 5.26126 15.6389 5.06725 15.4801 4.79806C15.4008 4.665 15.3504 4.51602 15.3323 4.36126L15.3231 4.20691C15.3457 3.54751 15.0716 2.84086 14.5636 2.31691C14.3102 2.05716 14.009 1.85126 13.6775 1.71106C13.346 1.57086 12.9906 1.49913 12.6319 1.50001ZM11.3643 3.02041H12.6319C12.9501 3.02041 13.2549 3.14851 13.4766 3.37741C13.6983 3.60526 13.8174 3.91396 13.8081 4.23211L13.8225 4.49776C13.8677 4.90201 13.9867 5.25376 14.1735 5.56981C14.5328 6.18301 15.1219 6.63031 15.8116 6.80881C16.5009 6.98849 17.2315 6.88603 17.848 6.52321L17.9547 6.46966L18.0676 6.42346C18.3228 6.34156 18.5976 6.35019 18.8474 6.44795C19.0972 6.54571 19.3074 6.72688 19.444 6.96211L20.0444 8.00686L20.0568 8.03626L20.1296 8.17696C20.3729 8.69776 20.1707 9.35715 19.6442 9.6648L19.4799 9.7698C19.1289 10.0124 18.861 10.3064 18.666 10.6487C18.4892 10.9528 18.374 11.2903 18.3273 11.641C18.2805 11.9916 18.3032 12.3483 18.394 12.6899C18.5629 13.3203 18.9504 13.8664 19.484 14.226L19.7632 14.4024C19.9929 14.571 20.1523 14.8216 20.2101 15.1047C20.2679 15.3877 20.2197 15.6827 20.0752 15.9312L19.4142 17.0358L19.3393 17.1513C19.0335 17.5923 18.4299 17.7551 17.9209 17.5314L17.5965 17.3655C17.2578 17.2093 16.8922 17.1233 16.5209 17.1125C15.8022 17.1043 15.1096 17.3874 14.5944 17.9C14.3447 18.1489 14.1464 18.4466 14.0114 18.7754C13.8763 19.1042 13.8072 19.4574 13.8081 19.8141L13.803 19.9296C13.7548 20.5071 13.2457 20.9796 12.6329 20.9796H11.3633C10.7577 20.9796 10.2579 20.5197 10.1943 19.9296L10.1737 19.5579C10.1119 19.0123 9.89003 18.4986 9.53737 18.0844C9.18471 17.6702 8.71776 17.3749 8.19794 17.2374C7.50945 17.053 6.77772 17.1517 6.15954 17.5125L6.04767 17.5671C5.80186 17.6703 5.52927 17.6858 5.27378 17.6112C4.97488 17.5266 4.72096 17.3242 4.56763 17.0484L3.921 15.9323L3.86353 15.8199C3.61925 15.3012 3.82247 14.6418 4.34798 14.3373L4.5081 14.2365C5.25941 13.7157 5.69459 12.8873 5.69459 12C5.69459 11.1002 5.24709 10.2644 4.51015 9.76455L4.24329 9.59445C4.01056 9.42654 3.84824 9.17506 3.7887 8.89015C3.72915 8.60524 3.77676 8.30785 3.92203 8.05726L4.58918 6.95896C4.74697 6.68334 5.00403 6.48193 5.30526 6.39792C5.60649 6.31391 5.92791 6.35399 6.2006 6.50956L6.39869 6.61456C6.76819 6.78466 7.12948 6.86656 7.49692 6.86971C8.91949 6.86971 10.0834 5.77456 10.1891 4.37911L10.2025 4.05886C10.223 3.81736 10.3421 3.56011 10.536 3.36481C10.7557 3.14431 11.0533 3.02041 11.3633 3.02041H11.3643ZM12.0027 8.56861C10.1121 8.56861 8.57873 10.1048 8.57873 12C8.57873 13.8953 10.1121 15.4314 12.0027 15.4314C13.8933 15.4314 15.4268 13.8953 15.4268 12C15.4268 10.1048 13.8933 8.56861 12.0027 8.56861ZM12.0027 10.089C13.0558 10.089 13.9098 10.9448 13.9098 12C13.9098 13.0553 13.0558 13.911 12.0027 13.911C10.9497 13.911 10.0957 13.0553 10.0957 12C10.0957 10.9448 10.9497 10.089 12.0027 10.089Z"
                  fill="#4D5053"
                />
              </svg>
              <span>Setup your subscribe</span>
            </div>
          </button> */}

          <button
            className={
              tab ? "nav-link color-bold active" : "nav-link color-bold"
            }
            id="v-pills-chat-tab"
            data-bs-toggle="pill"
            data-bs-target="#v-pills-chat"
            type="button"
            role="tab"
            aria-controls="v-pills-chat"
            aria-selected={false}
          >
            <div className="d-flex">
              <span> Step 2:</span>
              <span>Setup your chat fee</span>
            </div>
          </button>
        </div>
        <div className="tab-content col-9 " id="v-pills-tabContent">
          <div
            className={tab ? "tab-pane fade" : "tab-pane fade show active"}
            id="v-pills-home"
            role="tabpanel"
            aria-labelledby="v-pills-home-tab"
          >
            <div className="bg-body rounded-3 p-3">
              <div className="pb-4 border-bottom">
                <span className="fs-5 fw-semibold d-block mb-2">
                  Step 1: Your tax informations
                </span>

                <span className="fw-normal color-medium fs-6 ">
                  You must use your full, real informations exactly as displayed
                  on your passport, driving license or goverment issued Identity
                  Card. This is used for admin purposes only and is not shown
                  publicly.
                </span>
              </div>
              <Formik
                initialValues={initialValues["body"]}
                onSubmit={onSubmit}
                validationSchema={validationSchema}
              >
                {({ isSubmitting, isValid, dirty, setFieldValue }) => (
                  <Form action="">
                    <div className={"mt-3 w-50 pb-1 " + styles.form}>
                      <div className={styles.info + "  mb-4"}>
                        <span
                          className={styles.question + " " + styles.required}
                        >
                          Which country is this entity from?
                        </span>
                        {/* <CountrySelection
                          currentCountry="gb:United Kingdom"
                          onCountryChange={(country: string) => {
                            setFieldValue("country", country);
                          }}
                        /> */}
                      </div>

                      <div className={styles.info + "  mb-4"}>
                        <Input
                          type="text"
                          name="tax_number"
                          label="Tax number"
                          placeholder="********"
                        ></Input>
                      </div>

                      <div className={styles.info + "  mb-4"}>
                        {/* <label
                          htmlFor="input2"
                          className={
                            "form-label " +
                            styles.question +
                            " " +
                            styles.required
                          }
                        >
                          Insurance Number
                        </label> */}
                        <Input
                          type="text"
                          name="insurance_number"
                          label="Insurance Number"
                          placeholder="********"
                        ></Input>
                      </div>

                      <div className={styles.info + "  mb-4"}>
                        <label
                          htmlFor="exampleFormControlTextarea1"
                          className={"form-label " + styles.question}
                        >
                          List other social media presence accounts (optional)
                        </label>
                        <textarea
                          className={"form-control " + styles.input}
                          placeholder="Example: facebook, instagram,..."
                          id="exampleFormControlTextarea1"
                          rows={5}
                        ></textarea>
                      </div>
                      <div className={styles.info + "  mb-4"}>
                        <label
                          htmlFor="exampleFormControlTextarea2"
                          className={"form-label " + styles.question}
                        >
                          Additional comments (optional)
                        </label>
                        <Field
                          as="textarea"
                          className={"form-control " + styles.input}
                          placeholder="Enter Your Comment here"
                          id="exampleFormControlTextarea2"
                          rows={5}
                          name="reason"
                        ></Field>
                      </div>
                    </div>

                    <div
                      className="pt-2 border-top"
                      onClick={handleYourInfoClick}
                    >
                      <span className="fw-normal color-medium fs-7 d-block mb-3">
                        Lorem Ipsum is simply dummy text of the printing and
                        typesetting industry. Lorem Ipsum has been the
                        industry&apos;s standard dummy text ever.
                      </span>

                      <Button
                        isValid={isValid}
                        dirty={dirty}
                        // className="btn btn-purple mt-3 w-25"
                        isSubmitting={isSubmitting}
                        text="Next Step"
                      />
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
          {/* <div
            className={tab ? "tab-pane fade show active" : "tab-pane fade "}
            id="v-pills-profile"
            role="tabpanel"
            aria-labelledby="v-pills-profile-tab"
          >
            <SetupYourSubscribe />
          </div> */}
          <div
            className={tab ? "tab-pane fade show active" : "tab-pane fade "}
            id="v-pills-chat"
            role="tabpanel"
            aria-labelledby="v-pills-chat-tab"
          >
            <SetupYourChatfee isFormFill={isFormFilled} />
          </div>
        </div>
      </div>
    </section>
  );
}
