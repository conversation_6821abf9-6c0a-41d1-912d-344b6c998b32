import classNames from "classnames";
import Image from "next/image";
import { memo } from "react";

import { chatActions } from "@/redux-store/actions";
import type { Chat } from "@/redux-store/slices/chat.slice";
import { getAssetUrl } from "@/utils/assets";
import type { IChatSocket } from "@/utils/chatSocket";

import { LastSeen } from "../../LastSeen";
import { OnlineDot } from "../../OnlineDot";
import Badges from "../../badges";
import PostActionSendTip from "../../buttons/send-tip";
import { BlockBox, OptionIcon } from "../chat-box/utils/svg-utils";

interface ChatPersonUnavailableProps {
  handleChatClick: () => void;
  setChatBoxDisable: (state: boolean) => void;
  openOptions: boolean;
  extraOptionRef: React.RefObject<HTMLDivElement>;
}
interface ChatPersonProps {
  chat: Chat;
  user: any;
  targetUser: string;
  handleChatClick: () => void;
  setChatBoxDisable: (state: boolean) => void;
  socketChannel: IChatSocket;
  router: { push: (path: string) => void };
  dispatch: (action: any) => void;
  hasBlockedMe: Record<string, boolean>;
  blockedUser: Record<string, boolean>;
  setOpenOptions: (state: boolean) => void;
  handleDeleteChannel: (channelId: string) => void;
  handleBlockUser: () => void;
}

const ChatPersonUnavailableBase: React.FC<ChatPersonUnavailableProps> = ({
  handleChatClick,
  setChatBoxDisable,
  openOptions,
  extraOptionRef,
}) => {
  return (
    <div className="chat-person d-flex pointer align-items-center gap-3 w-100 position-relative invisible">
      {/* Back Arrow for mobile */}
      <div className="d-block d-md-none">
        <Image
          onClick={() => {
            handleChatClick();

            if (window.innerWidth >= 768) {
              setChatBoxDisable(false);
            }
          }}
          src="/settings/backArrow.png"
          alt="Back Arrow"
          width={24}
          height={16}
        />
      </div>

      {/* User details */}
      <div className="d-flex pointer align-items-center">
        <Image
          width={48}
          height={48}
          className="rounded-pill object-fit-cover"
          alt="User Avatar"
          src="/images/common/defaultBack.png"
        />
        <div className="person-details ms-2">
          <p className="mb-0 d-flex gap-1 align-items-center">
            <span>knky_user</span>
          </p>
        </div>
      </div>

      {/* Options dropdown */}
      <div
        className="d-flex justify-content-end align-items-center gap-3"
        style={{ flex: 1 }}
      >
        {openOptions && (
          <div
            className="position-absolute"
            ref={extraOptionRef}
            style={{ right: -110, top: 30, zIndex: 10 }}
          >
            <BlockBox />
          </div>
        )}
      </div>
    </div>
  );
};

export const ChatPersonUnavailable = memo(ChatPersonUnavailableBase);

const ChatPersonBase: React.FC<ChatPersonProps> = ({
  chat,
  user,
  targetUser,
  handleChatClick,
  setChatBoxDisable,
  socketChannel,
  router,
  dispatch,
  hasBlockedMe,
  blockedUser,
  setOpenOptions,
  handleDeleteChannel,
  handleBlockUser,
}) => {
  const isUserCreator =
    user.id === chat?.target?._id
      ? chat?.initiator?.user_type === "CREATOR"
      : chat?.target?.user_type === "CREATOR";

  const avatarUser: any =
    user.id === chat?.target?._id ? chat?.initiator : chat?.target;

  const onAvatarClick = () => {
    if (hasBlockedMe[targetUser]) return;
    router.push(
      `/${avatarUser?.user_type?.toLowerCase()}/${avatarUser?.username}`
    );
  };

  return (
    <div className="chat-person d-flex align-items-center gap-3 w-100 position-relative justify-content-between">
      <div className="d-flex gap-2 align-items-center">
        <BackArrow
          handleChatClick={handleChatClick}
          setChatBoxDisable={setChatBoxDisable}
          socketChannel={socketChannel}
          dispatch={dispatch}
        />

        <UserDetails
          avatarUser={avatarUser}
          onAvatarClick={onAvatarClick}
          user={user}
          chat={chat}
          hasBlockedMe={hasBlockedMe}
        />
      </div>

      <div className="d-flex justify-content-end align-items-center gap-3">
        {isUserCreator && (
          <PostActionSendTip
            onlyIcon={window.innerWidth < 768}
            postId={avatarUser?.username}
            author={avatarUser}
            cls={classNames({ "pe-none": hasBlockedMe[targetUser] })}
            type="user-profile"
            source="chat"
          />
        )}
        <OptionsDropdown
          setOpenOptions={setOpenOptions}
          handleDeleteChannel={handleDeleteChannel}
          handleBlockUser={handleBlockUser}
          blockedUser={blockedUser}
          targetUser={targetUser}
          channelId={chat.converse_channel_id}
        />
      </div>
    </div>
  );
};

export const ChatPerson = memo(ChatPersonBase);

const BackArrowBase = ({
  handleChatClick,
  setChatBoxDisable,
  socketChannel,
  dispatch,
}: {
  handleChatClick: () => void;
  setChatBoxDisable: (value: boolean) => void;
  socketChannel: IChatSocket;
  dispatch: Function;
}) => (
  <div className="d-block d-md-none">
    <Image
      onClick={() => {
        handleChatClick();

        if (window.innerWidth >= 768) {
          setChatBoxDisable(false);
        }

        dispatch(chatActions.setChannelId(""));
        dispatch(chatActions.setTargetUser(""));
        socketChannel.closeChannel();
      }}
      src="/settings/backArrow.png"
      alt="Back"
      width={24}
      height={16}
    />
  </div>
);
const BackArrow = memo(BackArrowBase);

const UserDetailsBase = ({
  avatarUser,
  onAvatarClick,
  user,
  chat,
  hasBlockedMe,
}: {
  avatarUser: any;
  onAvatarClick(): void;
  user: any;
  chat: Chat;
  hasBlockedMe: Record<string, boolean>;
}) => (
  <div
    className={classNames("d-flex align-items-center", {
      pointer: !hasBlockedMe[avatarUser?._id],
    })}
    onClick={onAvatarClick}
  >
    <div className="position-relative">
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        width={48}
        height={48}
        className="rounded-pill object-fit-cover"
        alt=""
        src={getAssetUrl({
          media: avatarUser?.avatar?.[0],
          defaultType: "avatar",
        })}
      />
      <OnlineDot
        userId={
          user.id === chat?.target?._id
            ? chat?.initiator?._id
            : chat?.target?._id
        }
        style={{ position: "absolute", right: 0, bottom: "-6%" }}
      />
    </div>
    <div className="person-details ms-2 d-flex flex-column">
      <UserNameAndBadges avatarUser={avatarUser} chat={chat} user={user} />
      <div className="fs-8 color-medium">
        <LastSeen
          userId={
            user.id === chat?.target?._id
              ? chat?.initiator?._id
              : chat?.target?._id
          }
        />
      </div>
    </div>
  </div>
);

const UserDetails = memo(UserDetailsBase);

const UserNameAndBadgesBase = ({
  avatarUser,
  chat,
  user,
}: {
  avatarUser: any;
  chat: Chat;
  user: any;
}) => (
  <div className="d-flex align-items-center">
    <p className="mb-0 d-flex gap-1 align-items-center">
      <span className="profile-user-name">{avatarUser?.display_name}</span>
      <span>
        <Badges
          array={
            user.id === chat?.target?._id
              ? chat?.initiator?.badges
              : chat?.target?.badges
          }
        />
      </span>
    </p>
  </div>
);
const UserNameAndBadges = memo(UserNameAndBadgesBase);

const OptionsDropdownBase = ({
  setOpenOptions,
  handleDeleteChannel,
  blockedUser,
  targetUser,
  handleBlockUser,
  channelId,
}: any) => (
  <>
    <span
      className="pointer"
      onClick={() => setOpenOptions(true)}
      data-bs-toggle="dropdown"
      aria-expanded="false"
    >
      <OptionIcon />
    </span>
    <ul className="dropdown-menu">
      <li
        className="fw-bold d-flex align-items-center gap-1 dropdown-item rounded pointer"
        onClick={() => handleDeleteChannel(channelId)}
      >
        <Image
          src={"/images/chat/delete.svg"}
          alt="Delete"
          height={24}
          width={24}
        />
        Delete Chat
      </li>
      {!blockedUser[targetUser] && (
        <li
          className="fw-bold d-flex align-items-center gap-1 dropdown-item rounded pointer"
          onClick={handleBlockUser}
        >
          <Image
            src={"/images/chat/block.svg"}
            alt="Block"
            height={24}
            width={24}
          />
          Block
        </li>
      )}
    </ul>
  </>
);
const OptionsDropdown = memo(OptionsDropdownBase);
