import API from ".";

export interface PremiumPostBody {
  post_id: string;
}
export interface WebHookBody {
  checkout_ref_id: string;
}

export interface PremiumPostResponse {
  message: "Success";
  status: 201;
  data: {
    order_id: string;
    checkout_url: string;
    checkout_ref_id: string;
  };
}

export interface SendTipBody {
  amount: number;
  tokenised_card_id?: string;
  payment_mode: string;
  source?: string;
}
export interface BuyPremiumPostBody {
  tokenised_card_id?: string;
  payment_mode: string;
}

export const PremiumPostPurchase = async (body: PremiumPostBody) =>
  API.post(`${API.SUBSCRIPTIONS}/post`, body) as Promise<PremiumPostResponse>;

export const WebHook = async (body: WebHookBody) =>
  API.post(`${API.SUBSCRIPTIONS}/webhook`, body) as Promise<any>;

export const SendPostTip = async (id: string, body: SendTipBody) =>
  API.post(`${API.POST}/${id}/tip`, body) as Promise<any>;

export const SendStoryTip = async (id: string, body: SendTipBody) =>
  API.post(`${API.STORY}/${id}/tip`, body) as Promise<any>;

export const SendChannelTip = async (id: string, body: SendTipBody) =>
  API.post(`${API.CHANNEL}/${id}/tip`, body) as Promise<any>;

export const SendUserTip = async (username: string, body: SendTipBody) =>
  API.post(`${API.USERS}/profile/${username}/tip`, body) as Promise<any>;

export const SendEventTip = async (id: string, body: SendTipBody) =>
  API.post(`${API.EVENT}/${id}/tip`, body) as Promise<any>;

export const BuyPremiumPost = async (id: string, body: BuyPremiumPostBody) =>
  API.post(`${API.POST}/${id}/buy-premium-post`, body) as Promise<any>;

export const BuyPremiumStroy = async (id: string, body: BuyPremiumPostBody) =>
  API.post(`${API.STORY}/${id}/buy-premium-story`, body) as Promise<any>;

export const SubscribeChannel = async (
  channelId: string,
  packageId: string,
  body: BuyPremiumPostBody
) =>
  API.post(
    `${API.CHANNEL}/${channelId}/subscribe/${packageId}`,
    body
  ) as Promise<any>;

export const UnsubscribeChannel = async (
  channelId: string,
  packageId: string,
  body: any
) => API.post(`${API.CHANNEL}/${channelId}/unsubscribe/${packageId}`, body);

export const SubscribeGroup = async (
  groupId: string,
  packageId: string,
  body: BuyPremiumPostBody
) =>
  API.post(
    `${API.GROUP}/${groupId}/subscribe/${packageId}`,
    body
  ) as Promise<any>;

export const UnsubscribeGroup = async (
  groupId: string,
  packageId: string,
  body: any
) => API.post(`${API.GROUP}/${groupId}/unsubscribe/${packageId}`, body);

export const SubscribeMatchPlan = async (
  packageId: string,
  body: BuyPremiumPostBody
) => {
  return API.post(`${API.MATCH}/subscribe/${packageId}`, body) as Promise<any>;
};

export const UnsubscribeMatchPlan = async (packageId: string) => {
  return API.post(
    `${API.MATCH}/subscribe/${packageId}/cancel`,
    {}
  ) as Promise<any>;
};

export const UnlockChat = async (
  targetUser: string,
  body: BuyPremiumPostBody & { duration?: number; service_id: string }
) => {
  return API.post(
    `${API.USERS}/unlock-chat/${targetUser}`,
    body
  ) as Promise<any>;
};

export const getPlatformSubscriptionPlans = async () =>
  API.get(`${API.SYSTEM_DEFAULTS}/knky-plans`);

export const buyPatformSubscriptionPlan = async (
  id: string,
  type: string,
  body: BuyPremiumPostBody
) => API.post(`${API.USERS}/subscription/${type}/${id}`, body);

export const unsubscribePlatformPlan = async (
  id: string,
  planName: string,
  body: any
) => API.post(`${API.USERS}/subscription/${planName}/${id}/cancel`, body);

export const getUserSubscriptions = async () =>
  API.get(`${API.USERS}/subscriptions`);

export const getUserPlatformSubscriptions = async () =>
  API.get(`${API.USERS}/platform/subscriptions`);

export const getGlobalOffers = async () =>
  API.get(`${API.ADMIN}/get-active-offers`);

export const getConsumablePlans = async () =>
  API.get(`${API.USERS}/platform/consumables`);

export const buyConsumablePlans = async (
  consumableId: string,
  packageId: string,
  body: any
) =>
  API.post(
    `${API.USERS}/buy/consumable/${consumableId}/package/${packageId}`,
    body
  );

export const addUserInterest = async (interest: string) =>
  API.post(`${API.USERS}/user-interest?interest=${interest}`, {});

export const getUserInterests = async () =>
  API.get(`${API.USERS}/user-interest`) as Promise<any>;
