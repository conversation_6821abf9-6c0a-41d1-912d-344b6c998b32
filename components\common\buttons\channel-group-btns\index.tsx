import Image from "next/image";
import Link from "next/link";

import type { SubscriptionSchema } from "@/api/channel";
import { createActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";
import type { Subscription } from "@/types/profile";

export function transformSubscriptions(subscriptionData: SubscriptionSchema[]) {
  const subscriptions: Record<string, Subscription> = {};

  subscriptionData.forEach((subscription) => {
    const name = subscription.name;
    const pack = {
      id: subscription._id!,
      subscription_type: subscription.validity_type,
      // price: parseInt(`${subscription.price}`),
      price: Number(subscription.price),

      is_subscribed: subscription?.is_subscribed!,
    };

    if (!subscriptions[name]) {
      subscriptions[name] = {
        name,

        offer_type: subscription.offer_type,
        is_active: subscription.is_active,
        trial_period: `${subscription.trial_period}`,
        array: [],
      };
    }

    subscriptions[name].array.push(pack);
  });

  return Object.values(subscriptions);
}

export function transformApiResponse(apiResponse: any) {
  console.log({ apiResponse });
  const transformedArray = apiResponse.map((item: any) => ({
    _id: item._id,
    subscription_type: item.validity_type,
    price: item.price,
  }));

  const transformedObject = {
    offer_type: apiResponse[0].offer_type,
    trial_period: apiResponse[0].trial_period,
    array: apiResponse?.[0].offer_type === "FREE" ? [] : transformedArray,
    is_active: apiResponse[0].is_active,
    _id: apiResponse[0]._id,
    name: "",
  };

  return transformedObject;
}

interface BtnParams {
  href: string;
  text: string;
  visible?: boolean;
  onClick?: () => void;
  isGroupPost?: boolean;
}

export const CreateBtn = ({ href, text, visible = true }: BtnParams) => {
  return visible ? (
    <Link
      href={href}
      className="btn btn-purple img-purple img-2white on-active-purple w-100 d-flex align-items-center justify-content-center gap-2"
    >
      <Image
        src="/images/post/line-plus-purple.svg"
        width={20}
        height={20}
        alt="edit"
      />
      <span>{text}</span>
    </Link>
  ) : (
    <></>
  );
};

export const EditBtn = ({ href, text, visible = true, onClick }: BtnParams) => {
  const dispatchAction = useAppDispatch();
  return visible ? (
    <Link
      href={href}
      className="btn btn-gray img-purple img-2black on-active-blue w-100 d-flex align-items-center justify-content-center gap-2 mb-1"
      onClick={() => {
        text == "Edit my collab" && dispatchAction(createActions.resetGroup());
        onClick && onClick();
      }}
    >
      <Image
        src="/images/post/line-pencil.svg"
        width={24}
        height={24}
        alt="edit"
      />
      <span>{text} </span>
    </Link>
  ) : (
    <></>
  );
};

export const FollowBtn = ({ text, visible = true }: any) => {
  return visible ? (
    <div
      // href={href}
      className="btn btn-gray img-purple img-2black on-active-blue w-100 d-flex align-items-center justify-content-center gap-2 mb-1"
    >
      <span>{text}</span>
    </div>
  ) : (
    <></>
  );
};
