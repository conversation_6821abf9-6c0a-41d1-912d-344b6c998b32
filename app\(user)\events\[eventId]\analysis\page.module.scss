.endEventWrapper {
  background: rgba(235, 235, 236, 1);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
}
.navTab {
  background-color: rgba(235, 235, 236, 1) !important;
  width: 50%;
  border-radius: 0;
}
.navLink {
  flex-grow: 1;
  color: black;
  border: none;
  border-radius: 0;
}
.navLink.active {
  border-top: 1px solid;
  border-bottom: 0px solid;
  border-left: 0px solid;
  border-right: 0px solid;
  border-color: var(--primary-color) !important;
  background-color: rgb(255, 255, 255) !important;
  border-radius: 0;
  background-color: rgba(255, 255, 255, 1) !important;
}

.overviewCard {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }
}
