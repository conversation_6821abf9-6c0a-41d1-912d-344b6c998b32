.post-comments {
  box-shadow: 0 -1pt 0 0 var(--bg-dark);
}

.post-comments-author {
  .dot {
    height: 0.5rem;
  }
}
.post-img-detail,
.post-video-detail {
  aspect-ratio: 9/5;
  object-fit: contain;
}

.input {
  border: 0;
  background-color: #f5f5f6;
  border-radius: 6px;
  padding: 1.2%;
  width: 100%;
}

.live-details {
  display: grid;
  grid-template-areas:
    "icon time"
    "icon count";
  grid-template-columns: min-content;

  .live-time-icon {
    grid-area: icon;
    width: 3rem;
    height: 3rem;

    @media (max-width: 768px) {
      width: 2rem;
      height: 2rem;
    }
  }

  .live-time {
    grid-area: time;
  }

  .live-ticket-counts {
    grid-area: count;
  }

  @media (max-width: 768px) {
    grid-template-areas:
      "icon time time"
      "count count count";
    grid-template-columns: 2rem;
  }
}

.premium-post {
  filter: blur(30px);
}
.premium-post-desc {
  filter: blur(4px);
}

.premium-post-locked {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.btn-premium {
  color: white;
  background-color: #ffb800;

  font-weight: 400 !important;
  &:hover,
  &.active {
    color: #ffb800;
    box-shadow: 0 0 0 1pt #ffb800;
  }
}
.single-post {
  .swiper-next {
    rotate: -180deg;
  }
  .swiper-prev,
  .swiper-next {
    transform: scale(1.35);
  }
}

.tagged-user-swiper .swiper-slide .rounded-3 .btn {
  min-width: 6rem !important;
}

.swiper {
  width: 100%;
  height: 100%;
}
.carousal-resolution {
  right: 5.8% !important;
}
@media screen and (max-width: 768px) {
  .post-img-detail,
  .post-video-detail {
    aspect-ratio: initial !important;
    // object-fit: contain;
  }

  .tagged-user-swiper .swiper-slide .rounded-3 .btn {
    min-width: 6rem !important;
  }
  .nonLiveStream {
    min-height: 0rem;
  }
  .carousal-resolution {
    right: 11% !important;
  }
}
@media screen and (max-width: 992px) and (min-width: 768px) {
  .carousal-resolution {
    right: 8% !important;
  }
}

.post-desc-ecllips {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  height: calc(1em * 1.2 * 3);
  line-height: 1.2em;
}

.show-resolution {
  position: absolute;
  right: 0px;
  z-index: 1;
  color: aliceblue;
  width: fit-content;
  height: fit-content;
  top: 0px;
}
.video-info-wrapper {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  width: 100%;
  .video-info {
    backdrop-filter: blur(1px);
    border-radius: 0.5em;
    background-color: rgba(0, 0, 0, 0.4);
  }
}
.resolution-text {
  border-radius: 7px;
  padding: 2px 9px;
  background: rgb(0 0 0 / 50%);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(12.9px);
  -webkit-backdrop-filter: blur(12.9px);
  letter-spacing: 1px;
  font-weight: 400;
  color: white;
}
@media screen and (min-width: 768px) {
  .post-aspect-ratio {
    aspect-ratio: 9/5 !important;
  }
}

.warning-effect {
  filter: blur(25px);
}
