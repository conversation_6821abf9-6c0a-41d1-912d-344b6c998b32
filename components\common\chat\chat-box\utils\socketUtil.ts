import type { MessageInterface } from "@/redux-store/slices/chat.slice";

import { updateMessageInArray } from "./messageUtil";

export const listenSocketEditMessage = (
  socketChannelInstance: any,
  dispatch: Function,
  setCurrentChat: Function,
  setPrevChat: Function
) => {
  socketChannelInstance?.channel?.listenEditMessage(
    (message: MessageInterface) => {
      try {
        if (message?.meta?.type === "RATING") {
          if (message?.meta?.requestAccept) {
            socketChannelInstance?.channel
              ?.getMessages({})
              .then((result: any) => {
                const updatedCurrentMessage = updateMessageInArray(
                  result.msgs.unread,
                  message
                );
                const updatedPrevMessage = updateMessageInArray(
                  result.msgs.read,
                  message
                );
                dispatch(setCurrentChat(updatedCurrentMessage));
                dispatch(setPrevChat(updatedPrevMessage));
              });
          } else if (message?.meta?.paid) {
            // window.location.reload();
          } else {
            socketChannelInstance?.channel
              ?.getMessages({})
              .then((result: any) => {
                const updatedCurrentMessage = updateMessageInArray(
                  result.msgs.unread,
                  message
                );
                const updatedPrevMessage = updateMessageInArray(
                  result.msgs.read,
                  message
                );
                dispatch(setCurrentChat(updatedCurrentMessage));
                dispatch(setPrevChat(updatedPrevMessage));
              });
          }
        }

        if (
          message?.meta?.type === "VOICE" ||
          message?.meta?.type === "VIDEO"
        ) {
          if (message?.meta?.requestAccept) {
            socketChannelInstance?.channel
              ?.getMessages({})
              .then((result: any) => {
                const updatedCurrentMessage = updateMessageInArray(
                  result.msgs.unread,
                  message
                );
                const updatedPrevMessage = updateMessageInArray(
                  result.msgs.read,
                  message
                );
                dispatch(setCurrentChat(updatedCurrentMessage));
                dispatch(setPrevChat(updatedPrevMessage));
              });
          } else {
            socketChannelInstance?.channel
              ?.getMessages()
              .then((result: any) => {
                const updatedCurrentMessage = updateMessageInArray(
                  result?.msgs?.unread,
                  message
                );
                const updatedPrevMessage = updateMessageInArray(
                  result?.msgs?.read,
                  message
                );
                dispatch(setCurrentChat(updatedCurrentMessage));
                dispatch(setPrevChat(updatedPrevMessage));
              });
          }
        }
      } catch (error) {
        console.error("Error processing edited message:", error);
      }
    }
  );
};
