"use client";

import "./index.scss";

import classNames from "classnames";
import { format } from "date-fns";
import { Field, Formik, useFormikContext } from "formik";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import * as Yup from "yup";

import { EVENT_ERROR_MSGS, EventSubmit, GetEvent } from "@/api/event";
import { PermissionModal } from "@/components/common/event/PermissionModal";
import FileInput from "@/components/common/file";
import Form from "@/components/common/form";
import Popup from "@/components/common/header/popup";
import Input from "@/components/common/input";
import InputGroup from "@/components/common/list";
import Select, { type SelectProps } from "@/components/common/select";
import Visualizer from "@/components/common/visualizer";
import Wrapper from "@/components/common/wrapper";
import { PostVisibility } from "@/global/constants";
import { EVENT_LIMITS } from "@/global/limits";
import {
  chatActions,
  configActions,
  liveEventActions,
} from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";
import type { Event } from "@/types/event";
import type { PostType } from "@/types/post";
import { formatCurrency } from "@/utils/formatter";
import { dateDiffInHhMmSs } from "@/utils/number";
import { sleep } from "@/utils/sleep";

export default function DevicePreview({
  params,
}: {
  params: { eventId: string };
}) {
  const router = useRouter();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(configActions.toggleShowPositionWidget());

    return () => {
      dispatch(configActions.toggleShowPositionWidget());
    };
  }, []);

  const initialValues = {
    camDeviceId: "",
    micDeviceId: "",
    media: "",
    visibility: "Public" as PostType,
    ticket: {
      type: "Free" as "Free" | "Fee",
      price: 0,
      release: {
        type: "Unlimited" as "Unlimited" | "Limited",
        amount: 0,
      },
    },
    timeLeft: "",
  };

  const validationSchema =
    params.eventId === "direct"
      ? Yup.object().shape({
          camDeviceId: Yup.string().required("Camera is required"),
          micDeviceId: Yup.string().required("Microphone is required"),
          visibility: Yup.string().required("Visibility is required"),
          ticket: Yup.object().shape({
            type: Yup.string()
              .oneOf(["Fee", "Free"])
              .required("Event Ticket type is required"),
            price: Yup.number().when("type", {
              is: "Fee",
              then: (schema) =>
                schema
                  .min(
                    EVENT_LIMITS.STREAM_TICKET_PRICE_MIN,
                    `Event Ticket price must be greater than or equal to ${formatCurrency(
                      EVENT_LIMITS.STREAM_TICKET_PRICE_MIN
                    )}`
                  )
                  .max(
                    EVENT_LIMITS.STREAM_TICKET_PRICE_MAX,
                    `Event Ticket price should not exceed ${formatCurrency(
                      EVENT_LIMITS.STREAM_TICKET_PRICE_MAX
                    )}`
                  )
                  .required("Event Ticket price is required"),
            }),
            release: Yup.object().shape({
              type: Yup.string()
                .oneOf(["Limited", "Unlimited"])
                .required("Event Release type is required"),
              amount: Yup.number().when("type", {
                is: "Limited",
                then: (schema) =>
                  schema
                    .moreThan(
                      0,
                      "Event ticket count needs to be greater than 0"
                    )
                    .required("Event ticket count is required"),
              }),
            }),
          }),
        })
      : Yup.object().shape({
          camDeviceId: Yup.string().required("Camera is required"),
          micDeviceId: Yup.string().required("Microphone is required"),
          timeLeft: Yup.string().matches(/00:00:00/),
        });

  const [event, setEvent] = useState<Event>();

  const [camDevices, setCamDevices] = useState<SelectProps[]>([]);
  const [micDevices, setmicDevices] = useState<SelectProps[]>([]);
  const camMsRef = useRef<MediaStream | null>(null);
  const micMsRef = useRef<MediaStream | null>(null);
  const [audioMsRef, setAudioMsRef] = useState<MediaStream | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [havePermission, setHavePermission] = useState(true);

  useEffect(() => {
    (async () => {
      if (params.eventId !== "direct") {
        const _event = (await GetEvent(params.eventId)).data;
        setEvent(_event);
      }

      let c: SelectProps[] = [];
      const m: SelectProps[] = [];
      let devices = await navigator.mediaDevices.enumerateDevices();

      let backCamCounts = 0;
      let isBackCamAvailable = false;

      if (devices && devices[0].deviceId === "") {
        // ask media permissions & cleanup
        setHavePermission(false);
        const ms = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: true,
        });
        devices = await navigator.mediaDevices.enumerateDevices();
        ms.getTracks().forEach((t) => t.stop());
        setHavePermission(true);
      }

      devices.forEach((device) => {
        if (device.kind === "videoinput") {
          /Back.*Camera/.test(device.label) && backCamCounts++;
          if (device.label === "Back Camera") isBackCamAvailable = true;

          c.push({
            text: device.label,
            value: device.deviceId,
            icon: "/images/event/cam.svg",
          });
        } else if (device.kind === "audioinput") {
          m.push({
            text: device.label,
            value: device.deviceId,
            icon: "/images/event/mic.svg",
          });
        }
      });

      if (!c.length)
        throw new Error("Atleast one camera is required to proceed.");
      if (!m.length)
        throw new Error("Atleast one microphone is required to proceed.");

      if (isBackCamAvailable && backCamCounts > 1) {
        c = c.filter(
          (c) =>
            !(/Back.*Camera/.test(c.text || "") && c.text !== "Back Camera")
        );
      }

      setCamDevices(c);
      setmicDevices(m);
    })().catch((e) => {
      switch (e?.message) {
        case EVENT_ERROR_MSGS.ALREADY_ENDED:
          Swal.fire({
            icon: "error",
            title: "Event Ended!",
            text: "This event has already ended.",
          }).then(() => {
            router.push("/fresh");
          });
          break;
        case "Permission denied":
          Popup.fire({
            icon: "error",
            title: "Permission denied!",
            text: "Please allow camera & mic permissions to proceed.",
          });
          break;
        case "Atleast one camera is required to proceed.":
          Popup.fire({
            icon: "error",
            title: "Camera not found!",
            text: "Atleast one camera is required to proceed.",
          });
          break;
        case "Atleast one microphone is required to proceed.":
          Popup.fire({
            icon: "error",
            title: "Microphone not found!",
            text: "Atleast one microphone is required to proceed.",
          });
          break;
        default:
          console.error(e);
          Popup.fire({
            icon: "error",
            title: "Ooops something went wrong!",
          });
          break;
      }
    });

    return () => {
      camMsRef.current?.getTracks().forEach((t) => t.stop());
      micMsRef.current?.getTracks().forEach((t) => t.stop());
    };
  }, []);

  const handleSubmit = async (data: typeof initialValues) => {
    sessionStorage.setItem("camDeviceId", data.camDeviceId);
    sessionStorage.setItem("micDeviceId", data.micDeviceId);

    let eventId = params.eventId;

    if (params.eventId === "direct") {
      const res = await EventSubmit({
        type: "Stream",
        description: "Watch my Live Stream!",
        media: data.media,
        date: format(new Date(), "yyyy-MM-dd"),
        time: format(new Date(), "HH:mm:ss"),
        duration: { type: "min", value: 60 * 12 },
        ticket: data.ticket,
        visibility: data.visibility,
        is_live_stream: true,
      });

      // @ts-expect-error showing event_id doesn't exist
      eventId = res.data[0].event_id;
    }

    dispatch(chatActions.clearStreamChat());
    dispatch(liveEventActions.resetLiveEvent());
    router.push("/events/" + eventId + "/publish");

    await sleep(2000);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ values, setFieldValue, ...rest }) => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          if (camDevices.length > 0) {
            setFieldValue("camDeviceId", camDevices[0].value);
            setTimeout(() => rest.setFieldTouched("camDeviceId", true), 1000);
          }
        }, [camDevices, setFieldValue]);

        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          if (micDevices.length > 0) {
            setFieldValue("micDeviceId", micDevices[0].value);
            setTimeout(() => rest.setFieldTouched("micDeviceId", true), 1000);
          }
        }, [camDevices, setFieldValue]);

        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          (async () => {
            if (!values.camDeviceId) return;
            camMsRef.current?.getTracks().forEach((t) => t.stop());
            const ms = await navigator.mediaDevices.getUserMedia({
              video: {
                deviceId: {
                  exact: values.camDeviceId,
                },
              },
            });
            camMsRef.current = ms;
            videoRef.current!.srcObject = ms;
          })();
        }, [values.camDeviceId]);

        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          (async () => {
            if (!values.micDeviceId) return;
            micMsRef.current?.getTracks().forEach((t) => t.stop());

            const ms = await navigator.mediaDevices.getUserMedia({
              audio: {
                deviceId: {
                  exact: values.micDeviceId,
                },
              },
            });
            micMsRef.current = ms;
            setAudioMsRef(ms);
          })();
        }, [values.micDeviceId]);

        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          if (values.ticket.release.type === "Unlimited") {
            setFieldValue("ticket.release.amount", "");
          }
        }, [values.ticket.release.type]);

        return (
          <>
            <Form
              title="Device Preview"
              nextBtnText={
                params.eventId === "direct" ? "Start Streaming" : "Start Event"
              }
              formikValues={{ ...rest, values, setFieldValue }}
            >
              <div className="d-flex col-lg-7 flex-column gap-3 flex-grow-1">
                <Wrapper title="Preview" className="w-100 ratio ratio-16x9">
                  <video
                    className="bg-black flip-x"
                    ref={videoRef}
                    autoPlay
                    muted
                    playsInline
                  ></video>
                </Wrapper>
                {params.eventId === "direct" && (
                  <>
                    <Wrapper title="Stream Information" className="stream-info">
                      <Select
                        name="visibility"
                        label="Who can see your event?"
                        array={PostVisibility.filter((v) =>
                          ["Public", "OnlyMe"].includes(v.value)
                        )}
                        selected={values.visibility}
                        setFieldValue={setFieldValue}
                      />
                    </Wrapper>
                    <Wrapper title="Price & Ticket">
                      <InputGroup
                        label=""
                        type="radio"
                        required={false}
                        theme="circle-purple"
                        name="ticket.type"
                        className="align-items-md-start"
                        childClass="ms-1"
                        array={[
                          { text: "Paid", value: "Fee" },
                          { text: "Free", value: "Free" },
                        ]}
                        selected={values.ticket.type}
                      />
                      <hr
                        className="m-0 border-2"
                        style={{ color: "lightgray" }}
                      />
                      {values.ticket.type === "Free" ? (
                        <></>
                      ) : (
                        <Input
                          name="ticket.price"
                          type="number"
                          label="Price"
                          placeholder="0"
                          priceInput={true}
                          className="w-lg-75"
                        >
                          <span className="color-medium fs-7">
                            Minimum{" "}
                            {formatCurrency(
                              EVENT_LIMITS.STREAM_TICKET_PRICE_MIN
                            )}{" "}
                            USD
                          </span>
                        </Input>
                      )}
                      <Input
                        label="How many tickets do you want to release?"
                        name="ticket.release.amount"
                        type={
                          values.ticket.release.type === "Unlimited"
                            ? "text"
                            : "number"
                        }
                        placeholder={
                          values.ticket.release.type === "Unlimited"
                            ? "Unlimited"
                            : "0"
                        }
                        disabled={values.ticket.release.type === "Unlimited"}
                      >
                        <InputGroup
                          label=""
                          type="radio"
                          required={false}
                          theme="square-filled-purple"
                          name="ticket.release.type"
                          className="position-absolute bottom-0 end-0 scale-p7 me-2"
                          array={[
                            { text: "Limited", value: "Limited" },
                            { text: "Unlimited", value: "Unlimited" },
                          ]}
                          selected={values.ticket.release.type}
                          disabled={values.ticket.type === "Free"}
                        />
                      </Input>
                    </Wrapper>
                  </>
                )}
                {params.eventId === "direct" && (
                  <div className="bg-body p-3 rounded-3">
                    <FileInput
                      name="media"
                      accept="*"
                      required={false}
                      values={values}
                      setFieldValue={setFieldValue}
                      className="mt-1"
                    >
                      <div className="d-flex flex-column align-items-center justify-content-center text-center p-2 px-3 border border-1 border-style-dashed border-opacity-50">
                        <Image
                          src="/images/post/line-plus.svg"
                          width={30}
                          height={30}
                          alt="plus_icon"
                        />
                        <h5>Upload a photo or video</h5>
                        <p className="color-medium">
                          *Files supported: JPG, PNG or MP4 (upto 30s)
                        </p>
                      </div>
                    </FileInput>
                  </div>
                )}
              </div>
              <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
                <Wrapper title="Select a video source">
                  <input
                    type="radio"
                    className="btn-check"
                    name="tt"
                    id="s-cam"
                    defaultChecked
                  />
                  <label
                    htmlFor="s-cam"
                    className="btn btn-outline-danger source-icon px-5 py-4 me-2"
                  >
                    <Image
                      className=""
                      src="/images/event/cam.svg"
                      alt="cam"
                      width={48}
                      height={48}
                    />
                    <div className="mt-2 fw-normal">Device Webcam</div>
                  </label>
                </Wrapper>
                <Wrapper title="Settings up" className="w-lg-75">
                  <div>
                    Check that your camera and microphone inputs are properly
                    working before event going.
                  </div>
                  <Select
                    name="camDeviceId"
                    label="Camera"
                    placeholder="Please select a camera..."
                    array={camDevices}
                    selected={values.camDeviceId}
                    setFieldValue={setFieldValue}
                  />
                  <Select
                    name="micDeviceId"
                    label="Microphone"
                    placeholder="Please select a microphone..."
                    array={micDevices}
                    selected={values.micDeviceId}
                    setFieldValue={setFieldValue}
                  />
                  <div className="visualizer-wrapper rounded-2">
                    <span>Voice testing</span>
                    <Visualizer mediaStream={audioMsRef} color="#AC1991" />
                  </div>
                  {event && (
                    <TimeToLive ts={new Date(event.scheduled_on).getTime()} />
                  )}
                </Wrapper>
              </div>
            </Form>
            <PermissionModal open={!havePermission} />
          </>
        );
      }}
    </Formik>
  );
}

function TimeToLive(props: { ts: number }) {
  const { setFieldValue } = useFormikContext();
  const [time, setTime] = useState("...");

  useEffect(() => {
    setFieldValue("timeLeft", time, true);
  }, [time, setFieldValue]);

  useEffect(() => {
    const loopRef = setInterval(() => {
      if (props.ts - Date.now() < 0) {
        clearInterval(loopRef);
        setTime("00:00:00");
        return;
      }

      setTime(dateDiffInHhMmSs(props.ts));
    }, 1000);

    return () => {
      if (loopRef) clearInterval(loopRef);
    };
  }, []);

  return (
    <>
      {time !== "00:00:00" && (
        <div className="d-flex justify-content-between fw-medium">
          <span>Time before you can go Live: </span>
          <span
            className={classNames({
              "text-success": time.startsWith("00:00:0"),
            })}
          >
            {time}
          </span>
        </div>
      )}
      <Field type="hidden" name="timeLeft" />
    </>
  );
}
