import "./globals.scss";

import { GoogleTagManager } from "@next/third-parties/google";
import type { Metadata } from "next";
import Script from "next/script";
import { Toaster } from "sonner";

import { AcuteFeedback } from "@/components/AcuteFeedback";
import { Dvh<PERSON>and<PERSON> } from "@/components/DvhHandler";
import { IntercomHandler } from "@/components/IntercomHandler";
import { NavigationEvents } from "@/components/NavigationEvents";
import { SentryFeedback } from "@/components/SentryFeedback";
import { SoundHandler } from "@/components/SoundHandler";
import { ShowFullscreenMedia } from "@/components/common/ShowFullscreenMedia";
import AgeWarning from "@/components/common/age-warning";
import { ModalWrapper } from "@/components/modals";
import type { Children } from "@/types/utils";

import "@/global/script";

import { PWAInstaller } from "@/components/PWAInstaller";
import "plyr-react/plyr.css";

import EventBridgeInit from "./event-bridge/event-bridge-init";
import ReduxProvider from "./providers/redux";

export const metadata: Metadata = {
  metadataBase: new URL(process.env.client),
  title: "KNKY",
  description:
    "Let’s get KNKY. A dedicated platform for knky creators and fans alike to create and share amazing content. ;)",
  openGraph: {
    title: "KNKY",
    description:
      "Let’s get KNKY. A dedicated platform for knky creators and fans alike to create and share amazing content. ;)",
    type: "website",
    url: process.env.client,
    images: [
      {
        url: "/manifest/opengraph-image.jpg",
        width: 1200,
        height: 615,
        alt: "KNKY",
      },
    ],
  },
};

export default function RootLayout({ children }: Children) {
  const isLive = process.env.environment === "live";

  return (
    <html lang="en" className="overflow-hidden">
      <head>
        {!isLive && <meta name="robots" content="noindex, nofollow" />}
      </head>
      <body>
        {isLive && <GoogleTagManager gtmId={process.env.gtm_id} />}
        <ReduxProvider>
          {children}
          <Toaster
            position="top-right"
            className="me-5"
            duration={3000}
            visibleToasts={5}
          />
          <AgeWarning />
          <EventBridgeInit />
          <IntercomHandler />
          <AcuteFeedback />
          <SentryFeedback />
          <NavigationEvents />
          <ModalWrapper />
          <SoundHandler />
          <PWAInstaller />
          <ShowFullscreenMedia />
        </ReduxProvider>
        <DvhHandler />
        <div id="modal-portal"></div>
        <Script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.3/js/bootstrap.bundle.min.js" />
      </body>
    </html>
  );
}
