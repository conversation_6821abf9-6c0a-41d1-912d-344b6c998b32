"use client";

import classNames from "classnames";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

import type { GetPost } from "@/api/post";
import { GetEventsByUserId, GetLivePosts } from "@/api/post";
import PostList from "@/components/common/post/list";
import Shimmer from "@/components/common/shimmer";
import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";
import { store } from "@/redux-store/store";
import { PostListEnum, type Post as PostType } from "@/types/post";
import { getAssetUrl } from "@/utils/assets";
import "./index.scss";

import NoContent from "@/components/common/post/no-content";

import { FiltersIcon } from "../components/navigation";
import LiveComp from "./liveComp";

const getTransformedData = (posts: GetPost[]) => {
  return posts.map((post): PostType => {
    const comments = post.comments.map((comment) => ({
      _id: comment?._id,
      author: {
        _id: comment?.author?._id,

        username: comment.author.username,
        pic: getAssetUrl({
          media: comment.author.avatar?.[0],
          defaultType: "avatar",
        }),

        display_name: comment?.author?.display_name,
        role: comment.author.user_type.toLowerCase(),
        badges: comment.author?.badges,
      },
      hearts: comment?.counter?.reactions || 0,
      text: comment.text,
      ts: new Date(comment.created_at).getTime(),
      is_liked: comment?.likes?.length != 0,
      replies: comment?.replies?.map((reply) => ({
        _id: reply._id,
        author: {
          _id: reply?.author?._id,
          username: reply?.author?.username,
          pic: getAssetUrl({
            media: reply?.author?.avatar?.[0],
            defaultType: "avatar",
          }),
          display_name: reply?.author?.display_name,
          role: reply?.author?.user_type?.toLowerCase(),
          badges: reply?.author?.badges,
        },
        text: reply.text,
        hearts: reply?.counter?.reactions,
        ts: new Date(reply.created_at).getTime(),
        is_liked: reply?.likes?.length != 0,
      })),
    }));

    return {
      _id: post._id,
      desc: post.caption,
      hashtags: post.hashtags,
      shop_item: {
        _id: post?.products?._id,
        name: post?.products?.name,
        brand: post?.products?.brand,
        category: post?.products?.category,
        description: post?.products?.description,
        media_categories: post?.media_categories,
        variations: {
          colors: post?.products?.variations?.colors,
          sizes: post?.products?.variations?.sizes,
        },
        end_date: post?.products?.end_date,

        price: post?.products?.price,

        media:
          post?.type === "Product"
            ? (post?.products?.media || []).map((media) => media)
            : [],

        quantity: post?.products?.quantity,
        sell_type: "Normal",
        // bid?: Bid,
        ts: new Date(post.created_at).getTime(),
        author: {
          _id: post?.products?.author?._id,
          username: post?.products?.author?.username,

          pic: getAssetUrl({ media: post.products?.author?.avatar?.[0]! }),
          role: post?.products?.author?.user_type || "",
          display_name: post?.products?.author?.display_name,
        },

        is_public: true,
      },
      channels: post?.channels || undefined,
      img:
        post.type !== "Product" ? getAssetUrl({ media: post?.media?.[0] }) : "",
      media: post.media,
      preview: post?.preview,
      poll: post?.poll,
      backgroundColor: post?.backgroundColor,
      textColor: post?.textColor,
      feeling: post?.feeling,
      tagged_users: post?.tagged_users,
      // products : post?.products,
      pay_and_watch_rate: post?.pay_and_watch_rate,
      is_purchased: post?.is_purchased,
      // To Make sure that Post type should have enum values
      // type: post.type as PostType,
      likes: post.likes,
      is_saved: post?.is_saved,
      is_subscribed: post?.is_subscribed,
      has_prime: post?.has_prime,
      has_appealed: post?.has_appealed,
      counter: post.counter,
      collaborators: post.collaborators,
      buyers: post?.buyers,
      seat_reserved: post?.seat_reserved,
      groups: post?.groups,
      type: post.visibility,
      post_type: post.type,
      initial_visibility: post?.initial_visibility,
      ts: new Date(post.created_at).getTime(),
      canComment: true,
      event: {
        _id: post?.event?._id,
        name: post?.event?.name,
        description: post?.event?.description,
        event_type: post?.event?.event_type,
        ticket_type: post?.event?.ticket_type,
        price: post?.event?.price,
        is_public: post?.event?.is_public,
        scheduled_on: post?.event?.scheduled_on,
        duration: post?.event?.duration,
        quantity: post?.event?.quantity,
        quantity_type: post?.event?.quantity_type,
        has_ended: post?.event?.has_ended,
        sold_ticket_count: post?.event?.sold_ticket_count,
        event_converse_channel_id: post?.event?.event_converse_channel_id,
        is_live_stream: post?.event?.is_live_stream,
        media: post?.event?.media,
      },
      min_subscription: {
        _id: post?.min_subscription?._id,
        price: post?.min_subscription?.price,
        author: post?.min_subscription?.author,
        channel: post?.min_subscription?.channel,
      },
      shared_entity: post?.shared_entity,
      shared_on_profile: post.shared_on_profile || false,
      followed_by_you: post?.followed_by_you,
      status: post?.status || "Completed",
      media_categories: post.media_categories,

      author: {
        _id: post?.author?._id || "",

        username: `${post?.author?.username || "johndoe"}`,
        // To Make sure that badges should have enum values
        // badges: post.author.badges as AuthorBadge[],
        badges: post.author.badges,
        pic: getAssetUrl({
          media: post?.author?.avatar?.[0],
          defaultType: "avatar",
        }),
        role: post?.author?.user_type?.toLowerCase(),
        display_name: post?.author?.display_name,
        account_status: post?.author?.account_status,

        background: post?.author?.background,
      },
      actions: {
        loved: {
          by: "",
          count: post.likes.length,
          active: post.likes.includes(store?.getState()?.user.id),
        },
        save: 0,
        comments: post.counter.comments,
        shares: 0,
      },
      comments: comments,
    };
  });
};

export const LiveCardsList = ({
  type = "homefeed",
  uid,
}: {
  type: string;
  uid?: string;
}) => {
  const user = useAppSelector((state) => state.user);
  const [time, setTime] = useState("");
  const gender = useAppSelector((s) => s.defaults.homeFeedFilters.gender);
  const author = useAppSelector((s) => s.defaults.homeFeedFilters.posts_of);
  const blockedByMe = useAppSelector((state) => state?.block?.blockedByMe);
  const hasBlockedMe = useAppSelector((state) => state?.block?.hasBlockedMe);
  const currentTime = new Date(Date.now()).toISOString();
  const role = useAppSelector((state) => state.user.role);
  const [posts, setPosts] = useState([] as PostType[]);
  const [hasMoreData, setHasMoreData] = useState(true);

  console.log({ uid });

  const fetchData = (currentTime?: string) => {
    const timestamp: string = currentTime || time;

    if (type === "homefeed") {
      return GetLivePosts({
        timestamp,
        status: "live",
        ...(gender !== "all"
          ? gender === "male"
            ? { gender: "Male" }
            : gender === "others"
            ? { gender: "Other" }
            : { gender: "Female" }
          : {}),
        ...(author !== "all" ? { user_type: author } : {}),
      })
        .then((res) => {
          if (res.data.length > 0) {
            setTime(res.data[res.data.length - 1].created_at);
          }

          const postData = res.data as GetPost[];
          setPosts((state) => {
            const filteredPosts = getTransformedData(postData).filter(
              (post) =>
                !(blockedByMe[post.author._id] || hasBlockedMe[post.author._id])
            );
            return state.concat(...filteredPosts);
          });

          setHasMoreData(res.data.length === 20);
        })
        .catch(console.error);
    } else {
      return GetEventsByUserId({
        timestamp,
        userId: uid,
        eventStatus: "live",
      })
        .then((res) => {
          if (res.data.length > 0) {
            setTime(res.data[res.data.length - 1].created_at);
          }

          const postData = res.data as GetPost[];
          setPosts((state) => {
            const filteredPosts = getTransformedData(postData).filter(
              (post) =>
                !(blockedByMe[post.author._id] || hasBlockedMe[post.author._id])
            );
            return state.concat(...filteredPosts);
          });

          setHasMoreData(res.data.length === 20);
        })
        .catch(console.error);
    }
  };

  useEffect(() => {
    fetchData(currentTime).then((e) => {
      // setPage(2);
    });
    setPosts([]);
  }, [role, author, gender]);
  return (
    <div
      // !add h-100 in case of any issue here.
      className="posts h-100 mb-2"
      // #FIXME: refactor and make it more readable
      onClick={() =>
        user.role === "guest" ? ModalService.open("SIGN_IN") : console.log("")
      }
    >
      <InfiniteScroll
        className="d-flex flex-column gap-3 "
        style={{ overflowX: "hidden" }}
        next={() => {
          console.log("calling");

          if (time < currentTime) {
            console.log("Fetch data calling again");
            fetchData();
          }
        }}
        dataLength={posts.length} //This is important field to render the next data
        hasMore={hasMoreData}
        loader={<Shimmer type="live"></Shimmer>}
        scrollThreshold={0.6}
        scrollableTarget={
          type === "homefeed" ? "infiniteScrollHome" : "profile-container"
        }
        endMessage={
          <div className="text-center bg-body rounded p-3 ">
            {posts.length ? (
              "Yay! You have seen it all."
            ) : (
              <NoContent
                text="No active streams or events, check back soon 😉"
                sub_text={
                  role === "guest"
                    ? "But don't worry, sign in to explore more!"
                    : ""
                }
              />
            )}
          </div>
        }
      >
        <LiveComp details={posts} />
      </InfiniteScroll>
    </div>
  );
};

export default function LiveList() {
  const user = useAppSelector((state) => state.user);
  const searchParams = useSearchParams();
  const currentTab = searchParams.get("type") === "upcoming-events" ? 1 : 0;

  const HomeFilters = () => {
    const userRole = useAppSelector((s) => s.user.role);

    return (
      <div className={classNames("d-flex gap-3 px-3 align-items-center")}>
        <div className="d-flex gap-2 d-none dropdown">
          <div>List</div>
          <div>Grid</div>
        </div>
        <FiltersIcon fill={""} />
        <ul
          className="dropdown-menu border-0 custom-transform"
          style={{
            boxShadow: "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
          }}
        >
          <li className="fw-bold ms-3 mt-2"> Options</li>
          <li
            className={classNames(
              "ms-3 mt-3 mb-2 d-flex justify-content-between align-items-center"
            )}
          >
            <span className="w-100">All creators</span>
          </li>
          <li
            className={classNames(
              "ms-3 mb-3 d-flex justify-content-between align-items-center"
            )}
          >
            <span className="w-100">Followings</span>
          </li>
        </ul>
      </div>
    );
  };

  return (
    <>
      <div className="subs-options rounded-3 mb-3 d-flex justify-content-between align-items-center bg-cream ">
        <div
          className="d-flex  gap-4 align-items-center  w-100 overflow-auto px-3 p-md-3 py-2"
          id="pills-tab"
          role="tablist"
        >
          <Link
            href={`?type=live`}
            className={`btn tab ${currentTab === 0 ? "active" : ""}`}
            type="button"
          >
            Live
          </Link>
          <Link
            href={`?type=upcoming-events`}
            className={`btn tab ${currentTab === 1 ? "active" : ""}`}
            type="button"
          >
            Upcoming events
          </Link>
        </div>
      </div>

      {currentTab == 1 && (
        <PostList uid={user?.id} type={PostListEnum.LIVE} from={"homefeed"} />
      )}

      {currentTab === 0 && (
        // !add h-100 in case of any issue here.
        <LiveCardsList type="homefeed" />
      )}
    </>
  );
}
