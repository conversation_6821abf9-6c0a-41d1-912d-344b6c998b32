import Image from "next/image";
import Link from "next/link";

interface DropdownItemProps {
  src: string;
  alt: string;
  text: string;
  href?: string;
  imageOnly?: boolean;
  className?: string;
  onClick?: any;
  from?: string;
}

export const DropdownItemClass =
  "dropdown-item color-bold py-2 rounded-2 d-flex gap-2 align-items-center pointer";

const DropdownItem = ({
  src,
  alt,
  text,
  href,
  onClick,
  imageOnly = false,
  className = "",
  from,
}: DropdownItemProps) => {
  if (onClick)
    return (
      <div
        className={`${DropdownItemClass} ${className}`}
        onClick={onClick}
        onKeyDown={(evt) => evt.key === "Enter" && onClick(evt)}
      >
        {src ? (
          <Image
            style={{ filter: "grayscale(1)" }}
            className={`invert ${imageOnly ? "w-fit h-100 m-auto" : ""}`}
            src={src}
            alt={alt}
            width={from === "header" ? 24 : 30}
            height={from === "header" ? 24 : 30}
          />
        ) : (
          <></>
        )}
        <div
          className="text-overflow-ellipsis"
          style={{ width: "calc(100% - 1em)" }}
        >
          {imageOnly ? "" : text}
        </div>
      </div>
    );

  return (
    <Link className={DropdownItemClass + " " + className} href={href || "#"}>
      <Image
        style={{ filter: "grayscale(1)" }}
        className="invert"
        src={src}
        alt={alt}
        width={from === "header" ? 24 : 30}
        height={from === "header" ? 24 : 30}
      />
      {text}
    </Link>
  );
};

export default DropdownItem;
