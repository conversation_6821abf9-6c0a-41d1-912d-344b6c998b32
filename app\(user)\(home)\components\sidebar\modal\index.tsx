import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import Swiper from "swiper";
import "swiper/css";
import "swiper/css/navigation";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import "swiper/swiper-bundle.css";
import "./index.scss";

import { getPlatformSubscriptionPlans } from "@/api/subscriptions";
import type { ModalImportProps } from "@/components/common/modal";
import Modal from "@/components/common/modal";
import { useAppSelector } from "@/redux-store/hooks";
import { formatCurrency } from "@/utils/formatter";

import PrimeBenefits from "./prime.json";
import UnlimitedBenefits from "./unlimited.json";

export interface PlanInfo {
  name: string;
  type: string;
  price: number;
  offer: string;
  _id: string;
}

export default function PlansModal({ setChildRef }: ModalImportProps) {
  const [plans, setPlans] = useState<{
    primePlan: PlanInfo;
    proCreatorPlan: PlanInfo;
  }>({
    primePlan: {
      name: "",
      type: "",
      price: 0,
      offer: "",
      _id: "",
    },
    proCreatorPlan: {
      name: "",
      type: "",
      price: 0,
      offer: "",
      _id: "",
    },
  });

  const is_Prime = useAppSelector(
    (state) => state?.user.profile.badges.subscription_badge
  ).filter((res) => res === "Prime")[0];
  const is_ProCreator = useAppSelector(
    (state) => state?.user.profile.badges.subscription_badge
  ).filter((res) => res === "CreatorPro")[0];

  const initializeSwiper = () => {
    new Swiper(`.prime-benefits`, {
      direction: "horizontal",
      effect: "slide",
      slidesPerView: 1,
      spaceBetween: 100,
      modules: [Navigation, Pagination, Autoplay],
      autoplay: {
        delay: 2300,

        disableOnInteraction: true,
      },
      speed: 700,

      navigation: {
        nextEl: `.swiper-button-next`,
        prevEl: `.swiper-button-prev`,
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      loop: true,
      grabCursor: true,
      // followFinger: true,
      // allowTouchMove: true,
      // autoHeight: false,
      // on: {
      //   slideChange: (swiper) => setViewImageIndex(swiper.activeIndex + 1),
      // },
    });

    new Swiper(`.unlimited-benefits`, {
      direction: "horizontal",
      effect: "slide",
      slidesPerView: 1,
      spaceBetween: 100,
      modules: [Navigation, Pagination, Autoplay],
      autoplay: {
        delay: 2300,

        disableOnInteraction: true,
      },
      speed: 700,

      navigation: {
        nextEl: `.swiper-button-next`,
        prevEl: `.swiper-button-prev`,
      },
      pagination: {
        el: "unlimited-benefits .swiper-pagination",
        clickable: true,
      },
      loop: true,
      grabCursor: true,
      // followFinger: true,
      // allowTouchMove: true,
      // autoHeight: false,
      // on: {
      //   slideChange: (swiper) => setViewImageIndex(swiper.activeIndex + 1),
      // },
    });
  };

  useEffect(() => {
    // if (shopItem.media) {
    setTimeout(() => {
      initializeSwiper();
    }, 0);

    getPlatformSubscriptionPlans().then((res) => {
      const prime = res.data.filter((res: any) => res.name === "Prime");
      const proCreator = res.data.filter(
        (res: any) => res.name === "Pro Creator"
      );

      setPlans({ primePlan: prime[0], proCreatorPlan: proCreator[0] });
    });
    // }
  }, []);

  return (
    <Modal
      title="Subscription Plans"
      subtitle={[
        "Join our unlimited community and explore till your hearts content.",
      ]}
      setChildRef={setChildRef}
      // cls="modal-sm"
      render={"direct"}
    >
      <section className="m-auto mt-3 plans-section ">
        <ul
          className="nav nav-pills  justify-content-center gap-3"
          id="pills-tab"
          role="tablist"
        >
          <li className="nav-item" role="presentation">
            <button
              className="nav-link active prime-btn fs-6 fw-bolder btn-lg d-flex align-items-center justify-content-center gap-2 "
              id="pills-prime-tab"
              data-bs-toggle="pill"
              data-bs-target="#pills-prime"
              type="button"
              role="tab"
              aria-controls="pills-prime"
              aria-selected="true"
            >
              <Image
                src="/images/home/<USER>"
                alt=""
                width={32}
                height={32}
              />
              Prime
            </button>
          </li>
          <li className="nav-item" role="presentation">
            <button
              className="nav-link pro-creator-btn fs-6 fw-bolder btn-lg d-flex align-items-center justify-content-center gap-2 "
              id="pills-pro-creator-tab"
              data-bs-toggle="pill"
              data-bs-target="#pills-pro-creator"
              type="button"
              role="tab"
              aria-controls="pills-pro-creator"
              aria-selected="false"
            >
              <Image
                src="/images/badges/creator-pro.svg"
                alt=""
                width={32}
                height={32}
              />
              <span> Pro Creator</span>
            </button>
          </li>
          <li className="nav-item" role="presentation">
            <button
              className="nav-link unlimited-btn fs-6 fw-bolder btn-lg d-flex align-items-center justify-content-center gap-2 "
              id="pills-unlimited-tab"
              data-bs-toggle="pill"
              data-bs-target="#pills-unlimited"
              type="button"
              role="tab"
              aria-controls="pills-unlimited"
              aria-selected="false"
            >
              <Image
                src="/images/home/<USER>"
                alt=""
                width={32}
                height={32}
                style={{ filter: "invert(1)" }}
              />
              Unlimited
            </button>
          </li>
        </ul>
        <div className="tab-content" id="pills-tabContent">
          <div
            className="tab-pane fade show active prime-div rounded-3 p-3 text-white text-center"
            id="pills-prime"
            role="tabpanel"
            aria-labelledby="pills-prime-tab"
          >
            <div className="prime-benefits swiper mt-3">
              <div className="swiper-wrapper">
                {PrimeBenefits?.map((benefit, i) => (
                  <div key={i} className="swiper-slide">
                    <div className=" d-flex flex-column gap-3 align-items-center m-auto mt-2 w-75 justify-content-center  ">
                      <Image src={benefit?.img} alt="" width={64} height={64} />
                      <h4 className="text-center">{benefit?.text}</h4>
                    </div>
                  </div>
                ))}
              </div>
              <div className="swiper-button-next">
                <Image
                  src={"/images/common/arrow-white.svg"}
                  alt=""
                  width={15}
                  height={27}
                />
              </div>
              <div className="swiper-button-prev">
                {" "}
                <Image
                  src={"/images/common/arrow-white.svg"}
                  alt=""
                  width={15}
                  height={27}
                  style={{ rotate: "180deg" }}
                />
              </div>
            </div>
            <div className="position-relative pagination-div">
              <div className="mb-2 swiper-pagination"></div>
            </div>

            <div className="mt-4">
              <div className="fw-bolder">
                Choose your package below to upgrade your account now!
              </div>
              <div className="d-flex gap-2 mt-4 justify-content-around">
                <Link
                  href={"/platform-plans"}
                  onClick={() => setChildRef.method.close()}
                  className="plans-card position-relative  d-flex flex-column gap-3 rounded-3 bg-body p-3 align-items-start pointer"
                >
                  <div>
                    {" "}
                    <div className="fw-bold   "> Monthly</div>{" "}
                    {is_Prime && (
                      <div className=" bg-black p-1  position-absolute top-0 end-0">
                        <span className=" fw-bold fs-8">Current Plan</span>
                      </div>
                    )}
                  </div>
                  <h4 className=" mb-0 ">
                    {formatCurrency(plans.primePlan?.price || 0)}
                  </h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Free trial 3 days
                  </div>
                </Link>
                {/* <div className="plans-card  d-flex flex-column gap-3 rounded-3 bg-body p-3  align-items-start">
                  <div className="fw-bold   text-start ">
                    6 Months <span className="color-sky-blue">Popular</span>
                  </div>
                  <h4 className=" mb-0 color-sky-blue"> $x.xx</h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Save: $x.xx
                  </div>
                </div>
                <div className="plans-card  d-flex flex-column gap-3 rounded-3 bg-body p-3  align-items-start">
                  <div className="fw-bold   ">1 year</div>
                  <h4 className=" mb-0  ">$x.xx</h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Save: $x.xx
                  </div>
                </div> */}
              </div>
            </div>
          </div>
          <div
            className="tab-pane fade pro-creator-div  rounded-3 p-3 text-white text-center"
            id="pills-pro-creator"
            role="tabpanel"
            aria-labelledby="pills-pro-creator-tab"
          >
            <div className="unlimited-benefits swiper mt-3">
              <div className="swiper-wrapper">
                {UnlimitedBenefits?.map((benefit, i) => (
                  <div key={i} className="swiper-slide">
                    <div className=" d-flex flex-column gap-3 align-items-center m-auto mt-2 w-75 justify-content-center  ">
                      <Image src={benefit?.img} alt="" width={64} height={64} />
                      <h4 className="text-center">{benefit?.text}</h4>
                    </div>
                  </div>
                ))}
              </div>
              <div className="swiper-button-next">
                <Image
                  src={"/images/common/arrow-white.svg"}
                  alt=""
                  width={15}
                  height={27}
                />
              </div>
              <div className="swiper-button-prev">
                {" "}
                <Image
                  src={"/images/common/arrow-white.svg"}
                  alt=""
                  width={15}
                  height={27}
                  style={{ rotate: "180deg" }}
                />
              </div>
            </div>
            <div className="position-relative pagination-div">
              <div className="mb-2 swiper-pagination"></div>
            </div>

            <div className="mt-4">
              <div className="fw-bolder">
                Choose your package below to upgrade your account now!
              </div>
              <div className="d-flex gap-2 mt-4 justify-content-around">
                <Link
                  className="plans-card position-relative  d-flex flex-column gap-3 rounded-3 bg-body p-3 align-items-start pointer"
                  href={"/platform-plans"}
                  onClick={() => setChildRef.method.close()}
                >
                  <div>
                    {" "}
                    <div className="fw-bold   "> Monthly</div>{" "}
                    {is_ProCreator && (
                      <div className=" bg-black p-1  position-absolute top-0 end-0">
                        <span className=" fw-bold fs-8">Current Plan</span>
                      </div>
                    )}
                  </div>
                  <h4 className=" mb-0 ">
                    {formatCurrency(plans.proCreatorPlan?.price || 0)}
                  </h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Free trial 3 days
                  </div>
                </Link>
                {/* <div className="plans-card  d-flex flex-column gap-3 rounded-3 bg-body p-3  align-items-start">
                  <div className="fw-bold   text-start ">
                    6 Months <span className="color-sky-blue">Popular</span>
                  </div>
                  <h4 className=" mb-0 color-sky-blue"> $x.xx</h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Save: $x.xx
                  </div>
                </div>
                <div className="plans-card  d-flex flex-column gap-3 rounded-3 bg-body p-3  align-items-start">
                  <div className="fw-bold   ">1 year</div>
                  <h4 className=" mb-0  ">$x.xx</h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Save: $x.xx
                  </div>
                </div> */}
              </div>
            </div>
          </div>
          <div
            className="tab-pane fade unlimited-div  rounded-3 p-3 text-white text-center"
            id="pills-unlimited"
            role="tabpanel"
            aria-labelledby="pills-unlimited-tab"
          >
            <div className="unlimited-benefits swiper mt-3">
              <div className="swiper-wrapper">
                {UnlimitedBenefits?.map((benefit, i) => (
                  <div key={i} className="swiper-slide">
                    <div className=" d-flex flex-column gap-3 align-items-center m-auto mt-2 w-75 justify-content-center  ">
                      <Image src={benefit?.img} alt="" width={64} height={64} />
                      <h4 className="text-center">{benefit?.text}</h4>
                    </div>
                  </div>
                ))}
              </div>
              <div className="swiper-button-next">
                <Image
                  src={"/images/common/arrow-white.svg"}
                  alt=""
                  width={15}
                  height={27}
                />
              </div>
              <div className="swiper-button-prev">
                {" "}
                <Image
                  src={"/images/common/arrow-white.svg"}
                  alt=""
                  width={15}
                  height={27}
                  style={{ rotate: "180deg" }}
                />
              </div>
            </div>
            <div className="position-relative pagination-div">
              <div className="mb-2 swiper-pagination"></div>
            </div>

            <div className="mt-4">
              {/* <div className="fw-bolder">
                Choose your package below to upgrade your account now!
              </div>
              <div className="d-flex gap-2 mt-4 justify-content-around">
                <div className="plans-card  d-flex flex-column gap-3 rounded-3 bg-body p-3 align-items-start">
                  <div className="fw-bold   "> Monthly</div>
                  <h4 className=" mb-0 "> $799</h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Free trial 3 days
                  </div>
                </div>
                <div className="plans-card  d-flex flex-column gap-3 rounded-3 bg-body p-3  align-items-start">
                  <div className="fw-bold   text-start ">
                    6 Months <span className="color-yellow">Popular</span>
                  </div>
                  <h4 className=" mb-0 color-yellow"> $x.xx</h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Save: $x.xx
                  </div>
                </div>
                <div className="plans-card  d-flex flex-column gap-3 rounded-3 bg-body p-3  align-items-start">
                  <div className="fw-bold   ">1 year</div>
                  <h4 className=" mb-0  ">$x.xx</h4>
                  <div className="fw-bold  color-medium fw-medium ">
                    {" "}
                    Save: $x.xx
                  </div>
                </div>
              </div> */}
              <h1 className="color-white">Coming Soon!</h1>
            </div>
          </div>
        </div>
        {/* <PlansSubscriptionModal
          setChildRef={plansSubscription}
          misc={primePlan}
        /> */}
      </section>
    </Modal>
  );
}
