"use client";

import "./index.scss";

import Link from "next/link";
import { useEffect } from "react";
import { toast } from "sonner";

import { CreateBtn } from "@/components/common/buttons/channel-group-btns";
import SubscribersList from "@/components/common/follower-following/subscribers-list";
import JoinUs from "@/components/common/join-us";
import MediaFilter from "@/components/common/media-filter";
import MediaList from "@/components/common/media/list";
import PostList from "@/components/common/post/list";
import SubtabsFilter from "@/components/common/subtabs-filter";
import useIsMobile from "@/hooks/useIsMobile";
import { useAppSelector } from "@/redux-store/hooks";
import { PostListEnum } from "@/types/post";
import type {
  GroupProfileSelectionMap,
  GroupProfileSelectionType,
  ProfileSectionCounter,
} from "@/types/profile";

const SectionMap: GroupProfileSelectionMap = {
  posts: {
    title: "Posts",
    value: "posts",
    not_allowed: {},
    content: (uid: string) => (
      <>
        <SubtabsFilter uid={uid} />
        <PostList uid={uid} type={PostListEnum.GROUP} />
      </>
    ),
  },
  media: {
    title: "Media",
    value: "media",
    not_allowed: {},
    content: (uid: string) => (
      <>
        <SubtabsFilter uid={uid} source="media" />
        <div className="container container-xxl  bg-body rounded-3">
          <MediaFilter from="group" />
          <MediaList uid={uid} mediaType="group" />
        </div>
      </>
    ),
  },
  subscriber: {
    title: "Subscribers",
    value: "subscriber",
    not_allowed: { guest: true },
    content: () => <SubscribersList type="group" />,
  },
  // followers: {
  //   title: "Followers",
  //   value: "followers",
  //   not_allowed: { guest: true },
  //   content: (_uid: string) => <FollowersList uid={""} type="group" />,
  //   // just for showing added static id. to replace it with uid when API is created
  // },
};

interface ProfileSectionsProps {
  params: {
    section: GroupProfileSelectionType;
  };
}

const ProfileSectionSelector = ({
  username,
  isOwner,
  selected,
  sections,
  counter,
  isActivated,
}: {
  username: string;
  isOwner: boolean;
  selected: GroupProfileSelectionType;
  sections: GroupProfileSelectionType[];
  counter: ProfileSectionCounter;
  isActivated: boolean;
}) => {
  const state = useAppSelector(
    (state) => state?.defaults?.creator_profile?.privacy_setting
  );
  const is_self = useAppSelector(
    (state) => state.defaults.creator_profile?.details?.self
  );

  const HideTabMap: any = {
    subscriber: !state?.showSubscribedGroups,
  };
  const isMobile = useIsMobile();
  useEffect(() => {
    const tooltipTriggerList = document.querySelectorAll(
      '[data-bs-toggle="tooltip"]'
    );
    const tooltipList = [...tooltipTriggerList].map(
      (tooltipTriggerEl) => new window.bootstrap.Tooltip(tooltipTriggerEl)
    );
  }, []);

  return (
    <div
      className="profile-section-selector d-flex justify-content-between bg-body px-3 gap-4 "
      style={{
        position: isMobile ? "sticky" : "unset",
        top: 0,
        zIndex: 3,
      }}
    >
      <div className="profile-section-selector-content flex-grow-1 vanish-bg-body ">
        <div className="profile-sections scrollable d-flex gap-1">
          {sections.map((section) => {
            if (section === "subscriber" && !isOwner) return null;
            return HideTabMap[section] && !is_self ? (
              <></>
            ) : (
              <Link
                scroll={true}
                href={`/collab/${username}/${section}`}
                key={section}
                className={`profile-section d-flex flex-column align-items-center justify-content-center scroll-item mw-4 px-4 py-1 ${
                  section === selected ? "active" : ""
                }`}
              >
                <div className="section-title fs-9 color-bold">
                  {SectionMap[section].title}
                </div>
                <div className="section-count fs-5 fw-bold">
                  {counter[SectionMap[section].value] || 0}{" "}
                </div>
              </Link>
            );
          })}
        </div>
      </div>
      <div className="d-lg-flex d-none gap-4 align-items-center">
        <CreateBtn
          href={"/create/new-post?isGroupPost=true"}
          text={"Create new post"}
          visible={isOwner && isActivated}
        />
        {/* <PostActionSendTip postAuthor={} authorId={username} /> */}
        {/* <Image
          alt=""
          width={32}
          height={32}
          src="/images/creator/three-dots.svg"
        /> */}
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          className="pointer"
          src="/images/svg/dropdown/copy-link.svg"
          alt=""
          data-bs-toggle="tooltip"
          data-bs-title={"Copy Collab Link"}
          onClick={() => {
            navigator.clipboard.writeText(
              `${process.env.client}/collab/${username}`
            );
            toast.success("Copied to clipboard");
          }}
        />
      </div>
    </div>
  );
};

const ProfileSections = ({ params: { section } }: ProfileSectionsProps) => {
  const role = useAppSelector((state) => state.user.role);
  const userId = useAppSelector((state) => state.user.profile._id);
  const profile = useAppSelector((state) => state.defaults.group_profile);

  const selected = SectionMap[section] ? section : "posts";
  const selectedSection = SectionMap[selected];

  const userSections = [
    "posts",
    "media",
    "subscriber",
  ] as GroupProfileSelectionType[];

  let isActivated = !profile.marked_as_deleted ?? false;
  isActivated = isActivated && profile.is_activated;

  return (
    <>
      {/* To Refactor it as its very lame .includes */}
      <ProfileSectionSelector
        username={profile.username}
        isOwner={profile.members
          .map(({ member_id }) => member_id._id)
          .includes(userId)}
        selected={selected}
        sections={userSections}
        counter={profile.counter}
        isActivated={isActivated}
      />
      {selectedSection.not_allowed[role] ? (
        <JoinUs />
      ) : (
        selectedSection.content(profile._id)
      )}
    </>
  );
};

export default ProfileSections;
