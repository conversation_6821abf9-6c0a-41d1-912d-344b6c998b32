import Swal from "sweetalert2";

import { EVENT_ERROR_MSGS } from "@/api/event";

export function showErrorResponse(e: any) {
  switch (e?.message) {
    case EVENT_ERROR_MSGS.ALREADY_ENDED:
      return Swal.fire({
        icon: "error",
        title: "Event Ended!",
        text: "This event has already ended.",
      });
    case EVENT_ERROR_MSGS.ALREADY_VIEWING:
      return Swal.fire({
        icon: "error",
        title: "Already Publishing!",
        html: "You are already publishing this stream.<br/>Please close the other tab or window.",
      });
    default:
      console.error(e);
      return Swal.fire({
        icon: "error",
        title: "Ooops something went wrong!",
        text: "Please try again later...",
        timer: 3000,
        timerProgressBar: true,
      });
  }
}
