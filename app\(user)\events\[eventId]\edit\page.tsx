"use client";

import classNames from "classnames";
import { format } from "date-fns";
import { Formik } from "formik";
import isEqual from "lodash/isEqual";
import Image from "next/image";
import { type SyntheticEvent, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import * as Yup from "yup";

import { EventDelete, EventUpdate, GetEvent } from "@/api/event";
import Button from "@/components/common/button";
import FileInput from "@/components/common/file";
import Form from "@/components/common/form";
import Input from "@/components/common/input";
import InputGroup from "@/components/common/list";
import Select from "@/components/common/select";
import TextArea from "@/components/common/textarea";
import Wrapper from "@/components/common/wrapper";
import { PostVisibility } from "@/global/constants";
import { EVENT_LIMITS } from "@/global/limits";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import type { Event } from "@/types/event";
import type { PostEvent, PostType } from "@/types/post";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";

import styles from "./page.module.scss";

export default function EditEvent({ params }: { params: { eventId: string } }) {
  const navigateBack = useNavigateBack();
  const [event, setEvent] = useState<Event | null>(null);
  const [initialValues, setInitialValues] = useState<Omit<
    PostEvent,
    "type" | "is_live_stream"
  > | null>(null);
  const [isEditTime, setIsEditTime] = useState(true);

  const minTicketPrice =
    event && event.ticket_type === "Fee" && event.sold_ticket_count > 0
      ? event.price
      : EVENT_LIMITS.STREAM_TICKET_PRICE_MIN;
  const minTickets =
    event && event.sold_ticket_count > 0 ? event.sold_ticket_count : 1;
  const paidTicketsSold =
    initialValues?.ticket.type === "Fee" &&
    Number(event?.sold_ticket_count) > 0;

  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .min(3, "Min of 3 characters required")
      .required("Event Name is required"),
    media: Yup.string().required("Event Media is required"),
    description: Yup.string()
      .min(4, "Min of 4 characters required")
      .max(400, "Max of 400 characters allowed")
      .required("Event Description is required"),
    date: Yup.string().required("Event Date is required"),
    time: Yup.string().required("Event Time is required"),
    ts: Yup.string().test(
      "timestamp-check",
      "Selected time should be atleast 5 min greater than current time",
      (value, context) => {
        const { date, time } = context.parent;
        const currentDate = new Date();
        const selectedDate = new Date(`${date}T${time}`);
        const diff = selectedDate.getTime() - currentDate.getTime();
        let errMsg = "";

        if (diff < EVENT_LIMITS.TIME_BUFFER_MIN)
          errMsg =
            "Selected date & time should be atleast 5 min greater than now";
        else if (diff > EVENT_LIMITS.TIME_BUFFER_MAX)
          errMsg = "Selected date & time should be less than 1 year from now";

        if (errMsg) {
          return context.createError({
            path: "time",
            message: errMsg,
          });
        } else return true;
      }
    ),
    duration: Yup.object()
      .shape({
        type: Yup.string()
          .oneOf(["hour", "min"])
          .required("Event Duration type is required"),
        value: Yup.number()
          .moreThan(0, "Event Duration needs to be greater than 0")
          .required("Event Duration value is required"),
      })
      .test(
        "duration-check",
        "Event Duration should be less than 12 hours",
        (value, context) => {
          const duration =
            value.value * (value.type === "hour" ? 60 : 1) * 60 * 1000;

          if (duration <= EVENT_LIMITS.STREAM_DURATION_MAX) return true;
          else {
            return context.createError({
              path: "duration.value",
              message: "Event Duration should be less than 12 hours",
            });
          }
        }
      ),
    ticket: Yup.object().shape({
      type: Yup.string()
        .oneOf(["Fee", "Free"])
        .required("Event Ticket type is required"),
      price: Yup.number()
        .when("type", {
          is: "Fee",
          then: (schema) =>
            schema
              .min(
                EVENT_LIMITS.STREAM_TICKET_PRICE_MIN,
                `Event Ticket price must be greater than or equal to ${EVENT_LIMITS.STREAM_TICKET_PRICE_MIN}`
              )
              .max(
                EVENT_LIMITS.STREAM_TICKET_PRICE_MAX,
                `Event Ticket price should not exceed ${EVENT_LIMITS.STREAM_TICKET_PRICE_MAX}`
              )
              .required("Event Ticket price is required"),
        })
        .test(
          "min-price-check",
          `${minTickets} ticket(s) have already been sold for this event. So you can't decrease the ticket price below ${formatCurrency(
            minTicketPrice
          )}.`,
          (value) => {
            return (
              (typeof value === "number" && value >= minTicketPrice) ||
              minTicketPrice === EVENT_LIMITS.STREAM_TICKET_PRICE_MIN
            );
          }
        ),
      release: Yup.object().shape({
        type: Yup.string()
          .oneOf(["Limited", "Unlimited"])
          .required("Event Release type is required"),
        amount: Yup.number()
          .when("type", {
            is: "Limited",
            then: (schema) =>
              schema
                .moreThan(0, "Event Ticket count needs to be greater than 0")
                .required("Event Ticket count is required"),
          })
          .test(
            "min-tickets-check",
            `${minTickets} ticket(s) have already been sold for this event. So you can't decrease the tickets below that.`,
            (value, context) => {
              const { type } = context.parent;
              return (
                (typeof value === "number" && value >= minTickets) ||
                type === "Unlimited"
              );
            }
          ),
      }),
    }),
    visibility: Yup.string()
      .required("Event Visibility is required")
      .test(
        "visibility-check",
        `Event Visibility can't be changed to OnlyMe since ${event?.sold_ticket_count} ticket(s) have already been sold for this event.`,
        (value) => {
          return (
            !event ||
            event.visibility === "OnlyMe" ||
            !(event.sold_ticket_count > 0 && value === "OnlyMe")
          );
        }
      ),
  });

  useEffect(() => {
    (async () => {
      const _event = (await GetEvent(params.eventId)).data;

      if (_event.has_ended) {
        swalFire(
          "Event Modification Not Allowed",
          "Modifications are not permitted since the event has already ended.",
          "error"
        ).then(() => navigateBack());
        return;
      }

      if (_event.is_live_stream) {
        swalFire(
          "Event Modification Not Allowed",
          "Modifications are not permitted since the event is a live stream.",
          "error"
        ).then(() => navigateBack());
        return;
      }

      setEvent(_event);

      const duration_type = _event.duration % 60 === 0 ? "hour" : "min";
      const duration_value =
        duration_type === "hour" ? _event.duration / 60 : _event.duration;
      const date = format(new Date(_event.scheduled_on), "yyyy-MM-dd");
      const time = format(new Date(_event.scheduled_on), "HH:mm");
      setInitialValues({
        name: _event.name,
        media: getAssetUrl({
          media: _event.media[0],
          defaultType: "background",
        }),
        description: _event.description,
        date: date,
        time: time,
        duration: {
          type: duration_type,
          value: duration_value,
        },
        ticket: {
          type: _event.ticket_type,
          price: _event.price,
          release: {
            type: _event.quantity_type,
            // @ts-expect-error showing amount should be string or number
            amount: _event.quantity || "",
          },
        },
        visibility: _event.visibility as PostType,
      });
    })().catch((e) => {
      console.error(e);

      if (e?.statusCode === 404) {
        swalFire(
          "Event Not Found",
          "The event you are looking for does not exist.",
          "error"
        ).then(() => navigateBack());
      } else {
        swalFire("Oops! Something went wrong", "", "error").then(() =>
          navigateBack()
        );
      }
    });
  }, []);

  useEffect(() => {
    if (!event) return;

    const t =
      new Date(event.scheduled_on) >
      new Date(Date.now() + EVENT_LIMITS.TIME_BUFFER_MIN);

    if (isEditTime !== t) {
      swalFire(
        "Event Modification Not Allowed",
        "Modifications are not permitted if the event is scheduled to start within the next 5 minutes.",
        "error"
      );
      setIsEditTime(t);
    }
  }, [event]);

  const onSubmit = async (data: NonNullable<typeof initialValues>) => {
    const body: Partial<typeof data> = Object.keys(data).reduce((acc, key) => {
      //@ts-expect-error to be handle later
      if (!isEqual(data[key], initialValues[key])) {
        //@ts-expect-error to be handle later
        return { ...acc, [key]: data[key] };
      }

      return acc;
    }, {});

    if (body.date || body.time) {
      body.date = data.date;
      body.time = data.time;
    }

    try {
      await EventUpdate(params.eventId, body);
      swalFire("Update Successful", "The event has been updated successfully.");
      setInitialValues(JSON.parse(JSON.stringify(data)));
    } catch (e: any) {
      console.error(e);

      if (e.errorCode === 1000) {
        Swal.fire({
          icon: "warning",
          title: "Oops! Some words aren’t allowed.",
          text: `The word "${e.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
        });
      } else {
        Swal.fire({
          icon: "error",
          title: e.message,
          showCloseButton: true,
        });
      }
    }
  };

  const onDelete = async (e: SyntheticEvent) => {
    e.preventDefault();

    try {
      await swalWarning(
        "Confirmation Required",
        "This action is irreversible. Please confirm if you wish to proceed.",
        "Delete This Event"
      );
      await swalWarning(
        "Confirm Event Deletion",
        "Upon confirming, all sold tickets will be refunded to the users.<br/>Please ensure you want to proceed with deleting this event.",
        "Confirm Event Deletion"
      );
      await EventDelete(params.eventId);
      await swalFire(
        "Delete Successful",
        "The event has been deleted successfully."
      );
      navigateBack();
    } catch (e) {
      if (!e) return;
      console.error(e);
      swalFire("Delete Failed", "Failed to delete the event.", "error");
    }
  };

  if (initialValues === null || event === null) return <></>;

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
      initialTouched={Object.keys(initialValues).reduce(
        (acc, key) => ({ ...acc, [key]: true }),
        {}
      )}
      enableReinitialize={true}
    >
      {({ values, setFieldValue, ...rest }) => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const lastReleaseAmount = useRef(values.ticket.release.amount);
        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          if (values.ticket.release.type === "Unlimited") {
            lastReleaseAmount.current = values.ticket.release.amount;
            setFieldValue("ticket.release.amount", "");
          } else {
            setFieldValue("ticket.release.amount", lastReleaseAmount.current);
          }
        }, [values.ticket.release.type]);

        return (
          <Form
            title="Edit event"
            nextBtnText="Update"
            formikValues={{ ...rest, values, setFieldValue }}
          >
            <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
              <Wrapper
                title="Event Information"
                className="w-lg-75"
                parentClass={classNames({
                  [styles["filter-disable"]]: !isEditTime,
                })}
              >
                <Input
                  name="name"
                  label="Event Name"
                  placeholder="Example: My Private Event Show"
                />
                <FileInput
                  name="media"
                  accept="*"
                  values={values}
                  setFieldValue={setFieldValue}
                >
                  <div className="d-flex flex-column align-items-center justify-content-center text-center p-2 px-3 border border-1 border-style-dashed border-opacity-50">
                    <Image
                      src="/images/post/line-plus.svg"
                      width={30}
                      height={30}
                      alt="plus_icon"
                    />
                    <h5>Upload photo or video</h5>
                    <p className="color-medium">
                      *Files supported: JPG, PNG or MP4 (upto 30s)
                    </p>
                  </div>
                </FileInput>
              </Wrapper>
              <Wrapper
                title="Description"
                counter={{ value: values.description, max: 400 }}
                parentClass={classNames({
                  [styles["filter-disable"]]: !isEditTime,
                })}
              >
                <TextArea
                  name="description"
                  type="text"
                  placeholder="Say somethings to users know more about your event here"
                />
              </Wrapper>
              <Wrapper
                title="Details"
                className="w-lg-75"
                parentClass={classNames({
                  [styles["filter-disable"]]: !isEditTime,
                })}
              >
                <div
                  className={classNames("d-flex gap-3", {
                    [styles["filter-disable"]]: paidTicketsSold,
                  })}
                >
                  <Input
                    name="date"
                    type="date"
                    label="Date"
                    placeholder="12/02/2001"
                    maxDate={Date.now() + EVENT_LIMITS.TIME_BUFFER_MAX}
                    disabled={event.sold_ticket_count > 0}
                  />
                  <Input
                    name="time"
                    type="time"
                    label="Time"
                    placeholder="10:00"
                  />
                </div>
                {paidTicketsSold && (
                  <InlineWarning>{`Event datetime can't be changed since ${event.sold_ticket_count} ticket(s) have already been sold.`}</InlineWarning>
                )}
                <Input
                  name="duration.value"
                  type="number"
                  label="How long is your event?"
                  placeholder="0"
                >
                  <InputGroup
                    label=""
                    type="radio"
                    theme="square-filled-purple"
                    name="duration.type"
                    className="position-absolute bottom-0 end-0 scale-p7 me-2"
                    array={[
                      { text: "Minute", value: "min" },
                      { text: "Hour", value: "hour" },
                    ]}
                    selected={values.duration.type}
                  />
                </Input>
              </Wrapper>
            </div>
            <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
              <Wrapper
                title="Price & Ticket"
                parentClass={classNames({
                  [styles["filter-disable"]]: !isEditTime,
                })}
              >
                <InputGroup
                  label=""
                  type="radio"
                  theme="circle-purple"
                  name="ticket.type"
                  className={classNames("align-items-md-start", {
                    [styles["filter-disable"]]: paidTicketsSold,
                  })}
                  childClass="ms-1"
                  array={[
                    { text: "Paid", value: "Fee" },
                    { text: "Free", value: "Free" },
                  ]}
                  selected={values.ticket.type}
                />
                {paidTicketsSold && (
                  <InlineWarning>{`Event ticket type can't be changed since ${event.sold_ticket_count} ticket(s) have already been sold.`}</InlineWarning>
                )}
                <hr className="m-0 border-2" style={{ color: "lightgray" }} />
                {values.ticket.type === "Free" ? (
                  <></>
                ) : (
                  <Input
                    name="ticket.price"
                    type="number"
                    label="Price"
                    placeholder="0"
                    priceInput={true}
                    className={classNames("w-lg-75", {
                      "mb-4": rest.errors.ticket?.price,
                    })}
                  >
                    <span className="color-medium fs-7">
                      Minimum{" "}
                      {formatCurrency(EVENT_LIMITS.STREAM_TICKET_PRICE_MIN)} USD
                    </span>
                  </Input>
                )}

                <Input
                  label="How many tickets do you want to release?"
                  name="ticket.release.amount"
                  type={
                    values.ticket.release.type === "Unlimited"
                      ? "text"
                      : "number"
                  }
                  placeholder={
                    values.ticket.release.type === "Unlimited"
                      ? "Unlimited"
                      : "0"
                  }
                  disabled={values.ticket.release.type === "Unlimited"}
                  className={classNames("w-lg-75", {
                    "mb-4":
                      values.ticket.release.type === "Limited" &&
                      rest.errors.ticket?.release?.amount,
                  })}
                >
                  <InputGroup
                    label=""
                    type="radio"
                    theme="square-filled-purple"
                    name="ticket.release.type"
                    className="position-absolute bottom-0 end-0 scale-p7 me-2"
                    array={[
                      { text: "Limited", value: "Limited" },
                      { text: "Unlimited", value: "Unlimited" },
                    ]}
                    selected={values.ticket.release.type}
                  />
                </Input>
              </Wrapper>
              <Wrapper
                title="Who can see your event?"
                className="w-lg-75"
                parentClass={classNames({
                  [styles["filter-disable"]]: !isEditTime,
                })}
              >
                <Select
                  name="visibility"
                  label=""
                  array={PostVisibility.filter((v) =>
                    ["Public", "OnlyMe"].includes(v.value)
                  )}
                  selected={values.visibility}
                  setFieldValue={setFieldValue}
                  className={classNames({ "mb-4": rest.errors.visibility })}
                />
              </Wrapper>
              <Button
                icon="/images/svg/delete-group.svg"
                text="Delete my event"
                className="d-flex align-items-start gap-2 fw-medium text-danger bg-body py-2 rounded-lg-3"
                isValid={true}
                dirty={true}
                isSubmitting={rest.isSubmitting}
                onClick={onDelete}
              />
            </div>
          </Form>
        );
      }}
    </Formik>
  );
}

function InlineWarning(props: { children: string }) {
  return (
    <div
      className="text-dark"
      style={{
        fontSize: "0.8em",
        marginTop: "-1.2em",
        marginBottom: "-1em",
      }}
    >
      {props.children}
    </div>
  );
}

function swalWarning(title: string, text: string, confirmText: string) {
  return new Promise<void>((resolve, reject) => {
    Swal.fire({
      imageUrl: "/images/event/warning-red.svg",
      imageHeight: 50,
      imageAlt: "Warn Icon",
      width: 600,
      title: title,
      html: text,
      confirmButtonText: confirmText,
      cancelButtonText: "Cancel",
      showCancelButton: true,
      showCloseButton: true,
      customClass: {
        image: "mb-0",
        title: "fw-semibold ",
        actions: "flex-wrap flex-md-nowrap w-75 gap-3",
        confirmButton: classNames(
          "w-100 w-md-50 btn btn-outline-danger fw-medium text-nowrap",
          styles["border-1"]
        ),
        cancelButton: "w-100 w-md-50 btn btn-purple fw-medium",
      },
      buttonsStyling: false,
    }).then((r) => {
      if (r.isConfirmed) resolve();
      else reject();
    });
  });
}

function swalFire(
  title: string,
  text: string,
  icon: "success" | "error" = "success"
) {
  return Swal.fire({
    width: 600,
    icon: icon,
    title: title,
    html: text,
    confirmButtonText: "Ok",
    showCloseButton: true,
    customClass: {
      image: "mb-0",
      title: "fw-semibold ",
      actions: "flex-wrap flex-md-nowrap w-75 gap-3",
      confirmButton: "w-100 w-md-50 btn btn-purple fw-medium",
    },
    buttonsStyling: false,
  });
}
