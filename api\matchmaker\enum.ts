export enum DrinkingHabits {
  NOT_FOR_ME = "Not for me",
  SOBER = "Sober",
  SOBER_CURIOUS = "Sober curios",
  SPECIAL_OCCASIONS = "Special occassions",
  SOCIALLY_ON_WEEKENDS = "Socially on weekends",
  MOST_NIGHTS = "Most nights",
}

export enum SmokingHabits {
  SOCIAL_SOMKER = "Social smoker",
  SMOKE_WHEN_DRINK = "Smoke when drink",
  NON_SMOKER = "Non-smoker",
  SMOKER = "Smoker",
}

export enum WorkoutHabit {
  EVERYDAY = "Everyday",
  OFTEN = "Often",
  SOMETIMES = "Sometimes",
  NEVER = "Never",
}

export enum PetInterest {
  DOG = "Dog",
  CAT = "Cat",
  REPTILE = "Reptile",
  AMPHIBIAN = "Amphibian",
  BIRD = "Bird",
  FISH = "Fish",
  DONT_HAVE_BUT_LOVE = "Don't have but love",
  OTHER = "Other",
  TURTLE = "Turtle",
  HAMSTER = "Hamster",
  RABBIT = "Rabbit",
  PET_FREE = "Pet-Free",
  ALL_PETS = "All the pets",
  WANT_ONE = "Want a pet",
  ALLERGIC = "Allergic to pets",
}

export enum CommunicationStyle {
  WHATAPP_ALL_DAY = "I stay on whatsapp all day",
  BIG_TEXTER = "Big time texter",
  PHONE_CALLER = "Phone caller",
  VIDEO_CALLER = "Video chatter",
  SLOW_ON_WHATSAPP = "I'm slow to answer on whatsapp",
  BAD_TEXTER = "Bad texter",
  BETTER_IN_PERSON = "Better in person",
}

export enum ReceiveLoveStyle {
  THOUGHTFUL_GESTURES = "Thoughtful gestures",
  PRESENTS = "Presents",
  TOUCH = "Touch",
  COMPLIMENTS = "Compliments",
  TIME_TOGETHER = "Time together",
}

export enum Education {
  BACHELOR = "Bachelor",
  IN_COLLEGE = "In college",
  HIGH_SCHOOL = "High School",
  PHD = "PhD",
  IN_GRAD_SCHOOL = "In Grad School",
  MASTER = "Master",
  TRADE_TOGETHER = "Trade together",
}

export enum Zodiac {
  CAPRICORN = "Capricorn",
  AQUARIUS = "Aquarius",
  PISCES = "Pisces",
  ARIES = "Aries",
  TAURUS = "Taurus",
  GEMINI = "Gemini",
  CANCER = "Cancer",
  LEO = "Leo",
  VIRGO = "Virgo",
  LIBRA = "Libra",
  SCORPIO = "Scorpio",
  SAGITTARIUS = "Sagittarius",
}

export enum Professions {
  BAKER = "Baker",
  BIOLOGIST = "Biologist",
  CHEF = "Chef",
  DANCER = "Dancer",
  DENTIST = "Dentist",
  ENGINEER = "Engineer",
  FARMER = "Farmer",
  FIREFIGHTER = "Firefighter",
  GRAPHIC_DESIGNER = "Graphic Designer",
  HAIRDRESSER = "Hairdresser",
  INTERIOR_DESIGNER = "Interior Designer",
  JOURNALIST = "Journalist",
  LAWYER = "Lawyer",
  MUSICIAN = "Musician",
  ACTOR = "Actor",
  ANIMATOR = "Animator",
}

interface Brackets {
  [key: string]: {
    min: number;
    max: number;
  };
}

export const PrivatEarningBrackets: Brackets = {
  "Less than $50k": {
    min: 0,
    max: 50_000,
  },
  "$50k - $100k": {
    min: 50_001,
    max: 100_000,
  },
  "$100k - $200k": {
    min: 100_001,
    max: 200_000,
  },
  "$200k - $400k": {
    min: 200_001,
    max: 400_000,
  },
  "$400k - $600k": {
    min: 400_001,
    max: 600_000,
  },
  "$600k - $1M": {
    min: 600_001,
    max: 1_000_000,
  },
  "More than $1M": {
    min: 1_000_001,
    max: 1_000_002,
  },
};

export enum LookingForRelationship {
  LONG_TERM_PARTNER = "Long-term partner",
  LONG_TERM_BUT_SHORT_TERM_OK = "Long-term, but short-term ok",
  SHORT_TERM_BUT_LONG_TERM_OK = "Short-term, but long-term ok",
  SHORT_TERM_FUN = "Short-term fun",
  NEW_FRIENDS = "New friends",
  STILL_FIGURING_OUT = "Still figuring it out",
}

export enum OpenToRelationship {
  MONOGAMY = "Monogamy",
  OPEN_RELATIONSHIP = "Open relationship",
  POLYAMORY = "Polyamory",
  OPEN_TO_EXPLORE = "Open to explore",
}

export enum Language {
  ENGLISH = "English|eng",
  SPANISH = "Spanish|spa",
  FRENCH = "French|fra",
  GERMAN = "German|deu",
  PORTUGUESE = "Portuguese|por",
  ITALIAN = "Italian|ita",
  DUTCH = "Dutch|nld",
  POLISH = "Polish|pol",
  SWAHILI = "Swahili|swa",
  ROMANIAN = "Romanian|ron",
  TURKISH = "Turkish|tur",
  VIETNAMESE = "Vietnamese|vie",
  CZECH = "Czech|ces",
  SWEDISH = "Swedish|swe",
  GREEK = "Greek|gre",
  RUSSIAN = "Russian|rus",
  UKRAINIAN = "Ukrainian|ukr",
  BULGARIAN = "Bulgarian|bul",
  SERBIAN = "Serbian|srp",
  MACEDONIAN = "Macedonian|mac",
  ARABIC = "Arabic|ara",
  PERSIAN = "Persian|fas",
  URDU = "Urdu|urd",
  PASHTO = "Pashto|pus",
  KURDISH_SORANI = "Kurdish Sorani|kur",
  HINDI = "Hindi|hin",
  MARATHI = "Marathi|mar",
  NEPALI = "Nepali|nep",
  SANSKRIT = "Sanskrit|san",
  TELUGU = "Telugu|tel",
  BENGALI = "Bengali|ben",
  GUJARATI = "Gujarati|guj",
  ODIA = "Odia|ory",
  KANNADA = "Kannada|kan",
  MANDARIN_CHINESE = "Mandarin Chinese|zho",
  CANTONESE = "Cantonese|yue",
  JAPANESE = "Japanese|jpn",
  KOREAN = "Korean|kor",
  WU_CHINESE = "Wu Chinese|wuu",
  YUE_CHINESE = "Yue Chinese|yue",
  HAKKA_CHINESE = "Hakka Chinese|hak",
  JIN_CHINESE = "Jin Chinese|jin",
  XIANG_CHINESE = "Xiang Chinese|hsn",
  MIN_NAN_CHINESE = "Min Nan Chinese|nan",
  KOREAN_HANGUL = "Korean (Hangul)|kor",
  HEBREW = "Hebrew|heb",
  YIDDISH = "Yiddish|yid",
  THAI = "Thai|tha",
  LAO = "Lao|lao",
  GEORGIAN = "Georgian|kat",
  AMHARIC = "Amharic|amh",
  TIGRINYA = "Tigrinya|tig",
  ARMENIAN = "Armenian|arm",
  TAMIL = "Tamil|tam",
  SINHALESE = "Sinhalese|sin",
  PUNJABI = "Punjabi|pan",
  SYRIAC = "Syriac|syc",
  TIBETAN = "Tibetan|tib",
  CHEROKEE = "Cherokee|chr",
  HAITIAN_CREOLE = "Haitian Creole|hat",
  KICHE = "K'iche'|kic",
  YUCATEC_MAYA = "Yucatec Maya|yua",
  JAVANESE = "Javanese|jav",
  MALAYALAM = "Malayalam|mal",
  IGBO = "Igbo|ibo",
  HAUSA = "Hausa|hau",
  BURMESE = "Burmese|mya",
  SUNDANESE = "Sundanese|sun",
  YORUBA = "Yoruba|yor",
  MALAY = "Malay|msa",
}

export enum MatchWith {
  MALE = "Male",
  FEMALE = "Female",
  OTHER = "Other",
}
