import { Formik } from "formik";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import * as Yup from "yup";
import "../index.scss";

import {
  SendChannelTip,
  SendEventTip,
  SendPostTip,
  SendStoryTip,
  SendUserTip,
} from "@/api/subscriptions";
import Badges from "@/components/common/badges";
import Form from "@/components/common/form";
import Input from "@/components/common/input";
import { PaymentMethod } from "@/components/common/payment-method";
import TextArea from "@/components/common/textarea";
import { useRegisterModal } from "@/hooks/useRegisterModal";
import { configActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { fetchWalletBalance } from "@/redux-store/slices/wallet.slice";
import { type Author } from "@/types/post";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";

interface TipDetail {
  postId: string;
  author: Author;
  type: string;
  source?: "chat" | "profile";
}

export default function SendTipModal() {
  const [postDetail, setPostDetail] = useState<TipDetail>({
    postId: "",
    author: {
      username: "",
      avatar: [],
      display_name: "",
      role: "",
      user_type: "",
      _id: "",
      pic: "",
    },
    type: "",
  });
  const [customTip, setCustomTip] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const dispatchAction = useAppDispatch();

  const modalRef = useRef<any>(null);
  const formikRef = useRef<any>(null);

  const amounts = [10, 20, 200, 500, 1000, "Custom"];

  const initialValues = {
    tip_amount: "",
    message: "",
  };

  const [showFrame, setShowFrame] = useState(false);
  const [iframeLink, setIframeLink] = useState("");

  const tax_percentage = useAppSelector(
    (state) => state?.user.taxInformation.tax_percentage
  );

  const validationSchema = Yup.object().shape({
    tip_amount: Yup.number()
      .min(5, "Tip amount should not be less than 5")
      .required("Tip amount is required")
      .max(5000, "Tip amount should not exceed 5000"),
  });

  useEffect(() => {
    if (window.bootstrap) {
      const initializeModal = () => {
        const myModal = new window.bootstrap.Modal(
          modalRef.current as HTMLElement,
          {
            backdrop: "static",
            keyboard: false,
          }
        );
        modalRef.current!.modalInstance = myModal;
      };

      setTimeout(initializeModal, 300);
    }
  }, [window.bootstrap]);

  useRegisterModal("SEND_TIP", (data) => {
    setPostDetail(data!);
    modalRef.current?.modalInstance?.show();
  });

  const closeModal = () => {
    modalRef.current?.modalInstance.hide();
    setCustomTip(false);
    setSelectedItem(10);

    if (formikRef.current) {
      formikRef.current.resetForm();
      formikRef.current.setFieldValue("tip_amount", "10");
    }
  };

  const handleSendTip = (
    tipObject: any,
    successMessage: string,
    apiCall: () => Promise<any>
  ) => {
    Swal.fire({
      icon: "warning",
      title: `Proceed with sending ${formatCurrency(
        tipObject.amount + (tipObject.amount * tax_percentage) / 100
      )} tip to ${postDetail.author?.display_name}`,
      customClass: {
        confirmButton: "custom-btn",
        cancelButton: "custom-btn",
      },
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      confirmButtonColor: "#AC1991",
      showCancelButton: true,
      showCloseButton: true,
    }).then((result: any) => {
      if (result.isConfirmed) {
        dispatchAction(configActions.toggleCardProcessing());
        apiCall()
          .then((res) => {
            dispatchAction(configActions.toggleCardProcessing());

            if (res?.data?.action) {
              setShowFrame(true);
              setIframeLink(res?.data?.action);
              return;
            }

            closeModal();
            dispatchAction(fetchWalletBalance());

            if (res?.data?.action) {
              setShowFrame(true);
              setIframeLink(res?.data?.action);
              return;
            }

            Swal.fire({
              icon: "success",
              title: successMessage,
              confirmButtonText: "Finish",
              confirmButtonColor: "#AC1991",
              denyButtonText: "Send another",
              showDenyButton: true,
              customClass: {
                actions: "vertical-btn",
                confirmButton: "tip-success-btn",
                title: "tip-title",
                denyButton: "tip-deny-btn",
              },
              showCloseButton: true,
            }).then((res: any) => {
              if (res.isDenied) {
                modalRef.current?.modalInstance.show();
              }
            });
          })
          .catch((error) => {
            dispatchAction(configActions.toggleCardProcessing());

            Swal.fire({
              icon: "error",
              title: error.message,
              confirmButtonText: "Finish",
              confirmButtonColor: "#AC1991",
              showCloseButton: true,
            });
          });
      }

      if (result.isDismissed) {
        closeModal();
      }
    });
  };

  const onSubmit = (values: any) => {
    const tipObject = {
      amount: +values.tip_amount,
      payment_mode: values.payment_type,
      ...(values.payment_type === "card" && {
        tokenised_card_id: values.payment_id,
      }),
      ...(postDetail?.source === "chat" ? { source: "chat" } : {}),
      ...(values.message && { message: values.message }),
    };

    const apiMapping: any = {
      post: () => SendPostTip(postDetail.postId, tipObject),
      story: () => SendStoryTip(postDetail.postId, tipObject),
      channel: () => SendChannelTip(postDetail.postId, tipObject),
      event: () => SendEventTip(postDetail.postId, tipObject),
      default: () => SendUserTip(postDetail.postId, tipObject),
    };

    const apiCall = apiMapping[postDetail.type] || apiMapping.default;
    const successMessage = `Tip sent!`;

    handleSendTip(tipObject, successMessage, apiCall);
  };

  return (
    <div
      className="modal fade"
      id="sendTipModal"
      ref={modalRef}
      tabIndex={-1}
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content">
          <div className="modal-body">
            {showFrame ? (
              <div
                style={{
                  height: "68dvh",
                }}
              >
                <iframe
                  src={iframeLink}
                  frameBorder="0"
                  height={"100%"}
                  width={"100%"}
                ></iframe>
              </div>
            ) : (
              <>
                <div className="d-flex justify-content-end">
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => closeModal()}
                    aria-label="Close"
                  ></button>
                </div>
                <div className="tip-wrapper px-5">
                  <h1 className="text-center fw-bold mb-3">Confirm Tip</h1>
                  <div className="d-flex gap-3 align-items-center">
                    <Image
                      src={
                        postDetail?.author?.pic ||
                        getAssetUrl({
                          media: postDetail?.author?.avatar?.[0],
                          defaultType: "avatar",
                        })
                      }
                      alt=""
                      width={48}
                      height={48}
                      className="rounded-circle mb-3"
                    />
                    <div className="d-flex flex-column">
                      <div className="d-flex align-items-center justify-content-center gap-1">
                        <p className="m-0 fw-semibold">
                          {postDetail?.author?.display_name}
                        </p>
                        <Badges
                          array={postDetail?.author?.badges || ([] as any)}
                        />
                      </div>
                      <p className="color-medium fs-7">
                        @{postDetail?.author?.username}
                      </p>
                    </div>
                  </div>

                  <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={() => console.log("first")}
                    innerRef={formikRef}
                  >
                    {({
                      isSubmitting,
                      isValid,
                      dirty,
                      values,
                      setFieldValue,
                    }) => {
                      // eslint-disable-next-line react-hooks/rules-of-hooks
                      useEffect(() => {
                        if (!customTip) setFieldValue("tip_amount", "10");
                        setSelectedItem(10);
                      }, []);
                      return (
                        <Form
                          formikValues={{ values, setFieldValue }}
                          className="w-100 flex-lg-column"
                        >
                          <div className="w-100">
                            <div className="payment-amount-wrapper">
                              {amounts.map((item) => (
                                <div
                                  onClick={() => {
                                    if (item === "Custom") {
                                      setFieldValue("tip_amount", "100");
                                    } else {
                                      setFieldValue("tip_amount", item);
                                    }

                                    setSelectedItem(item);
                                    setCustomTip(
                                      item === "Custom" ? true : false
                                    );
                                  }}
                                  className={`payment-amounts ${
                                    selectedItem === item ? "active-amount" : ""
                                  }`}
                                  key={item}
                                >
                                  {item !== "Custom"
                                    ? formatCurrency(+item).split(".")[0]
                                    : item}
                                </div>
                              ))}
                            </div>
                            {customTip && (
                              <div className="d-flex flex-column w-100 align-items-center flex-grow-1 mt-3">
                                <Input
                                  name="tip_amount"
                                  label="How much you would like to tip?"
                                  placeholder={"Enter the amount"}
                                  priceInput={true}
                                  type={"number"}
                                ></Input>
                              </div>
                            )}
                          </div>
                          <TextArea
                            name="message"
                            placeholder="Enter your message"
                            label="Message (optional)"
                            required={false}
                          />
                          <PaymentMethod
                            amount={+values.tip_amount}
                            isBtnValid={isValid}
                            onSubmit={(paymentInfo) => {
                              onSubmit({
                                ...values,
                                ...paymentInfo,
                              });
                            }}
                            onClose={closeModal}
                            type="Tip"
                          />
                        </Form>
                      );
                    }}
                  </Formik>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
