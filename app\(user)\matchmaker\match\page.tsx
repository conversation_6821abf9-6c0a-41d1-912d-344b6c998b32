// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

"use client";

import classNames from "classnames";
import { driver } from "driver.js";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import {
  EditLocation,
  GetYourMatches,
  PeopleWhoLikedYou,
  PeopleYouLiked,
  UpdateMatchProfile,
} from "@/api/matchmaker/matchmaker.api";
import type { ModalRef } from "@/components/common/modal";
import MatchCards from "@/components/matchmaker/match-cards";
import {
  FilterIcon,
  InformationIcon,
  UpdateProfileIcon,
} from "@/components/matchmaker/match-cards/utils/svg";
import MatchWith from "@/components/matchmaker/match-with";
import Filters from "@/components/matchmaker/match-with/filters";
import ProfileCard from "@/components/matchmaker/match-with/profile-card";
import LockedProfile from "@/components/matchmaker/match-with/profile-card/locked-profile";
import NoLikes from "@/components/matchmaker/match-with/profile-card/no-likes";
import ProfileCardSkeleton from "@/components/matchmaker/match-with/profile-card/profile-card-skeleton";
import EditMatchProfile from "@/components/matchmaker/modal/edit-profile";
import PlanModal from "@/components/matchmaker/modal/plans";
import UpgradeBar from "@/components/matchmaker/updgrade-bar";
import { UpgradeBarPlanDescription } from "@/components/matchmaker/updgrade-bar/utils";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import { matchMakerActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import "driver.js/dist/driver.css";
import "./Popover.scss";

const matchPlansRef = { method: {} as ModalRef };

export default function Matchfinder() {
  const dispatch = useAppDispatch();
  const navigateBack = useNavigateBack();
  const searchParams = useSearchParams();

  const filterRef = useRef<any | null>(null);
  const infoRef = useRef<any | null>(null);

  const matchWith = useAppSelector((state) => state.matchmakerData.matchWith);

  const [selectUser, setSelectUser] = useState<string>(matchWith.toLowerCase());
  const [selectTab, setSelectTab] = useState<number>(1);
  const [showFilter, setShowFilter] = useState<boolean>(false);
  const currentPlan = useAppSelector(
    (state) => state.matchmakerData.currentPlan
  );
  const [loading, setLoading] = useState<boolean>(false);
  const user = useAppSelector((state) => state.user);
  const [yourMatches, setYourMatches] = useState<Record<string, any>[]>([]);
  const [peopleLikedYou, setPeopleLikedYou] = useState<Record<string, any>[]>(
    []
  );
  const [_openInfo, setOpenInfo] = useState<boolean>(false);
  const [finalHeight, setFinalHeight] = useState(0);

  const currentProfile = useAppSelector(
    (state) => state.matchmakerData.currentProfile
  );

  const isProfileActivated = useAppSelector(
    (state) => state.matchmakerData.is_activated
  );
  const userRole = useAppSelector((state) => state.user.role);
  const router = useRouter();
  const hasBlockedMe = useAppSelector((state) => state.block.hasBlockedMe);
  const blockedByMe = useAppSelector((state) => state.block.blockedByMe);
  const locationAllowed = useAppSelector((s) => s.defaults.locationAllowed);
  const has_explored = useAppSelector((s) => s.matchmakerData.has_explored);
  const doesHaveCards = useAppSelector((s) => s.matchmakerData.doesHaveCards);

  useEffect(() => {
    if (
      currentProfile?.has_explored === false &&
      doesHaveCards &&
      has_explored === false &&
      ((window.innerWidth <= 576 && searchParams.get("type") === "match") ||
        window.innerWidth > 576)
    ) {
      const driverObj = driver({
        showProgress: true,
        animate: true,
        allowKeyboardControl: true,
        steps: [
          {
            element: ".page-header",
            popover: {
              onPopoverRender: (popover) => {
                const firstButton = document.createElement("button");
                firstButton.innerText = "I already know";
                popover.footerButtons.appendChild(firstButton);

                firstButton.addEventListener("click", () => {
                  UpdateMatchProfile(
                    {
                      has_explored: true,
                    },
                    currentProfile?._id
                  );
                  dispatch(matchMakerActions.setHasExplored(true));
                  driverObj.destroy();
                });
              },
              title: "Let's take a look how it works",
              description:
                "This is a quick tutorial to help you get familiar with the basic features of our match 😎",
              nextBtnText: "Discover",
              doneBtnText: "I already know",
              showButtons: ["next"],
              popoverClass: "main-popover",
            },
          },
          ...(user.role === "user"
            ? [
                {
                  element:
                    window.innerWidth > 768
                      ? ".match-with-container"
                      : ".match-with-container-1",
                  popover: {
                    title: "Choose who you want to match with",
                    description: "Creator or people like you",
                    side: "bottom",
                    nextBtnText: "Next",
                    align: "center",
                    showButtons: ["next"],
                  },
                },
              ]
            : []),
          {
            element:
              window.innerWidth > 768
                ? ".people-liked-you"
                : ".mobile-match-nav",
            popover: {
              title: "Creator, People liked you and your matches",
              description: "",
              align: "center",
              nextBtnText: "Next",
              showButtons: ["next"],
            },
          },
          {
            element: ".match-card-container",
            popover: {
              title: "Here is the user information",
              description: "",
              align: "center",
              nextBtnText: "Next",
              showButtons: ["next"],
            },
          },
          {
            element: ".right-side-card",
            popover: {
              title: "Click on the right side to see next content!",
              description: "",
              nextBtnText: "Next",
              side: "right",
            },
          },
          {
            element: ".left-side-card",
            popover: {
              title: "Click on the left side to see previous content!",
              description: "",
              nextBtnText: "Next",
              side: "left",
              align: "start",
            },
          },
          {
            element: ".pass-button",
            popover: {
              title: "You don't like this person? Click here to pass",
              showButtons: ["next"],
              nextBtnText: "Next",
            },
          },
          {
            element: ".like-button",
            popover: {
              title: "First step to be a match here 😍",
              showButtons: ["next"],
              nextBtnText: "Next",
            },
          },
          {
            element: ".page-header",
            popover: {
              title:
                "These are just some simple tips, a lot of other premium features ahead 😎",
              nextBtnText: "Woho! Let's match",
              nextBtnText: "Next",
              showButtons: ["next"],
              onNextClick: () => {
                UpdateMatchProfile(
                  {
                    has_explored: true,
                  },
                  currentProfile?._id
                ).then(() => {
                  dispatch(matchMakerActions.setHasExplored(true));
                  driverObj.destroy();
                });
              },
            },
          },
        ],
      });

      driverObj.drive();
    }
  }, [
    searchParams.get("type"),
    has_explored,
    doesHaveCards,
    currentProfile?.has_explored,
  ]);

  useEffect(() => {
    dispatch(matchMakerActions.setMatchWith(selectUser.toUpperCase()));
  }, [selectUser]);

  useEffect(() => {
    EditLocation({
      latitude: Number(sessionStorage.getItem("lat")),
      longitude: Number(sessionStorage.getItem("long")),
      match_profile_id: currentProfile?._id,
    })
      .then((res) => {
        console.log(res, "Done");
      })
      .catch((err) => {
        console.log(err, "Error");
      });
  }, [locationAllowed]);

  useEffect(() => {
    if (selectTab === 1) {
      router.replace("/matchmaker/match?type=match");
    } else if (selectTab === 2) {
      router.replace("/matchmaker/match?type=like");
    } else if (selectTab === 3) {
      router.replace("/matchmaker/match?type=you-like");
    }
  }, [selectTab]);

  useEffect(() => {
    if (searchParams.get("type") === "match") {
      setSelectTab(1);
    }
  }, [searchParams.get("type")]);

  useEffect(() => {
    if (user?.role === "guest" || !user.loggedIn) {
      router.push("/");
    }
  }, [user.role, user.loggedIn]);

  useEffect(() => {
    setLoading(true);

    if (selectTab === 2) {
      PeopleWhoLikedYou({ match_profile_id: currentProfile?._id })
        .then((result) => {
          const res = result.data.filter(
            (profile: any) =>
              !(
                blockedByMe[profile?.user_match_id?.user?._id] ||
                hasBlockedMe[profile?.user_match_id?.user?._id]
              )
          );
          setPeopleLikedYou(res);
        })
        .finally(() => setLoading(false));
    } else if (selectTab === 3) {
      if (
        user.role === "creator" ||
        (user.role === "user" && selectUser === "creator")
      ) {
        PeopleYouLiked({ match_profile_id: currentProfile?._id })
          .then((result) => {
            const res = result.data
              .filter(
                (profile: any) =>
                  !(
                    blockedByMe[profile?.target_match_id?.user?._id] ||
                    hasBlockedMe[profile?.target_match_id?.user?._id]
                  )
              )
              .filter((profile: any) => {
                return (
                  profile?.target_match_id?.user_type === matchWith ||
                  profile?.match_profile?.user_type === matchWith
                );
              });
            setYourMatches(res);
          })
          .finally(() => setLoading(false));
      } else
        GetYourMatches({ match_profile_id: currentProfile?._id })
          .then((result) => {
            const res = result.data
              .filter(
                (profile: any) =>
                  !(
                    blockedByMe[profile?.target_match_id?.user?._id] ||
                    hasBlockedMe[profile?.target_match_id?.user?._id]
                  )
              )
              .filter((profile: any) => {
                return (
                  profile?.target_match_id?.user_type === matchWith ||
                  profile?.match_profile?.user_type === matchWith
                );
              });
            setYourMatches(res);
          })
          .finally(() => setLoading(false));
    }
  }, [selectTab, matchWith, currentProfile, hasBlockedMe, blockedByMe]);

  useEffect(() => {
    if (!user.loggedIn) router.push("/fresh");

    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.get("type") === "like") {
      setSelectTab(2);
    } else if (urlParams.get("type") === "you-like") {
      setSelectTab(3);
    }

    function handleClickOutside(event: any) {
      if (infoRef.current && !infoRef?.current?.contains(event.target)) {
        setOpenInfo(false);
      }
    }

    document.body.addEventListener("click", handleClickOutside);

    return () => {
      document.body.removeEventListener("click", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    function handleClickOutside(event: any) {
      if (filterRef.current && !filterRef.current.contains(event.target)) {
        setShowFilter(false);
      }
    }

    document.body.addEventListener("click", handleClickOutside);

    return () => {
      document.body.removeEventListener("click", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const height1Element = document.querySelector(".header-height");
    const height2Element = document.querySelector(".height-2");
    const height3Element = document.querySelector(".footer-height");
    const height4Element = document.querySelector(".verification-bar");

    if (height1Element && height2Element && height3Element) {
      const height1 = height1Element.clientHeight;
      const height2 = height2Element.clientHeight;
      const height3 = height3Element.clientHeight;

      if (height4Element) {
        const height4 = height4Element.clientHeight;
        setFinalHeight(height1 + height2 + height3 + height4);
      } else {
        setFinalHeight(height1 + height2 + height3);
      }
    }
  }, [document, doesHaveCards]);

  return (
    user.role !== "guest" && (
      <div className="container-xxl p-0 h-100">
        <div
          className="d-flex justify-content-center gap-3 align-items-center position-relative overflow-md-hidden row w-100 m-0 align-items-stretch h-100"
          style={{
            overflowX: "hidden",
          }}
        >
          <div className="position-absolute top-0 start-0 m-2 w-fit">
            <Image
              src="/images/svg/back.svg"
              alt="Search Logo"
              className="d-none d-md-block pointer"
              width={54}
              height={54}
              priority
              onClick={() => {
                navigateBack();
              }}
            />
          </div>

          <div className="col-12 col-md-5 p-0 d-none d-md-block h-100">
            <MatchWith setChildRef={matchPlansRef} />
          </div>
          <div
            className={classNames(
              "d-md-flex col-12 p-0 col-md-5 justify-content-md-center align-items-md-center position-relative overflow-hidden",
              window.innerWidth <= 768 && "h-100"
            )}
          >
            <div className="d-block d-md-none d-lg-none d-xl-none height-2">
              <div
                className={classNames(
                  "d-flex justify-content-between align-items-center bg-body p-1",
                  {
                    "flex-row-reverse": user.role !== "user",
                    "d-none": user.role === "creator",
                  }
                )}
              >
                {user.role === "user" && (
                  <div className="d-flex align-items-center justify-content-md-center justify-content-start  gap-2 match-with-container-1 flex-grow-1">
                    <h1 className="fs-5 fw-bold mb-0 me-3 ms-2 ms-md-0">
                      Match with
                    </h1>
                    <div
                      className="d-flex border rounded"
                      style={{ padding: "2px" }}
                    >
                      <div
                        onClick={() => setSelectUser("creator")}
                        style={{
                          borderRadius: "2px",
                          background: selectUser === "creator" ? "#AC1991" : "",
                          color: selectUser === "creator" ? "#fff" : "",
                          cursor: "pointer",
                          minWidth: "1rem",
                          padding: "0.2rem 1rem",
                        }}
                      >
                        Creator
                      </div>

                      <div
                        onClick={() => {
                          if (isProfileActivated) {
                            setSelectUser("user");
                          } else {
                            Swal.fire({
                              icon: "info",
                              title:
                                "You need to create your match profile first",
                              text: "Please complete your profile to continue",
                              showCloseButton: true,
                              confirmButtonText: "Complete Profile",
                              confirmButtonColor: "#AC1991",
                              customClass: { confirmButton: "custom-btn" },
                            }).then((res) => {
                              if (res.isConfirmed) {
                                router.push("/matchmaker?mode=edit");
                              }
                            });
                          }
                        }}
                        style={{
                          borderRadius: "2px",
                          background: selectUser === "user" ? "#AC1991" : "",
                          color: selectUser === "user" ? "#fff" : "",
                          cursor: "pointer",
                          minWidth: "1rem",
                          padding: "0.2rem 1rem",
                        }}
                      >
                        People
                      </div>
                    </div>
                    {user.role === "user" && (
                      <span
                        className="position-relative z-3"
                        onClick={() => setOpenInfo(true)}
                      >
                        <div
                          className="tooltip show opacity-100"
                          data-direction="bottom"
                        >
                          <div className="tooltip__initiator">
                            <InformationIcon />
                          </div>
                          <div class="tooltip__item z-3">
                            Control who you want to match with
                          </div>
                        </div>
                      </span>
                    )}
                  </div>
                )}
                <span
                  className="d-flex gap-2 me-2"
                  {...(isProfileActivated
                    ? {
                        "data-bs-toggle": "modal",
                        "data-bs-target": "#editMatchProfileModal",
                      }
                    : {})}
                  onClick={() => {
                    if (!isProfileActivated) {
                      Swal.fire({
                        icon: "info",
                        title: "You need to create your match profile first",
                        text: "Please complete your profile to continue",
                        showCloseButton: true,
                        confirmButtonText: "Complete Profile",
                        confirmButtonColor: "#AC1991",
                        customClass: { confirmButton: "custom-btn" },
                      }).then((res) => {
                        if (res.isConfirmed) {
                          router.push("/matchmaker?mode=edit");
                        }
                      });
                    }
                  }}
                  style={{ cursor: "pointer", filter: "brightness(0)" }}
                >
                  <UpdateProfileIcon />
                </span>
              </div>
              <div className="d-flex gap-3 py-2 pe-1 w-100 align-items-center overflow-scroll mobile-match-nav">
                <button
                  className="w-100 border-0 rounded p-1 py-2 fw-bold fs-8 ms-2"
                  style={{
                    color: selectTab === 1 ? "#fff" : "#000",
                    backgroundColor: selectTab === 1 ? "#AC1991" : "#fff",
                  }}
                  onClick={() => setSelectTab(1)}
                >
                  Match
                </button>
                <button
                  className="w-100 border-0 rounded p-1 py-2 fw-bold fs-8"
                  style={{
                    color: selectTab === 2 ? "#fff" : "#000",
                    backgroundColor: selectTab === 2 ? "#AC1991" : "#fff",
                  }}
                  onClick={() => setSelectTab(2)}
                >
                  Liked you
                </button>
                <button
                  className="w-100 border-0 rounded p-1 py-2 fw-bold fs-8"
                  style={{
                    color: selectTab === 3 ? "#fff" : "#000",
                    backgroundColor: selectTab === 3 ? "#AC1991" : "#fff",
                  }}
                  onClick={() => setSelectTab(3)}
                >
                  {user.role === "creator" ||
                  (user.role === "user" && selectUser === "creator")
                    ? "You Liked"
                    : "Your Matches"}
                </button>
                <span
                  onClick={() => setShowFilter((prevState) => !prevState)}
                  className="me-2"
                >
                  <FilterIcon />
                </span>
                {userRole === "creator" && (
                  <div
                    className={classNames(
                      "d-flex justify-content-between align-items-center p-1 d-md-none",
                      {
                        "flex-row-reverse": user.role !== "user",
                      }
                    )}
                  >
                    {user.role === "user" && (
                      <div className="d-flex align-items-center justify-content-md-center justify-content-start  gap-2 match-with-container-1 flex-grow-1">
                        <h1 className="fs-5 fw-bold mb-0 me-3 ms-2 ms-md-0">
                          Match with
                        </h1>
                        <div
                          className="d-flex border rounded"
                          style={{ padding: "2px" }}
                        >
                          <div
                            onClick={() => setSelectUser("creator")}
                            style={{
                              borderRadius: "2px",
                              background:
                                selectUser === "creator" ? "#AC1991" : "",
                              color: selectUser === "creator" ? "#fff" : "",
                              cursor: "pointer",
                              minWidth: "1rem",
                              padding: "0.2rem 1rem",
                            }}
                          >
                            Creator
                          </div>

                          <div
                            onClick={() => {
                              if (isProfileActivated) {
                                setSelectUser("user");
                              } else {
                                Swal.fire({
                                  icon: "info",
                                  title:
                                    "You need to create your match profile first",
                                  text: "Please complete your profile to continue",
                                  showCloseButton: true,
                                  confirmButtonText: "Complete Profile",
                                  confirmButtonColor: "#AC1991",
                                  customClass: { confirmButton: "custom-btn" },
                                }).then((res) => {
                                  if (res.isConfirmed) {
                                    router.push("/matchmaker?mode=edit");
                                  }
                                });
                              }
                            }}
                            style={{
                              borderRadius: "2px",
                              background:
                                selectUser === "user" ? "#AC1991" : "",
                              color: selectUser === "user" ? "#fff" : "",
                              cursor: "pointer",
                              minWidth: "1rem",
                              padding: "0.2rem 1rem",
                            }}
                          >
                            People
                          </div>
                        </div>
                        {user.role === "user" && (
                          <span
                            className="position-relative z-3"
                            onClick={() => setOpenInfo(true)}
                          >
                            <div
                              className="tooltip show opacity-100"
                              data-direction="bottom"
                            >
                              <div className="tooltip__initiator">
                                <InformationIcon />
                              </div>
                              <div class="tooltip__item z-3">
                                Control who you want to match with
                              </div>
                            </div>
                          </span>
                        )}
                      </div>
                    )}
                    <span
                      className="d-flex gap-2 me-2"
                      {...(isProfileActivated
                        ? {
                            "data-bs-toggle": "modal",
                            "data-bs-target": "#editMatchProfileModal",
                          }
                        : {})}
                      onClick={() => {
                        if (!isProfileActivated) {
                          Swal.fire({
                            icon: "info",
                            title:
                              "You need to create your match profile first",
                            text: "Please complete your profile to continue",
                            showCloseButton: true,
                            confirmButtonText: "Complete Profile",
                            confirmButtonColor: "#AC1991",
                            customClass: { confirmButton: "custom-btn" },
                          }).then((res) => {
                            if (res.isConfirmed) {
                              router.push("/matchmaker?mode=edit");
                            }
                          });
                        }
                      }}
                      style={{ cursor: "pointer", filter: "brightness(0)" }}
                    >
                      <UpdateProfileIcon />
                    </span>
                  </div>
                )}
              </div>
            </div>
            {window.innerWidth > 576 && <MatchCards />}
            {window.innerWidth < 576 && selectTab === 1 && (
              <div
                className="position-relative"
                style={{ height: `calc(100dvh - ${finalHeight}px)` }}
              >
                <MatchCards />
              </div>
            )}
            {window.innerWidth < 576 &&
              (selectTab === 2 || selectTab === 3) && (
                <div
                  style={{
                    overflow: "scroll",
                    maxHeight: `calc(100dvh - ${finalHeight}px)`,
                  }}
                  className="people-liked-you h-100"
                >
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: !loading
                        ? (selectTab === 3 && yourMatches.length > 0) ||
                          (selectTab === 2 && peopleLikedYou.length > 0)
                          ? "repeat(2, 1fr)"
                          : ""
                        : "repeat(2, 1fr)",
                      gap: "0.7rem",
                      overflow: "scroll",
                      padding: "0.7rem",
                    }}
                    className="bg-body"
                  >
                    {loading ? (
                      Array.from({ length: 4 }, (_, i) => (
                        <ProfileCardSkeleton key={i} />
                      ))
                    ) : (
                      <>
                        {selectTab === 3 &&
                          (yourMatches && yourMatches.length > 0 ? (
                            yourMatches.map(
                              (profile: Record<string, any>, i: number) => (
                                <ProfileCard
                                  setLoading={setLoading}
                                  key={i}
                                  data={profile}
                                  setProfiles={setYourMatches}
                                />
                              )
                            )
                          ) : (
                            <NoLikes tab={selectTab} isMobile />
                          ))}
                        {selectTab === 2 &&
                          (peopleLikedYou && peopleLikedYou.length > 0 ? (
                            peopleLikedYou.map(
                              (profile: Record<string, any>, i: number) =>
                                currentPlan?.type === "Free" &&
                                userRole === "user" ? (
                                  <LockedProfile key={i} type={profile?.type} />
                                ) : (
                                  <ProfileCard
                                    setLoading={setLoading}
                                    key={i}
                                    isLikeRequired
                                    data={profile}
                                    setProfiles={setPeopleLikedYou}
                                  />
                                )
                            )
                          ) : (
                            <NoLikes tab={selectTab} isMobile />
                          ))}
                      </>
                    )}
                  </div>
                  {userRole === "user" && (
                    <UpgradeBar
                      plan={currentPlan?.type?.toUpperCase()}
                      planDescription={UpgradeBarPlanDescription(
                        currentPlan?.type
                      )}
                      onClick={() => matchPlansRef.method.open()}
                      style={{
                        zIndex: 1,
                        bottom: "1%",
                        background: "#18022C",
                      }}
                    />
                  )}
                </div>
              )}
          </div>
          {showFilter && (
            <div ref={filterRef}>
              <Filters />
            </div>
          )}
          <PlanModal setChildRef={matchPlansRef} />
          <EditMatchProfile />
        </div>
      </div>
    )
  );
}
