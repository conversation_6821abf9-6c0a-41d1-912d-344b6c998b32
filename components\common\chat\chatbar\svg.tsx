export const OptionPlusIcon = () => (
  <svg
    width="27"
    height="27"
    viewBox="0 0 27 27"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3333 0C5.96933 0 0 5.96933 0 13.3333C0 20.6987 5.96933 26.6667 13.3333 26.6667C20.6973 26.6667 26.6667 20.6987 26.6667 13.3333C26.6667 5.96933 20.6973 0 13.3333 0ZM13.3333 2C16.3391 2 19.2218 3.19404 21.3472 5.31946C23.4726 7.44487 24.6667 10.3275 24.6667 13.3333C24.6667 16.3391 23.4726 19.2218 21.3472 21.3472C19.2218 23.4726 16.3391 24.6667 13.3333 24.6667C10.3275 24.6667 7.44487 23.4726 5.31946 21.3472C3.19404 19.2218 2 16.3391 2 13.3333C2 10.3275 3.19404 7.44487 5.31946 5.31946C7.44487 3.19404 10.3275 2 13.3333 2Z"
      fill="currentColor"
    />
    <path
      d="M6.14933 13.3334C6.14938 13.6421 6.27204 13.9382 6.49035 14.1565C6.70866 14.3748 7.00474 14.4975 7.31348 14.4975L12.1791 14.4975L12.1791 19.3632C12.1844 19.6684 12.3094 19.9594 12.5272 20.1733C12.7449 20.3873 13.038 20.5072 13.3433 20.5072C13.6486 20.5072 13.9416 20.3873 14.1594 20.1733C14.3771 19.9594 14.5021 19.6684 14.5074 19.3632L14.5074 14.4975L19.3731 14.4975C19.6783 14.4922 19.9693 14.3672 20.1833 14.1495C20.3973 13.9317 20.5172 13.6386 20.5172 13.3334C20.5172 13.0281 20.3973 12.735 20.1833 12.5172C19.9693 12.2995 19.6783 12.1745 19.3731 12.1692L14.5074 12.1692V7.30356C14.5021 6.99831 14.3771 6.70736 14.1594 6.49337C13.9416 6.27938 13.6486 6.15947 13.3433 6.15947C13.038 6.15947 12.7449 6.27938 12.5272 6.49337C12.3094 6.70736 12.1844 6.99831 12.1791 7.30356V12.1692H7.31348C7.00474 12.1693 6.70866 12.2919 6.49035 12.5102C6.27204 12.7285 6.14938 13.0246 6.14933 13.3334Z"
      fill="currentColor"
    />
  </svg>
);

export const PromoteOptionsIcon = () => (
  <svg
    width="28"
    height="25"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M27.9054 11.4857L20.7128 10.3908L17.4947 3.56126C16.9314 2.36858 15.0683 2.36858 14.505 3.56126L11.2886 10.3908L4.09426 11.4857C2.73272 11.6935 2.1811 13.4538 3.17101 14.4666L8.37721 19.7822L7.14898 27.2893C6.91734 28.7107 8.34388 29.8021 9.5671 29.1298L15.9999 25.5867L22.4343 29.1316C23.6475 29.7969 25.0857 28.7229 24.8524 27.291L23.6242 19.7839L28.8304 14.4683C29.8186 13.4538 29.267 11.6935 27.9054 11.4857Z"
      fill="#52C0FF"
    />
  </svg>
);

export const PaidFeaturesIcon = () => (
  <svg
    width="28"
    height="25"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.8667 23.2268H14.5201C12.3334 23.2268 10.5601 21.3868 10.5601 19.1201C10.5601 18.5734 11.0134 18.1201 11.5601 18.1201C12.1067 18.1201 12.5601 18.5734 12.5601 19.1201C12.5601 20.2801 13.4401 21.2268 14.5201 21.2268H17.8667C18.7334 21.2268 19.4534 20.4534 19.4534 19.5201C19.4534 18.3601 19.0401 18.1334 18.3601 17.8934L12.9867 16.0001C11.9467 15.6401 10.5467 14.8668 10.5467 12.4801C10.5467 10.4268 12.1601 8.77344 14.1334 8.77344H17.4801C19.6667 8.77344 21.4401 10.6134 21.4401 12.8801C21.4401 13.4268 20.9867 13.8801 20.4401 13.8801C19.8934 13.8801 19.4401 13.4268 19.4401 12.8801C19.4401 11.7201 18.5601 10.7734 17.4801 10.7734H14.1334C13.2667 10.7734 12.5467 11.5468 12.5467 12.4801C12.5467 13.6401 12.9601 13.8668 13.6401 14.1068L19.0134 16.0001C20.0534 16.3601 21.4534 17.1334 21.4534 19.5201C21.4401 21.5601 19.8401 23.2268 17.8667 23.2268Z"
      fill="currentColor"
    />
    <path
      d="M16.0001 25.0001C15.4534 25.0001 15.0001 24.5467 15.0001 24.0001V8.00008C15.0001 7.45341 15.4534 7.00008 16.0001 7.00008C16.5467 7.00008 17.0001 7.45341 17.0001 8.00008V24.0001C17.0001 24.5467 16.5467 25.0001 16.0001 25.0001Z"
      fill="currentColor"
    />
    <path
      d="M16.0001 30.3334C8.09342 30.3334 1.66675 23.9067 1.66675 16.0001C1.66675 8.09342 8.09342 1.66675 16.0001 1.66675C23.9067 1.66675 30.3334 8.09342 30.3334 16.0001C30.3334 23.9067 23.9067 30.3334 16.0001 30.3334ZM16.0001 3.66675C9.20008 3.66675 3.66675 9.20008 3.66675 16.0001C3.66675 22.8001 9.20008 28.3334 16.0001 28.3334C22.8001 28.3334 28.3334 22.8001 28.3334 16.0001C28.3334 9.20008 22.8001 3.66675 16.0001 3.66675Z"
      fill="currentColor"
    />
  </svg>
);

export const SpecialOptionsIcon = () => (
  <svg
    width="28"
    height="22"
    viewBox="0 0 29 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.9623 21.0581C15.9623 18.2141 12.6132 15.9106 8.48116 15.9106C4.34906 15.9106 1 18.2141 1 21.0581M8.48116 12.0499C7.1584 12.0499 5.88983 11.5076 4.9545 10.5423C4.01918 9.57691 3.49372 8.26762 3.49372 6.90241C3.49372 5.5372 4.01918 4.22791 4.9545 3.26256C5.88983 2.29721 7.1584 1.75488 8.48116 1.75488C9.80391 1.75488 11.0725 2.29721 12.0078 3.26256C12.9431 4.22791 13.4686 5.5372 13.4686 6.90241C13.4686 8.26762 12.9431 9.57691 12.0078 10.5423C11.0725 11.5076 9.80391 12.0499 8.48116 12.0499Z"
      stroke="#131316"
      stroke-width="1.7"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M25.0484 3.95415C25.1211 4.02679 25.1619 4.12529 25.162 4.22801L25.162 10.3571V10.3646L25.1622 10.3721C25.1641 10.4241 25.1554 10.476 25.1368 10.5246C25.1181 10.5732 25.0899 10.6175 25.0537 10.655C25.0176 10.6924 24.9742 10.7222 24.9263 10.7426C24.8784 10.7629 24.8269 10.7734 24.7748 10.7734C24.7228 10.7734 24.6712 10.7629 24.6233 10.7426C24.5754 10.7222 24.5321 10.6924 24.4959 10.655C24.4598 10.6175 24.4315 10.5732 24.4129 10.5246C24.3942 10.476 24.3856 10.4241 24.3874 10.3721L24.3877 10.3646V10.3571V6.18914L24.3877 5.21482L23.6736 5.87764L17.5797 11.5337L17.5739 11.539L17.5683 11.5446C17.4956 11.6173 17.397 11.6582 17.2942 11.6582C17.1914 11.6582 17.0928 11.6173 17.0201 11.5446C16.9474 11.4719 16.9065 11.3733 16.9065 11.2705C16.9065 11.1699 16.9457 11.0733 17.0155 11.001L23.1026 5.35139L23.8961 4.61489L22.8134 4.61489L18.6455 4.61489L18.638 4.61489L18.6305 4.61515C18.5785 4.61699 18.5266 4.60833 18.478 4.58968C18.4294 4.57104 18.385 4.5428 18.3476 4.50665C18.3101 4.47049 18.2803 4.42716 18.26 4.37925C18.2397 4.33133 18.2292 4.27981 18.2292 4.22775C18.2292 4.17569 18.2397 4.12417 18.26 4.07625C18.2803 4.02833 18.3101 3.98501 18.3476 3.94885C18.385 3.9127 18.4294 3.88446 18.478 3.86581C18.5266 3.84717 18.5785 3.83851 18.6305 3.84035L18.638 3.84061L18.6455 3.84061L24.7746 3.84061C24.8773 3.84067 24.9758 3.88151 25.0484 3.95415Z"
      fill="black"
      stroke="#131316"
      stroke-width="0.85"
    />
  </svg>
);

export const ServicesIcon = ({ res = "24" }: { res?: string }) => (
  <svg
    width={res}
    height={res}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.929 8.61424L15.5345 7.79307L13.121 2.67089C12.6985 1.77637 11.3011 1.77637 10.8787 2.67089L8.4664 7.79307L3.07063 8.61424C2.04948 8.77009 1.63576 10.0903 2.3782 10.8499L6.28284 14.8366L5.36168 20.4669C5.18794 21.533 6.25785 22.3515 7.17526 21.8473L11.9998 19.19L16.8256 21.8486C17.7356 22.3476 18.8142 21.5421 18.6392 20.4682L17.7181 14.8379L21.6227 10.8512C22.3639 10.0903 21.9502 8.77009 20.929 8.61424Z"
      fill="#52C0FF"
    />
  </svg>
);
