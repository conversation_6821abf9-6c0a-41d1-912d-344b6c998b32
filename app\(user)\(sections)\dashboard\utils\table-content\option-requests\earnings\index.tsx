import Image from "next/image";
import Link from "next/link";

const EarningsTable = ({ tableProps }: any) => {
  return (
    <table className="table">
      {/*Table for earnings */}
      <thead>
        <tr className="fs-7">
          <th scope="col" className="bg-cream rounded-start-3">
            Payment ID
          </th>
          <th scope="col" className="bg-cream">
            Date & Time
          </th>
          <th scope="col" className="bg-cream">
            Subscription
          </th>
          <th scope="col" className="bg-cream ">
            User
          </th>
          <th scope="col" className="bg-cream ">
            Package
          </th>
          <th scope="col" className="bg-cream">
            Amount
          </th>
          <th scope="col" className="bg-cream rounded-end-3">
            {" "}
          </th>
        </tr>
      </thead>
      {!tableProps.empty && (
        <tbody className="bg-body border-0">
          {tableProps.tableValues!.map((earnings: any) => (
            <>
              <tr className="fs-7 table-row" key={earnings.paymentID}>
                <th scope="row" className="py-2 bg-body">
                  <span className="id-text">{earnings.paymentID}</span>
                </th>
                <td className="py-2 bg-body">
                  <div className="d-flex flex-column">
                    <p className="date mb-0 color-dark">
                      {earnings.dateTime.date}
                    </p>
                    <p className="time mb-0 color-dark">
                      {earnings.dateTime.time}
                    </p>
                  </div>
                </td>
                <td className="py-2 bg-body">
                  <p className="date mb-0 color-dark">
                    {earnings.subscription}
                  </p>
                </td>

                <td className="py-2 bg-body">
                  <Link
                    href={`/${earnings?.from?.user_type.toLowerCase()}/${
                      earnings?.from?.username
                    }`}
                    className="d-flex gap-2"
                  >
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={earnings.user.avatar}
                      width={35}
                      height={35}
                      alt="dashboard-user"
                    />
                    <div className="d-flex flex-column align-items-start">
                      <p className="fs-7 mb-0 text-overflow-ellipsis color-dark">
                        {earnings.user.name}
                      </p>
                      <p className="fs-8 mb-0 color-light">
                        {earnings.user.username}
                      </p>
                    </div>
                  </Link>
                </td>
                <td className="py-2 bg-body">
                  <p className="date mb-0 color-dark">{earnings.package}</p>
                </td>
                <td className="py-2 bg-body">
                  <p className="date mb-0 color-dark">
                    {earnings.amount.cash.length != 0 && earnings.amount.cash}
                    {earnings.amount.knkyCoin.length != 0 && (
                      <div className="d-flex align-items-center">
                        <Image
                          src={"/images/svg/knky-coin.svg"}
                          width={20}
                          height={20}
                          className="me-1"
                          alt="knky-coin"
                        />
                        <span>{earnings.amount.knkyCoin}</span>
                      </div>
                    )}
                  </p>
                </td>

                <td className="py-2 bg-body">
                  <button className="border-0 rounded-3 bg-transparent">
                    <Image
                      src={"/images/svg/options-dot.svg"}
                      width={25}
                      height={25}
                      alt="options"
                    />
                  </button>
                </td>
              </tr>
            </>
          ))}
        </tbody>
      )}
    </table>
  );
};

export default EarningsTable;
