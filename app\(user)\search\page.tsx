"use client";

import debounce from "lodash/debounce";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";

import { SearchTags } from "@/api/hashtags";
import { type BadgesType, GetTrendingHashtags } from "@/api/post";
import { GetFeaturedUserProfiles, getUserProfile } from "@/api/user";
import Badges from "@/components/common/badges";
import "@/components/common/header/index.scss";

import SearchLoader from "@/components/common/header/search/search-loader";
import SearchTabs from "@/components/common/header/search/search-tabs";
import ReferAndEarn from "@/components/settings/refer-and-earn";
import { configActions, defaultActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { type UserProfile } from "@/types/user";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";

interface HashtagsBody {
  _id: string;
  total_count: number;
  post_count: number;
}

export default function Search() {
  const router = useRouter();
  const user = useAppSelector((state) => state.user);
  const dispatchAction = useAppDispatch();

  const SearchBar = () => {
    const dispatch = useAppDispatch();
    const [searchTerm, setSearchTerm] = useState("");
    const [userList, setUserList] = useState<any[]>([]);
    const [showDropdown, setShowDropdown] = useState(false);
    const [hashtags, setHashtags] = useState<HashtagsBody[]>([]);
    const [tabIndex, setTabIndex] = useState<number>(0);
    const [completeData, setCompleteData] = useState<Record<string, any>[]>([]);
    const [channelList, setChannelList] = useState<any[]>([]);
    const [groupList, setGroupList] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [searchPage, setSearchPage] = useState<number>(1);
    const searchRef = useRef<any>(null);
    const [previousLength, setPreviousLength] = useState<number>(0);

    useEffect(() => {
      dispatch(configActions.toggleShowPositionWidget());

      function handleClickOutside(event: any) {
        if (searchRef.current && !searchRef?.current?.contains(event.target)) {
          setShowDropdown(false);
          setSearchTerm("");
          setSearchPage(1);
        }
      }

      document.body.addEventListener("click", handleClickOutside);

      return () => {
        dispatch(configActions.toggleShowPositionWidget());
        document.body.removeEventListener("click", handleClickOutside);
      };
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
      debouncedHandleSearch(e.target.value);
    };

    const handleHashtagClick = (hashtag: string) => {
      setShowDropdown(false);

      dispatchAction(
        defaultActions.setSelectedTag(encodeURIComponent(hashtag))
      );

      router.push(
        `/interested?hashtag=${encodeURIComponent(hashtag)}&redirect`
      );
    };

    const handleSearchMore = async () => {
      setLoading(true);

      try {
        if (previousLength === 0) return;
        setSearchPage((prevPage) => prevPage + 1);
        const data = await getUserProfile(searchTerm, 15, searchPage + 1);

        if (data && data.data && data.data[0]) {
          setUserList((prevList) => [...data.data[0].users, ...prevList]);
          setGroupList((prevList) => [...data.data[0].groups, ...prevList]);
          setChannelList((prevList) => [...data.data[0].channels, ...prevList]);
          setCompleteData((prevList) => [
            ...data.data?.[0]?.users,
            ...data.data?.[0]?.groups,
            ...data.data?.[0]?.channels,
            ...prevList,
          ]);
          setPreviousLength(
            [
              ...data.data?.[0]?.users,
              ...data.data?.[0]?.groups,
              ...data.data?.[0]?.channels,
            ].length
          );
        }

        setShowDropdown(true);
      } catch (error) {
        console.error("Error fetching more search results:", error);
      } finally {
        setLoading(false);
      }

      try {
        const tag =
          searchTerm.charAt(0) === "#" ? searchTerm.slice(1) : searchTerm;
        console.log(tag);
        const data = await SearchTags(tag, "", 15, searchPage + 1);
        setHashtags((prevState) => [...prevState, ...data.data]);
        setCompleteData((prevState) => [...prevState, ...data.data]);
        setShowDropdown(true);
      } catch (error) {
        console.error("Error fetching user data", error);
      } finally {
        setLoading(false);
      }
    };

    const debouncedHandleSearch = useRef(
      debounce(async (value) => {
        if (!value) {
          setShowDropdown(false);
          return;
        }

        setLoading(true);

        if (value.charAt(0) !== "#") {
          try {
            const data = await getUserProfile(value, 15, 1);

            setUserList([...data.data?.[0]?.users]);
            setGroupList([...data.data?.[0]?.groups]);
            setChannelList([...data.data?.[0]?.channels]);
            setCompleteData([
              ...data.data?.[0]?.users,
              ...data.data?.[0]?.groups,
              ...data.data?.[0]?.channels,
            ]);
            setPreviousLength(
              [
                ...data.data?.[0]?.users,
                ...data.data?.[0]?.groups,
                ...data.data?.[0]?.channels,
              ].length
            );
            setShowDropdown(true);
          } catch (error) {
            console.error("Error fetching user data", error);
          }
        }

        try {
          const tag = value.charAt(0) === "#" ? value.slice(1) : value;
          const data = await SearchTags(tag, "");
          setHashtags(data.data);
          setCompleteData((prevState) => [...prevState, ...data.data]);
          setShowDropdown(true);
        } catch (error) {
          console.error("Error fetching user data", error);
        }

        setLoading(false);
      }, 600)
    ).current;

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        e.preventDefault();
      }
    };

    return (
      <div className="bg-body p-3 position-sticky top-0">
        <h5>Search</h5>

        <div
          className={`header-search-box position-relative px-2 py-1 rounded-3 mt-3 ${
            user?.role == "guest" ? "opacity-50" : ""
          }`}
          ref={searchRef}
        >
          <div className="d-flex gap-2 align-items-center bg-cream rounded-pill">
            <Image
              src="/images/creator/search.svg"
              alt="Search Logo"
              className="brand-logo ms-2"
              width={20}
              height={20}
              priority
            />
            <form className="w-100">
              <input
                type="search"
                placeholder="Search..."
                value={searchTerm}
                onChange={handleInputChange}
                onKeyPress={handleKeyPress}
                disabled={user?.role == "guest"}
                className="w-100 p-2"
              />
            </form>
          </div>
          {showDropdown && (
            <ul
              className={`custom-menu d-flex ${
                loading ? "justify-content-center" : ""
              }`}
              style={{
                borderTop: "0",
                width: "100vw",
                marginLeft: "-16px",
                borderRadius: 0,
              }}
            >
              {loading ? (
                <div className="d-flex align-items-center justify-content-center h-100">
                  <SearchLoader />
                </div>
              ) : (
                <SearchTabs
                  tabIndex={tabIndex}
                  setTabIndex={setTabIndex}
                  completeData={completeData}
                  userList={userList}
                  channelList={channelList}
                  groupList={groupList}
                  hashtags={hashtags}
                  setShowDropdown={setShowDropdown}
                  handleHashtagClick={handleHashtagClick}
                  handleSearchMore={handleSearchMore}
                />
              )}
            </ul>
          )}
        </div>
      </div>
    );
  };

  const Hashtags = () => {
    const [tags, setTags] = useState<any>([]);

    useEffect(() => {
      GetTrendingHashtags().then((response) => {
        setTags(response.data);
      });
    }, []);

    const dispatchAction = useAppDispatch();

    const handleHashtagClick = (hashtag: string) => {
      dispatchAction(
        defaultActions.setSelectedTag(encodeURIComponent(hashtag))
      );

      router.push(
        `/interested?hashtag=${encodeURIComponent(hashtag)}&redirect`
      );
    };

    return (
      <div className="bg-body py-3">
        <h6 className="ms-3 mb-2">Trending Hashtags</h6>

        <div className="d-flex  flex-column gap-3 overflow-scroll mt-3">
          <div className="d-flex justify-content-start gap-1">
            {tags.map(
              (tag: any, i: number) =>
                i % 2 == 0 && (
                  <div
                    key={i}
                    onClick={() => handleHashtagClick(tag?._id.substring(1))}
                  >
                    <div
                      className="d-flex flex-column rounded-3 border-dark align-items-center border px-3 py-2 ms-2 pointer"
                      style={{ width: "fit-content" }}
                    >
                      <div className="fs-7 fw-medium color-bold text-nowrap">
                        {tag?._id}
                      </div>
                      <div
                        className="color-medium fs-7"
                        style={{ whiteSpace: "nowrap" }}
                      >
                        {tag?.post_count} posts
                      </div>
                    </div>
                  </div>
                )
            )}
          </div>
          <div className="d-flex justify-content-start gap-1">
            {tags.map(
              (tag: any, i: number) =>
                i % 2 != 0 && (
                  <div
                    key={i}
                    onClick={() => handleHashtagClick(tag?._id.substring(1))}
                  >
                    <div
                      key={i}
                      className="d-flex flex-column rounded-3 border-dark align-items-center border px-3 py-2 ms-2 "
                      style={{ width: "fit-content" }}
                    >
                      <div className="fs-7 fw-medium color-bold text-nowrap">
                        {tag?._id}
                      </div>
                      <div
                        className="color-medium fs-7"
                        style={{ whiteSpace: "nowrap" }}
                      >
                        {tag?.post_count} posts
                      </div>
                    </div>
                  </div>
                )
            )}
          </div>
        </div>
      </div>
    );
  };

  const HotCreators = () => {
    interface UserFeatured {
      id: string;
      back: string;
      profile: string;
      badges: BadgesType;
      role: string;
      display_name: string;
      username: string;
      price: number;
      minPrice: number;
      is_subscribed?: boolean;
    }

    function transformData(profiles: UserProfile[]): UserFeatured[] {
      return profiles.map((profile) => ({
        back: getAssetUrl({ media: profile.background[0] }),
        profile: getAssetUrl({
          media: profile.avatar[0],
          defaultType: "avatar",
        }),
        //  To Make a mechanism to save all the subscriptions and following peoples for the user
        subscribed: false,
        followed: false,
        role: profile.user_type.toLowerCase(),
        badges: profile?.badges,
        name: `${profile.f_name} ${profile.l_name}`,
        username: profile.username,
        price: profile.chat_fee?.[0]?.price || 0,
        followers: profile.follower_count,
        videos: profile.media_count,
        photos: profile.post_count,
        id: profile._id,
        minPrice: profile?.min_subscription?.price,
        is_subscribed: profile?.is_subscribed,
        f_name: profile?.f_name,
        display_name: profile?.display_name,
      }));
    }

    const [creators, setCreators] = useState<UserFeatured[]>([]);
    useEffect(() => {
      GetFeaturedUserProfiles()
        .then((response) => {
          setCreators(transformData(response.data));
        })
        .catch((err) => {
          console.error(err);
        });
    }, []);

    return (
      <div className="bg-body py-3">
        <div className="d-flex justify-content-between align-items-center">
          <h6 className="ms-3">Hot Creators</h6>
          <Link href={"/trending"}>
            <h6 className="me-3 underline pointer">View All</h6>
          </Link>
        </div>

        <div className="overflow-scroll d-flex ms-2">
          {creators.map((creator) => {
            return (
              <Link
                href={`/${creator.role}/${creator.username}`}
                key={creator.id}
                className={`${
                  user?.id === creator?.id && "d-none "
                } border pointer rounded-3 px-2 py-3 m-2 d-flex flex-column align-items-center gap-2 `}
                style={{ width: "fit-content", minWidth: "10rem" }}
              >
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src={creator?.profile}
                  width={64}
                  height={64}
                  alt=""
                  className="rounded-circle"
                />
                <div className="d-flex gap-1 justify-content-center align-items-center">
                  <h6
                    className="mb-0 text-overflow-ellipsis"
                    style={{ maxWidth: "6.5rem" }}
                  >
                    {creator.display_name}
                  </h6>
                  <Badges array={creator.badges} />
                </div>
                <div className="fs-7 color-medium fw-500">
                  @{creator?.username}
                </div>
                <div
                  className={`fs-7 color-medium ${
                    creator?.minPrice === undefined ? "invisible" : ""
                  }`}
                >
                  From{" "}
                  <span className="fw-bold color-bold">
                    {formatCurrency(creator?.minPrice)}
                  </span>
                  &nbsp;/Month
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    );
  };

  if (window.innerWidth > 992) router.push("/");

  return (
    window.innerWidth <= 992 && (
      <div className="d-flex overflow-hidden flex-column">
        <SearchBar />
        <div className="overflow-y-auto d-flex flex-column gap-2 mt-2">
          <Hashtags />
          <HotCreators />
          <ReferAndEarn isTableRequired={false} />
        </div>
      </div>
    )
  );
}
