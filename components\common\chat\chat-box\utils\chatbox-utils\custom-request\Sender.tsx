import classNames from "classnames";
import Image from "next/image";
import { useState } from "react";
import { toast } from "sonner";

import { CounterOfferAction } from "@/api/requests";
import ActionButton from "@/components/common/action-button";
import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import type { Media } from "@/types/media";
import { getAssetUrl } from "@/utils/assets";
import socketChannel from "@/utils/chatSocket";
import { formatCurrency } from "@/utils/formatter";

import ReadMoreContent from "../../../read-more-content";

const CustomRequestSender = ({ message }: { message: MessageInterface }) => {
  const isRequestSent = message?.meta?.requestAccept === "sent";
  const isAccepted = message?.meta?.requestAccept === true;
  const isPartialAccepted = message?.meta?.partial_accept;
  const counter_status = message?.meta?.counter_status;

  const chatList = useAppSelector((s) => s.chat.chatList);
  const targetUser = useAppSelector((s) => s.chat.targetUser);

  const fetchCurrentChat = (message: MessageInterface) => {
    const chat = chatList.find(
      (chat) =>
        chat.target._id === targetUser || chat.initiator._id === targetUser
    );
    const isTargetSender =
      message?.sid === chat?.target?._id ||
      message?.sender_id === chat?.target?._id;

    const target = isTargetSender ? chat?.initiator : chat?.target;
    const user = isTargetSender ? chat?.target : chat?.initiator;
    return {
      target,
      user,
    };
  };

  const custom_render = message?.meta?.custom_info;
  const media_array = (message?.meta?.media as Media[]) || [];
  const [currentIndex, setCurrentIndex] = useState(0);

  const usedAs = "sender";

  // @ts-expect-error-ts
  if (custom_render && message?.meta?.media?.[0]?.type !== "Completed") {
    return (
      <div className="color-medium p-2">
        Processing and Optimising media request...
      </div>
    );
  }

  if (media_array.length > 0) {
    const mediaItems = Array.isArray(message?.meta?.media)
      ? message.meta.media
      : [message?.meta?.media];

    const handleNext = () => {
      setCurrentIndex((prevIndex) =>
        prevIndex === mediaItems.length - 1 ? 0 : prevIndex + 1
      );
    };

    const handlePrevious = () => {
      setCurrentIndex((prevIndex) =>
        prevIndex === 0 ? mediaItems.length - 1 : prevIndex - 1
      );
    };

    if (!mediaItems || mediaItems.length === 0) {
      return <div>No media available</div>;
    }

    return (
      <div
        style={{
          position: "relative",
          width: "100%",
          maxWidth: "800px",
          margin: "0 auto",
          overflow: "hidden",
        }}
      >
        {message?.meta?.free_service && (
          <div className="position-absolute top-0 start-0 m-2 badge text-bg-secondary z-1">
            Free Service
          </div>
        )}
        <div
          style={{
            display: "flex",
            transition: "transform 0.5s ease",
            transform: `translateX(-${currentIndex * 100}%)`,
          }}
        >
          {media_array.map((media, index) => (
            <div
              key={index}
              className="p-1"
              style={{
                minWidth: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {media.type === "image" ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  key={media._id}
                  width={"100%"}
                  height={150}
                  className="object-fit-cover mw-100 d-block rounded pointer"
                  alt=""
                  src={getAssetUrl({ media: media, variation: "compressed" })}
                  onClick={() => {
                    window.KNKY.showFullscreenMedia(
                      "image",
                      undefined,
                      media_array,
                      index
                    );
                  }}
                />
              ) : media.type === "video" ? (
                <video
                  src={`${getAssetUrl({ media })}#t=0.1`}
                  className="object-fit-cover mw-100 d-block rounded pointer"
                  width={"100%"}
                  height={150}
                  key={media._id}
                  playsInline
                  controls
                  controlsList="nodownload"
                  onClick={() => {
                    window.KNKY.showFullscreenMedia(
                      "video",
                      undefined,
                      media_array,
                      index
                    );
                  }}
                ></video>
              ) : null}
            </div>
          ))}
        </div>

        {media_array.length > 1 && (
          <>
            <button
              onClick={handlePrevious}
              style={{
                position: "absolute",
                top: "50%",
                left: "10px",
                transform: "translateY(-250%)",
                background: "rgba(0, 0, 0, 0.5)",
                color: "#fff",
                border: "none",
                borderRadius: "50%",
                width: "30px",
                height: "30px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
              }}
            >
              <Image
                src="/images/chat/left-arrow.png"
                alt=""
                width={30}
                height={30}
              />
            </button>
            <button
              onClick={handleNext}
              style={{
                position: "absolute",
                top: "50%",
                right: "10px",
                transform: "translateY(-250%)",
                background: "rgba(0, 0, 0, 0.5)",
                color: "#fff",
                border: "none",
                borderRadius: "50%",
                width: "30px",
                height: "30px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
              }}
            >
              <Image
                src="/images/chat/right-arrow.png"
                alt=""
                width={30}
                height={30}
              />
            </button>
          </>
        )}

        {media_array.length > 1 && (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              marginTop: "10px",
            }}
          >
            {mediaItems.map((_, index) => (
              <div
                key={index}
                onClick={() => setCurrentIndex(index)}
                style={{
                  width: "6px",
                  height: "6px",
                  background:
                    currentIndex === index ? "#ac1991" : "rgb(185 182 184)",
                  borderRadius: "50%",
                  margin: "0 5px",
                  cursor: "pointer",
                }}
              ></div>
            ))}
          </div>
        )}

        <div className="px-2">
          {message?.meta?.request_note && (
            <div
              className={`d-flex gap-1 align-items-center mt-2 ${
                usedAs === "sender" ? "flex-row" : "flex-row-reverse"
              }`}
            >
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                alt=""
                height={25}
                width={25}
                className="rounded-circle border"
                src={getAssetUrl({
                  media: fetchCurrentChat(message)?.user?.avatar?.[0],
                  defaultType: "avatar",
                })}
              />
              <span
                className="d-flex"
                style={{
                  maxWidth: "60%",
                }}
              >
                <ReadMoreContent
                  characterCount={50}
                  text={message?.meta.request_note}
                  // textColor={usedAs === "sender" ? "white" : "gray"}
                />
              </span>
            </div>
          )}
          <div
            className={`d-flex gap-1 align-items-center ${
              usedAs === "sender" ? "flex-row-reverse" : "flex-row"
            }`}
          >
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              alt=""
              height={25}
              width={25}
              className="rounded-circle border"
              src={getAssetUrl({
                media: fetchCurrentChat(message)?.target?.avatar?.[0],
                defaultType: "avatar",
              })}
            />
            <span
              className="d-flex"
              style={{
                maxWidth: "60%",
              }}
            >
              <ReadMoreContent
                characterCount={50}
                text={message?.meta.details || ""}
                // textColor={usedAs === "sender" ? "white" : "gray"}
              />
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="d-flex flex-column px-2 gap-2">
      {message?.meta?.free_service && (
        <div className="position-absolute top-0 start-0 m-2 badge text-bg-warning">
          Free Service
        </div>
      )}
      <div className="py-2">
        <div className="position-absolute top-0 end-0 m-2">
          {!isRequestSent &&
            (isAccepted ? (
              <div
                className={classNames("badge p-2", {
                  "fs-10": window.innerWidth < 576,
                })}
                style={{
                  color: "rgba(0, 104, 255, 1)",
                  border: "1px solid rgba(0, 104, 255, 1)",
                  backgroundColor: "rgba(0, 104, 255, 0.12)",
                }}
              >
                Completed
              </div>
            ) : (
              (message?.meta?.counter_status === "rejected" ||
                message?.meta?.requestAccept === false) && (
                <div
                  className={classNames("badge p-2", {
                    "fs-10": window.innerWidth < 576,
                  })}
                  style={{
                    color: "rgba(245, 34, 45, 1)",
                    border: "1px solid rgba(245, 34, 45, 1)",
                    backgroundColor: "rgba(245, 34, 45, 0.2)",
                  }}
                >
                  Declined
                </div>
              )
            ))}
          {isRequestSent && !isPartialAccepted && (
            <div
              className={classNames("badge p-2", {
                "fs-10": window.innerWidth < 576,
              })}
              style={{
                color: "rgba(255, 168, 0, 1)",
                border: "1px solid rgba(255, 168, 0, 1)",
                backgroundColor: "rgba(255, 168, 0, 0.12)",
              }}
            >
              Waiting for creator
            </div>
          )}
          {isRequestSent &&
            isPartialAccepted &&
            counter_status !== "pending" && (
              <div
                className={classNames("badge p-2", {
                  "fs-10": window.innerWidth < 576,
                })}
                style={{
                  color: "rgba(86, 194, 45, 1)",
                  border: "1px solid rgba(86, 194, 45, 1)",
                  backgroundColor: "rgba(86, 194, 45, 0.2)",
                }}
              >
                Accepted
              </div>
            )}
          {counter_status === "pending" && (
            <div
              className={classNames("badge p-2", {
                "fs-10": window.innerWidth < 576,
              })}
              style={{
                color: "rgba(136, 77, 255, 1)",
                border: "1px solid rgba(136, 77, 255, 1)",
                backgroundColor: "rgba(136, 77, 255, 0.2)",
              }}
            >
              New Counter
            </div>
          )}
        </div>
        <div className="d-flex align-items-center gap-2">
          <div>
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={
                message?.meta?.request_icon?.length! > 0
                  ? getAssetUrl({ media: message?.meta?.request_icon?.[0] })
                  : "/images/chat/service-icon.png"
              }
              height={72}
              width={72}
              alt="Request Icon"
              className="rounded"
            />
          </div>
          <div className="d-flex flex-column justify-content-center">
            <div className="fs-5 fw-bold">Request</div>
            <div>
              Offer:{" "}
              {formatCurrency(
                (() => {
                  const price =
                    message?.meta?.discount?.discount_value ||
                    message?.meta?.price ||
                    0;
                  return price;
                })()
              )}{" "}
              {message?.meta?.counter_offer_price && (
                <span className="fw-bold">
                  | Counter:{" "}
                  {formatCurrency(message?.meta?.counter_offer_price || 0)}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
      <div>
        <p className="d-flex gap-1">
          <span className="color-medium text-nowrap">Requesting for:</span>{" "}
          <ReadMoreContent
            text={message?.meta?.request_note || ""}
            textColor="black"
            classes="w-100"
          />
        </p>
        {message?.meta?.counter_description && (
          <>
            <hr />
            <p className="d-flex gap-1">
              <span className="color-medium w-fit">
                Creator&apos;s Counter:
              </span>{" "}
              <ReadMoreContent
                text={message?.meta?.counter_description || ""}
                textColor="black"
                classes="w-100"
              />
            </p>
          </>
        )}
      </div>
      {message?.meta?.counter_offer_price && counter_status === "pending" && (
        <div className="d-flex align-items-center justify-content-center gap-2 flex-column flex-lg-row">
          <ActionButton
            className="w-100"
            onClick={() => {
              ModalService.open("COUNTER_OFFER_ACCEPT", {
                final_price: message?.meta?.counter_offer_price || 0,
                initial_price:
                  message?.meta?.discount?.discount_value ||
                  message?.meta?.price ||
                  0,
                request_id: message?.meta?.reqId || "",
                service_id: message?.meta?.serviceId || "",
                message_id:
                  message?._id ||
                  message?.messageId ||
                  message?.message_id ||
                  "",
                message,
              });
            }}
          >
            Accept for {formatCurrency(message?.meta?.counter_offer_price || 0)}
          </ActionButton>
          <ActionButton
            type="outline"
            variant="danger"
            className="w-100"
            onClick={() => {
              CounterOfferAction(false, message?.meta?.reqId || "").then(() => {
                toast.info("Counter Offer Declined");
                socketChannel?.channel?.editMessage({
                  message_id:
                    message?._id ||
                    message?.messageId ||
                    message?.message_id ||
                    "",
                  data: {
                    ...message,
                    meta: {
                      ...message?.meta,
                      counter_status: "rejected",
                      requestAccept: false,
                    },
                  },
                });
              });
            }}
          >
            Decline
          </ActionButton>
        </div>
      )}
    </div>
  );
};

export default CustomRequestSender;
