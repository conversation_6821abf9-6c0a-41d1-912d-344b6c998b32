"use client";

import Image from "next/image";
import Link from "next/link";
import { memo } from "react";
import Swal from "sweetalert2";

import { getReqById } from "@/api/chat";
import { handleRequest } from "@/api/dashboard";
import type { ModalRef } from "@/components/common/modal";
import RateModal from "@/components/common/modal/chat/rate-modal";
import VideoConf from "@/components/common/modal/chat/video-conf";
import { ModalService } from "@/components/modals";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { ServicesColor, ServicesImg } from "@/types/services";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";
import { formatDate } from "@/utils/number";

interface DashboardTableProps {
  tableData?: any[];
  chatType: string;
  getDataAgain: () => void;
}

const ratingRef = { method: {} as ModalRef };

const getStatusConfig = (status: number, type: string) =>
  ({
    0: { label: "New", className: "new" },
    1: {
      label: type === "RATING" ? "Completed" : "Accepted",
      className: "accepted",
    },
    2: { label: "Declined", className: "declined" },
    3: { label: "Declined", className: "declined" },
    4: { label: "Completed", className: "accepted" },
  }[status] || { label: "New", className: "new" });

const StatusButton = memo(
  ({ status, type }: { status: number; type: string }) => {
    const { label, className } = getStatusConfig(status, type);
    return (
      <div className={`dashboard-${className} dashboard-status`}>{label}</div>
    );
  }
);

const ActionButtons = ({
  res,
  status,
  is_promotion,
  onAccept,
  onJoinCall,
  onDecline,
  onRating,
  onRequest,
}: {
  res: any;
  status: number;
  is_promotion: boolean;
  onAccept: () => void;
  onJoinCall: () => void;
  onDecline: () => void;
  onRating: () => void;
  onRequest: () => void;
}) => {
  const OptionsButton = () => (
    <button className="border-0 rounded-3 bg-transparent">
      <Image
        src="/images/svg/options-dot.svg"
        width={25}
        height={25}
        alt="options"
      />
    </button>
  );

  const ActionButtons = () => {
    if (is_promotion && status !== 4) {
      return (
        <div className="d-flex justify-content-end">
          <div className="d-flex gap-3">
            <button className="bg-purple border-0 shadow-dark px-3 py-1 rounded-3">
              <Link href={`/chat?user=${res?.requestee?._id}`}>Chat</Link>
            </button>
            {OptionsButton()}
          </div>
        </div>
      );
    }

    switch (status) {
      case 0:
        return (
          <div className="d-flex justify-content-between">
            <div className="d-flex gap-3">
              <button
                className="btn-purple border-0 shadow-dark px-3 py-1 rounded-3"
                onClick={() =>
                  res.type === "RATING"
                    ? onRating()
                    : res.type === "CUSTOM-SERVICE"
                    ? onRequest()
                    : onAccept()
                }
                {...(res.type === "RATING"
                  ? {
                      "data-bs-toggle": "modal",
                      "data-bs-target": "#rateModal",
                    }
                  : {})}
              >
                {res.type === "RATING" ? "Rating" : "Accept"}
              </button>
              <button
                className="btn-light border shadow-dark px-3 py-1 rounded-3"
                onClick={onDecline}
              >
                Decline
              </button>
            </div>
            {OptionsButton()}
          </div>
        );
      case 1:
        return (
          <div className="d-flex gap-3 justify-content-end">
            {res.type === "VIDEO" || res.type === "VOICE" ? (
              <button
                className="bg-purple border-0 shadow-dark px-3 py-1 rounded-3"
                onClick={onJoinCall}
              >
                Join Call
              </button>
            ) : (
              <button className="bg-purple border-0 shadow-dark px-3 py-1 rounded-3">
                <Link href={`/chat?user=${res?.requestee?._id}`}>Chat</Link>
              </button>
            )}
            {OptionsButton()}
          </div>
        );
      default:
        return (
          <div className="d-flex justify-content-end">{OptionsButton()}</div>
        );
    }
  };

  return ActionButtons();
};

const ListingTable = memo(
  ({ tableData, chatType, getDataAgain }: DashboardTableProps) => {
    const dispatch = useAppDispatch();
    const userId = useAppSelector((s) => s.user.id);

    const handleOnClick = async ({
      reqId,
      channelId,
    }: {
      reqId: string;
      channelId: string;
    }) => {
      const getReq = await getReqById(reqId!);
      const isCompletedFlag = getReq?.data?.[0]?.status !== 1;

      if (!isCompletedFlag) {
        dispatch(chatActions.setChannelId(channelId));
        dispatch(chatActions.setCall(true));
        dispatch(chatActions.setReqId(reqId!));
        dispatch(chatActions.setCallToken(getReq?.data?.[0]?.token?.[userId]));
        dispatch(chatActions.setStartedAt(getReq?.data?.[0]?.started_at));
      }

      if (!isCompletedFlag) {
        const modal = document.getElementById("videoModal");
        if (modal) modal.style.display = "block";
      } else if (isCompletedFlag) {
        Swal.fire({
          icon: "error",
          title: "Uh-oh! The timer for the call has ended",
          confirmButtonText: "Close",
          confirmButtonColor: "#AC1991",
          customClass: {
            confirmButton: "custom-btn",
          },
          showCloseButton: true,
        });
      }
    };

    const handleRequestUpdate = (id: string, status: number) => {
      const body = { status, is_promotion: false };
      handleRequest(id, body)
        .then(() => {
          getDataAgain();
          const statusMessages: any = {
            1: {
              title: "Request Approved.",
              text: "Please find request at accepted section",
            },
            2: {
              title: "Request Declined.",
              text: "Please find request at declined section",
            },
            4: {
              title: "Request Completed.",
              text: "Please find request at completed section",
            },
          };
          const { title, text } = statusMessages[status] || {};
          Swal.fire({
            icon: "success",
            title,
            text,
            confirmButtonText: "OK",
            confirmButtonColor: "#AC1991",

            showCloseButton: true,
          });
        })
        .catch((error) => {
          Swal.fire({
            icon: "error",
            title: error.message,
            confirmButtonText: "Finish",
            confirmButtonColor: "#AC1991",

            showCloseButton: true,
          });
        });
    };

    return (
      <table className="table">
        <thead>
          <tr className="fs-7">
            <th scope="col" className="bg-cream">
              ID
            </th>
            <th scope="col" className="bg-cream">
              User
            </th>
            <th scope="col" className="bg-cream">
              Services
            </th>
            <th scope="col" className="bg-cream">
              {chatType === "CUSTOM-SERVICE"
                ? "Message"
                : chatType === "RATING"
                ? "Description"
                : "Time"}
            </th>

            <th scope="col" className="bg-cream">
              Amount
            </th>
            <th scope="col" className="bg-cream"></th>
          </tr>
        </thead>
        <tbody className="bg-body border-0">
          {tableData?.map((res) => (
            <tr className="fs-7 table-row" key={res._id}>
              <th scope="row" className="py-2 bg-body">
                <span className="id-text">#RQ{res?._id.slice(-6)}</span>
              </th>

              <td className="py-2 bg-body">
                <Link
                  href={`/${res?.requestee?.user_type.toLowerCase()}/${
                    res?.requestee?.username
                  }`}
                  className="d-flex gap-2"
                >
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={getAssetUrl({
                      media: res?.requestee?.avatar[0],
                      defaultType: "avatar",
                      variation: "thumb",
                    })}
                    className="rounded-circle"
                    width={35}
                    height={35}
                    alt="dashboard-user"
                  />
                  <div className="d-flex flex-column">
                    <p className="fs-7 mb-0 text-overflow-ellipsis color-dark">
                      {res?.requestee?.display_name}
                    </p>
                    <p className="fs-8 mb-0 color-light">
                      {formatDate(res?.created_at)}
                    </p>
                  </div>
                </Link>
              </td>
              <td className="py-2 bg-body w-25">
                <div className="d-flex align-items-center gap-3">
                  <div
                    style={{
                      backgroundColor: res?.service?.avatar?.length
                        ? "transparent"
                        : ServicesColor[res?.service?.type],
                      padding: res?.service?.avatar?.length ? "" : "0.25rem",
                      borderRadius: "8px",
                    }}
                  >
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={
                        res?.service?.avatar?.length
                          ? getAssetUrl({ media: res?.service?.avatar[0] })
                          : ServicesImg[res?.service?.type]
                      }
                      width={res?.service?.avatar?.length ? 32 : 24}
                      height={res?.service?.avatar?.length ? 32 : 24}
                      alt=""
                    />
                  </div>
                  <div>
                    <p className="m-0">{res?.service?.name}</p>
                  </div>
                </div>
              </td>
              {res?.type === "RATING" ? (
                <td className="py-2 w-25 bg-body">
                  <div>
                    <p
                      className="m-0 pointer text-decoration-underline mb-1"
                      onClick={() => {
                        window.KNKY.showFullscreenMedia(
                          res?.media[0].type,
                          getAssetUrl({
                            media: res?.media && res?.media[0],
                          })
                        );
                      }}
                    >
                      {res.media && res?.media[0]?.type === "image" && "Image"}
                      {res.media && res?.media[0]?.type === "video" && "Video"}
                    </p>
                    <p className="m-0 bg-grey">
                      {res?.extra_data?.rating_type}
                    </p>
                    <p className=" mb-0 color-dark text-truncate ">
                      {res?.extra_data?.description ||
                        "No description available"}
                    </p>
                  </div>
                  {res.status === 1 && (
                    <>
                      <hr />
                      <div className="d-flex flex-nowrap gap-2 py-2">
                        {Array(5)
                          .fill(null)
                          .map((_, i) => (
                            <span key={i}>
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 54 54"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M50.8094 17.9712L36.4241 15.7814L29.988 2.12228C28.8614 -0.263092 25.1351 -0.263092 24.0085 2.12228L17.5758 15.7814L3.18706 17.9712C0.463969 18.3868 -0.639265 21.9073 1.34056 23.9329L11.7529 34.5641L9.2965 49.5783C8.83321 52.4212 11.6863 54.604 14.1327 53.2594L26.9982 46.1731L39.8671 53.2629C42.2935 54.5935 45.1699 52.4456 44.7033 49.5818L42.2469 34.5676L52.6593 23.9364C54.6357 21.9073 53.5325 18.3868 50.8094 17.9712Z"
                                  fill={`${
                                    i - 1 <= res?.message_data?.meta?.stars
                                      ? "#FFB800"
                                      : "#EBEBEC"
                                  }`}
                                />
                              </svg>
                            </span>
                          ))}
                      </div>
                      <p>{res?.message_data?.meta.rateText}</p>
                    </>
                  )}
                </td>
              ) : res?.type === "CUSTOM-SERVICE" ? (
                <td className="py-2 w-25 bg-body">
                  {" "}
                  <p className="w-100 mb-0 color-dark">{res?.service?.note}</p>
                </td>
              ) : (
                <td className="py-2 w-25 bg-body">
                  {" "}
                  <p className="w-100 mb-0 color-dark">
                    {res?.extra_data?.duration / 60 || "0"} Minute
                  </p>
                </td>
              )}

              <td className="py-2 bg-body color-dark">
                {formatCurrency(res?.transaction_data?.locked_amount || 0)}
              </td>

              <td className="py-2 bg-body">
                <ActionButtons
                  res={res}
                  status={res.status}
                  is_promotion={res.is_promotion}
                  onAccept={() => handleRequestUpdate(res._id, 1)}
                  onJoinCall={() =>
                    handleOnClick({
                      reqId: res._id,
                      channelId: res?.message_data?.channel_id,
                    })
                  }
                  onDecline={() => handleRequestUpdate(res._id, 2)}
                  onRequest={() => {
                    ModalService.open("CUSTOM_REQUEST_ACCEPT", {
                      meta: res?.message_data?.meta,
                    } as any);
                  }}
                  onRating={() => {
                    dispatch(
                      chatActions.setFocusedReq({
                        ...res.message_data,
                        createdAt: res.message_data.creation_time,
                        meta: {
                          ...res.message_data.meta,
                          media: res.media,
                        },
                      })
                    );
                  }}
                />
                <RateModal
                  setChildRef={ratingRef}
                  cb={getDataAgain}
                  misc={{ targetName: res?.requestee?.display_name }}
                />
              </td>
            </tr>
          ))}
        </tbody>
        <VideoConf />
      </table>
    );
  }
);

ListingTable.displayName = "ListingTable";
StatusButton.displayName = "StatusButton";

export default ListingTable;
