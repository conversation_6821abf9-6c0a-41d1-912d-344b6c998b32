"use client";

import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import "./index.scss";

import { toast } from "sonner";

import { CreateBtn } from "@/components/common/buttons/channel-group-btns";
import PostActionSendTip from "@/components/common/buttons/send-tip";
import ServicesBtn from "@/components/common/chat/chatbar/helpers/ServicesBtn";
import SubscribersList from "@/components/common/follower-following/subscribers-list";
import JoinUs from "@/components/common/join-us";
import MediaFilter from "@/components/common/media-filter";
import MediaList from "@/components/common/media/list";
import PostList from "@/components/common/post/list";
import SubtabsFilter from "@/components/common/subtabs-filter";
import useIsMobile from "@/hooks/useIsMobile";
import { useAppSelector } from "@/redux-store/hooks";
import { type Author, PostListEnum } from "@/types/post";
import type {
  ChannelProfileSelectionMap,
  ChannelProfileSelectionType,
  ProfileSectionCounter,
} from "@/types/profile";

const SectionMap: ChannelProfileSelectionMap = {
  posts: {
    title: "Posts",
    value: "posts",
    not_allowed: {},
    content: (uid: string) => (
      <>
        <SubtabsFilter uid={uid} />
        <PostList uid={uid} type={PostListEnum.CHANNEL} />
      </>
    ),
  },
  media: {
    title: "Media",
    value: "media",
    not_allowed: {},
    content: (uid: string) => (
      <>
        <SubtabsFilter uid={uid} source="media" />
        <div className="container container-xxl  bg-body rounded-3">
          <MediaFilter from="channel" />
          <MediaList uid={uid} mediaType="channel" />
        </div>
      </>
    ),
  },
  subscriber: {
    title: "Subscribers",
    value: "subscriber",
    not_allowed: { guest: true },
    content: (uid: string) => (
      <div>
        <SubscribersList type="channel" />
      </div>
    ),
  },
};

interface ProfileSectionsProps {
  params: {
    section: ChannelProfileSelectionType;
  };
}

const ProfileSectionSelector = ({
  username,
  isOwner,
  selected,
  sections,
  counter,
}: {
  username: string;
  isOwner: boolean;
  selected: ChannelProfileSelectionType;
  sections: ChannelProfileSelectionType[];
  counter: ProfileSectionCounter;
}) => {
  const id = useParams();
  const [author, setAuthor] = useState({} as Author);
  const state = useAppSelector((state) => state?.defaults?.channel_profile);

  useEffect(() => {
    setAuthor({
      f_name: state?.name,
      // l_name: "string",
      // avatar: state?.img,
      _id: state?._id,

      username: state?.author?.username,
      // badges: state?.details?.badges,
      pic: state?.img,
      role: state?.author?.role,
      user_type: state?.author?.role,
      display_name: state.author.display_name,
    });
    const tooltipTriggerList = document.querySelectorAll(
      '[data-bs-toggle="tooltip"]'
    );
    const tooltipList = [...tooltipTriggerList].map(
      (tooltipTriggerEl) => new window.bootstrap.Tooltip(tooltipTriggerEl)
    );
  }, []);
  const privacy = useAppSelector(
    (state) => state?.defaults?.creator_profile?.privacy_setting
  );
  const is_self = useAppSelector(
    (state) => state.defaults.creator_profile?.details?.self
  );

  const HideTabMap: any = {
    subscriber: !privacy?.showSubscribedChannels,
  };
  const isMobile = useIsMobile();

  return (
    <div
      className="profile-section-selector d-flex justify-content-between bg-body px-3 gap-4 "
      style={{
        position: isMobile ? "sticky" : "unset",
        top: 0,
        zIndex: 3,
      }}
    >
      <div className="profile-section-selector-content flex-grow-1 vanish-bg-body ">
        <div className="profile-sections scrollable d-flex gap-1">
          {sections.map((section) => {
            if (section === "subscriber" && !isOwner) return null;
            return HideTabMap[section] && !is_self ? (
              <></>
            ) : (
              <Link
                scroll={true}
                href={`/channel/${username}/${section}`}
                key={section}
                className={`profile-section d-flex flex-column align-items-center justify-content-center scroll-item mw-4 px-4 py-1 ${
                  section === selected ? "active" : ""
                }`}
              >
                <div className="section-title fs-9 color-bold">
                  {SectionMap[section]?.title}
                </div>
                <div className="section-count fs-5 fw-bold">
                  {counter[SectionMap[section]?.value]}
                </div>
              </Link>
            );
          })}
        </div>
      </div>
      {!state?.marked_as_deleted && (
        <div className="d-lg-flex d-none gap-4 gap-lg-3 align-items-center">
          <CreateBtn
            href={`/create/new-post/${id?.id}?isChannelPost=true`}
            text={"Create new post"}
            visible={isOwner}
          />
          {!isOwner && (
            <>
              <ServicesBtn
                target={{
                  target_user_display_name: state.author.display_name,
                  target_user_id: state.author.id,
                }}
              />
              <PostActionSendTip
                postId={state?._id}
                author={author}
                type="channel"
              />
            </>
          )}
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            className="pointer"
            src="/images/svg/dropdown/copy-link.svg"
            alt=""
            data-bs-toggle="tooltip"
            data-bs-title={"Copy Channel Link"}
            onClick={() => {
              navigator.clipboard.writeText(
                `${process.env.client}/channel/${state.username}`
              );
              toast.success("Copied to clipboard");
            }}
          />
          {/* <Image
          alt=""
          width={32}
          height={32}
          src="/images/creator/three-dots.svg"
        /> */}
        </div>
      )}
    </div>
  );
};

const ProfileSections = ({ params: { section } }: ProfileSectionsProps) => {
  const role = useAppSelector((state) => state?.user?.role);
  const userId = useAppSelector((state) => state?.user.profile?._id);
  const profile = useAppSelector((state) => state?.defaults?.channel_profile);

  const selected = SectionMap[section] ? section : "posts";
  const selectedSection = SectionMap[selected];

  const userSections = [
    "posts",
    "media",
    "subscriber",
    "followers",
  ] as ChannelProfileSelectionType[];

  return (
    <>
      <ProfileSectionSelector
        username={profile.username}
        isOwner={profile.author.id === userId}
        selected={selected}
        sections={userSections}
        counter={profile.counter!}
      />
      {selectedSection.not_allowed[role] ? (
        <JoinUs />
      ) : (
        selectedSection.content(profile.author.id)
      )}
    </>
  );
};

export default ProfileSections;
