import { toast } from "sonner";

import { configActions } from "@/redux-store/actions";
import { store } from "@/redux-store/store";
import type { VFile, VFolder } from "@/types/vault";

import API from ".";

export function CreateNewFolder({
  folder_name,
  root,
  parent_folder,
}: {
  folder_name: string;
  root?: boolean;
  parent_folder?: string;
}) {
  return API.post(`${API.VAULT}/folder`, {
    name: folder_name,
    ...(root !== undefined ? { root } : {}),
    ...(parent_folder !== "" ? { parent_folder } : {}),
  }) as Promise<{
    status: number;
    data: {
      _id: string;
      name: string;
    };
  }>;
}

export function GetFolders() {
  return API.get(`${API.VAULT}/folder`) as Promise<{
    status: number;
    data: VFolder[];
  }>;
}

export function UpdateFolderName(data: { folder_id: string; name: string }) {
  return API.patch(`${API.VAULT}/folder/${data.folder_id}`, {
    name: data.name,
  }) as Promise<any>;
}

export function DeleteFolder({ folder_id }: { folder_id: string }) {
  return API.delete(`${API.VAULT}/folder/${folder_id}`) as Promise<any>;
}

export async function UploadMediaToVault(data: {
  media: File;
  folder_id: string;
  addMediaCb: (media: VFile) => void;
  browser?: {
    name: string;
    os: string;
  };
  type: "image" | "video";
  path: string;
  originalname: string;
}) {
  await API.post(`${API.VAULT}/files`, {
    type: data.type,
    path: data.path,
    originalname: data.originalname,
    folder_id: data?.folder_id,
  })
    .then((res) => {
      data.addMediaCb(res?.data?.[0]);
      toast.success(`Files uploaded successfully!`);
    })
    .catch((error) => {
      console.error("Upload failed:", error);
      toast.error("Failed to upload some files. Please try again.");
    })
    .finally(() => {
      store.dispatch(configActions.setUploadingFilesNumber("-1"));
    });
}

export function GetFiles(data?: { folder_id: string }) {
  const params = new URLSearchParams();
  data && params.append("folder_id", data.folder_id);

  return API.get(`${API.VAULT}/file?${params.toString()}`) as Promise<{
    status: number;
    data: { files: VFile[] };
  }>;
}

export function DeleteFiles(data: { file_ids: string[] }) {
  return API.post(`${API.VAULT}/file/delete`, data) as Promise<any>;
}

export function MoveFiles(data: {
  target_folder_id: string;
  file_ids: string[];
}) {
  return API.patch(`${API.VAULT}/file/move`, data) as Promise<any>;
}

export function UpdateBulkFileTags(data: {
  file_ids: string[];
  add_tags: string[];
  remove_tags: string[];
}) {
  return API.patch(`${API.VAULT}/file`, {
    file_ids: data.file_ids,
    add_tags: data.add_tags,
    remove_tags: data.remove_tags,
  }) as Promise<any>;
}

export function UpdateReleaseFormTags(data: {
  file_ids: string[];

  release_ids: string[];
  // collaborators: any[];
  tagged_users: {
    username: string;
  }[];
}) {
  return API.patch(`${API.VAULT}/file`, {
    file_ids: data.file_ids,

    release_ids: data.release_ids,
    // collaborators: data.collaborators,
    tagged_users: data.tagged_users,
  }) as Promise<any>;
}
