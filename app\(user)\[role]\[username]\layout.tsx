/* eslint-disable @next/next/no-img-element */
"use client";

import Image from "next/image";
import Link from "next/link";
import { notFound, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";
import "./index.scss";

import classNames from "classnames";

import { BlockUser, GetUserProfileByUsername, UnblockUser } from "@/api/user";
import NotFound from "@/app/not-found";
import { OnlineDot } from "@/components/common/OnlineDot";
import ActionButton from "@/components/common/action-button";
import Badges from "@/components/common/badges";
import Follow from "@/components/common/buttons/follow";
import PostActionSendTip from "@/components/common/buttons/send-tip";
import ReadMoreContent from "@/components/common/chat/chat-box/read-more-content";
import EditHashTag from "@/components/common/edit-hashtag";
import PostTypeSelection from "@/components/common/header/post-type-selection";
import Loader from "@/components/common/loader/loader";
import { type ModalRef } from "@/components/common/modal";
import SliderDiv from "@/components/common/slider-div";
import Stories from "@/components/common/stories";
import { useIsLive } from "@/components/common/useIsLive";
import { ModalService } from "@/components/modals";
import { type SocialMedia, SocialMediaImgMap } from "@/global/constants";
import useIsMobile from "@/hooks/useIsMobile";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import {
  blockActions,
  defaultActions,
  userActions,
} from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { store } from "@/redux-store/store";
import type { Author } from "@/types/post";
import type {
  ModifiedUserProfile,
  ProfileSelectionType,
  Tags,
} from "@/types/profile";
import type { UserAccount, UserProfile } from "@/types/user";
import { UserRole } from "@/types/user";
import type { Children } from "@/types/utils";
import { getAssetUrl } from "@/utils/assets";

function refacData(data: UserProfile): any {
  const subscribedChannels =
    store.getState().userData.subscribed.channelSubscriptions;

  const subscribedCollabs =
    store.getState().userData.subscribed.groupSubscriptions;

  // To get the count of subscribed channels/collabs that are marked for deleted, so that its count should not get subtracted
  const deletedSubscribedChannelsCount = subscribedChannels?.filter(
    (channel: any) =>
      channel?.channel?.author === data?._id &&
      channel?.channel?.marked_as_deleted
  ).length;

  const deletedSubscribedCollabsCount = subscribedCollabs?.filter(
    (group: any) =>
      group.group?.mark_as_deleted &&
      group.group?.members.some((member: any) => member.member_id === data?._id)
  ).length;

  const channelCount = data?.self
    ? data.channel_count
    : data?.channel_count -
      data?.channel_deleted_count +
      (deletedSubscribedChannelsCount || 0);

  const collabCount = data?.self
    ? data.group_count
    : data.group_count -
      data?.group_deleted_count +
      (deletedSubscribedCollabsCount || 0);

  return {
    id: data?._id,
    role: data?.user_type.toLowerCase() as UserAccount,
    high_res_avatar: getAssetUrl({
      media: data?.avatar?.[0],
      variation: data?.avatar?.[0]?.variations.includes("high")
        ? "high"
        : "compressed",
      defaultType: "avatar",
    }),
    img: getAssetUrl({ media: data?.avatar?.[0], defaultType: "avatar" }),
    background: getAssetUrl({ media: data?.background?.[0] }),
    details: {
      name: `${data?.f_name} ${data?.l_name}`,
      username: data?.username,
      display_name: data?.display_name,
      loc: data?.city,
      badges: data?.badges,
      // To Make description to have next line on \n or something that supports images also.
      desc: {
        text: data?.about ? data?.about : "",
        images: [
          "/images/svg/socials/facebook.svg",
          "/images/svg/socials/folow.svg",
          "/images/svg/socials/phub.svg",
          "/images/svg/socials/twitter.svg",
          "/images/svg/socials/instagram.svg",
          "/images/svg/socials/onlyfans.svg",
        ],
      },
      self: data?.self,
    },
    privacy_setting: {
      showActiveStatus: data?.privacy_setting?.showActiveStatus,
      showSubscribedChannels: data?.privacy_setting?.showSubscribedChannels,
      showSubscribedGroups: data?.privacy_setting?.showSubscribedGroups,
      showMyFollowers: data?.privacy_setting?.showMyFollowers,
      showMyFollowings: data?.privacy_setting?.showMyFollowings,
      isAnonymous: data?.privacy_setting?.isAnonymous,
    },
    interests: data?.preferences?.kinks,
    lifestyle: data?.preferences?.lifestyle_topics,
    hash: data?.preferences?.hashtags,
    followed_by_you: data?.followed_by_you,
    featured: [],
    counter: {
      posts: data.post_count || 0,
      media: data.media_count?.image_count + data.media_count?.video_count,
      events: data?.self
        ? data.event_count
        : data.event_count - data?.private_event_count || 0,

      subscriber: data?.subscriber_count || 0,
      subscribed: data?.subscribed_count || 0,
      purchased: data?.purchased_items_count || 0,
      channel: channelCount,
      collabs: collabCount,
      subs: channelCount + collabCount,
      "sell-item": data.shop_count || 0,
      wishlist: data.wishlist_count || 0,
      saved: data.pinned_post_count || 0,
      followers: data.follower_count || 0,
      followings: data.following_count || 0,
      vault: data.vault_files_count || 0,
    },
    totalLikes: data?.totalLikes,
    media_count: {
      image_count: data?.media_count?.image_count,
      video_count: data?.media_count?.video_count,
      private_image_count: data?.media_count?.private_image_count,
      private_video_count: data?.media_count?.private_video_count,
    },
    social_handles: data.social_handles || [],
  };
}

async function getData(username: string) {
  try {
    const response = await GetUserProfileByUsername(username);

    if (!response.data.length) {
      throw new Error(`No user found for username : ${useState}`);
    }

    // Return the data as props

    return {
      err: false,
      data: refacData(response.data[0]),
      profileImage: response.data[0].avatar,
      backgroundImage: response.data[0].background,
    };
  } catch (err) {
    return {
      err: err,
      data: {} as ModifiedUserProfile,
      profileImage: [],
      backgroundImage: [],
    };
  }
}

export default function CreatorLayout({
  children,
  params,
}: Children & {
  params: {
    username: string;
    section: ProfileSelectionType;
  };
}) {
  const router = useRouter();
  const navigate = useNavigateBack();
  const hasBlockedMe = useAppSelector((state) => state?.block?.hasBlockedMe);
  const [error, setError] = useState<any>("loading");
  const [state, setState] = useState({} as ModifiedUserProfile);
  const [avatar, setAvatar] = useState("");
  const dispatchAction = useAppDispatch();
  const [author, setAuthor] = useState({} as Author);
  const user = useAppSelector((state) => state.user.profile);
  const userRole = useAppSelector((state) => state.user.role);
  const profImageRef = useRef<any>(null);
  const profBgRef = useRef<any>(null);

  useEffect(() => {
    getData(params.username).then(
      ({ err, data, profileImage, backgroundImage }) => {
        localStorage.setItem("profile-view-id", data.id);
        setError(err);

        setState(data);

        if (profImageRef.current) {
          profImageRef.current.src = data.img;
        }

        if (profBgRef.current) {
          profBgRef.current.src = data.background;
        }

        if (user?._id === data?.id) {
          dispatchAction(userActions.setUserImage(profileImage));
          dispatchAction(userActions.setUserBackground(backgroundImage));
        }

        dispatchAction(defaultActions.setCreatorProfile(data));

        setAuthor({
          f_name: data?.details?.name,
          // l_name: "string",
          avatar: data?.img,
          _id: data?.id,

          username: data?.details?.username,
          badges: data?.details?.badges,
          pic: data?.img,
          role: data?.role,
          user_type: data?.role,
          display_name: data?.details?.display_name,
          followed_by_you: data?.details?.followed_by_you,
        });
      }
    );
  }, [params]);
  const blockedByMe = useAppSelector((state) => state?.block?.blockedByMe);
  const isUserBlocked = blockedByMe[state.id];

  if (
    (!["creator", "user"].includes(window.location.pathname.split("/")[1]) ||
      (state.role && state.role !== window.location.pathname.split("/")[1])) &&
    error !== "loading"
  ) {
    return NotFound();
  }

  if (error === "loading") {
    return (
      <div className="container-xxl bd-gutter g-lg-4 g-0 d-flex align-items-center vh-100 justify-content-center">
        <Loader />
      </div>
    );
  } else if (error) {
    if (error.statusCode === 404) {
      notFound();
    } else {
      throw error;
    }
  }

  const ProfileHeader = () => (
    <div className="profile-header position-relative">
      <div onClick={() => navigate()} className=" pointer backButton">
        <Image
          src={"/images/svg/back.svg"}
          className="back-button-drop-shadow"
          width={54}
          height={54}
          alt="back"
        />{" "}
      </div>
      <div className="profile-image position-relative">
        {/* eslint-disable-next-line @next/next/no-img-element */}

        <img
          ref={profBgRef}
          src={state.background}
          alt="User Profile Background Image"
          width={1280}
          height={720}
          onClick={() => {
            window.KNKY.showFullscreenMedia("image", state.background);
          }}
        />

        {user._id === state.id && user.is_profile_completed && (
          <div className="position-absolute z-3 bottom-0 end-0 p-3">
            <Link
              href={`/${state.role.toLowerCase()}/edit-profile/${state.id}`}
            >
              <Image
                src={"/images/creator/edit.svg"}
                width={20}
                height={20}
                alt="edit profile"
                className="position-relative bottom-0 end-0"
              />
            </Link>
          </div>
        )}
      </div>
    </div>
  );

  const TagContainer = (tags: Tags) => {
    const editTagModalRef = { method: {} as ModalRef };
    const [selectedTags, setSelectedTags] = useState([]);

    const openEditTag = (editTag: any) => {
      setSelectedTags(editTag);
      editTagModalRef.method.open();
    };

    const handleHashtagClick = (hashtag: string) => {
      dispatchAction(
        defaultActions.setSelectedTag(encodeURIComponent(hashtag))
      );
      router.push(
        `/interested?hashtag=${encodeURIComponent(hashtag)}&redirect`
      );
    };

    return (
      <>
        <div className="bg-body d-lg-flex p-3 gap-2 flex-column rounded-lg-3">
          <div className="d-flex justify-content-between align-items-center ">
            <h4 className="">{tags.title}</h4>

            {user._id === state.id && (
              <h6 className="pointer" onClick={() => openEditTag(tags)}>
                Edit
              </h6>
            )}
          </div>
          {tags.array.length > 0 ? (
            <div className=" d-flex gap-3 flex-wrap">
              {tags.array.map((tag) => (
                <span
                  key={tag}
                  onClick={() => handleHashtagClick(tag.substring(1))}
                  className="tag rounded-3 pointer"
                >
                  {tag}
                </span>
              ))}
            </div>
          ) : (
            <div className="text-center">
              {user._id === state.id ? (
                <p className="pointer" onClick={() => openEditTag(tags)}>
                  Add Tags
                </p>
              ) : (
                <p>No Tags</p>
              )}
            </div>
          )}
        </div>
        <EditHashTag
          setChildRef={editTagModalRef}
          misc={selectedTags}
          title={tags.title}
        />
      </>
    );
  };

  const ProfileAboutMe = () =>
    state.details.desc.text ||
    state.social_handles.length ||
    state.details.self ? (
      <div className="bg-body d-flex p-3 gap-2 flex-column rounded-lg-3 ">
        <div className="d-flex align-items-center justify-content-between">
          <h4 className="d-lg-block d-none">About Me</h4>{" "}
        </div>

        <div className=" d-flex gap-3 flex-wrap flex-column">
          {state.details.desc.text && (
            <p className="aboutMe-text">
              <ReadMoreContent
                text={state.details.desc.text}
                characterCount={200}
                btnColor="#ac1991"
              />
            </p>
          )}
          <div className="image-row d-flex align-items-center gap-2 flex-wrap">
            {state.social_handles && state.social_handles.length ? (
              <>
                {state.social_handles.map((social: any, i) => (
                  <a
                    key={i}
                    href={social.external_url}
                    target="_blank"
                    className=""
                  >
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={
                        SocialMediaImgMap[
                          social.social_media_type as SocialMedia
                        ]
                      }
                      className="svg-icon"
                      width={30}
                      height={30}
                      alt={social}
                    />
                  </a>
                ))}
              </>
            ) : state?.details?.self ? (
              <div className="tag rounded">
                <Link href={"/settings/account-information"}>
                  Add Social Media +
                </Link>
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>
    ) : (
      <></>
    );

  const ProfileFeatured = () =>
    state.role.toUpperCase() === UserRole.CREATOR.toUpperCase() &&
    state.id === user?._id && (
      <div className="bg-body featured-container d-flex p-3 gap-2 flex-column rounded-lg-3">
        <h4 className="d-lg-flex d-none">Story</h4>
        <Stories
          uid={state.id}
          type="profile"
          cls="p-0"
          canCreate={
            state.role.toUpperCase() === UserRole.CREATOR.toUpperCase() &&
            state.id === user?._id
          }
        />
      </div>
    );

  const ProfileDetails = () => {
    const isMobile = useIsMobile();
    const [isVisible, setIsVisible] = useState(false);
    const isLive = useIsLive(state.id);

    const toggleVisibility = () => {
      console.log("calling");
      setIsVisible(!isVisible);
    };

    const [hideTagContainer, setHideTagContainer] = useState(
      window.innerWidth <= 768
    );

    useEffect(() => {
      const handleResize = () => {
        if (window.innerWidth <= 768) {
          setHideTagContainer(true);
        } else {
          setHideTagContainer(false);
        }
      };

      handleResize();
      window.addEventListener("resize", handleResize);

      return () => window.removeEventListener("resize", handleResize);
    }, []);

    const isGuest = useAppSelector((state) => state?.user?.role) === "guest";

    const unblockUser = () => {
      Swal.fire({
        title: `Are you sure you want to unblock ${state.details?.display_name}?`,
        icon: "question",
        confirmButtonText: "Unblock",
        confirmButtonColor: "#ac1991",
      }).then((res: any) => {
        if (res.isConfirmed) {
          UnblockUser(state.id)
            .then(() => {
              dispatchAction(blockActions.removeBlockedUser(state.id));
              Swal.fire({
                title: `You unblocked ${state.details?.display_name}`,
                icon: "success",
                confirmButtonColor: "#ac1991",
              }).then((res) => {
                if (res.isConfirmed) {
                  window.location.reload();
                }
              });
            })
            .catch((err) => {
              Swal.fire({
                title: `${err.message}`,
                icon: "error",

                confirmButtonColor: "#ac1991",
              });
            });
        }
      });
    };

    const [followersCount, setFollowersCount] = useState<number>(
      state?.counter?.followers
    );

    const isUserBlocked = blockedByMe[state.id];

    const blockUser = () => {
      if (isUserBlocked) {
        Swal.fire({
          title: `Are you sure you want to unblock ${state.details?.display_name}?`,
          icon: "question",
          confirmButtonText: "Unblock",
          confirmButtonColor: "#ac1991",
        }).then((res: any) => {
          if (res.isConfirmed) {
            UnblockUser(state.id)
              .then(() => {
                dispatchAction(blockActions.removeBlockedUser(state.id));
                Swal.fire({
                  title: `You unblocked ${state.details?.display_name}`,
                  icon: "success",
                  confirmButtonColor: "#ac1991",
                });
              })
              .catch((err) => {
                Swal.fire({
                  title: `${err.message}`,
                  icon: "error",

                  confirmButtonColor: "#ac1991",
                });
              });
          }
        });
      } else {
        Swal.fire({
          title: `Are you sure you want to block ${state.details?.display_name}?`,
          icon: "question",
          confirmButtonText: "Block",
          confirmButtonColor: "#ac1991",
        }).then((res: any) => {
          if (res.isConfirmed) {
            BlockUser(state.id)
              .then((res) => {
                dispatchAction(blockActions.addBlockedUser(res.data));
                Swal.fire({
                  title: `You blocked ${state.details?.display_name}`,
                  icon: "success",
                  confirmButtonColor: "#ac1991",
                });
              })
              .catch((err) => {
                Swal.fire({
                  title: `${err.message}`,
                  icon: "error",

                  confirmButtonColor: "#ac1991",
                });
              });
          }
        });
      }
    };

    const Toast = Swal.mixin({
      toast: true,
      position: "top-end",
      iconColor: "white",
      customClass: {
        popup: "colored-toast",
      },
      showConfirmButton: false,
      timer: 1500,
      timerProgressBar: true,
    });

    const onClick = async () => {
      const location = new URL(window.location.href).origin;
      const link = `${location}/${author?.role.toLowerCase()}/${
        author?.username
      }`;
      await navigator.clipboard.writeText(link).then(() => {
        Toast.fire({
          icon: "success",
          title: "Link copied",
        });
      });
    };

    const [forceRender, setForceRender] = useState(0);

    return (
      <>
        <div className="profile-details d-flex flex-grow-1 flex-column gap-lg-4">
          <div className="profile-view d-flex flex-column p-3 pt-0 gap-lg-2 gap-0 align-items-lg-center rounded-bottom-lg-4">
            <div className="d-flex justify-content-between align-items-start">
              <div className="profile-view-image centered-image mb-4 mb-lg-0">
                <>
                  {/* eslint-disable-next-line @next/next/no-img-element */}

                  <img
                    ref={profImageRef}
                    key={forceRender}
                    className={classNames(
                      "rounded-pill object-fit-cover bg-cream pointer",
                      { "is-live": isLive }
                    )}
                    alt=""
                    width={256}
                    height={256}
                    src={`${state.img}?t=${new Date().getTime()}`}
                    onClick={() => {
                      window.KNKY.showFullscreenMedia(
                        "image",
                        state.high_res_avatar
                      );
                    }}
                  />
                </>

                <OnlineDot
                  userId={state.id}
                  style={{
                    position: "absolute",
                    bottom: "10%",
                    right: window.innerWidth > 768 ? "-21%" : "-40%",
                  }}
                />
                {isLive && (
                  <div className="position-absolute bg-red fw-semibold rounded-1 px-2 fs-7 fs-md-6  live-badge">
                    <div className="text-white">LIVE</div>
                  </div>
                )}
              </div>
              <div className={`d-lg-none d-flex gap-3 align-items-center mt-3`}>
                {state.id !== user?._id &&
                  !isUserBlocked &&
                  state.role === "creator" && (
                    <div
                      onClick={() =>
                        isGuest ? ModalService.open("SIGN_IN") : null
                      }
                      className="d-lg-none d-flex align-self-center"
                    >
                      {/* <PostActionSendTip postAuthor={params} authorId={""} /> */}
                      <PostActionSendTip
                        postId={state?.details?.username}
                        author={author}
                        type="user-profile"
                      />

                      {/* To Add Star in Mobile View */}
                    </div>
                  )}

                <div className="dropdown">
                  <button
                    className="pointer bg-transparent border-0"
                    type="button"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <Image
                      width={28}
                      height={28}
                      alt=""
                      src="/images/post/line-more.svg"
                      className="svg-icon"
                    />
                  </button>
                  <ul
                    className="dropdown-menu p-0"
                    style={{ width: "max-content" }}
                  >
                    <li className="d-flex p-1 align-items-center">
                      <button
                        className="fs-6 mb-0 fw-medium border-0 bg-transparent px-2 d-flex align-items-center gap-2 justify-content-start"
                        type="button"
                        onClick={onClick}
                      >
                        <Image
                          src={"/images/svg/dropdown/copy-link.svg"}
                          width={22}
                          height={22}
                          alt="set as owner"
                          className="svg-icon"
                        />
                        Copy profile link
                      </button>
                    </li>
                    {/* commenting out Report account as no functionalities available for now */}
                    {state.id !== user?._id && !isGuest && (
                      <li className="d-flex p-1 align-items-center">
                        <button
                          className="fs-6 mb-0 w-100 fw-medium border-0 bg-transparent px-2 d-flex align-items-center gap-2 justify-content-start color-red"
                          type="button"
                          onClick={() => blockUser()}
                        >
                          <Image
                            src={"/images/svg/dropdown/red-close.svg"}
                            width={22}
                            height={21}
                            alt="set as owner"
                          />
                          {`${isUserBlocked ? "Unblock" : " Block"}`}
                        </button>
                      </li>
                    )}
                  </ul>
                </div>
              </div>
            </div>
            <h4
              className={`profile-username d-flex text-center align-items-center gap-1 mb-1  ${
                isLive && "mt-3"
              }`}
            >
              {state?.details?.display_name}
              <Badges array={state?.details?.badges} />
            </h4>
            <h5 className="profile-name fw-normal color-black ">
              @{state?.details?.username}
            </h5>
            <h4 className="fw-light color-medium d-lg-none d-block">
              {state.details.loc}
            </h4>
            {/* add justify-content-between justify-content-lg-around when removing d-none and want to show followers count */}
            <div className="d-flex   justify-content-around w-100 px-1 px-lg-0   mb-4 mt-1 mb-lg-2">
              {/* changing d-flex to d-none for client's event */}
              <div
                className={classNames("d-none gap-1", {
                  pointer: state?.details?.username === user?.username,
                })}
                onClick={() => {
                  if (state?.details?.username === user?.username)
                    router.push(
                      `/creator/${state?.details?.username}/followers?scroll=down`
                    );
                }}
              >
                <Image
                  src={"/images/home/<USER>"}
                  width={24}
                  height={24}
                  alt="followers"
                />
                <span className="color-dark fw-medium">{followersCount}</span>
              </div>
              <div className="d-flex gap-1">
                <Image
                  src={"/images/svg/heart.svg"}
                  width={24}
                  height={24}
                  alt="image"
                  className="grayscale"
                />
                <span className="color-dark fw-medium">{state.totalLikes}</span>
              </div>
              <div className="d-flex gap-1">
                <Image
                  src={"/images/home/<USER>"}
                  width={24}
                  height={24}
                  alt="image"
                />
                <span className="color-dark fw-medium">
                  {state?.media_count?.image_count}
                </span>
              </div>
              <div className="d-flex gap-1">
                <Image
                  src={"/images/home/<USER>"}
                  width={24}
                  height={24}
                  alt="video"
                />
                <span className="color-dark fw-medium">
                  {state?.media_count?.video_count}
                </span>
              </div>
            </div>
            {!isUserBlocked ? (
              <div
                onClick={() => (isGuest ? ModalService.open("SIGN_IN") : null)}
                className="d-flex gap-2 align-items-center justify-content-center flex-grow-1 w-100"
              >
                <Follow
                  cls="w-50"
                  visible={true}
                  author={state.id}
                  followed_by_you={state.followed_by_you!}
                  setFollowersCount={setFollowersCount}
                />
                <div className="d-flex flex-column w-100 gap-2">
                  <ActionButton
                    variant="dark"
                    type="outline"
                    className={`w-100`}
                  >
                    <Link
                      href={
                        !isGuest
                          ? state.id !== user?._id
                            ? `/chat?user=${state.id}`
                            : `/settings/edit-information`
                          : ""
                      }
                      className=" d-flex justify-content-center align-items-center gap-2"
                    >
                      {state.id !== user?._id ? (
                        "Chat"
                      ) : (
                        <>
                          <Image
                            src={"/images/svg/dropdown/edit.svg"}
                            width={25}
                            height={25}
                            alt="edit"
                          />
                          <span>Edit my profile</span>
                        </>
                      )}
                    </Link>
                  </ActionButton>
                  {isMobile && state.id === user._id && (
                    <ActionButton
                      className="d-flex gap-2 w-100 justify-content-center align-items-center"
                      onClick={() => router.push("/create/new-post")}
                    >
                      <Image
                        src="/images/post/line-plus.svg"
                        width={20}
                        height={20}
                        className="text-white"
                        style={{ filter: "invert(100%) brightness(2)" }}
                        alt="create"
                      />
                      Create new post
                    </ActionButton>
                  )}
                </div>
              </div>
            ) : (
              <div
                className="btn btn-purple w-100"
                onClick={() => unblockUser()}
              >
                Unblock
              </div>
            )}
            {state.id !== user?._id &&
              !isUserBlocked &&
              state?.counter?.subs !== 0 && (
                <Link
                  href={`/${state?.role}/${state?.details?.username}/subs?type=all`}
                  className="w-100 mt-3 mt-lg-1 "
                >
                  <ActionButton className="w-100  subscribe-btn">
                    View Subscriptions
                  </ActionButton>
                </Link>
              )}
          </div>

          {userRole === "creator" && <ProfileFeatured />}
          <ProfileAboutMe />
          {isMobile && !hideTagContainer && (
            <div
              className="bg-body px-3 color-black fw-medium pointer"
              onClick={() => setHideTagContainer(true)}
            >
              See less
            </div>
          )}
          {hideTagContainer ? null : (
            <>
              <TagContainer title="KNKS" array={state.interests} />
              <TagContainer title="Lifestyle" array={state.lifestyle} />
              {/* <TagContainer title="Hashtags" array={state.hash} /> */}
            </>
          )}
          {isMobile && hideTagContainer && (
            <div
              className="bg-body px-3 color-black fw-medium pointer"
              onClick={() => setHideTagContainer(false)}
            >
              See more
            </div>
          )}
        </div>
        <SliderDiv
          title="Create new post"
          isVisible={isVisible}
          toggleVisibility={toggleVisibility}
          child={<PostTypeSelection toggleVisibility={toggleVisibility} />}
        />
      </>
    );
  };

  if (hasBlockedMe[state.id]) {
    return <></>;
  }

  return (
    <div
      id="profile-container"
      className="container-xxl bd-gutter g-lg-4 g-0 d-flex flex-column  position-relative"
    >
      <ProfileHeader />
      <div className="d-flex profile-content flex-column flex-lg-row gap-lg-4">
        <ProfileDetails />
        <div
          className={classNames(
            "profile-sections-container d-flex flex-grow-1 flex-column gap-md-4 gap-3 "
          )}
        >
          {!isUserBlocked ? (
            children
          ) : (
            <div className="bg-body p-3  d-flex flex-column gap-2 align-items-center">
              <h3>@{state.details.username} is blocked</h3>
              <div className="fs-5">
                You have blocked this user. To see their posts & message them,
                you need to unblock the user.
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
