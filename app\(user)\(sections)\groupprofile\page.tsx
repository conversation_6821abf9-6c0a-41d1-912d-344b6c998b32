"use client";

import "./index.scss";

import PostNavbar from "@/app/(user)/(sections)/navbar";

import GroupInfo from "./info";
import AboutGroup from "./about";
import GroupSocial from "./socials";
import GroupHash from "./hashtags";
import GroupFee from "./fee";
import GroupEarning from "./earnings";
import DeleteGroup from "./delete";

export default function page() {
  return (
    <>
      <PostNavbar
        text={"Edit group profile"}
        icon="/images/svg/nav-back.svg"
        buttonText={"Save Changes"}
        showBtn={true}
        submitPost={() => {}}
      />
      <div className="container p-3">
        <div className="group-settings-wrapper d-flex gap-3 justify-content-center w-100 flex-md-row flex-column">
          <div className="col-md-6 col-sm-12 d-flex flex-column gap-3">
            <GroupInfo />
            <AboutGroup />
            <GroupSocial />
            <GroupHash />
          </div>
          <div className="col-md-6 col-sm-12 group-fee-col d-flex flex-column gap-3">
            <GroupFee />
            <GroupEarning />
            <DeleteGroup />
          </div>
        </div>
      </div>
    </>
  );
}
