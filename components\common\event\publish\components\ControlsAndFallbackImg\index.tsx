import Image from "next/image";
import { memo, useEffect, useRef, useState } from "react";
import classNames from "classnames";
import { TrackToggle, useRoomContext } from "@livekit/components-react";
import { Track } from "livekit-client";

import { ExitEvent } from "@/components/common/event/ExitEvent";

import styles from "./index.module.scss";

function ControlsAndFallbackImgBase() {
  const roomContext = useRoomContext();
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);
  const deviceChangeInProgress = useRef(false);

  const [rotateAnimation, setRotateAnimation] = useState(false);
  const [isCamEnabled, setIsCamEnabled] = useState(true);
  const [isMicEnabled, setIsMicEnabled] = useState(true);

  useEffect(() => {
    (async () => {
      let backCamCounts = 0;
      let isBackCamAvailable = false;

      const devices = await navigator.mediaDevices.enumerateDevices();
      let cameras = [] as MediaDeviceInfo[];

      devices.forEach((device) => {
        if (device.kind === "videoinput") {
          /Back.*Camera/.test(device.label) && backCamCounts++;
          if (device.label === "Back Camera") isBackCamAvailable = true;
          cameras.push(device);
        }
      });

      if (isBackCamAvailable && backCamCounts > 1) {
        cameras = cameras.filter(
          (c) =>
            !(/Back.*Camera/.test(c.label || "") && c.label !== "Back Camera")
        );
      }

      setDevices(cameras);
    })();
  }, []);

  const rotateCamera = () => {
    try {
      if (deviceChangeInProgress.current) return;
      deviceChangeInProgress.current = true;
      setRotateAnimation(true);

      const deviceId = roomContext.getActiveDevice("videoinput");
      if (!deviceId) return;

      const idx = devices.findIndex((d) => d.deviceId === deviceId);
      const nextIdx = (idx + 1) % devices.length;

      roomContext.switchActiveDevice("videoinput", devices[nextIdx].deviceId);
    } catch (e) {
      console.error(e);
    } finally {
      deviceChangeInProgress.current = false;
    }
  };

  return (
    <>
      <div className="position-absolute top-0 end-0 z-1 d-flex flex-column gap-2 p-3">
        <ExitEvent type="publish" />

        {devices.length && (
          <button
            className="btn bg-black p-2 rounded-3 "
            style={{ minWidth: "fit-content" }}
            disabled={!isCamEnabled}
            onClick={rotateCamera}
          >
            <Image
              src="/images/event/camera_switch.svg"
              alt="rotate-camera"
              width={28}
              height={28}
              className={classNames({
                [styles["rotate-animation"]]: rotateAnimation,
              })}
              onAnimationEnd={() => setRotateAnimation(false)}
            />
          </button>
        )}

        <TrackToggle
          source={Track.Source.Camera}
          showIcon={false}
          className={classNames(
            "btn p-2 rounded-3",
            isCamEnabled ? "bg-black" : "bg-danger"
          )}
          style={{ minWidth: "fit-content" }}
          onChange={(enabled) => setIsCamEnabled(enabled)}
        >
          <Image
            src={
              "/images/event/videocam" +
              (isCamEnabled ? "" : "_off") +
              "_white.svg"
            }
            alt="toggle-camera"
            width={28}
            height={28}
          />
        </TrackToggle>

        <TrackToggle
          source={Track.Source.Microphone}
          showIcon={false}
          className={classNames(
            "btn p-2 rounded-3",
            isMicEnabled ? "bg-black" : "bg-danger"
          )}
          style={{ minWidth: "fit-content" }}
          onChange={(enabled) => setIsMicEnabled(enabled)}
        >
          <Image
            src={
              "/images/event/mic" + (isMicEnabled ? "" : "_off") + "_white.svg"
            }
            alt="toggle-microphone"
            width={28}
            height={28}
          />
        </TrackToggle>
      </div>

      {/* Default conver when the video is turned off */}
      {!isCamEnabled && (
        <Image
          src="/images/common/default.svg"
          width={72}
          height={72}
          alt="default user logo"
          className="position-absolute top-50 start-50 translate-middle z-1"
        />
      )}
    </>
  );
}

export const ControlsAndFallbackImg = memo(ControlsAndFallbackImgBase);
