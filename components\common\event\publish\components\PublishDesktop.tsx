import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { LiveKitRoom, useTracks, VideoTrack } from "@livekit/components-react";
import { type Room, Track } from "livekit-client";
import Swal from "sweetalert2";
import classNames from "classnames";

import { EventBridge } from "@/app/event-bridge";
import type { Subscription } from "@/types/event-bridge";
import {
  EVENT_ERROR_MSGS,
  GetEvent,
  GetEventStreamToken,
  GetEventTips,
  GetTotalTipsEarned,
  UpdateEvent,
} from "@/api/event";
import type { Event } from "@/types/event";
import { useAppDispatch } from "@/redux-store/hooks";
import { chatActions, liveEventActions } from "@/redux-store/actions";
import Wrapper from "@/components/common/wrapper";
import LiveStreamChat from "@/components/common/chat/stream-chat";
import { TipHighlight } from "@/components/common/event/tips";
import { FlyingReactions } from "@/components/common/event/reactions";
import { StreamStatus } from "@/components/common/event/stream-status";
import { ViewerCount } from "@/components/common/event/viewer-count";
import { StreamInsights } from "@/components/common/event/stream-insights";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import { sleep } from "@/utils/sleep";

import { Header } from "./Header";
import { Overview } from "./Overview";
import { DisconnectBtn } from "./DisconnectBtn";
import { TimerFromTS } from "./TimerFromTS";
import { ControlsAndFallbackImg } from "./ControlsAndFallbackImg";
import { showErrorResponse } from "./publish-errors";

export function PublishDesktop(props: { eventId: string }) {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const navigateBack = useNavigateBack();

  const [lkAuthToken, setLkAuthToken] = useState<string>();
  const [event, setEvent] = useState<Event>();
  const [isEventEnded, setIsEventEnded] = useState(false);
  const isEventEndedRef = useRef(isEventEnded);
  const [timerTs, setTimerTs] = useState<number>(0);
  const liveKitRoomContextRef = useRef<Room>(null);

  const subRefs = useRef<Subscription[]>([]);

  useEffect(() => {
    (async () => {
      if (!sessionStorage.getItem("camDeviceId")) {
        router.push(`/events/${props.eventId}/device-preview`);
        return;
      }

      const _event = (await GetEvent(props.eventId)).data;
      const _token = (await GetEventStreamToken(props.eventId)).data.token;
      const _tips = (await GetEventTips({ eventId: props.eventId, limit: 10 }))
        .data;
      const _total_tips_earned = (await GetTotalTipsEarned(props.eventId)).data
        .earning;

      if (!EventBridge.isConnected) await sleep(3000);
      const r = await EventBridge.request("LiveStream/AlreadyViewing", {
        event_id: props.eventId,
      });
      if (r.alreadyViewing) throw new Error(EVENT_ERROR_MSGS.ALREADY_VIEWING);

      setEvent(_event);
      setLkAuthToken(_token);
      dispatch(liveEventActions.setLiveEvent(_event));
      dispatch(liveEventActions.addTips({ tips: _tips }));
      dispatch(liveEventActions.setTotalTips(_total_tips_earned));

      const s1 = EventBridge.join("LiveStream/" + props.eventId);
      const s2 = EventBridge.on("LiveStream/Tip", (data) => {
        window.KNKY.chime();
        dispatch(liveEventActions.addTips({ tips: data.tip }));
      });
      await EventBridge.request("LiveStream/Status", {
        event_id: props.eventId,
        is_live: true,
      });
      const s3 = EventBridge.on("LiveStream/End", (data) => {
        EventBridge.request("LiveStream/Status", {
          event_id: props.eventId,
          is_live: false,
        });
        if (data.reason === "AutoEnd")
          liveKitRoomContextRef.current?.disconnect(true);
        setIsEventEnded(true);
        Swal.fire({
          icon: "info",
          title: "Event Ended!",
          text: "Thank you for Streaming!",
        });
      });
      subRefs.current.push(s1, s2, s3);

      setTimerTs(
        new Date(_event.scheduled_on).getTime() +
          (_event.is_live_stream ? 0 : _event.duration * 60 * 1000)
      );
    })().catch((e) => showErrorResponse(e).then(() => navigateBack()));

    return () => {
      subRefs.current.forEach((s) => s.unsubscribe());

      EventBridge.request("LiveStream/Status", {
        event_id: props.eventId,
        is_live: false,
      });

      if (isEventEndedRef.current) {
        dispatch(chatActions.clearStreamChat());
        dispatch(liveEventActions.resetLiveEvent());
      }
    };
  }, []);

  useEffect(() => {
    isEventEndedRef.current = isEventEnded;
  }, [isEventEnded]);

  const onManualEnd = () => {
    UpdateEvent(props.eventId, { has_ended: true });
    setIsEventEnded(true);
  };

  return (
    <>
      <LiveKitRoom
        serverUrl={process.env.livekit}
        token={lkAuthToken}
        connect={true}
        video={{
          deviceId: sessionStorage.getItem("camDeviceId") as string,
          facingMode: "user",
          resolution: { width: 1280, height: 720, frameRate: 30 },
        }}
        audio={{ deviceId: sessionStorage.getItem("micDeviceId") as string }}
        className="d-flex flex-column"
      >
        {isEventEnded && <Header />}
        <div className="container-xxl p-0 p-md-3 overflow-hidden">
          {event && (
            <div className="row g-lg-3">
              <div
                className={classNames(
                  "col-lg-" + (isEventEnded ? 12 : 7),
                  "d-flex flex-column gap-2 gap-md-3 mb-2 mb-md-3"
                )}
              >
                <div className="row g-2 g-lg-3">
                  <div
                    className={classNames(
                      isEventEnded ? "col-lg-8" : "col-lg-12",
                      "flex-column"
                    )}
                  >
                    <CreatorVideoRenderer ended={isEventEnded} />
                  </div>
                  {isEventEnded && (
                    <div className="col-lg-4">
                      <Overview />
                    </div>
                  )}
                </div>

                <Wrapper>
                  <div className="fs-5 fw-medium">Insights</div>
                  <StreamInsights
                    eventId={props.eventId}
                    ended={isEventEnded}
                  />
                </Wrapper>
              </div>

              {!isEventEnded && (
                <div className="col-lg-5 d-flex flex-column gap-2 gap-md-3">
                  <LiveStreamChat postId={event.post} iAmOwner />

                  <Wrapper title="">
                    <div className="d-flex align-items-center justify-content-between">
                      <span className="fs-4 fw-medium">
                        {!event.is_live_stream && "Will end in: "}
                        <TimerFromTS
                          ts={timerTs}
                          reverse={!event.is_live_stream}
                        />
                      </span>
                      <DisconnectBtn
                        ref={liveKitRoomContextRef}
                        onClick={onManualEnd}
                      />
                    </div>
                  </Wrapper>
                </div>
              )}
            </div>
          )}
        </div>
      </LiveKitRoom>
      <FlyingReactions />
    </>
  );
}

function CreatorVideoRenderer(props: { ended: boolean }) {
  const trackRefs = useTracks([Track.Source.Camera]);
  const cameraTrackRef = trackRefs[0];

  return (
    <div className="ratio ratio-16x9 rounded-md-3 overflow-hidden">
      <div className="bg-black text-white">
        <div className="position-absolute top-0 start-0 z-1 d-flex gap-3 p-3 user-select-none">
          <StreamStatus
            status={
              props.ended ? "ENDED" : cameraTrackRef ? "LIVE" : "STARTING"
            }
          />
          {!props.ended && <ViewerCount />}
        </div>

        {!props.ended && <ControlsAndFallbackImg />}

        {cameraTrackRef && (
          <VideoTrack
            trackRef={cameraTrackRef}
            className="w-100 h-100 flip-x"
          />
        )}

        <TipHighlight />
      </div>
    </div>
  );
}
