"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { ExpoLogin } from "@/api/user";
import LoadingSpinner from "@/components/common/loading";
import { ModalPortal } from "@/components/portals/ModalPortal";
import { userActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";

export default function OnBoardingPage({ setRedirect }: { setRedirect: any }) {
  const dispatchAction = useAppDispatch();
  const param = useSearchParams();

  const ot = param.get("ot");
  const kyc_id = param.get("id");

  const [error, setError] = useState<string>("");

  useEffect(() => {
    if (!ot) return;
    ExpoLogin({ ot, ...(kyc_id && { kyc_id }) })
      .then((res) => {
        dispatchAction(userActions.setUserToken(res.data.token));
        dispatchAction(userActions.setUserRefreshToken(res.data.refresh_token));
        setTimeout(() => {
          window.location.href = window.location.origin;
        }, 500);
      })
      .catch((err) => {
        setError(err.message);
      });
  }, []);

  return (
    <>
      <ModalPortal>
        <div
          className="d-flex flex-column gap-4 justify-content-center align-items-center h-100 position-absolute top-0 left-0 w-100"
          style={{
            background:
              "linear-gradient(180deg, rgba(48,6,89,1) 0%, rgba(70,21,119,1) 55%, rgba(97,38,159,1) 100%)",
            zIndex: "9999",
          }}
        >
          {!error ? (
            <>
              <div className="scale-4">
                <LoadingSpinner />
              </div>
              <div className="d-flex flex-column align-items-center gap-1 text-white">
                <h3 className=" fw-bold">Welcome to KNKY</h3>
                <h4>Hold tight, while we onboard you...</h4>
              </div>
            </>
          ) : (
            <div className="d-flex flex-column align-items-center gap-2 text-white">
              <h3 className=" fw-bold">{error}</h3>
              <button
                className="btn btn-purple "
                onClick={() => (window.location.href = window.location.origin)}
              >
                Back to homepage
              </button>
            </div>
          )}
        </div>
      </ModalPortal>
    </>
  );
}
