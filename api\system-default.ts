import API from ".";

export const ListCountries = (searchTerm?: string, strict: boolean = false) => {
  return API.get(
    `${API.SYSTEM_DEFAULTS}/countries${
      searchTerm ? `?country=${searchTerm}` : ""
    }${strict ? "&strict_search=true" : ""}`
  ) as Promise<any>;
};

export const ListCities = async (countryId: string, searchTerm?: string) => {
  return API.get(
    `${API.SYSTEM_DEFAULTS}/countries/${countryId}/cities?limit=30${
      searchTerm ? `&city=${searchTerm}` : ""
    }`
  ) as Promise<any>;
};

export const MatchMakerPlans = async () => {
  return API.get(`${API.SYSTEM_DEFAULTS}/match-maker-plans`) as Promise<any>;
};

export const GetSingleMatchPlan = async (planId: string) => {
  return API.get(
    `${API.SYSTEM_DEFAULTS}/match-maker-plans/${planId}`
  ) as Promise<any>;
};
