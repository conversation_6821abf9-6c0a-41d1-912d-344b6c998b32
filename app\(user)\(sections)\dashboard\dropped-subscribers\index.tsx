import { useEffect, useState } from "react";

import { getDroppedSubscribers } from "@/api/dashboard";
import useIsMobile from "@/hooks/useIsMobile";
import { useAppSelector } from "@/redux-store/hooks";

import DonutChart from "../utils/donut-chart";

export default function DroppedSubscribers() {
  const dateFilter = useAppSelector(
    (state) => state.user.creatorDashboardDateFilter
  );
  const [chartData, setChartData] = useState({
    labels: [],
    series: [],
  }) as any;
  const [droppedSubs, setDroppedSubs] = useState(0);
  const isMobile = useIsMobile();
  useEffect(() => {
    getChartData();
  }, []);

  const getChartData = async () => {
    try {
      const res = await getDroppedSubscribers(dateFilter);
      setDroppedSubs(res.data.total_dropped_subs);
      categorizeData(res.data);
    } catch (err) {
      console.error(err);
    }
  };

  const categorizeData = (data: any) => {
    const reasons = [
      "Not enough updates",
      "Found better pages",
      "Too expensive",
      "Content I do not like",
      "Service promised but not delivered",
      "Just want to stop temporarily",
      "To many advertisements",
      "Other",
    ];

    const reasonCounts = reasons.reduce((acc: any, reason) => {
      acc[reason] = 0;
      return acc;
    }, {});

    data.dropped_subs_by_reason.forEach(({ _id, count }: any) => {
      reasonCounts[_id] = count;
    });

    const labels = Object.keys(reasonCounts);
    const series = Object.values(reasonCounts);

    setChartData({ labels: labels, series: series });
  };

  const hasNonZeroSeries = chartData.series.some((value: number) => value > 0);

  return (
    <div className="container bg-body p-3 rounded-lg-3 rounded-0">
      <div className="d-flex flex-column">
        <div className="d-flex flex-column gap-2">
          <h5 className="fw-semibold mb-0">Dropped subscribers</h5>
          <h5 className="fw-medium color-black mb-0 mt-2">
            Total dropped subscribers
          </h5>
          <div className="d-flex gap-2 align-items-baseline">
            <h2 className="fw-bold mb-0">{droppedSubs}</h2>
            {/* <small className="color-red bg-cream px-2 rounded-2">
              {droppedPercent}
            </small> */}
          </div>
        </div>
        <hr  />
        {hasNonZeroSeries ? (
          <div className="cancel-wrapper mt-2">
            <h4 className="fw-semibold mb-0">Cancellation Reasons</h4>
            <div className="donut-chart-wrapper d-flex flex-column flex-lg-row gap-3 scrollable p-3">
              <DonutChart data={chartData} />
            </div>
            {isMobile && (
              <div className="d-flex flex-column gap-2">
                {chartData.labels.map((label: any, idx: number) => (
                  <div
                    className="d-flex gap-2 align-items-center"
                    key={idx + 1}
                  >
                    <div className={`color-div color-${idx + 1}`}></div>
                    <div className="d-flex gap-1">
                      <p className="m-0 fw-medium color-black">{label}</p>
                      <p className="m-0 fw-medium color-black">
                        {chartData.series[idx]}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center">No data available</div>
        )}
      </div>
    </div>
  );
}
