import styles from "./index.module.scss";

import { memo, useEffect, useRef, useState } from "react";
import { useTracks } from "@livekit/components-react";
import { Track } from "livekit-client";
import type { LocalAudioTrack, LocalVideoTrack } from "livekit-client";
import classNames from "classnames";

import type { TimeGraphRef } from "@/components/common/event/time-graph";
import { TimeGraph } from "@/components/common/event/time-graph";
import { formatShortNumber } from "@/utils/formatter";

const STATS_INTERVAL = 2000;

function getQualityValue(id: string) {
  switch (id) {
    case "q":
      return 1;
    case "h":
      return 2;
    case "f":
      return 3;
    default:
      return 0;
  }
}

const defaultProps = {
  realTimeStatsType: "Graph" as "Graph" | "Count",
};

export function StreamStatsBase(props: {
  stop?: boolean;
  className?: string;
  realTimeStatsType?: "Graph" | "Count";
}) {
  const _props = { ...defaultProps, ...props };
  const videoTrackPubRef = useTracks([Track.Source.Camera])[0]?.publication;
  const audioTrackPubRef = useTracks([Track.Source.Microphone])[0]?.publication;

  const videoBitrateGraphRef = useRef<TimeGraphRef>(null);
  const videoFpsGraphRef = useRef<TimeGraphRef>(null);
  const audioBitrateGraphRef = useRef<TimeGraphRef>(null);

  const [videoDimensions, setVideoDimensions] = useState({
    width: 0,
    height: 0,
  });
  const [audioCodec, setAudioCodec] = useState(null);
  const [videoBitrate, setVideoBitrate] = useState(0);
  const [videoFps, setVideoFps] = useState(0);
  const [audioBitrate, setAudioBitrate] = useState(0);

  const videoStats = useRef({
    framesSent: 0,
    timestamp: 0,
  });

  const divRef = useRef<HTMLDivElement>(null);
  const isDivVisible = useRef<boolean>(false);

  const [tabs, setTabs] = useState([
    { name: "Max Resolution", value: "720p" },
    { name: "Max Live Duration", value: "12 hours" },
    { name: "Video Resolution", value: "0 x 0" },
    { name: "Audio Format", value: "-" },
  ]);

  useEffect(() => {
    if (!divRef.current) return;

    const observer = new IntersectionObserver((entries) => {
      isDivVisible.current = entries[0].isIntersecting;
    });
    observer.observe(divRef.current);

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    setTabs((tabs) =>
      tabs.map((tab) => {
        if (tab.name === "Video Resolution") {
          return {
            ...tab,
            value: `${videoDimensions.width} x ${videoDimensions.height}`,
          };
        } else if (tab.name === "Audio Format") {
          return {
            ...tab,
            value: audioCodec || "-",
          };
        } else return tab;
      })
    );
  }, [videoDimensions, audioCodec]);

  useEffect(() => {
    if (_props.stop) return;

    let interval: NodeJS.Timeout;

    const processStats = async () => {
      const ts = Date.now();
      await processVideoStats(ts);
      await processAudioStats(ts);
      interval = setTimeout(processStats, STATS_INTERVAL);
    };

    processStats();

    return () => clearTimeout(interval);
  }, [videoTrackPubRef, audioTrackPubRef, _props.stop]);

  const formatBitrate = (bitrate: number) => {
    return formatShortNumber(bitrate).replace(/(\d+)([a-zA-Z]+)/, "$1 $2bps");
  };

  const processVideoStats = async (ts: number) => {
    let fps = 0;

    const stats = await (
      videoTrackPubRef?.track as LocalVideoTrack
    )?.getRTCStatsReport();

    if (stats && stats.size) {
      let s;

      for (const report of Array.from(stats.values())) {
        if (
          report.type === "outbound-rtp" &&
          report.kind === "video" &&
          typeof report.rid === "string"
        ) {
          if (
            getQualityValue(report.rid) >= getQualityValue(s?.rid || "q") &&
            report.frameWidth &&
            report.frameWidth > 0
          ) {
            s = report;
          }
        }
      }

      const t_diff = (s?.timestamp || 0) - videoStats.current.timestamp;

      if (t_diff > 0) {
        const f_diff = s.framesSent - videoStats.current.framesSent;
        fps = Math.round(f_diff / (t_diff / 1000));

        if (
          s.frameWidth &&
          (videoDimensions.width !== s.frameWidth ||
            videoDimensions.height !== s.frameHeight)
        ) {
          setVideoDimensions({
            width: s.frameWidth,
            height: s.frameHeight,
          });
        }

        videoStats.current.framesSent = s.framesSent;
        videoStats.current.timestamp = s.timestamp;
      }
    }

    setVideoFps(fps);
    videoFpsGraphRef.current?.append({
      value: fps,
      ts,
      draw: isDivVisible.current,
    });

    const bitrate = videoTrackPubRef?.track?.currentBitrate || 0;
    setVideoBitrate(bitrate);
    videoBitrateGraphRef.current?.append({
      value: Math.floor(bitrate / 1000),
      ts,
      draw: isDivVisible.current,
    });
  };

  const processAudioStats = async (ts: number) => {
    if (audioCodec === null) {
      const stats = await (
        audioTrackPubRef?.track as LocalAudioTrack
      )?.getRTCStatsReport();

      if (stats && stats.size) {
        for (const report of Array.from(stats.values())) {
          if (report.type === "codec") {
            setAudioCodec(report.mimeType.split("/")[1].toUpperCase());
            break;
          }
        }
      }
    }

    const bitrate = audioTrackPubRef?.track?.currentBitrate || 0;
    setAudioBitrate(bitrate);
    audioBitrateGraphRef.current?.append({
      value: Math.floor(bitrate / 1000),
      ts,
      draw: isDivVisible.current,
    });
  };

  return (
    <div
      className={classNames(
        _props.className,
        "d-flex flex-md-column flex-column-reverse"
      )}
      ref={divRef}
    >
      <div className="container-fluid mt-4 mt-md-0">
        <div className="row">
          {tabs.map((tab, i) => (
            <div
              className={classNames(
                "col-6 col-md-3 d-flex flex-column text-center mb-md-3",
                { "mb-3": i < 2 },
                styles.tab
              )}
              key={tab.name}
            >
              <div className="fs-6 fw-medium">{tab.value}</div>
              <div className="text-body-secondary fs-8">{tab.name}</div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <div className="fs-5 fw-medium">Stream Metrics</div>

        {_props.realTimeStatsType === "Graph" && (
          <>
            <GraphWithData
              graphRef={videoBitrateGraphRef}
              data={_props.stop ? "-" : formatBitrate(videoBitrate)}
              label="Video Bitrate"
              thresholdMin={50}
              maxY={3000}
            />

            <GraphWithData
              graphRef={videoFpsGraphRef}
              data={_props.stop ? "-" : `${videoFps} fps`}
              label="Frame Rate"
              thresholdMin={10}
              maxY={45}
            />

            <GraphWithData
              graphRef={audioBitrateGraphRef}
              data={_props.stop ? "-" : formatBitrate(audioBitrate)}
              label="Audio Bitrate"
              thresholdMin={5}
              maxY={200}
            />
          </>
        )}

        {_props.realTimeStatsType === "Count" && (
          <div className="d-flex mt-2">
            <div className="flex-grow-1">
              <div className="fs-6 fw-medium text-nowrap text-center">
                {_props.stop ? "-" : formatBitrate(videoBitrate)}
              </div>
              <div className="text-body-secondary text-nowrap text-center fs-8">
                Video Bitrate
              </div>
            </div>
            <div className="flex-grow-1">
              <div className="fs-6 fw-medium text-nowrap text-center">
                {_props.stop ? "-" : `${videoFps} fps`}
              </div>
              <div className="text-body-secondary text-nowrap text-center  fs-8">
                Frame Rate
              </div>
            </div>
            <div className="flex-grow-1">
              <div className="fs-6 fw-medium text-nowrap text-center">
                {_props.stop ? "-" : formatBitrate(audioBitrate)}
              </div>
              <div className="text-body-secondary text-nowrap text-center  fs-8">
                Audio Bitrate
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export const StreamStats = memo(StreamStatsBase);

function GraphWithData(props: {
  graphRef: any;
  data: string;
  label: string;
  thresholdMin: number;
  maxY: number;
}) {
  return (
    <div className="d-flex align-items-center">
      <div className="flex-grow-1" style={{ height: "120px" }}>
        <TimeGraph
          ref={props.graphRef}
          color="#AC1991"
          width="100%"
          height="100%"
          window={20}
          thresholdMin={props.thresholdMin}
          maxY={props.maxY}
          hideYAxis
        />
      </div>
      <div className="d-flex flex-column text-center ps-3 px-md-5">
        <div className="fs-6 fw-medium text-nowrap">{props.data}</div>
        <div
          className="text-body-secondary text-nowrap"
          style={{ fontSize: "0.8em", width: "5.5em" }}
        >
          {props.label}
        </div>
      </div>
    </div>
  );
}
