"use client";

import { Formik } from "formik";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useRef } from "react";
import * as Yup from "yup";

import FileInput from "@/components/common/file";
import Form from "@/components/common/form";
import Input from "@/components/common/input";
import InputGroup from "@/components/common/list";
import Select from "@/components/common/select";
import TextArea from "@/components/common/textarea";
import Wrapper from "@/components/common/wrapper";
import { EventTypeSelectOptions } from "@/global/constants";
import { EVENT_LIMITS } from "@/global/limits";
import { configActions, createActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { formatCurrency } from "@/utils/formatter";

export default function CreateEvent() {
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  const initialValues = useAppSelector((state) => state.create.event);

  useEffect(() => {
    dispatchAction(configActions.toggleShowPositionWidget());

    return () => {
      dispatchAction(configActions.toggleShowPositionWidget());
    };
  }, []);

  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .min(3, "Min of 3 characters required")
      .required("Event Name is required"),
    media: Yup.string().required("Event Media is required"),
    description: Yup.string()
      .min(4, "Min of 4 characters required")
      .max(400, "Max of 400 characters allowed")
      .required("Event Description is required"),
    date: Yup.string().required("Event Date is required"),
    time: Yup.string().required("Event Time is required"),
    ts: Yup.string().test(
      "timestamp-check",
      "Selected time should be atleast 5 min greater than current time",
      (value, context) => {
        const { date, time } = context.parent;
        const currentDate = new Date();
        const selectedDate = new Date(`${date}T${time}`);
        const diff = selectedDate.getTime() - currentDate.getTime();
        let errMsg = "";

        if (diff < EVENT_LIMITS.TIME_BUFFER_MIN)
          errMsg =
            "Selected date & time should be atleast 5 min greater than now";
        else if (diff > EVENT_LIMITS.TIME_BUFFER_MAX)
          errMsg = "Selected date & time should be less than 1 year from now";

        if (errMsg) {
          return context.createError({
            path: "time",
            message: errMsg,
          });
        } else return true;
      }
    ),
    duration: Yup.object()
      .shape({
        type: Yup.string()
          .oneOf(["hour", "min"])
          .required("Event Duration type is required"),
        value: Yup.number()
          .moreThan(0, "Event Duration needs to be greater than 0")
          .required("Event Duration value is required"),
      })
      .test(
        "duration-check",
        "Event Duration should be less than 12 hours",
        ({ type, value }, context) => {
          const duration = value * (type === "hour" ? 60 : 1) * 60 * 1000;

          if (duration <= EVENT_LIMITS.STREAM_DURATION_MAX) return true;
          else {
            return context.createError({
              path: "duration.value",
              message: "Event Duration should be less than 12 hours",
            });
          }
        }
      ),
    ticket: Yup.object().shape({
      type: Yup.string()
        .oneOf(["Fee", "Free"])
        .required("Event Ticket type is required"),
      price: Yup.number().when("type", {
        is: "Fee",
        then: (schema) =>
          schema
            .min(
              EVENT_LIMITS.STREAM_TICKET_PRICE_MIN,
              `Event Ticket price must be greater than or equal to ${EVENT_LIMITS.STREAM_TICKET_PRICE_MIN}`
            )
            .max(
              EVENT_LIMITS.STREAM_TICKET_PRICE_MAX,
              `Event Ticket price should not exceed ${EVENT_LIMITS.STREAM_TICKET_PRICE_MAX}`
            )
            .required("Event Ticket price is required"),
      }),
      release: Yup.object().shape({
        type: Yup.string()
          .oneOf(["Limited", "Unlimited"])
          .required("Event Release type is required"),
        amount: Yup.number().when("type", {
          is: "Limited",
          then: (schema) =>
            schema
              .moreThan(0, "Event Ticket count needs to be greater than 0")
              .required("Event Ticket count is required"),
        }),
      }),
    }),
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      initialTouched={initialValues.saved ? { media: true, time: true } : {}}
      onSubmit={(data) => {
        router.push("/events/new/preview");
        dispatchAction(
          createActions.setEvent({
            ...data,
            saved: true,
            from: "/events/new",
            nextBtnText: "Create new event",
          })
        );
      }}
    >
      {({ values, setFieldValue, ...rest }) => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const lastReleaseAmount = useRef(values.ticket.release.amount);
        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          if (values.ticket.release.type === "Unlimited") {
            lastReleaseAmount.current = values.ticket.release.amount;
            setFieldValue("ticket.release.amount", "");
          } else {
            setFieldValue("ticket.release.amount", lastReleaseAmount.current);
          }
        }, [values.ticket.release.type]);

        return (
          <Form
            title="Create new event"
            nextBtnText="Next"
            dirty={values.saved}
            formikValues={{ values, setFieldValue, ...rest }}
          >
            <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
              <Wrapper title="Event Information" className="w-lg-75">
                <Select
                  name="type"
                  label="Event Type"
                  array={EventTypeSelectOptions}
                  selected={values.type}
                  setFieldValue={setFieldValue}
                />
                <Input
                  name="name"
                  label="Event Name"
                  placeholder="Example: My Private Event Show"
                />
                <FileInput
                  name="media"
                  accept="*"
                  required={false}
                  values={values}
                  setFieldValue={setFieldValue}
                  className="mt-1"
                >
                  <div className="d-flex flex-column align-items-center justify-content-center text-center p-2 px-3 border border-1 border-style-dashed border-opacity-50">
                    <Image
                      src="/images/post/line-plus.svg"
                      width={30}
                      height={30}
                      alt="plus_icon"
                    />
                    <h5>Upload a photo or video</h5>
                    <p className="color-medium">
                      *Files supported: JPG, PNG or MP4 (upto 30s)
                    </p>
                  </div>
                </FileInput>
              </Wrapper>
              <Wrapper
                title="Description"
                counter={{ value: values.description, max: 400 }}
              >
                <TextArea
                  name="description"
                  type="text"
                  required={false}
                  placeholder="Say somethings to users know more about your event here"
                />
              </Wrapper>
            </div>
            <div className="d-flex col-lg-5 flex-column gap-3 flex-grow-1">
              <Wrapper title="Details" className="w-lg-75">
                <div className="d-flex gap-3">
                  <Input
                    name="date"
                    type="date"
                    label="Date"
                    placeholder="12/02/2001"
                    maxDate={Date.now() + EVENT_LIMITS.TIME_BUFFER_MAX}
                  />
                  <Input
                    name="time"
                    type="time"
                    label="Time"
                    placeholder="10:00"
                  />
                </div>
                <Input
                  name="duration.value"
                  type="number"
                  label="How long is your event?"
                  placeholder="0"
                >
                  <InputGroup
                    label=""
                    type="radio"
                    required={false}
                    theme="square-filled-purple"
                    name="duration.type"
                    className="position-absolute bottom-0 end-0 scale-p7 me-2"
                    array={[
                      { text: "Minute", value: "min" },
                      { text: "Hour", value: "hour" },
                    ]}
                    selected={values.duration.type}
                  />
                </Input>
              </Wrapper>
              <Wrapper title="Price & Ticket">
                <InputGroup
                  label=""
                  type="radio"
                  required={false}
                  theme="circle-purple"
                  name="ticket.type"
                  className="align-items-md-start"
                  childClass="ms-1"
                  array={[
                    { text: "Paid", value: "Fee" },
                    { text: "Free", value: "Free" },
                  ]}
                  selected={values.ticket.type}
                />
                <hr className="m-0 border-2" style={{ color: "lightgray" }} />
                {values.ticket.type === "Free" ? (
                  <></>
                ) : (
                  <Input
                    name="ticket.price"
                    type="number"
                    label="Price"
                    placeholder="0"
                    priceInput={true}
                    className="w-lg-75 "
                  >
                    <span className="color-medium fs-7">
                      Minimum{" "}
                      {formatCurrency(EVENT_LIMITS.STREAM_TICKET_PRICE_MIN)} USD
                    </span>
                  </Input>
                )}
                <Input
                  label="How many tickets do you want to release?"
                  name="ticket.release.amount"
                  type={
                    values.ticket.release.type === "Unlimited"
                      ? "text"
                      : "number"
                  }
                  placeholder={
                    values.ticket.release.type === "Unlimited"
                      ? "Unlimited"
                      : "0"
                  }
                  disabled={values.ticket.release.type === "Unlimited"}
                  className="w-lg-75"
                >
                  <InputGroup
                    label=""
                    type="radio"
                    required={false}
                    theme="square-filled-purple"
                    name="ticket.release.type"
                    className="position-absolute bottom-0 end-0 scale-p7 me-2"
                    array={[
                      { text: "Limited", value: "Limited" },
                      { text: "Unlimited", value: "Unlimited" },
                    ]}
                    selected={values.ticket.release.type}
                  />
                </Input>
              </Wrapper>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
}
