"use client";

import { useEffect, useState } from "react";

import { GetExternalTagInformation, GetSignedUrl } from "@/api/user";
import ActionButton from "@/components/common/action-button";
import type { Media } from "@/types/media";
import type { Author } from "@/types/post";

const ExternalPreview = () => {
  const [state, setState] = useState<"1" | "2" | "3">("1");
  const [otpId, setOtpId] = useState("");
  const [otp, setOtp] = useState("");
  const [verificationResponse, setVerificationResponse] = useState<{
    full_kyc_status: string;
    extraKycDetails: string;
  }>();
  const [data, setData] = useState<{
    media: Media[];
    author: Author;
  }>();

  useEffect(() => {
    const token = localStorage.getItem("external_token");

    if (token) {
      const [_tokenHeader, tokenPayload, _tokenSignature] = token.split(".");

      console.log({ tokenPayload });

      const decoded = JSON.parse(atob(tokenPayload));

      const expiry = new Date(decoded.exp * 1000);

      if (expiry < new Date()) {
        localStorage.removeItem("external_token");
        setState("1");
      } else {
        setState("3");
      }
    }
  }, []);

  async function handleSendCode() {
    try {
      const response = await GetExternalTagInformation({
        email: "<EMAIL>",
      });

      setOtpId(response.data.otp_id);
      setState("2");
    } catch (error) {
      console.error(error);
    }
  }

  async function handleVerifyOtp() {
    try {
      const response = await GetExternalTagInformation({
        email: "<EMAIL>",
        otp_id: otpId,
        otp: otp,
      });
      setVerificationResponse({
        extraKycDetails: response.extra_kyc_details,
        full_kyc_status: response.full_kyc_status,
      });
      setState("3");
      localStorage.setItem("external_token", response.data);
      const res = await fetch(
        `${process.env.backend}/v1/users/media-consent/68108b3c903ba769a538921e/preview?source=POST`,
        {
          headers: {
            Authorization: `Bearer ${response.data}`,
            "x-api-key": process.env.x_api_key,
          },
        }
      );
      const data = await res.json();
      setData({
        media: data.data?.media as Media[],
        author: data.data?.author as Author,
      });
    } catch (error) {
      console.log(error);
    }
  }

  async function handlePreview() {
    if (!data || !data.media) return;

    const paths = data.media.map((item) => {
      const [name, ext] = String(item?.path).split(".");
      return `${name}_compressed.${ext}`;
    });

    if (paths.length === 0) return;

    const res = await GetSignedUrl({
      assets_path: paths,
      type: "signedFullUrl",
      skipAuth: true,
      customHeader: {
        Authorization: `Bearer ${localStorage.getItem("external_token")}`,
        "x-api-key": process.env.x_api_key,
      },
    });

    const urls = data.media
      .filter((item) => item.type === "image" || item.type === "video")
      .map((item) => {
        const [name, ext] = String(item?.path).split(".");
        const compressedPath = `${name}_compressed.${ext}`;
        return {
          url: res.result?.[compressedPath],
          type: item.type as "image" | "video",
        };
      });

    if (!urls.length) return;

    window.KNKY.showFullscreenMedia("image", undefined, [], 0, urls);
  }

  function handleKYC() {}

  if (state === "3") {
    return (
      <div className="h-100 d-flex justify-content-center align-items-center gap-2">
        <ActionButton onClick={handlePreview}>View Preview</ActionButton>
        <ActionButton variant="info" onClick={handleKYC}>
          Proceed with KYC
        </ActionButton>
      </div>
    );
  }

  return (
    <div className="h-100 d-flex flex-column justify-content-center align-items-center gap-2">
      <div>Hello User,</div>
      <div>Verify your email to continue</div>
      <div>{state === "1" ? "Send" : "Sent"} code to ro********@***.c**</div>
      {state === "1" && (
        <ActionButton onClick={handleSendCode}>Send Code</ActionButton>
      )}
      {state === "2" && (
        <div className="d-flex gap-2 flex-column">
          <input
            value={otp}
            type="number"
            placeholder="Enter OTP"
            onChange={(e) => setOtp(e.target.value)}
            className="form-control w-full bg-cream"
          />
          <ActionButton onClick={handleVerifyOtp}>Verify</ActionButton>
        </div>
      )}
    </div>
  );
};

export default ExternalPreview;
