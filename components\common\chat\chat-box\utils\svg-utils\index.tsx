export const VideoIcon = () => (
  <svg
    width="28"
    height="20"
    viewBox="0 0 28 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.1505 0.666504H13.8732C17.1025 0.666504 19.3572 2.74348 19.3572 5.71895V14.2807C19.3572 17.2562 17.1038 19.3332 13.8732 19.3332H6.1505C2.92117 19.3332 0.666504 17.2562 0.666504 14.2807V5.71895C0.666504 2.74473 2.92117 0.666504 6.1505 0.666504ZM24.6105 3.62704C25.1958 3.34953 25.8825 3.37815 26.4412 3.70668C26.9998 4.03397 27.3332 4.60268 27.3332 5.2249V14.776C27.3332 15.3982 26.9998 15.967 26.4412 16.2942C26.1696 16.4551 25.8577 16.5469 25.5363 16.5607C25.215 16.5744 24.8954 16.5096 24.6092 16.3726L22.6345 15.4418C22.2763 15.272 21.9757 15.0124 21.7666 14.6922C21.5575 14.3719 21.448 14.0037 21.4505 13.6286V6.36979C21.4505 5.59699 21.9038 4.90135 22.6345 4.55788L24.6105 3.62704Z"
      fill="#29A81E"
    ></path>
  </svg>
);

export const AudioIcon = ({ color }: { color?: string }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.47 8.78556C13.1229 4.79076 8.9579 2.83064 8.78163 2.75063C8.61685 2.67313 8.43218 2.64836 8.25282 2.67972C3.44448 3.47795 2.72123 6.27813 2.69216 6.3945C2.6526 6.55666 2.65889 6.72664 2.71033 6.88544C8.44545 24.6902 20.3645 27.9904 24.2824 29.0759C24.5841 29.1596 24.833 29.2268 25.022 29.2887C25.2377 29.3592 25.472 29.3455 25.678 29.2505C25.798 29.1959 28.6292 27.8613 29.3216 23.5083C29.3523 23.3183 29.3217 23.1234 29.2343 22.9519C29.1725 22.8319 27.6915 20.0117 23.5846 19.0153C23.4453 18.9796 23.2994 18.9784 23.1595 19.0116C23.0196 19.0449 22.8898 19.1117 22.7814 19.2062C21.4857 20.3136 19.6958 21.4936 18.9235 21.6155C13.7462 19.0826 10.8551 14.2223 10.746 13.3004C10.6824 12.7822 11.8691 10.9621 13.2338 9.48196C13.3196 9.38881 13.3849 9.27872 13.4256 9.15878C13.4663 9.03884 13.4814 8.91171 13.47 8.78556Z"
      fill={color ? color : "#ececeb"}
    />
  </svg>
);

export const RatingIcon = () => (
  <svg
    width="60"
    height="60"
    viewBox="0 0 72 72"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M62.7871 25.8427L46.6035 23.3792L39.3629 8.01266C38.0955 5.32911 33.9034 5.32911 32.636 8.01266L25.3992 23.3792L9.2119 25.8427C6.14843 26.3103 4.90729 30.2708 7.13459 32.5496L18.8485 44.5097L16.085 61.4007C15.5638 64.5989 18.7735 67.0546 21.5258 65.5419L35.9995 57.5699L50.4769 65.5458C53.2067 67.0428 56.4426 64.6264 55.9177 61.4046L53.1542 44.5136L64.8681 32.5535C67.0917 30.2708 65.8505 26.3103 62.7871 25.8427Z"
      fill="#27B1FF"
    />
  </svg>
);

export const ExtraOptionIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={{ transform: "rotate(90deg)" }}
  >
    <path
      d="M9.5987 16.0005C9.5987 17.1787 8.64357 18.1339 7.46536 18.1339C6.28716 18.1339 5.33203 17.1787 5.33203 16.0005C5.33203 14.8223 6.28716 13.8672 7.46536 13.8672C8.64357 13.8672 9.5987 14.8223 9.5987 16.0005Z"
      fill="#4D5053"
    />
    <path
      d="M18.132 16.0005C18.132 17.1787 17.1769 18.1339 15.9987 18.1339C14.8204 18.1339 13.8653 17.1787 13.8653 16.0005C13.8653 14.8223 14.8204 13.8672 15.9987 13.8672C17.1769 13.8672 18.132 14.8223 18.132 16.0005Z"
      fill="#4D5053"
    />
    <path
      d="M26.6653 16.0005C26.6653 17.1787 25.7102 18.1339 24.5319 18.1339C23.3537 18.1339 22.3986 17.1787 22.3986 16.0005C22.3986 14.8223 23.3537 13.8672 24.5319 13.8672C25.7102 13.8672 26.6653 14.8223 26.6653 16.0005Z"
      fill="#4D5053"
    />
  </svg>
);

export const OptionIcon = () => (
  <span>
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.60016 16C9.60016 17.1782 8.64504 18.1333 7.46683 18.1333C6.28862 18.1333 5.3335 17.1782 5.3335 16C5.3335 14.8218 6.28862 13.8667 7.46683 13.8667C8.64504 13.8667 9.60016 14.8218 9.60016 16Z"
        fill="#4D5053"
      />
      <path
        d="M18.1335 16C18.1335 17.1782 17.1783 18.1333 16.0001 18.1333C14.8219 18.1333 13.8668 17.1782 13.8668 16C13.8668 14.8218 14.8219 13.8667 16.0001 13.8667C17.1783 13.8667 18.1335 14.8218 18.1335 16Z"
        fill="#4D5053"
      />
      <path
        d="M26.6667 16C26.6667 17.1782 25.7116 18.1333 24.5334 18.1333C23.3552 18.1333 22.4001 17.1782 22.4001 16C22.4001 14.8218 23.3552 13.8667 24.5334 13.8667C25.7116 13.8667 26.6667 14.8218 26.6667 16Z"
        fill="#4D5053"
      />
    </svg>
  </span>
);

export const BlockBox = () => (
  <svg
    width="244"
    height="87"
    viewBox="0 0 244 87"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_d_2391_44627)">
      <rect x="8" y="23" width="128" height="56" rx="8" fill="white" />
      <svg
        width="196"
        height="24"
        viewBox="0 0 196 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        y="35"
        x="20"
      >
        <path
          d="M11 16C11 15.4477 11.4477 15 12 15C12.5523 15 13 15.4477 13 16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16ZM11 8C11 7.44772 11.4477 7 12 7C12.5523 7 13 7.44772 13 8V12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12V8ZM11.99 2C6.47 2 2 6.48 2 12C2 17.52 6.47 22 11.99 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 11.99 2ZM12 20C7.58 20 4 16.42 4 12C4 7.58 7.58 4 12 4C16.42 4 20 7.58 20 12C20 16.42 16.42 20 12 20Z"
          fill="#131416"
        />
        <path
          d="M37.5625 18H33.0391V6.72656H37.5391C39.5391 6.72656 40.8516 7.83594 40.8516 9.53906C40.8516 10.7344 39.9688 11.8047 38.8438 11.9922V12.0547C40.375 12.2266 41.4062 13.3125 41.4062 14.8125C41.4062 16.7969 39.9609 18 37.5625 18ZM34.7891 8.15625V11.5234H36.75C38.2656 11.5234 39.1016 10.8984 39.1016 9.78125C39.1016 8.73438 38.3828 8.15625 37.1172 8.15625H34.7891ZM34.7891 16.5703H37.2031C38.7812 16.5703 39.6172 15.9219 39.6172 14.7031C39.6172 13.4844 38.7578 12.8594 37.1016 12.8594H34.7891V16.5703ZM42.7734 18V6.72656H44.4609V18H42.7734ZM49.7266 18.1562C47.375 18.1562 45.8281 16.5391 45.8281 13.8906C45.8281 11.25 47.3828 9.63281 49.7266 9.63281C52.0703 9.63281 53.625 11.25 53.625 13.8906C53.625 16.5391 52.0781 18.1562 49.7266 18.1562ZM49.7266 16.7656C51.0547 16.7656 51.9062 15.7188 51.9062 13.8906C51.9062 12.0703 51.0469 11.0234 49.7266 11.0234C48.4062 11.0234 47.5469 12.0703 47.5469 13.8906C47.5469 15.7188 48.4062 16.7656 49.7266 16.7656ZM61.9844 12.6094H60.375C60.2031 11.7344 59.5469 11.0391 58.4453 11.0391C57.1406 11.0391 56.2812 12.125 56.2812 13.8906C56.2812 15.6953 57.1484 16.7422 58.4609 16.7422C59.5 16.7422 60.1797 16.1719 60.375 15.2188H62C61.8203 16.9688 60.4219 18.1562 58.4453 18.1562C56.0938 18.1562 54.5625 16.5469 54.5625 13.8906C54.5625 11.2812 56.0938 9.63281 58.4297 9.63281C60.5469 9.63281 61.8359 10.9922 61.9844 12.6094ZM64.9609 13.3047L68.2812 9.78125H70.2656L66.8594 13.3203L70.3984 18H68.4375L65.6484 14.3516L64.9453 15.0469V18H63.2578V6.72656H64.9453V13.3047H64.9609Z"
          fill="#131416"
        />
      </svg>
      <svg
        width="28"
        height="15"
        viewBox="0 0 28 15"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        x="106"
        y="10"
      >
        <path
          d="M12.4948 0.720174C13.2917 -0.190481 14.7083 -0.190479 15.5052 0.720175L28 15H0L12.4948 0.720174Z"
          fill="white"
        />
      </svg>
    </g>
    <defs>
      <filter
        id="filter0_d_2391_44627"
        x="0"
        y="-1"
        width="244"
        height="88"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="4" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_2391_44627"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_2391_44627"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

export const RightArrowOptionIcon = ({
  color,
  width,
  height,
}: {
  color?: string;
  width?: string;
  height?: string;
}) => (
  <svg
    width={width || "24"}
    height={height || "24"}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.00313 5.09976L13.5584 12.0016L7.79737 18.8906C7.68929 19.0205 7.60789 19.1705 7.5578 19.3319C7.50771 19.4933 7.48991 19.663 7.50544 19.8313C7.52096 19.9996 7.5695 20.1631 7.64827 20.3127C7.72704 20.4622 7.83451 20.5948 7.96455 20.7028C8.09458 20.8108 8.24463 20.8922 8.40612 20.9422C8.56761 20.9923 8.73739 21.0101 8.90576 20.9946C9.07413 20.979 9.2378 20.9305 9.38741 20.8518C9.53702 20.7731 9.66965 20.6657 9.77773 20.5357L16.2075 12.8242C16.3966 12.5942 16.5 12.3057 16.5 12.008C16.5 11.7103 16.3966 11.4219 16.2075 11.1919L9.99634 3.48034C9.89022 3.34831 9.75904 3.23854 9.61034 3.15734C9.46164 3.07615 9.29835 3.02513 9.12985 3.00721C8.96135 2.9893 8.79098 3.00484 8.62851 3.05295C8.46604 3.10106 8.31469 3.18078 8.18316 3.28754C8.052 3.39453 7.94328 3.52634 7.86321 3.67542C7.78315 3.8245 7.73332 3.98792 7.7166 4.15628C7.69987 4.32466 7.71657 4.49467 7.76574 4.65658C7.81491 4.81848 7.89558 4.96909 8.00313 5.09976Z"
      fill={color || "#131416"}
    />
  </svg>
);

export const VideoOption = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.1505 6.66699H15.8732C19.1025 6.66699 21.3572 8.74397 21.3572 11.7194V20.2812C21.3572 23.2567 19.1038 25.3337 15.8732 25.3337H8.1505C4.92117 25.3337 2.6665 23.2567 2.6665 20.2812V11.7194C2.6665 8.74521 4.92117 6.66699 8.1505 6.66699ZM26.6105 9.62753C27.1958 9.35001 27.8825 9.37864 28.4412 9.70717C28.9998 10.0345 29.3332 10.6032 29.3332 11.2254V20.7765C29.3332 21.3987 28.9998 21.9674 28.4412 22.2947C28.1696 22.4556 27.8577 22.5474 27.5363 22.5612C27.215 22.5749 26.8954 22.5101 26.6092 22.3731L24.6345 21.4423C24.2763 21.2725 23.9757 21.0129 23.7666 20.6926C23.5575 20.3724 23.448 20.0041 23.4505 19.6291V12.3703C23.4505 11.5975 23.9038 10.9018 24.6345 10.5584L26.6105 9.62753Z"
      fill="#29A81E"
    />
  </svg>
);

export const VoiceOption = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.47 8.78605C13.1229 4.79125 8.9579 2.83113 8.78163 2.75112C8.61685 2.67361 8.43218 2.64885 8.25282 2.68021C3.44448 3.47844 2.72123 6.27861 2.69216 6.39499C2.6526 6.55715 2.65889 6.72713 2.71033 6.88593C8.44545 24.6907 20.3645 27.9909 24.2824 29.0764C24.5841 29.16 24.833 29.2273 25.022 29.2891C25.2377 29.3597 25.472 29.346 25.678 29.251C25.798 29.1964 28.6292 27.8618 29.3216 23.5088C29.3523 23.3188 29.3217 23.1239 29.2343 22.9524C29.1725 22.8324 27.6915 20.0122 23.5846 19.0158C23.4453 18.9801 23.2994 18.9789 23.1595 19.0121C23.0196 19.0454 22.8898 19.1122 22.7814 19.2067C21.4857 20.314 19.6958 21.4941 18.9235 21.6159C13.7462 19.0831 10.8551 14.2227 10.746 13.3009C10.6824 12.7827 11.8691 10.9625 13.2338 9.48245C13.3196 9.3893 13.3849 9.27921 13.4256 9.15927C13.4663 9.03933 13.4814 8.91219 13.47 8.78605Z"
      fill="#AC1991"
    />
  </svg>
);

export const RatingOption = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M27.9052 11.486L20.7125 10.3911L17.4945 3.56151C16.9312 2.36882 15.068 2.36882 14.5047 3.56151L11.2884 10.3911L4.09402 11.486C2.73247 11.6938 2.18086 13.454 3.17077 14.4668L8.37696 19.7824L7.14874 27.2895C6.91709 28.711 8.34363 29.8024 9.56686 29.1301L15.9996 25.5869L22.434 29.1318C23.6473 29.7971 25.0855 28.7232 24.8521 27.2913L23.6239 19.7841L28.8301 14.4686C29.8184 13.454 29.2667 11.6938 27.9052 11.486Z"
      fill="#27B1FF"
    />
  </svg>
);

export const RemoveFileSelected = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="16" height="16" rx="3" fill="#131416" />
    <path
      d="M12.3028 12.3028C12.1764 12.4291 12.0051 12.5 11.8265 12.5C11.6479 12.5 11.4766 12.4291 11.3502 12.3028L7.99593 8.94847L4.64164 12.3028C4.51458 12.4255 4.34442 12.4934 4.16779 12.4918C3.99116 12.4903 3.82221 12.4195 3.69731 12.2946C3.57241 12.1697 3.50156 12.0007 3.50003 11.8241C3.49849 11.6474 3.56639 11.4773 3.6891 11.3502L7.0434 7.99593L3.6891 4.64163C3.56639 4.51458 3.49849 4.34442 3.50003 4.16779C3.50156 3.99116 3.57241 3.82221 3.69731 3.69731C3.82221 3.57241 3.99116 3.50156 4.16779 3.50003C4.34442 3.49849 4.51458 3.56639 4.64164 3.6891L7.99593 7.0434L11.3502 3.6891C11.4773 3.56639 11.6474 3.49849 11.8241 3.50003C12.0007 3.50156 12.1697 3.57241 12.2946 3.69731C12.4195 3.82221 12.4903 3.99116 12.4918 4.16779C12.4934 4.34442 12.4255 4.51458 12.3028 4.64163L8.94847 7.99593L12.3028 11.3502C12.4291 11.4766 12.5 11.6479 12.5 11.8265C12.5 12.0051 12.4291 12.1764 12.3028 12.3028Z"
      fill="white"
    />
  </svg>
);

export const MicIconChatbar = () => (
  <svg
    width="28"
    height="22"
    viewBox="0 0 29 29"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.1845 0.333984H11.814C8.229 0.333984 5.3235 3.23945 5.3235 6.82398V13.5208C5.3235 17.1053 8.2305 20.0108 11.814 20.0108H12.186C15.771 20.0108 18.675 17.1053 18.675 13.5208V6.82398C18.675 3.23798 15.7695 0.333984 12.186 0.333984H12.1845ZM11.814 2.54132H12.186C14.4285 2.54132 16.2675 4.26612 16.452 6.46025H14.772L14.622 6.47052C14.3545 6.50944 14.1104 6.64165 13.9348 6.84274C13.7592 7.04383 13.6639 7.30024 13.6665 7.56465C13.6665 8.17332 14.1615 8.66758 14.7705 8.66758H16.4685V11.1286H13.3695L13.2195 11.1389C12.9523 11.1782 12.7086 11.3105 12.5333 11.5116C12.358 11.7126 12.2629 11.9689 12.2655 12.2331C12.2655 12.8417 12.7605 13.336 13.3695 13.336H16.4685V13.5223C16.4685 15.888 14.5515 17.8049 12.186 17.8049H11.814C9.4485 17.8049 7.5315 15.8865 7.5315 13.5223V6.82398C7.5315 4.45972 9.45 2.54132 11.814 2.54132ZM22.896 12.4267C23.5065 12.4267 24 12.9224 24 13.5311C24 19.7864 19.215 24.9226 13.104 25.48V28.5629C13.104 29.173 12.609 29.6673 12 29.6673C11.7303 29.6651 11.4707 29.5672 11.2693 29.3919C11.0679 29.2165 10.9384 28.9756 10.905 28.714L10.896 28.5629V25.48C4.785 24.9226 0 19.7878 0 13.5311C0 12.9224 0.495 12.4281 1.104 12.4281C1.713 12.4281 2.208 12.9224 2.208 13.5311C2.208 18.9401 6.591 23.3225 11.9985 23.3225C17.4075 23.3225 21.7935 18.9401 21.7935 13.5311C21.7935 12.9224 22.287 12.4281 22.896 12.4281V12.4267Z"
      fill="#131416"
    />
  </svg>
);

export const PauseMicIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.5 5C5.5 4.44772 5.94772 4 6.5 4H8.5C9.05228 4 9.5 4.44772 9.5 5V19C9.5 19.5523 9.05228 20 8.5 20H6.5C5.94772 20 5.5 19.5523 5.5 19V5Z"
      fill="#4D5053"
    />
    <path
      d="M14.5 5C14.5 4.44772 14.9477 4 15.5 4H17.5C18.0523 4 18.5 4.44772 18.5 5V19C18.5 19.5523 18.0523 20 17.5 20H15.5C14.9477 20 14.5 19.5523 14.5 19V5Z"
      fill="#4D5053"
    />
  </svg>
);

export const StopRecordingIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.07444 7.07509C6.81251 7.3371 6.66536 7.69242 6.66536 8.06291C6.66536 8.43339 6.81251 8.78871 7.07444 9.05072L14.0315 16.0078L7.07444 22.9648C6.81993 23.2284 6.6791 23.5813 6.68228 23.9476C6.68547 24.314 6.83241 24.6644 7.09146 24.9235C7.35051 25.1825 7.70094 25.3294 8.06728 25.3326C8.43362 25.3358 8.78655 25.195 9.05007 24.9405L16.0071 17.9834L22.9642 24.9405C23.2277 25.195 23.5806 25.3358 23.947 25.3326C24.3133 25.3294 24.6638 25.1825 24.9228 24.9235C25.1819 24.6644 25.3288 24.314 25.332 23.9476C25.3352 23.5813 25.1943 23.2284 24.9398 22.9648L17.9828 16.0078L24.9398 9.05072C25.1943 8.78721 25.3352 8.43427 25.332 8.06794C25.3288 7.7016 25.1819 7.35116 24.9228 7.09211C24.6638 6.83306 24.3133 6.68612 23.947 6.68294C23.5806 6.67976 23.2277 6.82058 22.9642 7.07509L16.0071 14.0322L9.05007 7.07509C8.78806 6.81316 8.43274 6.66601 8.06225 6.66601C7.69177 6.66601 7.33645 6.81316 7.07444 7.07509Z"
      fill="#131416"
    />
  </svg>
);
export const FinishRecordingIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="16" cy="16" r="12" fill="white" />
    <path
      d="M16.0003 2.66699C8.64833 2.66699 2.66699 8.64833 2.66699 16.0003C2.66699 23.3523 8.64833 29.3337 16.0003 29.3337C23.3523 29.3337 29.3337 23.3523 29.3337 16.0003C29.3337 8.64833 23.3523 2.66699 16.0003 2.66699ZM14.3945 20.8248C13.8092 21.4101 12.8603 21.4107 12.2743 20.826L9.3276 17.8856C8.80659 17.3657 8.80554 16.5219 9.32524 16.0007C9.84527 15.4792 10.6897 15.4783 11.2109 15.9986L12.9788 17.7634C13.1741 17.9584 13.4904 17.9582 13.6856 17.7631L19.4483 12.0003C19.9689 11.4797 20.813 11.4797 21.3337 12.0003C21.8543 12.5209 21.8543 13.365 21.3337 13.8857L14.3945 20.8248Z"
      fill="#AC1991"
    />
  </svg>
);

export const BlockIcon = () => (
  <span>
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM4 12C4 7.58 7.58 4 12 4C13.85 4 15.55 4.63 16.9 5.69L5.69 16.9C4.63 15.55 4 13.85 4 12ZM12 20C10.15 20 8.45 19.37 7.1 18.31L18.31 7.1C19.37 8.45 20 10.15 20 12C20 16.42 16.42 20 12 20Z"
        fill="#131416"
      />
    </svg>
  </span>
);

export const DeleteIcon = ({ color }: { color?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M15.2833 4.251C15.0425 3.4914 14.4225 3 13.7191 3H10.28L10.1492 3.0054C9.80566 3.03849 9.48022 3.18624 9.218 3.42816C8.95577 3.67007 8.76979 3.99415 8.68584 4.3554L8.47918 5.49121L8.45501 5.59111C8.40805 5.74935 8.3161 5.88757 8.19223 5.98613C8.06835 6.08469 7.91887 6.13857 7.76501 6.14011H5.10836L5.02586 6.14641C4.87838 6.17087 4.74401 6.25197 4.64742 6.3748C4.55084 6.49763 4.4985 6.65396 4.50003 6.81511C4.50003 7.18771 4.77253 7.49011 5.1092 7.49011H18.8916L18.9741 7.48381C19.1215 7.45936 19.2557 7.3784 19.3523 7.25576C19.4488 7.13311 19.5013 6.977 19.4999 6.81601C19.501 6.72847 19.4862 6.64156 19.4561 6.56024C19.4261 6.47892 19.3815 6.4048 19.3249 6.3421C19.2684 6.27941 19.2009 6.22938 19.1264 6.19488C19.0519 6.16037 18.9718 6.14206 18.8908 6.14101H16.235L16.1416 6.13471C15.9888 6.11135 15.8466 6.03677 15.735 5.92148C15.6235 5.80619 15.5483 5.65601 15.52 5.49211L15.3225 4.3977L15.2825 4.2519L15.2833 4.251ZM14.4283 6.14101C14.4003 6.06393 14.3764 5.9852 14.3566 5.90521L14.325 5.75581L14.1366 4.7028C14.1161 4.61531 14.0726 4.53608 14.0114 4.47462C13.9502 4.41316 13.8739 4.37207 13.7916 4.3563L13.72 4.35H10.28C10.2007 4.35005 10.123 4.37335 10.055 4.41741C9.98705 4.46148 9.93146 4.52466 9.89417 4.6002L9.8725 4.6614L9.675 5.75581C9.65 5.88901 9.61584 6.01771 9.57167 6.14011H14.4283V6.14101ZM17.815 9.04801C18.1233 9.07501 18.3583 9.35131 18.375 9.68341L18.3666 9.86881L18.105 13.3347L17.83 16.7169C17.7716 17.3919 17.72 17.9625 17.675 18.4107C17.5183 19.9731 16.58 20.9397 15.1641 20.9676C13.0357 21.0122 10.9067 21.0107 8.77834 20.9631C7.40335 20.9316 6.47835 19.9551 6.32502 18.4161L6.21836 17.283L6.03336 15.0843L5.84336 12.6723L5.62669 9.77431C5.61208 9.59816 5.66259 9.42291 5.76719 9.28683C5.8718 9.15075 6.02201 9.0649 6.18502 9.04801C6.49335 9.02101 6.76502 9.25141 6.82919 9.57631L6.85419 9.84181L7.05835 12.5571L7.28085 15.3714C7.38085 16.59 7.46668 17.5764 7.53585 18.2685C7.62335 19.1451 8.05084 19.596 8.80334 19.6131C10.8467 19.6608 12.9516 19.6617 15.1416 19.6176C15.94 19.6014 16.3741 19.155 16.4633 18.2613L16.5691 17.1345C16.6 16.7871 16.6325 16.4028 16.6675 15.9861L16.8908 13.2186L17.1591 9.66721C17.1689 9.50628 17.2324 9.35462 17.3378 9.24042C17.4432 9.12621 17.5835 9.0572 17.7325 9.04621L17.8158 9.04801H17.815Z"
      fill={color || "#131416"}
    />
  </svg>
);

export const DirectMessageIcon = () => (
  <svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.2584 18.705L7.23633 23.1323L7.99331 17.7349L9.95091 15.1844L13.2589 16.1223L14.2584 18.705Z"
      fill="#0092E2"
    />
    <mask
      id="mask0_15614_73"
      style={{
        maskType: "luminance",
      }}
      maskUnits="userSpaceOnUse"
      x={0}
      y={0}
      width={25}
      height={24}
    >
      <path d="M0.5 1.90735e-06H24.5V24H0.5V1.90735e-06Z" fill="white" />
    </mask>
    <g mask="url(#mask0_15614_73)">
      <path
        d="M1.36907 7.48884C0.814258 7.65009 0.667915 8.36756 1.11529 8.73318L4.77004 11.7203L13.033 8.8822L24.1484 0.868026L1.36907 7.48884Z"
        fill="#83DEFF"
      />
      <path
        d="M9.94727 15.1844L15.3906 19.6335L18.2223 21.9481C18.634 22.2846 19.2559 22.072 19.3754 21.5539L24.146 0.868067L13.2223 9.01386L9.94727 15.1844Z"
        fill="#83DEFF"
      />
      <path
        d="M24.1479 0.868179L9.94922 15.1846L7.23464 23.1325L4.76953 11.7205L24.1479 0.868179Z"
        fill="#00B4FD"
      />
      <path
        d="M11.934 4.4181L1.36905 7.48884C0.814238 7.65009 0.667941 8.36756 1.11527 8.73318L4.77002 11.7203L24.1484 0.868026L13.2571 4.03354"
        stroke="black"
        strokeWidth={0.703125}
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.9822 12.1243L9.94727 15.1844L15.3906 19.6335L18.2223 21.9481C18.634 22.2846 19.2559 22.072 19.3754 21.5539L24.146 0.868067L13.9518 11.1467"
        stroke="black"
        strokeWidth={0.703125}
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.23633 23.1324L14.2584 18.7051L9.95091 15.1844L7.23633 23.1324Z"
        stroke="black"
        strokeWidth={0.703125}
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.9538 11.1467L24.148 0.86806L4.76953 11.7203L7.23464 23.1324L9.94922 15.1844L12.9842 12.1243"
        stroke="black"
        strokeWidth={0.703125}
        strokeMiterlimit={10}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
  </svg>
);

export const PromoteOptions = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.94076 4H22.0327C23.0332 4.01172 24.0208 4.22738 24.9351 4.63387C25.8493 5.04028 26.6711 5.62907 27.3499 6.36401C28.0269 7.09367 28.5501 7.9519 28.8887 8.88786C29.2272 9.82373 29.3742 10.8182 29.3206 11.812L29.318 20.128C29.3776 21.1313 29.2339 22.1362 28.8958 23.0827C28.5576 24.0292 28.0319 24.8976 27.3499 25.636C26.6694 26.3721 25.8457 26.9615 24.9294 27.368C24.0129 27.7745 23.0231 27.9896 22.0207 28H9.94076C5.71533 28 2.66602 24.5854 2.66602 20.188V11.812C2.66602 7.41602 5.71403 4 9.94076 4ZM23.9966 11.2617C24.113 11.3262 24.2153 11.4131 24.2979 11.5173C24.4497 11.7084 24.5285 11.9475 24.5195 12.1915C24.5107 12.4355 24.4152 12.6683 24.2498 12.848L24.1405 12.9507L18.6644 17.3653C18.02 17.8715 17.2308 18.159 16.4118 18.1861C15.5928 18.2132 14.7863 17.9785 14.1099 17.516L13.8952 17.36L8.38184 12.9533C8.17139 12.7843 8.0363 12.539 8.00586 12.2708C7.97542 12.0026 8.05208 11.7333 8.21908 11.5213C8.36898 11.3303 8.58285 11.1999 8.82113 11.1541C8.95264 11.1287 9.08675 11.1302 9.21582 11.157C9.3208 11.1788 9.42236 11.2174 9.51644 11.272L9.63916 11.3573L15.1458 15.7573C15.4425 15.991 15.804 16.1278 16.1812 16.1491C16.388 16.1607 16.5941 16.1373 16.791 16.0809C16.9531 16.0345 17.1089 15.9658 17.2537 15.876L17.4098 15.7653L22.8765 11.3586C22.98 11.2751 23.099 11.213 23.2266 11.1758C23.3543 11.1385 23.4881 11.127 23.6203 11.1417C23.7524 11.1565 23.8802 11.1973 23.9966 11.2617Z"
      fill="#131416"
    />
  </svg>
);

export const RatingPromotion = ({ color = "white" }: { color?: string }) => (
  <svg
    width="15"
    height="15"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.15323 3.408C8.42023 1.136 9.05323 0 10.0002 0C10.9472 0 11.5802 1.136 12.8472 3.408L13.1752 3.996C13.5352 4.642 13.7152 4.965 13.9952 5.178C14.2752 5.391 14.6252 5.47 15.3252 5.628L15.9612 5.772C18.4212 6.329 19.6502 6.607 19.9432 7.548C20.2352 8.488 19.3972 9.469 17.7202 11.43L17.2862 11.937C16.8102 12.494 16.5712 12.773 16.4642 13.117C16.3572 13.462 16.3932 13.834 16.4652 14.577L16.5312 15.254C16.7842 17.871 16.9112 19.179 16.1452 19.76C15.3792 20.341 14.2272 19.811 11.9252 18.751L11.3282 18.477C10.6742 18.175 10.3472 18.025 10.0002 18.025C9.65323 18.025 9.32623 18.175 8.67223 18.477L8.07623 18.751C5.77323 19.811 4.62123 20.341 3.85624 19.761C3.08924 19.179 3.21623 17.871 3.46923 15.254L3.53523 14.578C3.60723 13.834 3.64323 13.462 3.53523 13.118C3.42923 12.773 3.19024 12.494 2.71424 11.938L2.28024 11.43C0.603235 9.47 -0.234765 8.489 0.057235 7.548C0.349235 6.607 1.58024 6.328 4.04024 5.772L4.67624 5.628C5.37524 5.47 5.72424 5.391 6.00524 5.178C6.28624 4.965 6.46523 4.642 6.82523 3.996L7.15323 3.408Z"
      fill={color}
    />
  </svg>
);

export const VideoPromotion = ({ color = "white" }: { color?: string }) => (
  <svg
    width="15"
    height="15"
    viewBox="0 0 30 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0 4.64286C0 3.41149 0.513563 2.23057 1.42771 1.35986C2.34186 0.489157 3.58171 0 4.87451 0H14.6235C15.2637 0 15.8975 0.120091 16.4889 0.353417C17.0803 0.586742 17.6177 0.928732 18.0703 1.35986C18.523 1.79099 18.882 2.30282 19.127 2.86611C19.372 3.42941 19.498 4.03315 19.498 4.64286V15.3571C19.498 15.9669 19.372 16.5706 19.127 17.1339C18.882 17.6972 18.523 18.209 18.0703 18.6401C17.6177 19.0713 17.0803 19.4133 16.4889 19.6466C15.8975 19.8799 15.2637 20 14.6235 20H4.87451C3.58171 20 2.34186 19.5108 1.42771 18.6401C0.513563 17.7694 0 16.5885 0 15.3571V4.64286ZM25.8829 18.1257L20.9979 14.9143V5.08857L25.8829 1.87571C27.6242 0.73 30 1.91857 30 3.93286V16.0686C30 18.0829 27.6242 19.2714 25.8829 18.1257Z"
      fill={color}
    />
  </svg>
);

export const VoicePromotion = ({ color = "white" }: { color?: string }) => (
  <svg
    width="15"
    height="15"
    viewBox="0 0 16 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.93196 6.98129L6.27776 8.46514C6.49643 9.21343 6.80187 9.93358 7.18787 10.6109C7.5905 11.2833 8.07565 11.9027 8.63205 12.4548L10.7743 11.7948C11.9745 11.4249 13.2846 11.8048 14.0827 12.7547L15.3029 14.2066C15.7969 14.7894 16.0445 15.5416 15.9934 16.3039C15.9422 17.0661 15.5963 17.7785 15.0288 18.2902C13.0386 20.106 9.97422 20.7199 7.67993 18.9181C5.66262 17.3317 3.95635 15.3858 2.64731 13.1787C1.33494 10.9824 0.456281 8.55473 0.0589882 6.02738C-0.381066 3.18366 1.70119 0.907879 4.29751 0.131955C5.84571 -0.332 7.49791 0.463922 8.06598 1.94778L8.73606 3.69761C9.17612 4.84949 8.86008 6.14937 7.93196 6.98129Z"
      fill={color}
    />
  </svg>
);

export const DurationPromotion = ({ color = "white" }: { color?: string }) => (
  <svg
    width="15"
    height="15"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.5417 11.25H9.375C9.20924 11.25 9.05027 11.1842 8.93306 11.0669C8.81585 10.9497 8.75 10.7908 8.75 10.625V4.79167C8.75 4.62591 8.81585 4.46694 8.93306 4.34972C9.05027 4.23251 9.20924 4.16667 9.375 4.16667C9.54076 4.16667 9.69973 4.23251 9.81694 4.34972C9.93415 4.46694 10 4.62591 10 4.79167V10H13.5417C13.7074 10 13.8664 10.0658 13.9836 10.1831C14.1008 10.3003 14.1667 10.4592 14.1667 10.625C14.1667 10.7908 14.1008 10.9497 13.9836 11.0669C13.8664 11.1842 13.7074 11.25 13.5417 11.25ZM10 0C4.47667 0 0 4.4775 0 10C0 15.5225 4.47667 20 10 20C15.5225 20 20 15.5225 20 10C20 4.4775 15.5225 0 10 0Z"
      fill={color}
    />
  </svg>
);

export const ProcessingLoader = ({ color }: { color?: string }) => {
  return (
    <svg
      width="24"
      fill={color || "#000"}
      height="24"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="4" cy="12" r="3">
        <animate
          id="spinner_qFRN"
          begin="0;spinner_OcgL.end+0.25s"
          attributeName="cy"
          calcMode="spline"
          dur="0.6s"
          values="12;6;12"
          keySplines=".33,.66,.66,1;.33,0,.66,.33"
        />
      </circle>
      <circle cx="12" cy="12" r="3">
        <animate
          begin="spinner_qFRN.begin+0.1s"
          attributeName="cy"
          calcMode="spline"
          dur="0.6s"
          values="12;6;12"
          keySplines=".33,.66,.66,1;.33,0,.66,.33"
        />
      </circle>
      <circle cx="20" cy="12" r="3">
        <animate
          id="spinner_OcgL"
          begin="spinner_qFRN.begin+0.2s"
          attributeName="cy"
          calcMode="spline"
          dur="0.6s"
          values="12;6;12"
          keySplines=".33,.66,.66,1;.33,0,.66,.33"
        />
      </circle>
    </svg>
  );
};

export const ForwardMessageIcon = () => (
  <svg
    baseProfile="tiny"
    height="18px"
    id="Layer_1"
    viewBox="0 0 24 24"
    width="18px"
    xmlSpace="preserve"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <g>
      <path d="M4,19.999c-0.096,0-0.191-0.015-0.286-0.042C3.29,19.831,3,19.441,3,18.999v-1c0-4.8,3.381-8.864,8-9.796V6.499   c0-0.534,0.208-1.036,0.585-1.414c0.756-0.757,2.075-0.756,2.829-0.001l6.288,6.203C20.893,11.475,21,11.73,21,11.999   s-0.107,0.524-0.298,0.712l-6.293,6.207c-0.746,0.746-2.067,0.751-2.823-0.005C11.208,18.535,11,18.033,11,17.499v-1.437   c-2.495,0.201-4.523,0.985-6.164,3.484C4.646,19.834,4.331,19.999,4,19.999z M12,14.01c0.262,0,1-0.01,1-0.01v3.499l5.576-5.5   L13,6.503V10c0,0-0.384-0.004-0.891,0.052c-3.416,0.378-6.125,2.864-6.892,6.08C7.338,14.404,9.768,14.066,12,14.01z" />
    </g>
  </svg>
);
