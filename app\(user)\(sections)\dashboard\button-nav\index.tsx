import Image from "next/image";
import { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Swal from "sweetalert2";

import ActionButton from "@/components/common/action-button";
import { useNavigateBack } from "@/hooks/useNavigateBack";
import { userActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { formatToUTCDate, getDateRange } from "@/utils/creator-dashboard";

export default function ButtonNav() {
  const [fromDate, setFromDate] = useState() as any;
  const [toDate, setToDate] = useState() as any;
  const filter = useAppSelector((state) => state.user.creatorDashboardFilter);

  const dispatch = useAppDispatch();
  const [active, setActive] = useState(filter);
  const navigationBack = useNavigateBack();
  const today = new Date();
  useEffect(() => {
    const { start, end } = getDateRange(filter);
    setFromDate(new Date(end));
    setToDate(start);
  }, [filter]);

  const handleFilterClick = () => {
    if (fromDate && toDate) {
      const date = formatToUTCDate(fromDate, toDate);
      const formattedToDate = date.to;
      const formattedFromDate = date.from;
      Swal.fire({
        icon: "success",
        title: "Filter successfully",
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
      });
      dispatch(userActions.setCreatorFilter(""));
      setActive("");
      dispatch(
        userActions.setCreatorDateFilter(
          `fromDate=${formattedFromDate}&toDate=${formattedToDate}`
        )
      );
    } else {
      Swal.fire({
        icon: "error",
        title: "Please select a date",
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
      });
    }
  };

  const onChange = (dates: any) => {
    const [start, end] = dates;
    setFromDate(start);
    setToDate(end);
  };

  const changeFilter = (type: string) => {
    const date = getDateRange(type);
    dispatch(
      userActions.setCreatorDateFilter(
        `fromDate=${date.start}&toDate=${date.end}`
      )
    );
    dispatch(userActions.setCreatorFilter(type));
  };

  return (
    <nav className=" navbar navbar-light bg-cream dashboard-nav position-sticky top-0 z-2">
      <div className="container-sm px-lg-3 px-0">
        <div className="d-flex py-2 align-items-center justify-content-between  scrollable px-3 w-100 gap-lg-0 gap-md-3 gap-3">
          <div onClick={() => navigationBack()} className="pointer">
            <Image
              src={"/images/svg/back.svg"}
              width={40}
              height={40}
              alt="back"
            />{" "}
          </div>
          <div className="d-flex py-2 align-items-center gap-3 flex-nowrap  text-nowrap ">
            <button
              type="button"
              className={`btn-light  border-0 px-3 py-1 rounded-3 fw-medium fs-6 ${
                active == "today" && "active"
              }`}
              onClick={() => {
                setActive("today");
                changeFilter("today");
              }}
            >
              Today
            </button>
            <button
              type="button"
              className={`btn-light  border-0 px-3 py-1 rounded-3 fw-medium fs-6 ${
                active == "yesterday" && "active"
              }`}
              onClick={() => {
                setActive("yesterday");
                changeFilter("yesterday");
              }}
            >
              Yesterday
            </button>
            <button
              type="button"
              className={`btn-light  border-0 px-3 py-1 rounded-3 fw-medium fs-6 ${
                active == "week" && "active"
              }`}
              onClick={() => {
                setActive("week");
                changeFilter("week");
              }}
            >
              Week
            </button>
            <button
              type="button"
              className={`btn-light  border-0 px-3 py-1 rounded-3 fw-medium fs-6 ${
                active == "month" && "active"
              }`}
              onClick={() => {
                setActive("month");
                changeFilter("month");
              }}
            >
              Month
            </button>
            <button
              type="button"
              className={`btn-light  border-0 px-3 py-1 rounded-3 fw-medium fs-6 ${
                active == "year" && "active"
              }`}
              onClick={() => {
                setActive("year");
                changeFilter("year");
              }}
            >
              Year
            </button>
          </div>
          <div className="d-flex gap-2 btn-light  border-0 px-3 py-1 rounded-3 fw-medium fs-6">
            <div className="d-flex creator-dashboard-calender align-items-center">
              <DatePicker
                onChange={onChange}
                selectsStart
                isClearable={true}
                selected={fromDate}
                maxDate={today}
                startDate={fromDate}
                endDate={toDate}
                selectsRange={true}
                calendarClassName="custom-calendar"
                placeholderText="Select Start to End date"
                dateFormat="dd MMM yyyy"
              />
              <ActionButton
                className="bg-purple border-0 px-3 py-1 rounded text-white"
                onClick={handleFilterClick}
              >
                Filter
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
