import classNames from "classnames";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { memo, useEffect, useState } from "react";

import { GetSignedUrl } from "@/api/user";
import WaveAudioPlayer from "@/components/WaveAudioPlayer";
import {
  AudioReceived,
  InformationIcon,
  RatingReceiverSent,
  VideoCallEnded,
} from "@/components/matchmaker/match-cards/utils/svg";
import type { MessageInterface } from "@/redux-store/slices/chat.slice";
import { getAssetUrl } from "@/utils/assets";
import { formatCurrency } from "@/utils/formatter";

import { isAudioMedia, isImageMedia } from "..";
import ReadMoreContent from "../../../../read-more-content";
import { ProcessingLoader } from "../../../svg-utils";

const MediaRendererSender = ({
  message,
  setImageMessage,
}: {
  message: MessageInterface;
  setImageMessage: (value: any) => void;
}) => {
  const router = useRouter();
  const media = message?.meta?.media;
  const mediaFee = message?.meta?.media_fee;
  const mediaStatus = Array.isArray(media) ? media[0]?.status : media?.status;
  const mediaType = Array.isArray(media) ? media[0]?.type : media?.type;
  const [customMedialUrl, setCustomMediaUrl] = useState("");
  const mediaUrl = Array.isArray(media)
    ? getAssetUrl({ media: media[0] })
    : getAssetUrl({ media });

  useEffect(() => {
    if (mediaStatus !== "Completed" || !media) return;

    const [name, ext] = String(
      Array.isArray(media) ? media?.[0]?.path : media?.path
    )?.split(".");

    if (!name || !ext) return;

    GetSignedUrl({
      assets_path: [`${name}_compressed.${ext}`],
      type: "signedFullUrl",
    }).then((response) => {
      setCustomMediaUrl(response.result[`${name}_compressed.${ext}`]);
    });
  }, [media]);

  const handleImageClick = () => {
    if (message?.meta?.address) return;
    setImageMessage(message);

    if (!media) return;

    const { path } = Array.isArray(media) ? media?.[0] : media;

    if (["video", "image"].includes(mediaType!)) {
      window.KNKY.showFullscreenMedia(
        mediaType === "audio" ? "image" : mediaType!,
        path.startsWith("vault") ? customMedialUrl : mediaUrl
      );
    }
  };

  const renderMediaBadge = (need_absolute: boolean = true) =>
    mediaFee > 0 && (
      <div
        className={classNames(
          "m-2 top-0 end-0",
          {
            "badge text-bg-warning": !message?.meta?.is_unlocked,
            "badge text-bg-success": message?.meta?.is_unlocked,
          },
          need_absolute ? "position-absolute" : "position-relative"
        )}
      >
        {!message?.meta?.is_unlocked ? "Pending" : "Paid"}{" "}
        {formatCurrency(mediaFee)}
      </div>
    );

  const renderMediaContent = () => {
    if (message?.meta?.type === "story-reply") return;

    if (isImageMedia(media) && mediaStatus === "Completed") {
      if (!media) return;

      const { path } = Array.isArray(media) ? media?.[0] : media;
      return (
        <>
          {renderMediaBadge()}
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            width={350}
            height={150}
            className="object-fit-cover mw-100 d-block rounded-1 pointer"
            alt=""
            src={path.startsWith("vault") ? customMedialUrl : mediaUrl}
            onClick={handleImageClick}
          />
        </>
      );
    }

    if (isAudioMedia(media) && mediaStatus === "Completed") {
      return (
        <div>
          <div className="text-end w-100">{renderMediaBadge(false)}</div>
          <WaveAudioPlayer
            height={25}
            url={mediaUrl}
            className="mb-2 mt-2 w-100 h-100"
            progressColor="rgb(172,25,145, 0.5)"
            waveColor="#ddd"
          />
        </div>
      );
    }

    if (mediaType === "video" && mediaStatus === "Completed") {
      return (
        <>
          {renderMediaBadge()}
          <video
            src={`${mediaUrl}#t=0.1`}
            className="object-fit-cover mw-100 d-block rounded-1 pointer"
            width={350}
            height={150}
            preload="metadata"
            playsInline
            controls
            controlsList="nodownload"
          ></video>
        </>
      );
    }

    if (["Failed", "Optimizing", "Moderating"].includes(mediaStatus!)) {
      return (
        <div className="text-white opacity-75 m-2 mb-0">
          <InformationIcon />
          <span className="ms-2 color-medium">
            {mediaStatus === "Failed"
              ? "The moderation of the media has failed due to our content moderation policy."
              : "Processing"}
            {mediaStatus === "Failed" && (
              <Link
                className="pointer underline text-capitalize "
                href="/articles/content-moderation-and-protection-policy"
                target="_blank"
              >
                Read policy.
              </Link>
            )}
            {mediaStatus !== "Failed" && <ProcessingLoader />}
          </span>
        </div>
      );
    }

    return null;
  };

  const renderRatingStars = () =>
    message?.meta?.stars && (
      <div className="text-center">
        <RatingReceiverSent starCount={message?.meta?.stars} />
      </div>
    );

  const renderMessageContent = () =>
    message?.message !== "Attachment" && (
      <div>
        {"meta" in message &&
        message?.meta?.type === "ACCEPT_CALL" &&
        message?.meta?.isCompleted ? (
          message?.message === "Video Call" ? (
            <VideoCallEnded color="#ac1991" />
          ) : (
            <AudioReceived />
          )
        ) : null}
        <ReadMoreContent
          text={
            "meta" in message &&
            message?.meta?.type === "ACCEPT_CALL" &&
            message?.meta?.isCompleted
              ? message?.message === "Video Call"
                ? "Video Call ended"
                : "Voice Call ended"
              : message?.meta?.stars
              ? `You rated the user with ${message?.meta?.stars} stars.`
              : message.message
          }
          characterCount={700}
          classes="px-2 pt-1"
          textColor="black"
        />
      </div>
    );

  const renderAddress = () =>
    message?.meta?.address && (
      <div className="ms-2">
        Address:{" "}
        {`${message?.meta?.address.street}, ${message?.meta?.address.city}, ${
          message?.meta?.address.state
        }, ${message?.meta?.address.zip_code}, ${
          message?.meta?.address?.country?.split(":")?.[1]
        }`}
        .<br />
        <Link
          href={`/shop/${message.meta.transaction_id}/track`}
          className="mt-2 underline fw-bold"
        >
          Go to item
        </Link>
      </div>
    );

  const renderEntityMessage = () =>
    message?.meta?.entity_type && (
      <div className="mx-2">
        <span className="fw-bold">
          {message?.meta?.entity_type.charAt(0).toUpperCase() +
            message?.meta?.entity_type.slice(1)}
        </span>
        : {message?.meta?.channel_name}
        <div>
          <span className="fw-bold">Subscription Plan:</span>{" "}
          {message?.meta?.subscription_type}
        </div>
        <Link
          href={`/channel/${message?.meta?.tag_name}`}
          className="pointer underline"
        >
          Visit the channel
        </Link>
      </div>
    );
  return (
    <div
      className={`position-relative ${
        mediaType === "audio" ? "w-100 h-100 p-1" : ""
      }`}
      onClick={() => {
        if (message?.meta?.address && message.meta.address.transaction_id)
          router.push(`/shop/${message.meta.address.transaction_id}/track`);
      }}
    >
      {renderMediaContent()}
      {renderRatingStars()}
      {message?.meta?.type !== "ACCEPT_CALL" && renderMessageContent()}
      {renderAddress()}
      {renderEntityMessage()}
    </div>
  );
};

export default memo(MediaRendererSender);
