import Swal from "sweetalert2";

import { store } from "@/redux-store/store";

import API_ROUTE from "./routes";

type methods = "POST" | "GET" | "PUT" | "PATCH" | "DELETE";

interface API extends API_ROUTE {
  headers: {
    Authorization: string;
    "x-api-key": string;
  };
  fetch: (
    uri: string,
    method: methods,
    body: object | FormData | string,
    headers: object,
    skipAuth?: boolean 
  ) => Promise<Response | any>;
  get: (
    uri: string,
    headers?: object,
    skipAuth?: boolean 
  ) => Promise<Response | any>;
  post: (
    uri: string,
    body: object | FormData,
    headers?: object,
    skipAuth?: boolean 
  ) => Promise<Response | any>;
  put: (
    uri: string,
    body: object | FormData,
    headers?: object,
    skipAuth?: boolean 
  ) => Promise<Response | any>;
  delete: (
    uri: string,
    body?: object | FormData,
    headers?: object,
    skipAuth?: boolean 
  ) => Promise<Response | any>;
  patch: (
    uri: string,
    body: object | FormData,
    headers?: object,
    skipAuth?: boolean 
  ) => Promise<Response | any>;
  errorHandler: (loc: string, err: Error, alertIt?: boolean) => void;
}


Object.entries(API_ROUTE).forEach(([key, value]) => {
  (API_ROUTE as any)[key] = process.env.backend + value;
});

const API: API = {
  ...API_ROUTE,

  fetch(uri, method, body, headers, skipAuth = false) {
    if (typeof body === "object" && body.constructor.name !== "FormData") {
      body = JSON.stringify(body);
      headers = {
        ...headers,
        "Content-Type": "application/json",
      };
    }

    // Conditionally adding Authorization header
    const finalHeaders = {
      ...(skipAuth
        ? {}
        : { Authorization: `Bearer ${store.getState()?.user?.token}` }),
      "x-api-key": process.env.x_api_key,
      ...headers,
    };

    return new Promise((resolve, reject) =>
      fetch(uri, {
        method,
        headers: finalHeaders,
        ...(body ? { body: body as string } : {}),
      })
        .then((res) => res.json())
        .then((res) => {
          if (res.status === 200 || res.status === 201) {
            resolve(res);
          } else {
            reject(res);
          }
        })
        .catch(reject)
    );
  },
  get(uri, headers, skipAuth = false) {
    return this.fetch(uri, "GET", "", headers || {}, skipAuth);
  },
  post(uri, body, headers, skipAuth = false) {
    return this.fetch(uri, "POST", body, headers || {}, skipAuth);
  },
  put(uri, body, headers, skipAuth = false) {
    return this.fetch(uri, "PUT", body, headers || {}, skipAuth);
  },
  delete(uri, body, headers, skipAuth = false) {
    return this.fetch(uri, "DELETE", body || {}, headers || {}, skipAuth);
  },
  patch(uri, body, headers, skipAuth = false) {
    return this.fetch(uri, "PATCH", body, headers || {}, skipAuth);
  },
  get headers() {
    return {
      Authorization: `Bearer ${store.getState()?.user?.token}`,
      "x-api-key": process.env.x_api_key,
    };
  },
  errorHandler(loc, err, alertIt = true) {
    if (alertIt) {
      Swal.fire({
        title: err.message,
        confirmButtonColor: "#ac1991",
        timer: 2000,
      });
    }

    console.error({ err });
  },
};

export default API;
