import { memo, useEffect, useState } from "react";

type CountdownProps = {
  endDate: string;
  onOver?: () => void;
};

const DateCountdown: React.FC<CountdownProps> = ({ endDate, onOver }) => {
  const target = new Date(endDate);
  const [distance, setDistance] = useState("");

  useEffect(() => {
    const updateCountdown = () => {
      const now = new Date();
      const distance = target.getTime() - now.getTime();

      if (distance < 0) {
        setDistance("Time's up!");
        return;
      }

      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      const padZero = (num: number) => String(num).padStart(2, "0");

      let countdown = "";

      if (days > 0) {
        countdown += `${days}d `;
      }

      if (hours > 0 || days > 0) {
        countdown += `${padZero(hours)}h `;
      }

      if (minutes > 0 || hours > 0 || days > 0) {
        countdown += `${padZero(minutes)}m `;
      }

      countdown += `${padZero(seconds)}s`;

      setDistance(countdown);
    };

    updateCountdown();
    const intervalId = setInterval(updateCountdown, 1000);

    return () => clearInterval(intervalId);
  }, [target]);

  if (distance === "Time's up!" && onOver) {
    onOver();
    return;
  }

  return distance;
};

export const EndDateCountDown = memo(DateCountdown);
