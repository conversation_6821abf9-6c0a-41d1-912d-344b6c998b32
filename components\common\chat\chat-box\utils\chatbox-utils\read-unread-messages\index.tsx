import classNames from "classnames";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { memo, useEffect, useRef, useState } from "react";
import Swal from "sweetalert2";

import ActionButton from "@/components/common/action-button";
import DateFormatter from "@/components/common/date";
import { ModalPortal } from "@/components/portals/ModalPortal";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Chat, MessageInterface } from "@/redux-store/slices/chat.slice";
import type { HashObject } from "@/types/utils";
import { getAssetUrl } from "@/utils/assets";
import socketChannel, { getSortedMessages } from "@/utils/chatSocket";

import { datesAreEqual } from "../../helper/find-date";
import { ForwardMessageIcon } from "../../svg-utils";
import MessageBubble from "../message-bubble";

export const getFileExtension = (url: string): string => {
  const extension = url?.split(".").pop();
  return extension ? extension.toLowerCase() : "";
};

export const isImageAsset: HashObject = {
  png: true,
  jpg: true,
  jpeg: true,
  webp: true,
  avif: true,
};

export const isImageMedia = (media: any): boolean => {
  if (typeof media === "string") {
    return !!isImageAsset[getFileExtension(media)];
  } else if (Array.isArray(media)) {
    return isImageMedia(media?.[0]);
  } else if (typeof media === "object" && media !== null) {
    return (
      media.type === "image" || !!isImageAsset[getFileExtension(media?.url)]
    );
  }

  return false;
};

export const isAudioMedia = (media: any): boolean => {
  if (typeof media === "string") {
    return !!isImageAsset[getFileExtension(media)];
  } else if (Array.isArray(media)) {
    return isImageMedia(media?.[0]);
  } else if (typeof media === "object" && media !== null) {
    return (
      media.type === "audio" || !!isImageAsset[getFileExtension(media?.url)]
    );
  }

  return false;
};

const ReadUnreadMessagesWrapper = ({
  messageArray = [],
  usedAs = "current",
  videoConfRef,
  rateRef,
  setCallReqId,
  imageModalRef,
  setImageMessage,
  setShowBuyTicketModal,
  receiver,
  buyTicketModalRef,
  showBuyTicketModal,
  setReceiver,
  ratingReqref,
}: any) => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const userId = useAppSelector((state) => state.user.id);
  const channelId = useAppSelector((s) => s.chat.channelId);
  const currentMessages = useAppSelector((state) => state.chat.currentMessages);
  const prevMessages = useAppSelector((state) => state.chat.prevMessages);
  const chatList = useAppSelector((state) => state.chat.chatList);
  const targetUser = useAppSelector((state) => state.chat.targetUser);
  const [shareableMessage, setShareableMessage] = useState({
    message: "",
    meta: {},
  });
  const [completeMessages, setCompleteMessages] = useState<any[]>([]);

  useEffect(() => {
    setCompleteMessages([
      ...(prevMessages ? prevMessages : []),
      ...(currentMessages ? currentMessages : []),
    ]);
  }, [currentMessages, prevMessages]);

  const handleDeleteMessage = (message: MessageInterface) => {
    Swal.fire({
      text: "Are you sure you want to delete this message?",
      icon: "warning",
      showCancelButton: false,
      showDenyButton: true,
      confirmButtonColor: "#AC1991",
      confirmButtonText: "For me",
      denyButtonText: "For everyone",
      customClass: {
        confirmButton: "custom-btn",
        denyButton: "custom-btn",
      },
      showCloseButton: true,
    }).then((result) => {
      if (result.isDenied) {
        socketChannel.channel?.deleteMessageForEveryone({
          message_id: message._id || message?.messageId,
        });
      } else if (result.isConfirmed) {
        socketChannel.channel?.deleteMessageForMe({
          message_id: message._id || message?.messageId,
        });

        const prev_index = prevMessages.filter((mess: any) => {
          return mess._id !== message._id;
        });

        const curr_index = currentMessages.filter((mess: any) => {
          return mess.messageId !== message.messageId;
        });

        dispatch(chatActions.setPrevChat(prev_index));
        dispatch(chatActions.setCurrentChat(curr_index));

        if (messageArray[messageArray.length - 1] === message) {
          dispatch(
            chatActions.updateLastMessage({
              channelId,
              message: completeMessages[completeMessages.length - 2]?.message,
            })
          );
        }
      }
    });
  };

  const messageRef = useRef<any>(null);

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (
        messageRef &&
        messageRef.current &&
        !messageRef.current.contains(event.target)
      ) {
        setCompleteMessages((prevData: any) => {
          const newArr = JSON.parse(JSON.stringify(prevData));
          newArr.forEach((item: { showDelete: boolean }) => {
            item.showDelete = false;
          });
          return newArr;
        });
      }
    };

    document.body.addEventListener("click", handleClickOutside);

    return () => {
      document.body.removeEventListener("click", handleClickOutside);
    };
  }, []);

  const getMessageWidthClass = (message: MessageInterface) => {
    const isPromotion = message.message === "Promotion";
    const isWideScreen = window.innerWidth > 576;

    if (isWideScreen) {
      return [
        "message",
        "direct-message",
        "auto-message",
        "story-reply",
        "MASS-MESSAGE",
      ].includes(message?.meta?.type) || !message?.meta?.type
        ? "w-max-35"
        : isPromotion ||
          [
            "CUSTOM-SERVICE",
            "VOICE",
            "VIDEO",
            "RATING",
            "SENT-TIP",
            "EMBEDS",
            "SET-PRICE",
          ].includes(message?.meta?.type)
        ? "w-50"
        : "w-35";
    } else {
      return [
        "message",
        "direct-message",
        "auto-message",
        "story-reply",
        "MASS-MESSAGE",
      ].includes(message?.meta?.type)
        ? "w-max-60"
        : isPromotion ||
          [
            "CUSTOM-SERVICE",
            "VOICE",
            "VIDEO",
            "RATING",
            "SENT-TIP",
            "EMBEDS",
            "SET-PRICE",
          ].includes(message?.meta?.type)
        ? "w-80"
        : "w-65";
    }
  };

  const getBackgroundStyle = (message: MessageInterface) => {
    return {
      backgroundColor:
        message?.meta?.type === "SENT-TIP" ? "rgba(255, 246, 224, 1)" : "",
    };
  };

  const getMessageBgClass = (type: "receiver" | "sender") => {
    return type === "receiver" ? "bg-chat-receiver" : "bg-chat-sender";
  };

  const handleDeleteSenderMessage = (message: MessageInterface, i: number) => {
    if (
      [
        "VIDEO",
        "VOICE",
        "RATING",
        "ACCEPT_CALL",
        "CUSTOM-SERVICE",
        "SENT-TIP",
        "SET-PRICE",
      ].includes(message?.meta?.type)
    )
      return;

    if (
      message?.meta?.type === "message-attachment" &&
      message?.meta?.media_fee > 0
    )
      return;

    if (completeMessages[i]?.showDelete) {
      setCompleteMessages((prevData: any) => {
        const newArr = JSON.parse(JSON.stringify(prevData));
        newArr.forEach((item: { showDelete: boolean }) => {
          item.showDelete = false;
        });
        return newArr;
      });
      return;
    }

    setCompleteMessages((prevData: any) => {
      const newArr = JSON.parse(JSON.stringify(prevData));
      newArr.forEach((item: { showDelete: boolean }) => {
        item.showDelete = false;
      });
      newArr[i].showDelete = true;
      return newArr;
    });
  };

  const messageRefs = useRef<any>({});
  const timeoutRefs = useRef<any>({});

  const scrollToMessageId = (messageId: string) => {
    if (messageRefs.current[messageId]) {
      messageRefs.current[messageId].scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center",
      });

      messageRefs.current[messageId].classList.add("blinking-border");

      if (timeoutRefs.current[messageId]) {
        clearTimeout(timeoutRefs.current[messageId]);
      }

      timeoutRefs.current[messageId] = setTimeout(() => {
        messageRefs?.current?.[messageId]?.classList?.remove("blinking-border");
      }, 6000);
    }
  };

  const fetchCurrentChat = (message: MessageInterface) => {
    const chat = chatList.find(
      (chat: Chat) =>
        chat.target._id === targetUser || chat.initiator._id === targetUser
    );
    const isTargetSender =
      message?.sid === chat?.target?._id ||
      message?.sender_id === chat?.target?._id;

    const target = isTargetSender ? chat?.initiator : chat?.target;
    const user = isTargetSender ? chat?.target : chat?.initiator;
    return {
      target,
      user,
    };
  };

  useEffect(() => {
    dispatch(chatActions.setReplyMessage({}));
  }, [targetUser]);

  const ChatUnlockMessage = ({
    message,
    userId,
  }: {
    message: MessageInterface;
    userId: string;
  }) => {
    const isUnlockedByUser = message?.meta?.paid_by === userId;
    const displayName = message?.meta?.displayName;

    return (
      <div className="d-flex gap-2 align-items-center my-2">
        <hr
          className="color-medium w-100 flex-grow-1"
          style={{ height: "2px" }}
        />
        <div className="text-nowrap text-center color-medium">
          {isUnlockedByUser ? (
            <>You unlocked the chat.</>
          ) : (
            <>{displayName} has unlocked the chat.</>
          )}
        </div>
        <hr
          className="color-medium w-100 flex-grow-1"
          style={{ height: "2px" }}
        />
      </div>
    );
  };

  const BubbleOptions = ({
    message,
    i,
    isDeleteRequired = true,
  }: {
    message: MessageInterface;
    i: number;
    isDeleteRequired?: boolean;
  }) => {
    return (
      <div className="d-flex flex-column align-items-center gap-0">
        {isDeleteRequired && (
          <Image
            src="/images/common/delete.svg"
            alt=""
            width={13}
            height={14}
            className={`pointer justify-content-end mt-1 ${
              completeMessages[i]?.showDelete ? "" : "d-none"
            }`}
            style={{ filter: "brightness(0.25)" }}
            onClick={() => handleDeleteMessage(message)}
          />
        )}
        <div
          className={`pointer justify-content-end mt-1 ${
            completeMessages[i]?.showDelete ? "" : "d-none"
          }`}
          data-bs-toggle="modal"
          data-bs-target="#shareModal"
          onClick={() => {
            setShareableMessage({
              message: message?.message,
              meta: message?.meta,
            });
          }}
        >
          <ForwardMessageIcon />
        </div>
        <div
          className={`${completeMessages[i]?.showDelete ? "" : "d-none"}`}
          onClick={() => {
            dispatch(
              chatActions.setReplyMessage({
                ...message,
                display_name:
                  fetchCurrentChat(message)?.user?._id === userId
                    ? fetchCurrentChat(message)?.user?.display_name
                    : fetchCurrentChat(message)?.target?.display_name,
              })
            );
          }}
        >
          <Image
            src={"/images/chat/reply.svg"}
            className="pointer"
            alt="Reply"
            width="20"
            height="20"
          />
        </div>
      </div>
    );
  };

  const renderStoryMessage = ({
    message,
    usedAs = "receiver",
  }: {
    message: MessageInterface;
    usedAs?: "sender" | "receiver";
  }) => {
    const is_media_empty = !message?.meta?.story_data?.media?.length;
    const media = Array.isArray(message?.meta?.story_data?.media)
      ? message?.meta?.story_data?.media[0]
      : message?.meta?.story_data?.media;

    if (!message?.meta?.expiry_date) return null;

    const is_expired = new Date(message?.meta?.expiry_date) < new Date();

    return (
      <div
        className={classNames(
          "d-flex flex-column mt-2",
          usedAs === "sender" ? "align-items-end" : "align-items-start",
          usedAs === "sender" ? "text-end" : "text-start"
        )}
      >
        <span className="fs-9 color-medium">
          {usedAs === "receiver"
            ? "Replied to your story"
            : "You replied to their story"}
        </span>
        {is_expired ? (
          <div
            style={{
              width: "90px",
              height: "160px",
              backgroundColor: "rgba(39, 41, 50, 1)",
            }}
            className="rounded d-flex justify-content-center align-items-center p-1"
          >
            <span
              style={{
                color: "rgba(128, 131, 134, 1)",
              }}
              className="text-wrap text-center"
            >
              Story Expired
            </span>
          </div>
        ) : (
          <div
            onClick={() => {
              router.push(
                `/fresh?storyId=${message?.meta?.story_id}&userId=${
                  message?.meta?.author ||
                  (usedAs === "sender" ? targetUser : userId)
                }`
              );
            }}
            className="pointer"
          >
            {is_media_empty ? (
              <div
                style={{
                  width: "90px",
                  height: "160px",
                  backgroundColor: "#000",
                }}
                className="rounded d-flex justify-content-center align-items-center"
              >
                <div>
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={getAssetUrl({
                      media:
                        message?.meta?.story_data?.post_id?.media?.[0] ||
                        message?.meta?.story_data?.product?.media?.[0],
                      variation: "compressed",
                      defaultType: "background",
                    })}
                    alt=""
                    height={80}
                    style={{
                      aspectRatio: "1 / 1",
                    }}
                    className="object-fit-cover rounded border border-2"
                  />
                </div>
              </div>
            ) : media!.type === "video" ? (
              <video
                controls
                className="object-fit-cover rounded"
                height={160}
                style={{
                  objectFit: "cover",
                  aspectRatio: "9 / 16",
                }}
              >
                <source
                  src={getAssetUrl({
                    media,
                    variation: "compressed",
                    defaultType: "background",
                  })}
                  type="video/mp4"
                />
                Your browser does not support the video tag.
              </video>
            ) : (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                alt=""
                src={getAssetUrl({
                  media,
                  variation: "compressed",
                  defaultType: "background",
                })}
                className="object-fit-cover rounded"
                height={160}
                style={{
                  objectFit: "cover",
                  aspectRatio: "9 / 16",
                }}
              />
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div>
      {getSortedMessages(messageArray)?.map(
        (message: MessageInterface, i: number) => {
          if (!message?.sid && !message.sender_id) return;
          if (message?.meta?.delete_for === userId) return;

          if (message?.meta?.type === "chat-unlock") {
            return (
              <ChatUnlockMessage message={message} userId={userId} key={i} />
            );
          }

          return userId === message.sid || userId === message.sender_id ? (
            <>
              {usedAs === "previous" && i === 0 && (
                <div className="w-100 text-center color-medium">
                  <DateFormatter
                    dateString={message?.createdAt}
                    formatType="MM/dd/yy"
                    isChat
                  />
                </div>
              )}
              {usedAs === "previous" &&
                i >= 1 &&
                !datesAreEqual(
                  new Date(messageArray?.[i - 1]?.createdAt),
                  new Date(messageArray?.[i]?.createdAt)
                ) && (
                  <div className="w-100 text-center color-medium">
                    <DateFormatter
                      dateString={message?.createdAt}
                      formatType="MM/dd/yy"
                      isChat
                    />
                  </div>
                )}
              {message?.meta?.story_id &&
                renderStoryMessage({ message, usedAs: "sender" })}
              <div
                className="d-flex flex-row align-items-center gap-2 justify-content-end mt-1"
                onDoubleClick={(e) => {
                  if (e.target !== e.currentTarget) {
                    return;
                  }

                  dispatch(
                    chatActions.setReplyMessage({
                      ...message,
                      display_name:
                        fetchCurrentChat(message)?.user?._id === userId
                          ? fetchCurrentChat(message)?.user?.display_name
                          : fetchCurrentChat(message)?.target?.display_name,
                    })
                  );
                }}
              >
                <BubbleOptions i={i} message={message} />
                <MessageBubble
                  message={message}
                  messageRefs={messageRefs}
                  messageRef={messageRef}
                  type={"sender"}
                  getMessageWidthClass={getMessageWidthClass}
                  getMessageBgClass={getMessageBgClass}
                  getBackgroundStyle={getBackgroundStyle}
                  handleDeleteSenderMessage={handleDeleteSenderMessage}
                  index={i}
                  scrollToMessageId={scrollToMessageId}
                  videoConfRef={videoConfRef}
                  buyTicketModalRef={buyTicketModalRef}
                  ratingReqref={ratingReqref}
                  receiver={receiver}
                  setCallReqId={setCallReqId}
                  setReceiver={setReceiver}
                  setShowBuyTicketModal={setShowBuyTicketModal}
                  showBuyTicketModal={showBuyTicketModal}
                  rateRef={rateRef}
                  imageModalRef={imageModalRef}
                  setImageMessage={setImageMessage}
                />
              </div>
            </>
          ) : (
            <>
              {usedAs === "previous" && i === 0 && (
                <div className="w-100 text-center color-medium">
                  <DateFormatter
                    dateString={message?.createdAt}
                    formatType="MM/dd/yy"
                    isChat
                  />
                </div>
              )}
              {usedAs === "previous" &&
                i >= 1 &&
                !datesAreEqual(
                  new Date(messageArray?.[i - 1].createdAt),
                  new Date(messageArray?.[i].createdAt)
                ) && (
                  <div className="w-100 text-center color-medium">
                    <DateFormatter
                      dateString={message?.createdAt}
                      formatType="MM/dd/yy"
                      isChat
                    />
                  </div>
                )}
              {message?.meta?.story_id && renderStoryMessage({ message })}

              <div
                className="d-flex flex-row align-items-center justify-content-start h-100 mt-1"
                onDoubleClick={(e) => {
                  if (e.target !== e.currentTarget) {
                    return;
                  }

                  dispatch(
                    chatActions.setReplyMessage({
                      ...message,
                      display_name:
                        fetchCurrentChat(message)?.user?._id === userId
                          ? fetchCurrentChat(message)?.user?.display_name
                          : fetchCurrentChat(message)?.target?.display_name,
                    })
                  );
                }}
              >
                <MessageBubble
                  message={message}
                  messageRefs={messageRefs}
                  messageRef={messageRef}
                  type={"receiver"}
                  getMessageWidthClass={getMessageWidthClass}
                  getMessageBgClass={getMessageBgClass}
                  getBackgroundStyle={getBackgroundStyle}
                  handleDeleteSenderMessage={() => {}}
                  index={i}
                  scrollToMessageId={scrollToMessageId}
                  videoConfRef={videoConfRef}
                  buyTicketModalRef={buyTicketModalRef}
                  ratingReqref={ratingReqref}
                  receiver={receiver}
                  setCallReqId={setCallReqId}
                  setReceiver={setReceiver}
                  setShowBuyTicketModal={setShowBuyTicketModal}
                  showBuyTicketModal={showBuyTicketModal}
                  rateRef={rateRef}
                  imageModalRef={imageModalRef}
                  setImageMessage={setImageMessage}
                />
                <BubbleOptions
                  i={i}
                  message={message}
                  isDeleteRequired={false}
                />
              </div>
            </>
          );
        }
      )}
      <ShareModal message={shareableMessage} />
    </div>
  );
};

export default memo(ReadUnreadMessagesWrapper);

const ShareModal = ({
  message,
}: {
  message: { message: string; meta: Record<string, any> };
}) => {
  const chatList = useAppSelector((s) => s.chat.chatList);
  const userId = useAppSelector((s) => s.user.id);

  const [channelIdArray, setChannelIdArray] = useState<string[]>([]);

  const handleOnClick = (channelId: string) => {
    setChannelIdArray((prevArray) => {
      if (prevArray.includes(channelId)) {
        return prevArray.filter((id) => id !== channelId);
      } else {
        return [channelId, ...prevArray];
      }
    });
  };

  const handleShare = async () => {
    await socketChannel.forwardMessage(channelIdArray, {
      message: message?.message,
      meta: { ...message?.meta, forward: true },
    });

    Swal.fire({
      title: "Message shared successfully",
      icon: "success",
      confirmButtonText: "Okay",
      timer: 1500,
    });

    if (document) {
      document.getElementById("shareModalCloseButton")?.click();
    }
  };

  return (
    <ModalPortal>
      <div
        className="modal fade"
        id="shareModal"
        tabIndex={-1}
        aria-labelledby="shareModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered modal-dialog-scrollable">
          <div className="modal-content">
            <div className="modal-header">
              <h1 className="modal-title fs-5" id="shareModalLabel">
                Share Message
              </h1>
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                id="shareModalCloseButton"
              ></button>
            </div>
            <div className="modal-body">
              {chatList.map((chat, index) => {
                const chatPartner =
                  userId === chat?.target?._id ? chat?.initiator : chat?.target;
                return (
                  <div
                    key={chatPartner?._id}
                    className={classNames("p-2 pointer", {
                      "border-bottom": index !== chatList.length - 1,
                    })}
                    style={{
                      backgroundColor: channelIdArray.includes(
                        chat?.converse_channel_id
                      )
                        ? "rgb(249, 244, 248)"
                        : "",
                    }}
                    onClick={() => handleOnClick(chat.converse_channel_id)}
                  >
                    <div className="d-flex align-items-center h-100 w-100">
                      <div className="d-flex align-items-center me-2">
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          className="rounded-circle"
                          src={getAssetUrl({
                            media: chatPartner?.avatar?.[0],
                            defaultType: "avatar",
                            variation: "thumb",
                          })}
                          alt={chatPartner?.display_name}
                          width="40"
                          height="40"
                        />
                      </div>
                      <div className="flex-grow-1">
                        <p>{chatPartner?.display_name}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="modal-footer">
              <span data-bs-dismiss="modal">
                <ActionButton btnType="button" onClick={handleShare}>
                  Share
                </ActionButton>
              </span>
            </div>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};
