import classNames from "classnames";
import { memo, type FC, type ReactNode } from "react";

import Style from "./style.module.scss";

export type ActionButtonVariant =
  | "primary"
  | "dark"
  | "info"
  | "warning"
  | "danger"
  | "success"
  | "white"
  | "prime";

interface ButtonProps {
  children: ReactNode;
  variant?: ActionButtonVariant;
  type?: "solid" | "outline";
  loading?: boolean;
  className?: string;
  disabled?: boolean;
  btnType?: "submit" | "reset" | "button";
  styles?: React.CSSProperties;
  onClick?: (value?: any) => void;
}

const ActionButton: FC<ButtonProps> = ({
  children,
  variant = "primary",
  type = "solid",
  loading,
  className,
  disabled,
  btnType,
  onClick,
  styles,
}) => {
  return (
    <button
      onClick={onClick}
      style={styles}
      disabled={disabled || loading}
      className={classNames(
        "position-relative",
        Style.btn,
        Style[`btn-knky-${variant}`],
        {
          [Style.outline]: type === "outline",
        },
        className
      )}
      type={btnType}
    >
      {loading ? (
        <svg
          className={Style.container}
          x="0px"
          y="0px"
          viewBox="0 0 37 37"
          height="37"
          width="37"
          preserveAspectRatio="xMidYMid meet"
        >
          <path
            className={Style.track}
            fill="none"
            strokeWidth="5"
            pathLength="100"
            d="M0.37 18.5 C0.37 5.772 5.772 0.37 18.5 0.37 S36.63 5.772 36.63 18.5 S31.228 36.63 18.5 36.63 S0.37 31.228 0.37 18.5"
          ></path>
          <path
            className={Style.car}
            fill="none"
            strokeWidth="5"
            pathLength="100"
            d="M0.37 18.5 C0.37 5.772 5.772 0.37 18.5 0.37 S36.63 5.772 36.63 18.5 S31.228 36.63 18.5 36.63 S0.37 31.228 0.37 18.5"
          ></path>
        </svg>
      ) : (
        children
      )}
    </button>
  );
};

export default memo(ActionButton);
