import TextBox from "@/components/common/textbox";
import { useState } from "react";

export default function AboutGroup() {
  const [charCount, setCharCount] = useState(0);
  return (
    <div className="bg-body container d-flex flex-column p-3 rounded-3">
      <div className="d-flex justify-content-between">
        <p className="fs-5 fw-medium">About channel</p>
        <p className="group-about-count">{`${charCount}/400`}</p>
      </div>

      <div className="group-about-wrapper px-3">
        <TextBox
          className="bg-cream border-0 shadow-none rounded-3 px-2 pb-3 pt-1 fs-6 color-dark"
          characters={(num: number) => setCharCount(num)}
        />
      </div>
    </div>
  );
}
