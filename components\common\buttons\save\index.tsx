import { useState } from "react";
import "./index.scss";

import { UserSavePost, UserUnSavePost } from "@/api/post";
import { ModalService } from "@/components/modals";
import { useAppSelector } from "@/redux-store/hooks";

import { PostActionBtnClass } from "../../class";

interface PostAction {
  postId: string;
  cls?: string;
  saveDetails?: boolean;
  from?: string;
}

interface PostSaveBtnState {
  cls: string;
  img: string;
  title: string;
}

const View: {
  [key: string]: PostSaveBtnState;
} = {
  inactive: {
    cls: "",
    img: "new-save",
    title: "Save",
  },
  active: {
    cls: "active",
    img: "new-fill-save",
    title: "Saved",
  },
};

const PostActionSave = ({
  postId,
  cls,
  saveDetails,
  from = "post",
}: PostAction) => {
  const [isSaved, setIsSaved] = useState(saveDetails || false);
  const state = View[isSaved ? "active" : "inactive"];

  const role = useAppSelector((state) => state.user.role);

  const onClick = () => {
    if (isSaved) {
      setIsSaved(false);

      UserUnSavePost(postId)
        .then(() => {
          console.info("success");
        })
        .catch(console.error);
    } else {
      setIsSaved(true);

      UserSavePost(postId)
        .then(() => console.info("success"))
        .catch(console.error);
    }
  };

  return (
    <>
      <div
        onClick={
          role === "guest" ? () => ModalService.open("SIGN_IN") : onClick
        }
        className={`post-action-save ${PostActionBtnClass} ${cls} ${state.cls}`}
      >
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          width={32}
          height={32}
          className=""
          alt=""
          src={`/images/post/${state.img}.svg`}
          style={{
            filter: from === "reel" && !isSaved ? "brightness(5)" : "",
          }}
        />
        {from !== "reel" && (
          <>
            <div
              className={`d-md-block d-sm-none d-none ${state.cls}`}
              style={{ width: "3rem" }}
            >
              {state.title}
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default PostActionSave;
