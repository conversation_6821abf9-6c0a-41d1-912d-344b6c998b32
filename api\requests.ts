import { type PageData } from "@/types/notification";

import API from ".";

export interface RecieveCallAndRating {
  message: string;
  data: CallRatingData[];
}

export interface CallRatingData {
  _id: string;
  requestee: {
    _id: string;
    f_name: string;
    l_name: string;
    username: string;
  };
  status: string;
  extra_data: {
    duration: number;
  };
  token: string;
  // refreshToken:string;
  media: [];
}

export const CallAndRating = async (user_type: string) => {
  return API.get(`${API.REQUESTS}/calls-and-ratings?user_type=${user_type}`);
};

// notification API

export interface getNotificationData {
  page_data: PageData;
  data: any;
}

interface GetNotificationInterface {
  limit: number;
  type?: string;
  seen?: string;
  timestamp?: string;
  direction?: "backward" | "forward";
}

export const GetNotification = async (props: GetNotificationInterface) => {
  const query = new URLSearchParams();

  query.set("d", props.direction || "backward");
  props.timestamp && query.set("t", props.timestamp);

  query.set("limit", String(props.limit));
  props.type && query.set("type", props.type);
  props.seen && query.set("seen", props.seen);

  return API.get(
    `${API.NOTIFICATION}?${query.toString()}`
  ) as Promise<getNotificationData>;
};

export const GetNotificationCounts = async () =>
  API.get(`${API.NOTIFICATION}/read-unread-counts`) as Promise<any>;

export const SeenNotification = async (body: { notification_ids: string[] }) =>
  API.put(
    `${API.NOTIFICATION}/acknowledge-notifications`,
    body
  ) as Promise<any>;

export const RequestNotification = async (postId: any, action: any) =>
  API.patch(
    `${API.POST}/${postId}/collab-request/${action}`,
    {}
  ) as Promise<getNotificationData>;

interface LedgerBodyInterface {
  fromDate: string;
  toDate: string;
  timezone: string;
}

export const RequestLedger = async (ledgerBody: LedgerBodyInterface) =>
  API.post(`${API.REQUESTS}/transaction-ledger`, ledgerBody) as Promise<any>;

interface ServiceRequestInterface {
  is_promotion: boolean;
  target_user?: string;
  service_id: string;
  proposed_amount?: number;
  duration?: number;
  service_type?: "per_minute" | "fixed_duration";
  unit?: number;
  unit_type?: string;
  media?: FileList | File[];
  extra_data?: any;
  proposed_duration?: number;
  request_note?: string;
  offered_amount?: number;
}

export const RequestService = async (body: ServiceRequestInterface) => {
  const formData = new FormData();

  for (const key in body) {
    const value = body[key as keyof ServiceRequestInterface];

    if (value !== undefined && value !== null) {
      if (key === "media") {
        const mediaFiles = Array.isArray(value) ? value : Array.from(value);
        mediaFiles.forEach((file) => {
          formData.append("media", file);
        });
      } else if (key === "extra_data") {
        formData.append(key, value as any);
      } else {
        formData.append(key, String(value));
      }
    }
  }

  return API.post(`${API.REQUESTS}/calls-and-ratings`, formData) as Promise<{
    status: number;
    data: any;
  }>;
};

export const CounterOfferAction = async (
  counter_offer_accepted: boolean,
  request_id: string,
  payment_data?: { payment_type: string; payment_id: string }
) => {
  const payload: any = {
    counter_status: counter_offer_accepted ? "accepted" : "rejected",
  };

  if (payment_data?.payment_type === "card") {
    payload.tokenised_card_id = payment_data.payment_id;
    payload.payment_mode = payment_data.payment_type;
  }

  return API.post(
    `${API.REQUESTS}/respond-to-counter-offer/${request_id}`,
    payload
  ) as Promise<any>;
};
