import styles from "./PlaybackDesktop.module.scss";

import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import {
  LiveKitRoom,
  RoomAudioRenderer,
  useTracks,
  VideoTrack,
} from "@livekit/components-react";
import { Track } from "livekit-client";
import Swal from "sweetalert2";
import classNames from "classnames";

import { EventBridge } from "@/app/event-bridge";
import type { Subscription } from "@/types/event-bridge";
import {
  EVENT_ERROR_MSGS,
  GetEvent,
  GetEventLatestTips,
  GetEventStreamToken,
} from "@/api/event";
import type { EventGetResponse } from "@/api/event";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { chatActions, liveEventActions } from "@/redux-store/actions";
import LiveStreamChat from "@/components/common/chat/stream-chat";
import { TipHighlight, TipHistory } from "@/components/common/event/tips";
import { FlyingReactions } from "@/components/common/event/reactions";
import { StreamStatus } from "@/components/common/event/stream-status";
import { ViewerCount } from "@/components/common/event/viewer-count";
import { sleep } from "@/utils/sleep";
import { useNavigateBack } from "@/hooks/useNavigateBack";

import { PlaybackControlsAndFallbackImg } from "./PlaybackControlsAndFallbackImg";
import { showErrorResponse } from "./playback-errors";

export function PlaybackDesktop(props: { eventId: string }) {
  const router = useRouter();
  const navigateBack = useNavigateBack();
  const dispatch = useAppDispatch();
  const myProfile = useAppSelector((state) => state.user.profile);
  const expanded = useAppSelector((state) => state.liveEvent.expanded);
  const muted = useAppSelector((state) => state.liveEvent.muted);

  const [lkAuthToken, setLkAuthToken] = useState<string>();
  const [event, setEvent] = useState<EventGetResponse["data"]>();
  const [isEventEnded, setIsEventEnded] = useState(false);

  const subRefs = useRef<Subscription[]>([]);

  useEffect(() => {
    (async () => {
      const _event = (await GetEvent(props.eventId)).data;

      if (_event.author === myProfile._id) {
        // can't watch your own event
        router.push("/fresh");
        return;
      }

      const _token = (await GetEventStreamToken(props.eventId)).data.token;
      const _tips = (await GetEventLatestTips(props.eventId, 1)).data;

      if (!EventBridge.isConnected) await sleep(3000);
      const r = await EventBridge.request("LiveStream/AlreadyViewing", {
        event_id: props.eventId,
      });
      if (r.alreadyViewing) throw new Error(EVENT_ERROR_MSGS.ALREADY_VIEWING);

      setEvent(_event);
      setLkAuthToken(_token);
      dispatch(liveEventActions.setLiveEvent(_event));
      dispatch(liveEventActions.addTips({ tips: _tips, trim: 5 }));

      const s1 = EventBridge.join("LiveStream/" + props.eventId);
      const s2 = EventBridge.on("LiveStream/Tip", (data) => {
        dispatch(liveEventActions.addTips({ tips: data.tip, trim: 5 }));
      });
      const s3 = EventBridge.on("LiveStream/End", (_data) => {
        setIsEventEnded(true);
        Swal.fire({
          icon: "info",
          title: "Event Ended!",
          text: "This event has ended. Thank you for watching!",
        }).then(() => {
          dispatch(chatActions.clearStreamChat());
          dispatch(liveEventActions.resetLiveEvent());
          navigateBack();
        });
      });
      const s4 = EventBridge.on("LiveStream/KickedOut", (_data) => {
        setIsEventEnded(true);
        Swal.fire({
          icon: "error",
          title: "Kicked Out!",
          text: "You have been kicked out of this event.",
          timer: 3000,
          timerProgressBar: true,
        }).then(() => {
          dispatch(chatActions.clearStreamChat());
          dispatch(liveEventActions.resetLiveEvent());
          navigateBack();
        });
      });
      subRefs.current.push(s1, s2, s3, s4);
    })().catch((e) => showErrorResponse(e).then(() => navigateBack()));

    return () => {
      subRefs.current.forEach((s) => s.unsubscribe());
    };
  }, []);

  return (
    <>
      <LiveKitRoom
        serverUrl={process.env.livekit}
        token={lkAuthToken}
        connect={true}
        className={classNames(
          expanded ? "container-fluid" : "container-xxl",
          "p-0 p-md-3 overflow-hidden",
          styles["main-container"]
        )}
      >
        <div
          className={classNames("row g-lg-3", {
            "justify-content-center": expanded,
          })}
        >
          <div
            className={classNames(
              "col-lg-7 d-flex flex-column gap-2 gap-md-3 mb-2 mb-md-3",
              { [styles["playback-container"]]: expanded }
            )}
          >
            <LiveVideoPlayback ended={isEventEnded} />
            <TipHistory />
          </div>
          <div
            className={classNames("col-lg-5 d-flex flex-column gap-2 gap-md-3")}
            style={{ maxWidth: "600px" }}
          >
            {event?.event_converse_channel_id && (
              <LiveStreamChat postId={event.post} />
            )}
          </div>
        </div>
        <RoomAudioRenderer volume={muted ? 0 : 1} />
      </LiveKitRoom>
      <FlyingReactions />
    </>
  );
}

function LiveVideoPlayback(props: { ended: boolean }) {
  const cameraTrackRef = useTracks([Track.Source.Camera], {
    onlySubscribed: true,
  })[0];
  const isVideoOff = cameraTrackRef?.publication.track?.isMuted;

  const micTrackRef = useTracks([Track.Source.Microphone], {
    onlySubscribed: true,
  })[0];
  const isMicOff = micTrackRef?.publication.track?.isMuted;

  return (
    <div
      className="ratio ratio-16x9 rounded-md-3 overflow-hidden"
      id="stream-video-container"
    >
      <div className="bg-black text-white">
        <div className="position-absolute top-0 start-0 z-1 d-flex gap-3 p-3 user-select-none">
          <StreamStatus
            status={props.ended ? "ENDED" : cameraTrackRef ? "LIVE" : "WAITING"}
          />
          {!props.ended && <ViewerCount />}
        </div>

        {!props.ended && (
          <PlaybackControlsAndFallbackImg
            videoFallback={isVideoOff}
            audioFallback={isMicOff}
            isMobile={false}
          />
        )}

        {cameraTrackRef && (
          <VideoTrack
            id="stream-video"
            trackRef={cameraTrackRef}
            className={classNames("w-100 h-100 flip-x", {
              [styles["obscure"]]: props.ended,
            })}
            disablePictureInPicture
          />
        )}

        <TipHighlight />
      </div>
    </div>
  );
}
