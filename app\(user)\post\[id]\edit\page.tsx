"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import type { GetPost } from "@/api/post";
import { GetSinglePost } from "@/api/post";
import CreatePost from "@/app/(user)/(sections)/create/new-post/[[...id]]/page";
import { userDataActions } from "@/redux-store/actions";
import { useAppDispatch } from "@/redux-store/hooks";
import type { Media } from "@/types/media";
import type { Params } from "@/types/utils";

export default function EditPost({ params }: Params) {
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  const [media, setMedia] = useState<Media[]>([]);
  useEffect(() => {
    GetSinglePost(params.id)
      .then((res) => {
        const post: GetPost = res.data[0];
        dispatchAction(userDataActions.setPostCaption(post?.caption));

        setMedia(post?.media || []);

        if (post?.initial_visibility) {
          dispatchAction(
            userDataActions.setInitialVisibility(post?.initial_visibility)
          );
        }

        if (post.scheduled_on) {
          dispatchAction(
            userDataActions.setScheduleDateTime(post.scheduled_on)
          );
        }

        if (post.expired_on) {
          dispatchAction(userDataActions.setExpireDateTime(post.expired_on));
        }
      })
      .catch(() => {
        router.push("/");
      });

    return () => {
      dispatchAction(userDataActions.setExpireDateTime(""));
      dispatchAction(userDataActions.setScheduleDateTime(""));
    };
  }, []);

  return CreatePost({ isEdit: true, postId: params.id, media: media });
}
