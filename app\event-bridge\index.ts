import { io } from "socket.io-client";
import type { Socket } from "socket.io-client";

import type {
  SocketRecvEvent,
  SocketSendEvent,
  Subscription,
} from "@/types/event-bridge";

class EventBridgeClass {
  socket: Socket = io(process.env.eventbridge, {
    transports: ["websocket"],
    path: "/eventbridge",
    autoConnect: false,
  });
  joinedRooms = new Set<string>();

  get isConnected() {
    return this.socket?.connected || false;
  }

  connect(token: string) {
    this.socket.auth = { token };
    this.socket.connect();

    this.socket.on("connect", () => {
      // console.log("Connected to EventB<PERSON>");
      // rejoin rooms if any
      this.joinedRooms.forEach((room) => this.socket?.emit("join", room));
    });

    this.socket.on("disconnect", () => {
      // console.log("Disconnected from EventBridge");
    });
  }

  disconnect() {
    this.socket?.disconnect();
  }

  on<T extends SocketRecvEvent["event"]>(
    event: T,
    cb: (data: Extract<SocketRecvEvent, { event: T }>["data"]) => void
  ): Subscription {
    // @ts-expect-error to be handled later
    this.socket?.on(event, cb);
    return {
      unsubscribe: () => {
        // @ts-expect-error to be handled later
        this.socket?.off(event, cb);
      },
    };
  }

  join(target: string): Subscription {
    if (!this.socket || !this.socket.connected)
      throw new Error("Socket not connected!");

    this.socket?.emit("join", target);
    this.joinedRooms.add(target);
    return {
      unsubscribe: () => {
        this.socket?.emit("leave", target);
        this.joinedRooms.delete(target);
      },
    };
  }

  send<T extends SocketSendEvent["event"]>(
    event: T,
    data: Extract<SocketSendEvent, { event: T }>["data"],
    volatile = false
  ) {
    if (!this.socket || !this.socket.connected)
      throw new Error("Socket not connected!");

    volatile
      ? this.socket?.volatile.emit(event, data)
      : this.socket?.emit(event, data);
  }

  request<T extends SocketSendEvent["event"]>(
    event: T,
    data: Extract<SocketSendEvent, { event: T }>["data"]
  ): Promise<Extract<SocketRecvEvent, { event: T }>["data"]> {
    if (!this.socket || !this.socket.connected)
      throw new Error("Socket not connected!");

    return this.socket?.emitWithAck(event, data);
  }
}

export const EventBridge = new EventBridgeClass();
