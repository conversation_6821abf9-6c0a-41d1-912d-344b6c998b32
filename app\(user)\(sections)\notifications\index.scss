:root {
  --bg-unseen-light: #f9f4f8;
  --bg-seen-light: transparent;
  --bg-unseen-dark: #2a1f25;
  --bg-seen-dark: transparent;
}

[data-bs-theme="dark"] {
  --bg-unseen-light: var(--bg-unseen-dark);
  --bg-seen-light: var(--bg-seen-dark);
}

.notification-item .active {
  color: #ac1191 !important;
  border-bottom: 1px solid #ac1191;
}

.noNotification {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

@media screen and (max-width: 767px) {
  .notification-body {
    margin-bottom: 3rem !important;
  }
  .notification-hr-line {
    border-bottom: 1px solid rgba(0, 0, 0, 0.25);
  }
  .vfile-name {
    max-width: 13rem;
    display: block;
  }
}
