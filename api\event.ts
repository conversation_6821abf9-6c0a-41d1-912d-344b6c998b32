import API from ".";

import type { PostEvent } from "@/types/post";
import FileHandler from "@/components/common/file/handler";
import type {
  Event,
  EventPaginationData,
  EventTip,
  EventTipFrom,
} from "@/types/event";

export const enum EVENT_ERROR_MSGS {
  ALREADY_ENDED = "Event has already ended",
  ACCESS_DENIED = "Access denied",
  ALREADY_VIEWING = "Already viewing",
  KICKED_OUT = "You have been kicked out from this event",
}

interface Response {
  message: string;
  status: number;
}

interface EventSubmitResponse {
  data: "success";
}

export function EventSubmit(body: PostEvent) {
  const form = new FormData();
  form.append("event_type", body.type);
  body.name && form.append("name", body.name);
  body.description && form.append("description", body.description);
  form.append(
    "scheduled_on",
    new Date(`${body.date}T${body.time}`).toISOString()
  );
  form.append(
    "duration",
    (body.duration.value * (body.duration.type === "min" ? 1 : 60)).toString()
  );
  form.append("ticket_type", body.ticket.type);
  form.append("price", body.ticket.price.toString());
  form.append("quantity_type", body.ticket.release.type);
  body.ticket.release.type === "Limited" &&
    form.append("quantity", body.ticket.release.amount.toString());
  form.append("visibility", body.visibility);
  form.append("is_live_stream", body.is_live_stream.toString());

  if (body.media) {
    const media = FileHandler.get(body.media);
    media && form.append("media", media);
  }

  return API.post(API.EVENT, form) as Promise<EventSubmitResponse>;
}

export function EventUpdate(
  eventId: string,
  body: Partial<Omit<PostEvent, "type" | "is_live_stream">>
) {
  const form = new FormData();
  body.name && form.append("name", body.name);
  body.description && form.append("description", body.description);
  body.date &&
    body.time &&
    form.append(
      "scheduled_on",
      new Date(`${body.date}T${body.time}`).toISOString()
    );
  body.duration &&
    form.append(
      "duration",
      (body.duration.value * (body.duration.type === "min" ? 1 : 60)).toString()
    );

  if (body.ticket) {
    form.append("ticket_type", body.ticket.type);
    form.append("price", body.ticket.price.toString());
    form.append("quantity_type", body.ticket.release.type);
    body.ticket.release.type === "Limited" &&
      form.append("quantity", body.ticket.release.amount.toString());
  }

  body.visibility && form.append("visibility", body.visibility);

  if (body.media) {
    const media = FileHandler.get(body.media);
    media && form.append("media", media);
  }

  return API.patch(`${API.EVENT}/${eventId}`, form);
}

export function EventDelete(eventId: string) {
  return API.delete(`${API.EVENT}/${eventId}`);
}

export interface EventGetResponse extends Response {
  data: Event;
}

export function GetEvent(eventId: string) {
  return API.get(`${API.EVENT}/${eventId}`) as Promise<EventGetResponse>;
}

interface EventGetStreamTokenResponse extends Response {
  data: { token: string };
}

export function GetEventStreamToken(eventId: string) {
  return API.get(
    `${API.EVENT}/${eventId}/get-streaming-token`
  ) as Promise<EventGetStreamTokenResponse>;
}

interface UpdateEventBody {
  has_ended: boolean;
}

export function UpdateEvent(
  eventId: string,
  data: Partial<PostEvent> & UpdateEventBody
) {
  const form = new FormData();
  if (data.has_ended) form.append("has_ended", data.has_ended.toString());
  return API.patch(`${API.EVENT}/${eventId}`, form);
}

export interface SeatReservationBody {
  tokenised_card_id?: string;
  payment_mode: string;
}

export function SeatReservation(eventId: string, body: SeatReservationBody) {
  return API.post(`${API.EVENT}/${eventId}/seat-reservation`, body);
}

interface EventTipsResponse extends Response {
  data: EventTip[];
  page_data: EventPaginationData;
}

export function GetEventLatestTips(eventId: string, page: number) {
  return API.get(
    `${API.HANDLER}/event-transactions?event_id=${eventId}&page=${page}&limit=10`
  ) as Promise<EventTipsResponse>;
}

export function GetEventTips(params: {
  eventId: string;
  skip?: number;
  limit?: number;
}) {
  const query = new URLSearchParams();
  query.set("event_id", params.eventId);
  query.set("category", "Tip");
  params.skip && query.set("skip", String(params.skip));
  params.limit && query.set("limit", String(params.limit));

  return API.get(
    `${API.HANDLER}/transactions?${query.toString()}`
  ) as Promise<EventTipsResponse>;
}

interface EventTipsByUserResponse extends Response {
  data: {
    _id: string;
    tip_count: number;
    total_tip: number;
    from: EventTipFrom;
  }[];
  page_data: EventPaginationData;
}

export function GetEventTipsByUser(params: {
  eventId: string;
  skip?: number;
  limit?: number;
}) {
  const query = new URLSearchParams();
  query.set("category", "Tip");
  params.skip && query.set("skip", String(params.skip));
  params.limit && query.set("limit", String(params.limit));

  return API.get(
    `${API.EVENT}/${params.eventId}/tip-senders?${query.toString()}`
  ) as Promise<EventTipsByUserResponse>;
}

interface EventTotalTipsEarnedResponse extends Response {
  data: { earning: number; earning_excludiing_platform_fee: number };
}

export function GetTotalTipsEarned(eventId: string) {
  return API.get(
    `${API.EVENT}/${eventId}/total-event-tip`
  ) as Promise<EventTotalTipsEarnedResponse>;
}

export interface TicketResponse {
  _id: string;
  buyer: Record<string, any>;
  event: string;
  created_at?: string;
  updated_at?: string;
}

interface EventsTotalTicketsSoldResponse extends Response {
  data: TicketResponse[];
  page_data: EventPaginationData;
}

export function GetTicketsSold(eventId: string, page: number) {
  return API.get(
    `${API.EVENT}/${eventId}/reservations?page=${page}&limit=10`
  ) as Promise<EventsTotalTicketsSoldResponse>;
}

export interface EventAnalyticsResponseResult {
  total_amount: number;
  count: number;
  utcTimestamp: string;
  hour?: number;
  day?: number;
}
interface EventAnalyticsResponse extends Response {
  data: { result: EventAnalyticsResponseResult[] };
}

export function GetEventAnalytics({
  eventId,
  groupBy,
  type,
  start_time,
  end_time,
}: {
  eventId: string;
  groupBy: "day" | "hour";
  type: "tips" | "sold_tickets";
  start_time?: Date | string;
  end_time?: Date | string;
}) {
  const query = new URLSearchParams();
  query.set("group_by", groupBy);
  query.set("type", type);
  start_time && query.set("start_time", start_time.toString());
  end_time && query.set("end_time", end_time.toString());

  return API.get(
    `${API.EVENT}/${eventId}/analytics?${query.toString()}`
  ) as Promise<EventAnalyticsResponse>;
}

export function KickOutUser(eventId: string, userId: string) {
  return API.post(`${API.EVENT}/${eventId}/kickout/${userId}/remove/true`, {});
}
