import React, { useState } from "react";

type TextColorInterface = "black" | "white" | "gray";

// Changed the logics in this file to enable this feature in case if hashtags included in text (Hashtags are jsx elements)
const ReadMoreContent = ({
  text = [],
  characterCount = 100,
  textColor = "black",
  btnColor = "black",
  fontWeight,
  classes = "",
  btnClasses = "",
  isBtnInSameLine = false,
  textShadow,
}: {
  text: string | (string | JSX.Element)[];
  characterCount?: number;
  textColor?: TextColorInterface;
  btnColor?: string;
  fontWeight?: string;
  classes?: string;
  btnClasses?: string;
  isBtnInSameLine?: boolean;
  textShadow?: boolean;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const textArray = Array.isArray(text) ? text : [text];

  const totalLength = textArray.reduce(
    (acc, item) => (typeof item === "string" ? acc + item.length : acc),
    0
  );

  // Calculating truncated content for display
  const truncatedContent = [];
  let currentLength = 0;

  for (const item of textArray) {
    if (typeof item === "string") {
      if (currentLength + item.length > characterCount) {
        truncatedContent.push(item.slice(0, characterCount - currentLength));
        break;
      } else {
        truncatedContent.push(item);
        currentLength += item.length;
      }
    } else {
      // For JSX elements, add them directly if within the limit
      truncatedContent.push(item);
    }
  }

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const renderText = (content: (string | JSX.Element)[]) => {
    const para: any = [];

    for (const data of content) {
      if (typeof data === "string") {
        const strArr = data.split("\n");
        strArr.forEach((element, index) => {
          para.push(<span key={`${para.length}-text`}>{element}</span>);
          para.push(<br key={`${para.length}-br`} />);
        });
        para.pop(); // Remove the last <br /> added by the forEach loop
      } else {
        // Ensure data is a valid React element
        if (React.isValidElement(data)) {
          para.push(React.cloneElement(data, { key: `${para.length}-jsx` }));
        }
      }
    }

    return <div className={classes}>{para}</div>;
  };

  return (
    <span
      className={`${
        textColor === "white"
          ? "text-white text-[16px] "
          : textColor === "gray"
          ? "text-muted"
          : ""
      } ${isBtnInSameLine && !isExpanded ? "d-flex" : ""} text-start`}
      style={{
        wordBreak: "break-word",
        ...(textColor === "white" && textShadow
          ? {
              textShadow:
                "1px 0px 10px rgba(0, 0, 0, 0.5), 1px 2px 5px rgba(0, 0, 0, 0.7)",
            }
          : {}),
      }}
    >
      <span className={fontWeight}>
        {isExpanded ? renderText(textArray) : renderText(truncatedContent)}
      </span>
      {totalLength > characterCount && (
        <span
          onClick={(e) => {
            e.stopPropagation();
            toggleExpand();
          }}
          className={`fw-bold ${btnClasses}`}
          style={{
            cursor: "pointer",
            color: btnColor,
            ...(textShadow
              ? { textShadow: "1px 1px 3px rgba(0, 0, 0, 0.5)" }
              : {}),
          }}
        >
          {isExpanded ? (
            <span>&nbsp;&nbsp;read less</span>
          ) : (
            <span>... read more</span>
          )}
        </span>
      )}
    </span>
  );
};

export default ReadMoreContent;
