"use client";

import "./index.scss";

import Image from "next/image";
import { useRouter } from "next/navigation";
import * as Yup from "yup";
import React, { useEffect } from "react";
import { Formik } from "formik";

import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import { createActions } from "@/redux-store/actions";
import { EventSubmit } from "@/api/event";
import { PostVisibility } from "@/global/constants";
import { formatDateForLiveEvent } from "@/utils/number";
import { getAssetUrl } from "@/utils/assets";
import Form from "@/components/common/form";
import Popup from "@/components/common/header/popup";
import Wrapper, { Divider } from "@/components/common/wrapper";
import Select from "@/components/common/select";
import PostAuthor from "@/components/common/post/author";
import FileInput from "@/components/common/file";
import FileHandler from "@/components/common/file/handler";
import Swal from "sweetalert2";

export default function EventPreview() {
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  const initialValues = useAppSelector((state) => state.create.event);
  const user = useAppSelector((state) => state.user);

  const validationSchema = Yup.object().shape({
    visibility: Yup.string().required("Event Visibility is required"),
  });

  const isTimeDiffValid = (
    date = initialValues.date,
    time = initialValues.time
  ) => {
    const filledDate = new Date(`${date}T${time}`);
    return filledDate.getTime() - Date.now() >= 5 * 60 * 1000;
  };

  useEffect(() => {
    const _filledDate = new Date(`${initialValues.date}T${initialValues.time}`);

    if (!FileHandler.isValidFiles(initialValues.media) || !isTimeDiffValid()) {
      router.push(initialValues.from);
    }
  }, []);

  const onSubmit = (data: typeof initialValues) => {
    if (!isTimeDiffValid(data.date, data.time)) {
      Popup.error(
        new Error(
          "Selected time should be atleast 5 min greater than current time"
        )
      ).then((r) => {
        if (r.isConfirmed) router.push(initialValues.from);
      });
      return;
    }

    EventSubmit(data)
      .then(() => {
        Popup.fire({
          icon: "success",
          title: "Event Created Successfully",
          showCloseButton: true,
        }).then(() => {
          dispatchAction(createActions.resetEvent());
          router.push("/fresh");
        });
      })
      .catch((error) => {
        if (error.errorCode === 1000) {
          Swal.fire({
            icon: "error",
            title: "Oops! Some words aren’t allowed.",
            text: `The word "${error.data.banned_words[0]}" isn’t allowed. Please keep it friendly!`,
          });
        } else {
          Swal.fire({
            icon: "error",
            title: error.message,
          });
        }
      });
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ values, setFieldValue, ...rest }) => (
        <Form
          title="Back to event details"
          nextBtnText="Create event"
          dirty={true}
          formikValues={{ values, setFieldValue, ...rest }}
        >
          <div className="d-lg-block d-none col-xxl-4 col-xl-5 col-6 flex-grow-1">
            <Wrapper>
              <PostAuthor
                author={{
                  _id: user.id,
                  username: user.profile.username,
                  pic: getAssetUrl({
                    media: user?.profile?.avatar[0],
                    variation: "thumb",
                    defaultType: "avatar",
                  }),
                  role: user.role,
                  display_name: user.profile.display_name,
                }}
                shared_on_profile={true}
                ts={Date.now()}
                type={values.visibility}
                post_type={""}
              />
              <div className="post-content d-flex flex-column gap-3">
                <div className="d-flex gap-2 p-2 bg-unlimited-light rounded-3 w-fit">
                  <Image
                    src="/images/post/fill-calendar.svg"
                    alt="play_icon"
                    width={24}
                    height={24}
                  />
                  <span className="color-unlimited">Upcoming Event</span>
                </div>
                <div>
                  <h5>{values.name}</h5>
                  <h6>{values.description}</h6>
                </div>
                <div className="position-relative">
                  <FileInput
                    name="media"
                    values={values}
                    display={true}
                    setFieldValue={() => {}}
                  />
                  <div className="live-details-container position-md-absolute m-3 mb-md-3 mb-0 shadow-dark rounded-3 start-0 end-0 bottom-0 bg-body p-3">
                    <div className="d-flex flex-md-row flex-column align-items-center gap-md-0 gap-2 ">
                      <div className="live-details align-items-center column-gap-3 row-gap-1">
                        <Image
                          src="/images/post/fill-time.svg"
                          alt="live-time-icon"
                          className="live-time-icon"
                          width={48}
                          height={48}
                        />
                        <div className="live-time color-dark fs-5 fw-semibold ">
                          {formatDateForLiveEvent(
                            new Date(
                              `${initialValues.date}T${initialValues.time}`
                            ).getTime()
                          )}
                        </div>
                        <div className="live-ticket-counts d-flex align-items-center column-gap-3 row-gap-1  color-bold flex-wrap ">
                          {values.ticket.type === "Fee" ? (
                            <div>${values.ticket.price}/ticket</div>
                          ) : (
                            <div>Free event</div>
                          )}
                          <Divider direction="end" />
                          <div>
                            {values.duration.value} {values.duration.type}
                          </div>
                          <Divider direction="end" />
                          <div className="d-flex align-items-center gap-2">
                            <Image
                              src="/images/post/fill-ticket.svg"
                              alt="live-time-icon"
                              width={24}
                              height={24}
                            />
                            {values.ticket.release.type == "Unlimited" ? (
                              <span>Unlimited</span>
                            ) : (
                              <span>
                                Sold {0}/{values.ticket.release.amount}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Wrapper>
          </div>
          <div className="d-flex flex-column col-xl-6 col-lg-5 gap-1 flex-grow-1">
            <Wrapper className="w-xl-75">
              <Select
                name="visibility"
                required={false}
                label="Who can see your event?"
                labelClass="fs-5 color-bold fw-medium"
                className="gap-3 w-xxl-50 w-xl-75 w-lg-75"
                array={PostVisibility.filter((v) =>
                  ["Public", "OnlyMe"].includes(v.value)
                )}
                selected={values.visibility}
                setFieldValue={setFieldValue}
              />
              {/* <Divider />
              <InputGroup
                label="Post to"
                type="checkbox"
                required={false}
                theme="slider"
                name="default.post_to"
                className="d-flex flex-column align-items-baseline gap-3"
                labelClass="fs-5 color-bold fw-semibold"
                childClass="flex-column"
                array={[
                  {
                    text: "Main Profile",
                    value: "profile",
                  },
                  {
                    text: "Share to my story",
                    value: "story",
                  },
                ]}
                selected={values.default.post_to || []}
              />
              <Divider /> */}
            </Wrapper>
          </div>
        </Form>
      )}
    </Formik>
  );
}
