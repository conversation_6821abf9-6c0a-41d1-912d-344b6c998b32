import Image from "next/image";
import { memo, useEffect, useState } from "react";

import { PostVisibility } from "@/global/constants";
import { configActions, userDataActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

let isSubscriptionPost = false;

const SelectPostVisibility = ({
  props,

  onMobile = false,
}: {
  props: any;

  onMobile?: boolean;
}) => {
  const statePremiumValue = useAppSelector(
    (s) => s?.userData?.post?.premiumValue
  );
  const [premiumValue, setPremiumValue] = useState(statePremiumValue);

  const [visibilityIndex, setVisibilityIndex] = useState(0);
  const dispatchAction = useAppDispatch();

  useEffect(() => {
    setPremiumValue(statePremiumValue);
  }, [statePremiumValue]);

  useEffect(() => {
    dispatchAction(configActions.toggleShowPositionWidget());
    dispatchAction(configActions.toggleShowCreateWidget());

    return () => {
      dispatchAction(configActions.toggleShowPositionWidget());
      dispatchAction(configActions.toggleShowCreateWidget());
    };
  }, []);

  const handlePremium = (e: any) => {
    let value = e.target.value;

    // Allow only digits and at most one decimal point
    if (/[^0-9.]/.test(value) || value.split(".").length > 2) {
      return; // Don't update state if value contains invalid characters or more than one decimal point
    }

    // Remove leading zeros unless it's a decimal value (e.g., 0.5)
    if (value !== "0" && value !== "0." && !value.startsWith("0.")) {
      value = value.replace(/^0+/, "");
    }

    setPremiumValue(value);

    dispatchAction(userDataActions.setPremiumValue(value));
  };

  const user = useAppSelector((state) => state.user);
  const initial_visibility = useAppSelector(
    (s) => s.userData.post.initial_visibility
  );

  const visibilityTextMappings: Record<string, JSX.Element> = {
    Prime: (
      <div className="">
        <span>
          Get discovered by all KNKY Prime users! Feature your short-form
          content in our ‘Match’, ‘Featured’ and ‘For You’ feeds to boost your
          visibility and maximize sales.
        </span>

        <div className="mt-2">
          Supports:
          <ul>
            <li>Up to 20 media per post</li>
            <li>FHD (1080p) media quality</li>
            <li>Max. total video length 30min</li>
          </ul>
        </div>
      </div>
    ),
    Premium: (
      <div className="">
        <span>
          Offer your long-form content and larger media collections on KNKY with
          Pay-To-View access for your fans!
        </span>

        <div className="mt-2">
          Supports:
          <ul>
            <li>Up to 60 media per post</li>
            <li>QHD (1440p) or 4K (2160p) media quality</li>
            <li>Max. total video length 125min</li>
          </ul>
          *Publishing in 4K requires a ‘ProCreator’ Plan
        </div>
      </div>
    ),
    Public: (
      <div className="">
        <span>
          Post public clips and teasers to showcase your content, boost fan
          engagement and drive conversions!
        </span>

        <div className="mt-2">
          Supports:
          <ul>
            <li>Up to 6 media per post</li>
            <li>HD (720p) media quality</li>
            <li>Max. total video length 2min</li>
          </ul>
        </div>
      </div>
    ),
    OnlyMe: (
      <div className="">
        <span>Post draft content in advance for when your ready to post.</span>

        <div className="mt-2">
          Supports:
          <ul>
            <li>Up to 20 media per post</li>
            <li>FHD (1080p) media quality</li>
            <li>Max. total video length 30min</li>
          </ul>
        </div>
      </div>
    ),

    // Subscription: (
    //   <div className="">
    //     <span>
    //       Post public clips and teasers to showcase your content, boost fan
    //       engagement and drive conversions!
    //     </span>

    //     <div className="mt-2">
    //       Supports:
    //       <ul>
    //         <li>Up to 6 media per post</li>
    //         <li>HD (720p) media quality</li>
    //         <li>Max. total video length 2min</li>
    //       </ul>
    //     </div>
    //   </div>
    // ),
  };

  const isMobile: boolean = window.innerWidth < 768;
  const [showMore, setShowMore] = useState(onMobile ? false : true);

  let checked: boolean;

  const renderPostVisibilityList = () => {
    if (isSubscriptionPost) {
      return PostVisibility.map((visibility, index) => {
        if (visibility.value === "Subscription") {
          return (
            <li
              key={index}
              className="user-group-wrapper d-flex pe-2 ps-2 gap-2 align-items-center mb-2 pointer"
              onClick={() => handleVisibilityChange(index)}
            >
              <label
                htmlFor={`flexCheckDefault-${index}`}
                className="d-flex align-items-center w-100 gap-2"
              >
                <div
                  className={`form-check d-flex align-items-center ${
                    isMobile && "d-none"
                  }`}
                >
                  <input
                    className="form-check-input rounded-pill shadow-dark fs-8"
                    type="radio"
                    name="visibility-radio"
                    id={`flexCheckDefault-${index}`}
                    onClick={() => handleVisibilityChange(index)}
                    checked={checked}
                  />
                </div>
                <Image
                  src={visibility.icon}
                  width={25}
                  height={25}
                  alt="visibility"
                />
                <p className="fs-6 mb-0 fw-medium">{visibility.text}</p>
              </label>
            </li>
          );
        }
      });
    } else {
      return PostVisibility.map((visibility, index) => {
        if (
          user.role === "user" &&
          (visibility.value === "Premium" ||
            visibility.value === "Prime" ||
            visibility.value === "Subscription")
        ) {
          return null;
        } else if (visibility.value === "Subscription") {
          return null;
        }

        return (
          <li
            key={index}
            className="user-group-wrapper d-flex pe-2 ps-2 gap-2 align-items-center mb-2 pointer"
            onClick={() => handleVisibilityChange(index)}
          >
            <label
              htmlFor={`flexCheckDefault-${index}`}
              className="d-flex align-items-center w-100 gap-2"
            >
              <div
                className={`form-check d-flex align-items-center ${
                  isMobile && "d-none"
                }`}
              >
                <input
                  className="form-check-input rounded-pill shadow-dark fs-8"
                  type="radio"
                  name="visibility-radio"
                  id={`flexCheckDefault-${index}`}
                  onClick={() => handleVisibilityChange(index)}
                  checked={checked}
                />
              </div>
              <Image
                src={visibility.icon}
                width={25}
                height={25}
                alt="visibility"
              />
              <p className="fs-6 mb-0 fw-medium">{visibility.text}</p>
            </label>
          </li>
        );
      });
    }
  };

  const role = useAppSelector((state) => state.user.role);

  const handleVisibilityChange = (index: number) => {
    props.setVisibility(PostVisibility[index]);
    setVisibilityIndex(index);

    setTimeout(() => {
      checked = index === visibilityIndex;
    }, 100);
  };

  useEffect(() => {
    if (role === "creator") {
      if (props.isSubscriptionVisibility && props.postingAsSubs) {
        props.setVisibility(PostVisibility[4]);
        isSubscriptionPost = true;
        setVisibilityIndex(4);
      } else {
        props.setVisibility(
          props.visibility.value === "Subscription"
            ? PostVisibility[0]
            : props.visibility
        );
        setVisibilityIndex(
          props.visibility.value === "Subscription"
            ? 0
            : PostVisibility.indexOf(props.visibility)
        );
        isSubscriptionPost = false;
      }
    }
  }, [props.isSubscriptionVisibility, props.postingAsSubs]);

  return (
    <>
      <div
        className={`bg-body rounded-3 p-3 ${isMobile && "border mx-2  pb-0 "}`}
      >
        <div className="d-flex justify-content-between">
          <h5 className={`fw-semibold mb-0 ${isMobile && "fs-6"}`}>
            Post Audience
          </h5>
          {onMobile && user.role === "creator" && (
            <span
              onClick={() => setShowMore(!showMore)}
              className="color-primary pointer fs-7 fw-medium color-dark underline align-items-center link-offset-1"
            >
              {showMore ? "Hide terms" : "Show terms"}
            </span>
          )}
        </div>
        <div className="mt-3">
          {
            // props.isGroup || props.isChannel ? (
            //   <>
            //     <div className="dropdown  w-100">
            //       <button
            //         className={`btn  btn-secondary w-100   d-flex align-items-center justify-content-between bg-cream ps-2 pe-2  ${
            //           props.isEdit && "  disable"
            //         }`}
            //         type="button"
            //         data-bs-toggle="dropdown"
            //         disabled={user.role === "user"}
            //         aria-expanded="false"
            //       >
            //         <div className="d-flex gap-1 w-100  align-items-center">
            //           <Image
            //             src={`${props.visibility.icon}`}
            //             width={25}
            //             height={25}
            //             alt="visibility"
            //           />
            //           <span className="fs-6 fw-medium color-dark">
            //             {props.visibility.text}
            //           </span>
            //         </div>
            //         <Image
            //           src={"/images/svg/chevron-down.svg"}
            //           width={22}
            //           height={22}
            //           alt="dropdown"
            //         />
            //       </button>
            //       <ul className="dropdown-menu w-100  ">
            //         {PostVisibility.filter(
            //           (visibility) =>
            //             visibility.value === "Prime" ||
            //             visibility.value === "OnlyMe"
            //         ).map((visibility, index) => (
            //           <li
            //             key={index}
            //             className="user-group-wrapper d-flex pe-2 ps-2 gap-2 align-items-center mb-2 pointer"
            //             onClick={() => {
            //               props.setVisibility(visibility);
            //               setVisibilityIndex(index);
            //             }}
            //           >
            //             <label
            //               htmlFor={`flexCheckDefault-${index}`}
            //               className="d-flex align-items-center w-100 gap-2"
            //             >
            //               <div
            //                 className={`form-check d-flex align-items-center ${
            //                   isMobile && "d-none"
            //                 }`}
            //               >
            //                 <input
            //                   className="form-check-input rounded-pill shadow-dark fs-8"
            //                   type="radio"
            //                   value=""
            //                   name="visibility-radio"
            //                   id={`flexCheckDefault-${index}`}
            //                   onChange={() => {
            //                     props.setVisibility(visibility);
            //                     setVisibilityIndex(index);
            //                   }}
            //                   checked={index === visibilityIndex}
            //                 />
            //               </div>
            //               <Image
            //                 src={`${visibility.icon}`}
            //                 width={25}
            //                 height={25}
            //                 alt="visibility"
            //               />
            //               <p className="fs-6 mb-0 fw-medium">{visibility.text}</p>
            //             </label>
            //           </li>
            //         ))}
            //       </ul>
            //     </div>
            //   </>
            // ) :

            <>
              <div className={`dropdown w-100`}>
                <button
                  className={
                    "btn  btn-secondary w-100   d-flex align-items-center justify-content-between bg-cream ps-2 pe-2 "
                  }
                  type="button"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <div className="d-flex gap-1 w-100  align-items-center">
                    <Image
                      src={`${
                        props.visibility.icon.includes("undefined")
                          ? `/images/post/fill-${initial_visibility?.toLowerCase()}.svg`
                          : props.visibility.icon
                      }`}
                      width={25}
                      height={25}
                      alt="visibility"
                    />
                    <span className="fs-6 fw-medium color-dark">
                      {props.visibility.text || initial_visibility}
                    </span>
                  </div>
                  <Image
                    src={"/images/svg/chevron-down.svg"}
                    width={22}
                    height={22}
                    alt="dropdown"
                  />
                </button>
                <ul className="dropdown-menu w-100 ">
                  {!props.isEdit ? (
                    renderPostVisibilityList()
                  ) : (
                    <>
                      <li
                        className="user-group-wrapper d-flex pe-2 ps-2 gap-2 align-items-center mb-2 pointer"
                        onClick={() => {
                          props.setVisibility({
                            text: initial_visibility,
                            value: initial_visibility,
                            icon:
                              initial_visibility === "OnlyMe"
                                ? "/images/post/fill-private.svg"
                                : `/images/post/fill-${initial_visibility?.toLowerCase()}.svg`,
                          });
                          setVisibilityIndex(0);
                        }}
                      >
                        <label
                          htmlFor={`flexCheckDefault-${0}`}
                          className="d-flex align-items-center w-100 gap-2"
                        >
                          <div
                            className={`form-check d-flex align-items-center ${
                              isMobile && "d-none"
                            }`}
                          >
                            <input
                              className="form-check-input rounded-pill shadow-dark fs-8"
                              type="radio"
                              value=""
                              name="visibility-radio"
                              id={`flexCheckDefault-${0}`}
                              onChange={() => {
                                props.setVisibility({
                                  text: initial_visibility,
                                  value: initial_visibility,
                                  icon:
                                    initial_visibility === "OnlyMe"
                                      ? "/images/post/fill-private.svg"
                                      : `/images/post/fill-${initial_visibility?.toLowerCase()}.svg`,
                                });
                                setVisibilityIndex(0);
                              }}
                              checked={0 === visibilityIndex}
                            />
                          </div>
                          <Image
                            src={`/images/post/fill-${initial_visibility?.toLowerCase()}.svg`}
                            width={25}
                            height={25}
                            alt="visibility"
                          />
                          <p className="fs-6 mb-0 fw-medium">
                            {initial_visibility}
                          </p>
                        </label>
                      </li>
                      <li
                        className="user-group-wrapper d-flex pe-2 ps-2 gap-2 align-items-center mb-2 pointer"
                        onClick={() => {
                          props.setVisibility({
                            text: "Draft",
                            value: "OnlyMe",
                            icon: "/images/post/fill-private.svg",
                          });
                          setVisibilityIndex(3);
                        }}
                      >
                        <label
                          htmlFor={`flexCheckDefault-${3}`}
                          className="d-flex align-items-center w-100 gap-2"
                        >
                          <div
                            className={`form-check d-flex align-items-center ${
                              isMobile && "d-none"
                            }`}
                          >
                            <input
                              className="form-check-input rounded-pill shadow-dark fs-8"
                              type="radio"
                              value=""
                              name="visibility-radio"
                              id={`flexCheckDefault-${3}`}
                              onChange={() => {
                                props.setVisibility({
                                  text: "Draft",
                                  value: "OnlyMe",
                                  icon: "/images/post/fill-private.svg",
                                });
                                setVisibilityIndex(3);
                              }}
                              checked={3 === visibilityIndex}
                            />
                          </div>
                          <Image
                            src={`${PostVisibility[3].icon}`}
                            width={25}
                            height={25}
                            alt="visibility"
                          />
                          <p className="fs-6 mb-0 fw-medium">{"Draft"}</p>
                        </label>
                      </li>
                    </>
                  )}
                </ul>
              </div>
              {!props?.isEdit && (
                <>
                  <label
                    htmlFor="Price to unlock"
                    className={
                      props.visibility.value !== "Premium"
                        ? "d-none"
                        : " align-self-start color-medium fs-7 mt-3"
                    }
                  >
                    Price to unlock
                    {/*  */}
                    <span className="color-red"> * </span>
                  </label>
                  <div
                    className={
                      props.visibility.value !== "Premium"
                        ? "d-none"
                        : "input-group   w-100   align-items-center bg-cream rounded-3 ps-2 pe-2"
                    }
                  >
                    <span>$</span>

                    <input
                      name="Price to unlock"
                      type="number"
                      placeholder="Price to unlock"
                      className={
                        " color-dark bg-transparent border-0 py-2 px-2 shadow-none flex-grow-1"
                      }
                      value={premiumValue}
                      onChange={handlePremium}
                      disabled={props.visibility.value !== "Premium"}
                    />
                  </div>
                </>
              )}
            </>
          }
          {user.role === "creator" ? (
            <div className="mt-3 fs-7 color-black ">
              {showMore ? (
                visibilityTextMappings[props.visibility.value]
              ) : (
                <></>
              )}
            </div>
          ) : (
            <div className="mt-3"></div>
          )}
        </div>
      </div>
    </>
  );
};

export const PostVisibilitySelector = memo(SelectPostVisibility);
