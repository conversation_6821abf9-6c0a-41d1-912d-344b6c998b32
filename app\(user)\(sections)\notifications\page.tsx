"use client";

import "./index.scss";

import { useIsFirstRender } from "@uidotdev/usehooks";
import classNames from "classnames";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Swal from "sweetalert2";

import { GetNotification } from "@/api/requests";
import { SoundAndBannerSettings, UpdateGlobalNotifications } from "@/api/user";
import ActionButton from "@/components/common/action-button";
import LoadingSpinner from "@/components/common/loading";
import Toggle from "@/components/matchmaker/modal/edit-profile/toggle";
import { ModalPortal } from "@/components/portals/ModalPortal";
import { SettingsIcon } from "@/components/settings/wallet/utils/svg";
import useFcmToken from "@/hooks/useFcmToken";
import { notificationActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Notification as NotificationType } from "@/types/notification";

import NotificationBox from "./notification-box";

const tabs = [
  { name: "All", type: "all" },
  { name: "Subscriptions", type: "1" },
  { name: "Payment", type: "2" },
  { name: "Requests", type: "3" },
  { name: "Match", type: "4" },
  { name: "Interactions", type: "5" },
  { name: "System", type: "6" },
  { name: "Complaints", type: "7" },
  { name: "Tags", type: "8" },
  { name: "Actionables", type: "actionable" },
];

const Notifications: React.FC = () => {
  const router = useRouter();
  const dispatchAction = useAppDispatch();
  const notificationLength = useAppSelector(
    (state) => state.notification.notifications.length
  );
  const [activeTab, setActiveTab] = useState(0);
  const blockedUsers = useAppSelector((state) => state.block.blockedByMe);
  const blockedMe = useAppSelector((state) => state.block.hasBlockedMe);
  const [toggle, setToggle] = useState(false);
  const [loading, setLoading] = useState(false);
  const isFirstRender = useIsFirstRender();

  const handleUnseenNotification = async () => {
    setLoading(true);
    const response = await GetNotification({
      limit: 20,
      seen: toggle ? "false" : "",
    });
    const group = response.data
      .filter((n: NotificationType) => !blockedUsers[n?.author?._id])
      .filter((n: NotificationType) => !blockedMe[n?.author?._id]);
    const page_data = response.page_data;
    dispatchAction(notificationActions.toggleUnseenOnly(toggle));
    dispatchAction(
      notificationActions.addNotifications({
        notification: group,
        append: false,
      })
    );
    dispatchAction(notificationActions.setPageData(page_data));
    setLoading(false);
  };

  useEffect(() => {
    if (!isFirstRender) handleUnseenNotification();
  }, [toggle]);

  return (
    <>
      <div className="container-sm rounded-3 gap-3 p-0 bg-body my-3 d-flex flex-column">
        <div className="d-flex justify-content-between align-items-start p-3">
          <div className="d-flex align-items-md-center">
            <div
              onClick={() => router.back()}
              className="pointer"
              style={{
                marginTop: window.innerWidth > 768 ? "0" : "-5px",
              }}
            >
              <Image
                src={"/images/svg/back.svg"}
                width={40}
                height={40}
                alt="back"
              />{" "}
            </div>
            <span className="fs-5 fw-medium">Notifications</span>
          </div>
          <div className="d-flex align-items-md-center align-items-end flex-column-reverse flex-md-row gap-1 gap-md-3">
            <div className="pointer fw-bold me-1 d-flex align-items-center gap-1">
              <span className="fs-7">Seen</span>
              <Toggle
                isToggled={toggle}
                setIsToggled={() => {
                  dispatchAction(notificationActions.toggleUnseenOnly(!toggle));
                  setToggle(!toggle);
                }}
                backgroundStyle={{
                  width: "40px",
                  height: "20px",
                }}
                circleStyle={{
                  width: "16px",
                  height: "16px",
                }}
              />
              <span className="fs-7">Unseen</span>
            </div>
            <div
              data-bs-toggle="modal"
              data-bs-target="#notificationSettingsModal"
              className="pointer"
              title="Notifications Settings"
            >
              <SettingsIcon />
            </div>
          </div>
        </div>

        <ul
          className="nav nav-pills mb-3 bg-transparent position-relative notification-hr-line"
          id="pills-tab"
          style={{ flexWrap: "nowrap", overflowX: "auto" }}
        >
          {window.innerWidth > 786 && (
            <hr className="w-100 position-absolute bottom-0 m-0 " />
          )}

          {tabs.map((tab, i) => (
            <li className="nav-item notification-item" key={tab.type}>
              <button
                className={classNames(
                  "nav-link color-dark fw-medium fs-6 bg-transparent rounded-0",
                  { active: activeTab === i }
                )}
                data-bs-toggle="pill"
                type="button"
                onClick={() => setActiveTab(i)}
              >
                {tab.name}
              </button>
            </li>
          ))}
        </ul>

        <div
          className="tab-content notification-body p-2 pt-0"
          style={{ minHeight: "30rem", position: "relative" }}
          id="pills-tabContent"
        >
          <div
            className={
              notificationLength
                ? "tab-pane fade show active"
                : "noNotification tab-pane fade show active"
            }
          >
            {loading ? (
              <div className="d-flex justify-content-center align-items-center">
                <LoadingSpinner />
              </div>
            ) : notificationLength ? (
              <NotificationBox type={tabs[activeTab].type} />
            ) : (
              <h4>No Notification</h4>
            )}
          </div>
        </div>
      </div>
      <NotificationSettingsModal />
    </>
  );
};

export default Notifications;

export interface GlobalNotification {
  email: boolean;
  platform: boolean;
}

export interface NotificationSettings {
  new_likes_and_interaction: GlobalNotification;
  new_comments_and_replies: GlobalNotification;
  new_mentions: GlobalNotification;
  new_matches: GlobalNotification;
  new_content_from_my_subscriptions: GlobalNotification;
  new_content_from_following: GlobalNotification;
}

const notificationDescriptions: { [key: string]: string } = {
  new_likes_and_interaction: "New likes and interactions",
  new_comments_and_replies: "New comments and replies",
  new_mentions: "New mentions",
  new_matches: "New matches",
  new_content_from_my_subscriptions: "New content from my subscriptions",
  new_content_from_following: "New content from creators I follow",
};

const NotificationSettingsModal = () => {
  const dispatch = useAppDispatch();
  const [isSoundOn, setIsSoundOn] = useState(false);
  const [isPushNotificationsOn, setIsPushNotificationsOn] = useState(false);
  const [isBannerOn, setIsBannerOn] = useState(false);
  const [notificationSettings, setNotificationSettings] =
    useState<NotificationSettings>({
      new_likes_and_interaction: {
        email: false,
        platform: false,
      },
      new_comments_and_replies: {
        email: false,
        platform: false,
      },
      new_mentions: {
        email: false,
        platform: false,
      },
      new_matches: {
        email: false,
        platform: false,
      },
      new_content_from_my_subscriptions: {
        email: false,
        platform: false,
      },
      new_content_from_following: {
        email: false,
        platform: false,
      },
    });

  const { requestPermissionManually, perm, fcmToken } = useFcmToken();

  useEffect(() => {
    if (perm === "granted") {
      setIsPushNotificationsOn(true);
    } else {
      setIsPushNotificationsOn(false);
    }
  }, [perm, fcmToken]);

  const notificationStateData = useAppSelector(
    (s) => s.notification.notifications
  );

  const soundSettings = useAppSelector((s) => s.notification.isSoundOn);
  const bannerSettings = useAppSelector((s) => s.notification.isBannerOn);
  const notificationSettingsRedux = useAppSelector(
    (s) => s.notification.notificationSettings
  );

  useEffect(() => {
    return () => {
      dispatch(
        notificationActions.markSeen(
          new Set(
            notificationStateData
              .filter((n) => n.seen === false || n?.seen === undefined)
              .map((n) => n._id)
          )
        )
      );
    };
  }, []);

  useEffect(() => {
    setIsBannerOn(bannerSettings);
    setIsSoundOn(soundSettings);
    setNotificationSettings(notificationSettingsRedux);
  }, [soundSettings, bannerSettings, notificationSettingsRedux]);

  const handleToggleChange = (
    settingKey: keyof NotificationSettings,
    property: keyof GlobalNotification,
    value: boolean
  ) => {
    setNotificationSettings((prev) => ({
      ...prev,
      [settingKey]: {
        ...prev[settingKey],
        [property]: value,
      },
    }));
  };

  const handleSaveChanges = async () => {
    try {
      if (isSoundOn !== soundSettings || isBannerOn !== bannerSettings) {
        await SoundAndBannerSettings({
          notification_banner: isBannerOn || false,
          notification_sound: isSoundOn || false,
        });

        dispatch(notificationActions.toggleNotificationBanner(isBannerOn));
        dispatch(notificationActions.toggleNotificationSound(isSoundOn));
      }

      if (
        JSON.stringify(notificationSettings) !==
        JSON.stringify(notificationSettingsRedux)
      ) {
        await UpdateGlobalNotifications(notificationSettings);

        dispatch(
          notificationActions.setNotificationSettings(notificationSettings)
        );
      }

      Swal.fire({
        text: "Your notification settings have been saved successfully",
        icon: "success",
        confirmButtonText: "Okay",
        confirmButtonColor: "#AC1991",
        customClass: {
          confirmButton: "custom-btn",
          cancelButton: "custom-btn",
        },
      }).then(() => {
        if (document) {
          document.getElementById("settingsCloseButton")?.click();
        }
      });
    } catch (err) {
      Swal.fire({
        text: "Something went wrong",
        icon: "error",
        confirmButtonText: "Okay",
        confirmButtonColor: "#AC1991",
        customClass: {
          confirmButton: "custom-btn",
          cancelButton: "custom-btn",
        },
      });
      console.log(err);
    }
  };

  return (
    <ModalPortal>
      <div
        className="modal fade"
        id="notificationSettingsModal"
        tabIndex={-1}
        aria-labelledby="notificationSettingsModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header">
              <h1
                className="modal-title fs-5"
                id="notificationSettingsModalLabel"
              >
                My Notifications
              </h1>
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                id="settingsCloseButton"
                aria-label="Close"
              ></button>
            </div>
            <div className="modal-body">
              <div className="d-flex align-items-center justify-content-between">
                <label className="form-check-label color-medium">
                  Push Notifications
                </label>
                <Toggle
                  isToggled={isPushNotificationsOn}
                  setIsToggled={setIsPushNotificationsOn}
                  customOnChange={requestPermissionManually}
                />
              </div>
              <div className="d-flex align-items-center justify-content-between my-3">
                <label className="form-check-label color-medium">
                  Notification Sound
                </label>
                <Toggle isToggled={isSoundOn} setIsToggled={setIsSoundOn} />
              </div>
              <div className="d-flex align-items-center justify-content-between my-3">
                <label className="form-check-label color-medium">
                  Notification Banner
                </label>
                <Toggle isToggled={isBannerOn} setIsToggled={setIsBannerOn} />
              </div>
              <div className="d-flex gap-3 align-items-center flex-row-reverse">
                <div className="fw-bold">Platform</div>
                <div className="fw-bold">Email</div>
              </div>
              {Object.keys(notificationSettings).map((setting, index) => (
                <div
                  className="d-flex align-items-center justify-content-between my-3"
                  key={index}
                >
                  <label className="form-check-label color-medium">
                    {
                      notificationDescriptions[
                        setting as keyof NotificationSettings
                      ]
                    }
                  </label>
                  <div className="d-flex gap-3">
                    <Toggle
                      isToggled={
                        notificationSettings[
                          setting as keyof NotificationSettings
                        ].email
                      }
                      setIsToggled={(value) =>
                        handleToggleChange(
                          setting as keyof NotificationSettings,
                          "email",
                          value
                        )
                      }
                    />
                    <Toggle
                      isToggled={
                        notificationSettings[
                          setting as keyof NotificationSettings
                        ].platform
                      }
                      setIsToggled={(value) =>
                        handleToggleChange(
                          setting as keyof NotificationSettings,
                          "platform",
                          value
                        )
                      }
                    />
                  </div>
                </div>
              ))}
            </div>
            <div className="modal-footer">
              <ActionButton
                onClick={handleSaveChanges}
                disabled={
                  isSoundOn === soundSettings &&
                  isBannerOn === bannerSettings &&
                  JSON.stringify(notificationSettings) ===
                    JSON.stringify(notificationSettingsRedux)
                }
              >
                Save Changes
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};
