"use client";

import Image from "next/image";
import { useEffect } from "react";
import "./index.scss";

import { GetUserOffersById } from "@/api/user";
import { ModalService } from "@/components/modals";
import { configActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";

const AgeWarning = () => {
  const role = useAppSelector((state) => state.user.role);
  const dispatchAction = useAppDispatch();
  const isMobile = window.innerWidth < 767;
  useEffect(() => {
    const deviceId = sessionStorage.getItem("hasVisited");
    const ageModal = document.getElementById("ageWarningModal");

    if (
      !deviceId &&
      role === "guest" &&
      window.location.pathname !== "/reset-password"
    ) {
      setTimeout(() => {
        if (ageModal) {
          const modalInstance = new window.bootstrap.Modal(ageModal!);
          modalInstance.show();
        }
      }, 100);
    }
  }, []);

  if (role !== "guest") return;
  return (
    <>
      <div
        className="modal fade"
        id="ageWarningModal"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabIndex={-1}
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered modal-lg">
          <div className="modal-content content-warning">
            <div className="modal-body ">
              <div className="container d-flex flex-column align-items-center gap-3 gap-lg-4">
                <div className="d-flex gap-2  align-items-center">
                  <Image
                    src={"/images/knky.svg"}
                    width={isMobile ? 38.48 : 60}
                    height={isMobile ? 23.38 : 60}
                    alt="logo"
                    className="img-fluid"
                  />
                  <h1 className="knky-text m-0 fw-semibold">KNKY</h1>
                </div>
                <div>
                  <span className="fw-semibold m-0 main-text">
                    This website is intended for adults only and contains age
                    restricted materials, including nudity and explicit
                    depictions of sexual activity. By entering this site, you
                    confirm that you are at least 18 years old or have reached
                    the age of majority in your jurisdiction and consent to
                    viewing sexually explicit content.
                  </span>
                </div>
                <div className="d-flex flex-column align-items-center gap-2 gap-lg-3">
                  <div>
                    <button
                      className="btn btn-purple w-100 fw-semibold"
                      data-bs-dismiss="modal"
                      id="age-wraning-close-button"
                      onClick={() => {
                        sessionStorage.setItem("hasVisited", "true");
                        const offerID = sessionStorage.getItem("offer_Id");
                        const init = sessionStorage.getItem("init");

                        if (offerID) {
                          GetUserOffersById(
                            sessionStorage.getItem("offer_Id") || ""
                          ).then((res) => {
                            sessionStorage.setItem(
                              "offerData",
                              JSON.stringify(res.data[0])
                            );

                            if (res.data[0].available_to == "All") {
                              dispatchAction(
                                configActions.openJoinUsForm(true)
                              );
                            } else {
                              const userType = res.data[0].available_to
                                .slice(0, -1)
                                .toUpperCase();

                              ModalService.open("SIGN_UP", {
                                userType,
                              });
                            }
                          });
                        } else if (init) {
                          const userType: any =
                            init.indexOf("User") !== -1
                              ? init.slice(init.indexOf("User")).toUpperCase()
                              : init.indexOf("Creator") !== -1
                              ? init
                                  .slice(init.indexOf("Creator"))
                                  .toUpperCase()
                              : "Not found";

                          if (userType === "Not found") {
                            dispatchAction(configActions.openJoinUsForm(true));
                          } else {
                            ModalService.open("SIGN_UP", {
                              userType,
                            });
                          }

                          sessionStorage.removeItem("init");
                        }
                      }}
                    >
                      I’m 18 or over - Enter
                    </button>
                  </div>

                  <div
                    className="fw-semibold pointer btn btn-black my-lg-0 my-2"
                    onClick={() => {
                      window.location.href = "https://www.google.com/";
                    }}
                  >
                    Exit here
                  </div>
                </div>

                <div className="color-black fw-medium fs-7">
                  By entering this website, you agree to our &nbsp;
                  <a
                    href="/articles/terms-of-service"
                    target="_blank"
                    className="text-decoration-underline"
                    rel="noopener noreferrer"
                  >
                    Terms of Service
                  </a>
                  &nbsp;&{" "}
                  <a
                    href="/articles/privacy-policy"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-decoration-underline"
                  >
                    Privacy Policy
                  </a>
                  .{isMobile ? " " : <br />}
                  If you do not agree please click the “Exit here” button above
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AgeWarning;
