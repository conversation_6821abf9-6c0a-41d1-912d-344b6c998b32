import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import type { ModalRef } from "@/components/common/modal";
import { useAppSelector } from "@/redux-store/hooks";
import type { Media } from "@/types/media";
import type { PostType } from "@/types/post";

import FileSupports from "../modal/file-support";

const MediaValidation = ({
  images,
  videos,
  vaultImages,
  vaultVideos,
  preview,
  poll,
  bg,
  promote,
  postOf,
  visibility,
  setSubmitPost,
  isPaidSubscription,
  isSubscriptionVisibility,
}: {
  images: File[];
  videos: File[];
  vaultImages: Media[];
  vaultVideos: Media[];
  preview: File;
  poll: any;
  bg: any;
  promote: any;
  postOf: any;
  visibility: PostType;
  setSubmitPost: any;
  isPaidSubscription: boolean;
  isSubscriptionVisibility: boolean;
}) => {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState("");
  const [upgradeToPro, setUpgradeToPro] = useState(false);
  const fileSupportsModalRef = { method: {} as ModalRef };
  const caption = useAppSelector((state) => state.userData?.post?.caption);
  const searchParams = useSearchParams();
  const isGroup = searchParams?.get("isGroupPost")
    ? !!searchParams?.get("isGroupPost")
    : false;
  const isChannel = searchParams?.get("isChannelPost")
    ? !!searchParams?.get("isChannelPost")
    : false;
  const role = useAppSelector((state) => state.user.role);
  const authorHasPro = useAppSelector((state) =>
    state.user.profile.badges.subscription_badge.includes("CreatorPro")
  );
  const mediaValidationRules = {
    Prime: {
      maxMedia: 20,
      maxVideoDuration: 30 * 60,
      maxResolution: "FHD",
    },
    Premium: {
      maxMedia: 60,
      maxVideoDuration: 125 * 60,
      maxResolution: "4K",
    },
    Public: {
      maxMedia: 6,
      maxVideoDuration: 2 * 60,
      maxResolution: "HD",
    },
    OnlyMe: {
      maxMedia: 20,
      maxVideoDuration: 30 * 60,
      maxResolution: "FHD",
    },
    Subscription: {
      maxMedia: 20,
      maxVideoDuration: 30 * 60,
      maxResolution: "FHD",
    },
  };
  const minHeight = 480;
  const minWidth = 640;
  const maxWidthHD = 1280;
  const maxHeightHD = 720;
  const maxWidthFHD = 1920;
  const maxHeightFHD = 1080;
  const maxWidthQHD = 2560;
  const maxHeightQHD = 1440;
  const maxWidth4K = 3840;
  const maxHeight4K = 2160;

  const premiumValue = useAppSelector((s) => s.userData?.post?.premiumValue);

  const getImageResolution = (
    file: File
  ): Promise<{ width: number; height: number }> => {
    return new Promise((resolve, reject) => {
      const imgElement = document.createElement("img");
      imgElement.src = URL.createObjectURL(file);

      imgElement.onload = () => {
        // URL.revokeObjectURL(imgElement.src);
        resolve({ width: imgElement.width, height: imgElement.height });
      };

      imgElement.onerror = () => {
        reject("Failed to load image");
      };
    });
  };

  const getVideoData = (
    file: File
  ): Promise<{ width: number; height: number; duration: number }> => {
    return new Promise((resolve, reject) => {
      const video = document.createElement("video");
      video.src = URL.createObjectURL(file);
      video.preload = "metadata";

      video.onloadedmetadata = () => {
        // URL.revokeObjectURL(video.src);
        resolve({
          width: video.videoWidth,
          height: video.videoHeight,
          duration: video.duration,
        });
      };

      video.onerror = () => {
        reject("Failed to load video metadata");
      };
    });
  };

  useEffect(() => {
    console.log({ isPaidSubscription });
  }, [isPaidSubscription]);

  const validateMediaSelection = async (
    images: File[],
    videos: File[],
    vaultImages: Media[],
    vaultVideos: Media[],
    visibility: PostType
  ): Promise<{ valid: boolean; errorMessage?: string }> => {
    const rules =
      mediaValidationRules[isPaidSubscription ? "Premium" : visibility];

    if (
      images.length + videos.length > rules.maxMedia ||
      vaultImages.length + vaultVideos.length > rules.maxMedia
    ) {
      return {
        valid: false,
        errorMessage: `You can upload up to ${
          rules.maxMedia
        } media for ${visibility.toLowerCase()} post.`,
      };
    }

    if (visibility === "Premium") {
      if (postOf.poll) {
        return {
          valid: true,
          errorMessage: `You can not post poll to pay-to-view audience`,
        };
      } else if (
        //@ts-expect-error ffssad
        premiumValue === "" ||
        //@ts-expect-error ffssad
        premiumValue === "0" ||
        premiumValue < 5
      ) {
        return {
          valid: false,
          errorMessage: `The minimum price for a pay-to-view post is $5`,
        };
      }
    }

    if (visibility === "Subscription") {
      if (postOf.tag) {
        return {
          valid: true,
          errorMessage: `You can not collab with other creators for subscription post`,
        };
      }
    }

    if (postOf.poll && !poll.question) {
      return {
        valid: false,
        errorMessage: "Please add all the details in Poll",
      };
    }

    let td = 0;

    for (const video of videos) {
      const { duration } = await getVideoData(video);

      td += duration;

      if (td > rules.maxVideoDuration) {
        return {
          valid: false,
          errorMessage: `Video duration exceeds the limit of ${`${
            rules.maxVideoDuration === 30
              ? `${rules.maxVideoDuration} seconds`
              : `${rules.maxVideoDuration / 60} minutes`
          }`} `,
        };
      }
    }

    for (const video of vaultVideos) {
      const { duration } = await video;

      td += duration!;

      if (td > rules.maxVideoDuration) {
        return {
          valid: false,
          errorMessage: `Video duration exceeds the limit of ${`${
            rules.maxVideoDuration === 30
              ? `${rules.maxVideoDuration} seconds`
              : `${rules.maxVideoDuration / 60} minutes`
          }`} `,
        };
      }
    }

    // if (!preview && (visibility === "Premium" || visibility === "Prime")) {
    //   return {
    //     valid: false,
    //     errorMessage: "Please select the thumbnail for your post",
    //   };
    // }

    for (const video of videos) {
      const { width, height } = await getVideoData(video);

      if (width < minWidth || height < minHeight) {
        return {
          valid: false,
          errorMessage: "Video resolution should be at least 480p",
        };
      } else if (rules.maxResolution == "HD") {
        if (width > maxWidthHD || height > maxHeightHD) {
          return {
            valid: true,
            errorMessage:
              "Video resolution will be compressed to HD (1280x720).",
          };
        }
      } else if (rules.maxResolution == "FHD") {
        if (width > maxWidthFHD || height > maxHeightFHD) {
          return {
            valid: true,
            errorMessage:
              "Video resolution will be compressed to FHD (1920x1080).",
          };
        }
      } else if (rules.maxResolution === "QHD") {
        if (width > maxWidthQHD || height > maxHeightQHD) {
          return {
            valid: true,
            errorMessage:
              "Video resolution will be compressed to QHD (2560x1440).",
          };
        }
      } else if (rules.maxResolution === "4K") {
        const buyProMsg =
          "Upgrade to Creator Pro to enable 4K video quality for your fans. ";

        if (!authorHasPro && (width === maxWidth4K || height === maxHeight4K)) {
          setUpgradeToPro(true);
          return {
            valid: true,
            errorMessage: buyProMsg,
          };
        }

        if (width > maxWidth4K || height > maxHeight4K) {
          let msg =
            "Videos exceeding 4K resolution will be compressed to 4K (3840x2160).";
          if (!authorHasPro) msg = buyProMsg + msg;
          setUpgradeToPro(true);
          return {
            valid: true,
            errorMessage: msg,
          };
        }
      }
    }

    for (const video of vaultVideos) {
      if (video.resolution) {
        const { width, height } = await video.resolution;

        if (width < minWidth || height < minHeight) {
          return {
            valid: false,
            errorMessage: "Video resolution should be at least 480p",
          };
        } else if (rules.maxResolution == "FHD") {
          if (width > maxWidthFHD || height > maxHeightFHD) {
            return {
              valid: true,
              errorMessage:
                "Video resolution will be compressed to FHD (1920x1080).",
            };
          }
        } else if (rules.maxResolution === "QHD") {
          if (width > maxWidthQHD || height > maxHeightQHD) {
            return {
              valid: true,
              errorMessage:
                "Video resolution will be compressed to QHD (2560x1440).",
            };
          }
        } else if (rules.maxResolution === "4K") {
          if (width > maxWidth4K || height > maxHeight4K) {
            return {
              valid: true,
              errorMessage:
                "Video resolution will be compressed to 4K (3840x2160).",
            };
          }
        }
      }
    }

    for (const image of images) {
      const { width, height } = await getImageResolution(image);

      if (width < minWidth || height < minHeight) {
        return {
          valid: false,
          errorMessage: "Image resolution should be at least 480p",
        };
      } else if (rules.maxResolution == "FHD") {
        if (width > maxWidthFHD || height > maxHeightFHD) {
          return {
            valid: true,
            errorMessage:
              "Image resolution will be compressed to FHD (1920x1080).",
          };
        }
      } else if (rules.maxResolution === "QHD") {
        if (width > maxWidthQHD || height > maxHeightQHD) {
          return {
            valid: true,
            errorMessage:
              "Image resolution will be compressed to QHD (2560 x 1440).",
          };
        }
      } else if (rules.maxResolution === "4K") {
        if (width > maxWidth4K || height > maxHeight4K) {
          return {
            valid: true,
            errorMessage:
              "Image resolution will be compressed to 4K (3840x2160).",
          };
        }
      }
    }

    for (const image of vaultImages) {
      if (image.resolution) {
        const { width, height } = await image.resolution;

        if (width < minWidth || height < minHeight) {
          return {
            valid: false,
            errorMessage: "Image resolution should be at least 480p",
          };
        } else if (rules.maxResolution == "FHD") {
          if (width > maxWidthFHD || height > maxHeightFHD) {
            return {
              valid: true,
              errorMessage:
                "Image resolution will be compressed to FHD (1920x1080).",
            };
          }
        } else if (rules.maxResolution === "QHD") {
          if (width > maxWidthQHD || height > maxHeightQHD) {
            return {
              valid: true,
              errorMessage:
                "Image resolution will be compressed to QHD (2560 x 1440).",
            };
          }
        } else if (rules.maxResolution === "4K") {
          if (width > maxWidth4K || height > maxHeight4K) {
            return {
              valid: true,
              errorMessage:
                "Image resolution will be compressed to 4K (3840x2160).",
            };
          }
        }
      }
    }

    return { valid: true };
  };

  useEffect(() => {
    const validateMedia = async () => {
      if (
        (images.length ||
          videos.length ||
          vaultImages.length ||
          vaultVideos.length) &&
        visibility
      ) {
        const { valid, errorMessage } = await validateMediaSelection(
          images,
          videos,
          vaultImages,
          vaultVideos,
          visibility
        );

        if (!valid) {
          setSubmitPost(false);
        } else {
          setSubmitPost(true);
        }

        if (errorMessage) {
          setErrorMessage(errorMessage);
        } else {
          setErrorMessage("");
        }
      } else if (visibility === "Premium" || visibility === "Prime") {
        setSubmitPost(false);
        setErrorMessage(`You need to add media to a ${visibility} post`);
      } else {
        setErrorMessage("");
      }
    };

    validateMedia();
  }, [
    images,
    videos,
    vaultImages,
    vaultVideos,
    visibility,
    premiumValue,
    isPaidSubscription,
    postOf,
    poll,
  ]);

  useEffect(() => {
    if (
      (visibility === "Public" || visibility === "OnlyMe") &&
      !(postOf.media || postOf.vault)
    ) {
      if (caption && !postOf.poll && !postOf.background && !postOf.promote) {
        if (caption.length > 10) {
          setSubmitPost(true);
          setErrorMessage(``);
        } else {
          setSubmitPost(false);
          setErrorMessage("Caption should be at least 10 characters");
        }
      } else if (postOf.poll) {
        if (poll.question) {
          setSubmitPost(true);
          setErrorMessage(``);
        } else {
          setSubmitPost(false);
          setErrorMessage(`Please add all the details in Poll`);
        }
      } else if (postOf.background) {
        if (bg && caption) {
          setSubmitPost(true);
          setErrorMessage(``);
        } else {
          setSubmitPost(false);
          setErrorMessage(`Please add all the details`);
        }
      } else if (postOf.promote) {
        if (promote.length) {
          setSubmitPost(true);
          setErrorMessage(``);
        }
      } else {
        setSubmitPost(false);
      }
    }
  }, [caption, visibility, postOf, poll, bg, promote]);

  return (
    <>
      {errorMessage && (
        <div
          className="p-3 rounded-3 d-flex align-items-center gap-3 "
          style={{ backgroundColor: "#FFD60026" }}
        >
          <Image
            src={"/images/svg/warning-yellow.svg"}
            alt={""}
            height={24}
            width={24}
          />

          <div>
            <span className="fw-bold">{errorMessage}</span>
            {role === "creator" &&
              !isSubscriptionVisibility &&
              (upgradeToPro ? (
                <div>
                  <u
                    className="link-offset-1 pointer"
                    onClick={() => router.push("/platform-plans")}
                  >
                    Get Creator Pro
                  </u>
                </div>
              ) : (
                <div>
                  Select your Post Audience below to view{" "}
                  <u
                    className="link-offset-1 pointer"
                    onClick={() => fileSupportsModalRef.method.open()}
                  >
                    supported media types
                  </u>
                  .
                </div>
              ))}
          </div>
        </div>
      )}
      <FileSupports setChildRef={fileSupportsModalRef} />
    </>
  );
};

export default MediaValidation;
