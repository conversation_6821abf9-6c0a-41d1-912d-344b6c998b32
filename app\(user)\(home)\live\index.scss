.tab {
  background: var(--bg-color);

  font-weight: 600;
}

.tab {
  &:hover {
    color: var(--primary-color) !important;
    background-color: var(--bg-color);
    box-shadow: 0 0 0 1pt var(--primary-color);
  }

  &:focus-visible {
    color: var(--primary-color) !important;
    background-color: var(--bg-color);
    box-shadow: 0 0 0 1pt var(--primary-color);
  }
}
.tab.active {
  background: var(--primary-color);
  color: var(--bg-color) !important;
}

.home-nav {
  .nav-active {
    background-color: var(--primary-color) !important;
    color: var(--bg-color) !important;
  }
}

@media screen and (min-width: 992px) {
  .custom-transform {
    transform: translate3d(-150px, 126px, 0) !important;
    width: 10rem !important;
  }
}

.form-select:focus {
  box-shadow: none !important;
}
