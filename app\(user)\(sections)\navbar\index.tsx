import Image from "next/image";
import { useState } from "react";

import { useNavigateBack } from "@/hooks/useNavigateBack";
import { useAppSelector } from "@/redux-store/hooks";

import Button from "../../../../components/common/button";

interface createNavProps {
  submitPost?: Function;
  buttonText?: string;
  btnIsValid?: boolean | any;
  btnIsDirty?: boolean | any;
  btnWidth?: number | any;
  btnIsSubmitting?: boolean | any;
  loader?: boolean | any;
  btnClass?: string;
  text?: string;
  icon: string;
  showBtn: boolean;
  // btnDisable is use for disabling the submit btn after clicking, to avoid multiple clicks and multiple api calls
  // set the btnDisable as true when submitPost function is call, wherever PostNavbar component is use.
  btnDisable?: boolean;
  postOf?: any;
  isPremiumPost?: boolean;
}

export default function PostNavbar(createNavProps: createNavProps) {
  const caption = useAppSelector((state) => state.userData?.post?.caption);
  const [btnIsValid, setbtnIsValid] = useState(false);

  // useEffect(() => {
  //   if (
  //     caption.length >= 10 &&
  //     !createNavProps?.postOf?.background &&
  //     !createNavProps?.postOf?.poll &&
  //     !createNavProps.isPremiumPost
  //   ) {
  //     setbtnIsValid(true);
  //   } else {
  //     setbtnIsValid(false);
  //   }

  //   return () => {
  //     setbtnIsValid(false);
  //   };
  // }, [caption, createNavProps.isPremiumPost, createNavProps.postOf]);
  const navigateBack = useNavigateBack();

  return (
    <nav
      className=" navbar navbar-light bg-cream createpost-nav"
      style={{
        position: "sticky",
        top: 0,
        // !changed its z-index from 10 to 5 because it was highlighting when loader shows on api call, make it 10 if any issue occurs
        zIndex: 5,
      }}
    >
      <div className="container-xxl">
        <div className="d-flex align-items-center gap-1">
          <div className="pointer" onClick={() => navigateBack()}>
            <Image
              width={36}
              height={36}
              src={createNavProps.icon}
              className="invert svg-icon"
              alt="close-icon pointer"
            />
          </div>
          <span className="mb-0 h4 d-none d-md-block">
            {createNavProps.text}
          </span>

          <span className="mb-0 h5 d-md-none">{createNavProps.text}</span>
        </div>
        {createNavProps.showBtn && (
          <span
            onClick={() => {
              if (
                (btnIsValid || createNavProps.btnIsValid) &&
                !createNavProps.btnDisable
              )
                createNavProps.submitPost && createNavProps.submitPost();
            }}
          >
            <Button
              isValid={
                (btnIsValid || createNavProps.btnIsValid) &&
                !createNavProps.btnDisable
              }
              loader={createNavProps.loader || false}
              dirty={
                btnIsValid ||
                (createNavProps.btnIsValid && !createNavProps.btnDisable)
              }
              isSubmitting={false || createNavProps.btnIsSubmitting}
              text={createNavProps.buttonText || "Next"}
              className={`${createNavProps.btnClass} createpost-btn`}
            />
          </span>
        )}
      </div>
    </nav>
  );
}
