import Image from "next/image";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import type { ReleaseFormsResponse } from "@/api/user";
import { getAssetUrl } from "@/utils/assets";

//components

interface User {
  name: string;
  username: string;
  pic: string;
  percentage?: number;
}

interface TagCollab {
  removeTag: Function;
  getUsers: {
    tags: any[];
    setTags: Function;
    collab: any[];
    setCollab: Function;
    releaseUsers: ReleaseFormsResponse[];
    setReleaseUsers: Function;
  };
  tagAndCollab: true | false;
}

export default function TagAndCollab(tagCollabProps: TagCollab) {
  const [showTag, setShowTag]: [User[], Function] = useState(
    tagCollabProps.getUsers.tags
  );
  const [showCollab, setShowCollab]: [User[], Function] = useState(
    tagCollabProps.getUsers.collab
  );
  const [showReleaseUsers, setShowReleaseUsers]: [
    ReleaseFormsResponse[],
    Function
  ] = useState(tagCollabProps.getUsers.releaseUsers);

  const [savedTagAndCollab, setSavedTagAndCollab] = useState(
    tagCollabProps.tagAndCollab
  );

  function removeTagUser(index: any) {
    const updatedTag = [...showTag];
    updatedTag.splice(index, 1);

    setShowTag(updatedTag);

    tagCollabProps?.getUsers?.setTags(updatedTag);

    if (showTag.length == 0) {
      tagCollabProps.removeTag();
    }
  }

  useEffect(() => {
    setShowTag(tagCollabProps.getUsers.tags);
  }, [tagCollabProps.getUsers.tags]);

  useEffect(() => {
    setShowCollab(tagCollabProps.getUsers.collab);
  }, [tagCollabProps.getUsers.collab]);

  useEffect(() => {
    setShowReleaseUsers(tagCollabProps.getUsers.releaseUsers);
  }, [tagCollabProps.getUsers.releaseUsers]);

  useEffect(() => {
    console.log({ showReleaseUsers });
  }, [showReleaseUsers]);

  const removeCollabUser = (index: number) => {
    const updatedCollabs = [...showCollab];
    const removedPercentage = updatedCollabs[index].percentage || 0;

    updatedCollabs.splice(index, 1); // Remove the user
    setRemainingPercentage(remainingPercentage + removedPercentage);
    setShowCollab(updatedCollabs);
    tagCollabProps.getUsers.setCollab(updatedCollabs);

    if (showReleaseUsers.length == 0 && showCollab.length == 0) {
      tagCollabProps.removeTag();
    }
  };

  const removeReleaseUser = (index: number) => {
    const updatedReleases = [...showReleaseUsers];

    updatedReleases.splice(index, 1); // Remove the user

    setShowReleaseUsers(updatedReleases);
    tagCollabProps.getUsers.setReleaseUsers(updatedReleases);

    if (showCollab.length == 0 && showReleaseUsers.length == 0) {
      tagCollabProps.removeTag();
    }
  };

  const [remainingPercentage, setRemainingPercentage] = useState(100);

  useEffect(() => {
    const updatedCollabs = [...showCollab];
    const finalTotalUsedPercentage = updatedCollabs.reduce(
      (sum, user) => sum + (user.percentage || 0),
      0
    );

    setRemainingPercentage(100 - finalTotalUsedPercentage);
  }, []);

  const handlePercentageChange = (index: number, newPercentage: number) => {
    const updatedCollabs = [...showCollab];

    if (newPercentage > 100) {
      toast("Percentage cannot be more than 100.");
      return;
    }

    const totalUsedPercentage = updatedCollabs.reduce(
      (sum, user, i) => (i === index ? sum : sum + (user.percentage || 0)),
      0
    );

    if (totalUsedPercentage + newPercentage > 100) {
      toast("Total percentage cannot exceed 100.");
      return;
    }

    updatedCollabs[index] = {
      ...updatedCollabs[index],
      percentage: newPercentage,
    };

    const finalTotalUsedPercentage = updatedCollabs.reduce(
      (sum, user) => sum + (user.percentage || 0),
      0
    );

    setRemainingPercentage(100 - finalTotalUsedPercentage);

    tagCollabProps?.getUsers?.setCollab(updatedCollabs);
    setShowCollab(updatedCollabs);
  };

  return (
    <>
      {savedTagAndCollab ? (
        <>
          {" "}
          {/* {showTag.length != 0 && <hr className="invert w-100 h-25" />} */}
          <div className="d-flex gap-3 tag-collab-box flex-column flex-md-row">
            {
              // #region
              // {(showTag.length > 0 || showReleaseUsers.length > 0) && (
              //   <div className="d-flex flex-column w-100 w-md-50">
              //     <p className="tag-collab-text fw-bold ">Tagged</p>
              //     {showTag.map((tag: any, index) => (
              //       <>
              //         <div
              //           className="d-flex w-100 align-items-center p-2 justify-content-between "
              //           key={"tag" + index}
              //         >
              //           <div className="d-flex align-items-center">
              //             <div className="rounded-pill profile-wrapper">
              //               {/* eslint-disable-next-line @next/next/no-img-element */}
              //               <img
              //                 width={100}
              //                 height={100}
              //                 className="img-fluid rounded-pill"
              //                 style={{ aspectRatio: "1", objectFit: "cover" }}
              //                 src={
              //                   tag.pic ||
              //                   getAssetUrl({
              //                     media: tag.avatar && tag?.avatar[0],
              //                   })
              //                 }
              //                 alt="profile-img"
              //               />
              //             </div>
              //             <div className="d-flex flex-column ps-2 pe-2">
              //               <p className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6">
              //                 {tag.display_name}
              //               </p>
              //               <p className="user-name text-overflow-ellipsis tag-box-title-small mb-0 fs-6">
              //                 {tag.username}
              //               </p>
              //             </div>
              //           </div>
              //           <Image
              //             onClick={() => removeTagUser(index)}
              //             src={"/images/svg/nav-close.svg"}
              //             width={20}
              //             height={20}
              //             className="invert svg-icon"
              //             alt="remove-tag"
              //           />
              //         </div>
              //       </>
              //     ))}
              //     {(showReleaseUsers.length
              //       ? showReleaseUsers
              //       : tagCollabProps.getUsers.releaseUsers
              //     )?.map((user: ReleaseFormsResponse, index) => (
              //       <>
              //         <div
              //           className="d-flex w-100 align-items-center p-2 justify-content-between "
              //           key={"release" + index}
              //         >
              //           <div className="d-flex align-items-center">
              //             <div className="rounded-pill profile-wrapper">
              //               {/* eslint-disable-next-line @next/next/no-img-element */}
              //               <img
              //                 width={100}
              //                 height={100}
              //                 className="img-fluid rounded-pill"
              //                 style={{ aspectRatio: "1", objectFit: "cover" }}
              //                 src={"/images/common/default.svg"}
              //                 alt="profile-img"
              //               />
              //             </div>
              //             <div className="d-flex flex-column ps-2 pe-2">
              //               <p className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6">
              //                 {user.friendly_name}
              //               </p>
              //               <p className="user-name text-overflow-ellipsis tag-box-title text-danger mb-0 fs-7">
              //                 External user
              //               </p>
              //             </div>
              //           </div>
              //           <div className="d-flex align-items-center justify-content-end gap-3">
              //             <Image
              //               onClick={() => removeReleaseUser(index)}
              //               src={"/images/svg/nav-close.svg"}
              //               width={20}
              //               height={20}
              //               className="invert svg-icon"
              //               alt="remove-tag"
              //             />
              //           </div>
              //         </div>
              //       </>
              //     ))}
              //   </div>
              // )}
              // #endregion
            }
            {showCollab.length > 0 && (
              <div className="col collab-col">
                <div className="d-flex align-items-center justify-content-between gap-2">
                  <p className="tag-collab-text fw-bold ">Collaborator</p>
                  <p>
                    Your share:{" "}
                    <span className="color-green">
                      {remainingPercentage.toFixed(2)}%
                    </span>
                  </p>
                </div>
                {(showCollab.length
                  ? showCollab
                  : tagCollabProps.getUsers.collab
                )?.map((collab: any, index) => (
                  <>
                    <div
                      className="d-flex w-100 align-items-center p-2 justify-content-between "
                      key={"collab" + index}
                    >
                      <div className="d-flex align-items-center">
                        <div className="rounded-pill profile-wrapper">
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            width={100}
                            height={100}
                            className="img-fluid rounded-pill"
                            style={{ aspectRatio: "1", objectFit: "cover" }}
                            src={
                              collab.pic ||
                              getAssetUrl({
                                media: collab?.avatar[0],
                                defaultType: "avatar",
                              })
                            }
                            alt="profile-img"
                          />
                        </div>
                        <div className="d-flex flex-column ps-2 pe-2">
                          <p className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6">
                            {collab.display_name}
                          </p>
                          <p className="user-name text-overflow-ellipsis tag-box-title-small mb-0 fs-6">
                            {collab.username}
                          </p>
                        </div>
                      </div>

                      <div className="d-flex align-items-center justify-content-end gap-3">
                        <div
                          style={{ width: "5rem" }}
                          className="position-relative"
                        >
                          <input
                            type="number"
                            value={collab.percentage}
                            className="form-control bg-cream pe-4"
                            onChange={(e) =>
                              handlePercentageChange(
                                index,
                                parseFloat(e.target.value)
                              )
                            }
                          />
                          <p className="position-absolute color-black top-50 me-2 end-0 translate-middle-y z-3">
                            %
                          </p>
                        </div>
                        <Image
                          onClick={() => removeCollabUser(index)}
                          src={"/images/svg/nav-close.svg"}
                          width={20}
                          height={20}
                          className="invert svg-icon"
                          alt="remove-tag"
                        />
                      </div>
                    </div>
                  </>
                ))}

                {/* <>
                  {showReleaseUsers.length > 0 && (
                    <div className="fs-7 mt-2">
                      You can&apos;t give share to the external users
                    </div>
                  )}
                </> */}
              </div>
            )}
          </div>
        </>
      ) : (
        <>
          <div className="d-flex flex-column  gap-1 tag-collab-box">
            {
              // #region
              // tagCollabProps.getUsers.tags.length > 0 ||
              // tagCollabProps.getUsers.releaseUsers?.length > 0) && (
              // <div className="d-flex flex-column w-50">
              //   <h5 className="tag-collab-text fw-medium ">Tagged</h5>
              //   {tagCollabProps.getUsers.tags.map((tag: any, index) => (
              //     <>
              //       <div
              //         className="d-flex w-100 align-items-center pt-2 justify-content-between "
              //         key={"tag" + index}
              //       >
              //         <div className="d-flex  align-items-center">
              //           <div className="rounded-pill profile-wrapper">
              //             {/* eslint-disable-next-line @next/next/no-img-element */}
              //             <img
              //               width={100}
              //               height={100}
              //               className="img-fluid rounded-pill"
              //               style={{ aspectRatio: "1", objectFit: "cover" }}
              //               src={
              //                 tag.pic ||
              //                 getAssetUrl({
              //                   media: tag.avatar && tag?.avatar[0],
              //                 })
              //               }
              //               alt="profile-img"
              //             />
              //           </div>
              //           <div className="d-flex flex-column ps-2 pe-2">
              //             <p className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6">
              //               {tag.display_name}
              //             </p>
              //             <p className="user-name text-overflow-ellipsis tag-box-title-small mb-0 fs-6">
              //               {tag.username}
              //             </p>
              //           </div>
              //         </div>
              //       </div>
              //     </>
              //   ))}
              //   {tagCollabProps.getUsers.releaseUsers?.map(
              //     (user: ReleaseFormsResponse, index) => (
              //       <>
              //         <div
              //           className="d-flex w-100 align-items-center pt-2 justify-content-between "
              //           key={"collab" + index}
              //         >
              //           <div className="d-flex align-items-center">
              //             <div className="rounded-pill profile-wrapper">
              //               {/* eslint-disable-next-line @next/next/no-img-element */}
              //               <img
              //                 width={100}
              //                 height={100}
              //                 className="img-fluid rounded-pill"
              //                 style={{ aspectRatio: "1", objectFit: "cover" }}
              //                 src={"/images/common/default.svg"}
              //                 alt="profile-img"
              //               />
              //             </div>
              //             <div className="d-flex flex-column ps-2 pe-2">
              //               <p className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6">
              //                 {user.friendly_name}
              //               </p>
              //               <p className="user-name text-overflow-ellipsis tag-box-title text-danger mb-0 fs-7">
              //                 External user
              //               </p>
              //             </div>
              //           </div>
              //         </div>
              //       </>
              //     )
              //   )}
              // </div>
              // )}
              // #endregion
            }

            {tagCollabProps.getUsers.collab.length > 0 && (
              <div className="col collab-col">
                <h5 className="tag-collab-text fw-medium ">
                  Collaborator selected
                </h5>
                {tagCollabProps.getUsers.collab?.map((collab: any, index) => (
                  <>
                    <div
                      className="d-flex w-100 align-items-center pt-2 justify-content-between "
                      key={"collab" + index}
                    >
                      <div className="d-flex align-items-center">
                        <div className="rounded-pill profile-wrapper">
                          {/* eslint-disable-next-line @next/next/no-img-element */}
                          <img
                            width={100}
                            height={100}
                            className="img-fluid rounded-pill"
                            style={{ aspectRatio: "1", objectFit: "cover" }}
                            src={
                              collab.pic ||
                              getAssetUrl({
                                media: collab?.avatar[0],
                                defaultType: "avatar",
                              })
                            }
                            alt="profile-img"
                          />
                        </div>
                        <div className="d-flex flex-column ps-2 pe-2">
                          <p className="user-name text-overflow-ellipsis tag-box-title mb-0 fs-6">
                            {collab.display_name}
                          </p>
                          <p className="user-name text-overflow-ellipsis tag-box-title-small mb-0 fs-6">
                            {collab.username}
                          </p>
                        </div>
                      </div>
                    </div>
                  </>
                ))}
              </div>
            )}
          </div>
        </>
      )}
    </>
  );
}
