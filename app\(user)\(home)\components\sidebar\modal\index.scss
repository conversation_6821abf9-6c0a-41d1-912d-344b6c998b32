.plans-section {
  width: 85%;
}

.prime-btn {
  background-color: #7a29cc !important;

  color: white !important;
  min-width: 12rem;
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.unlimited-btn {
  background-color: #ffb800 !important;

  color: black !important;
  min-width: 12rem;
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.pro-creator-btn {
  min-width: 12rem;
  background: linear-gradient(180deg, #27b1ff 0%, #006ead 100%) !important;
  color: #ffffff !important;
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.prime-div {
  background: rgb(122, 41, 204);
  background: linear-gradient(
    180deg,
    rgba(122, 41, 204, 1) 0%,
    rgba(79, 12, 147, 1) 100%
  );
  min-height: 25rem;
}
.pro-creator-div {
  min-height: 25rem;
  background: linear-gradient(180deg, #27b1ff 0%, #006ead 100%);
}
.unlimited-div {
  background: rgb(255, 184, 0);
  background: linear-gradient(
    180deg,
    rgba(255, 184, 0, 1) 0%,
    rgba(215, 129, 0, 1) 100%
  );
  min-height: 25rem;
}
.pagination-div {
  margin-top: 7% !important;
}
.swiper-pagination-bullet-active {
  background-color: #fff !important;
}

.swiper-button-next::after,
.swiper-button-prev::after {
  content: "" !important;
}

.prime-benefits .swiper-button-next,
.prime-benefits .swiper-button-prev,
.unlimited-benefits .swiper-button-next,
.unlimited-benefits .swiper-button-prev {
  --swiper-navigation-color: white !important;
  border-radius: 0 !important;
  box-shadow: 0 0 0 0 white !important;
  width: 3rem !important;
  height: 3rem !important;
  color: white !important;
  filter: invert(0) !important;

  &::after,
  &:before {
    content: "" !important;

    margin: 1rem !important;
    font-weight: bold !important;
    text-shadow: 0 0 0 var(--bg-color) !important;
    --swiper-navigation-size: 0rem !important;
  }
}
@media screen and (min-width: 1200px) {
  .plans-card {
    min-width: 30%;
  }
}
.tip-wrapper {
  padding: 0 20%;

  @media (max-width: 992px) {
    padding: 0;
  }
}
.payment-mode-section {
  display: flex;
  gap: 1rem;
  justify-content: center;
  border-bottom: 1px solid rgba(235, 235, 236, 1);
}
.payment-mode {
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
}
.active-mode {
  border-bottom: 1px solid var(--primary-color);
  color: var(--primary-color);
}
.active-amount {
  background: var(--primary-color) !important;
  color: #ffffff !important;
}
.payment-amounts {
  padding: 4px 10px 4px 10px;
  border-radius: 8px;
  background: var(--bg-cream);
  color: black;
  cursor: pointer;
  text-align: center;
}
.payment-amount-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}
.wallet-balance {
  color: rgba(39, 177, 255, 1);
}
.swal2-container {
  z-index: 1111 !important;
}

.golden {
  width: 10rem;

  @media (max-width: 786px) {
    width: 8rem;
  }
}

.onlyIcon {
  flex-grow: 0 !important;
  padding: 0 !important;
  box-shadow: none;
}
.color-prime {
  background: rgb(122, 41, 204);
}
.color-pro-creator {
  background: var(--blue-secondary);
}
