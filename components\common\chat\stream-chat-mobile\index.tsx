import { useLongPress } from "@uidotdev/usehooks";
import classNames from "classnames";
import { useEffect, useMemo, useRef, useState } from "react";

import { GetSinglePost } from "@/api/post";
import PostActionSendTip from "@/components/common/buttons/send-tip";
import PostActionShare from "@/components/common/buttons/share";
import { LiveReactionsBtn } from "@/components/common/event/reactions";
import { chatActions } from "@/redux-store/actions";
import { useAppDispatch, useAppSelector } from "@/redux-store/hooks";
import type { Post } from "@/types/post";
import { getAssetUrl } from "@/utils/assets";
import socketChannelInstance from "@/utils/chatSocket";

import Badges from "../../badges";
import ServicesBtn from "../chatbar/helpers/ServicesBtn";
import { MsgOptions, type MsgOptionsRef } from "../stream-chat/MsgOptions";
import styles from "./index.module.scss";

export default function LiveStreamChatMobile(props: {
  postId: string;
  iAmOwner?: boolean;
  style?: React.CSSProperties;
  className?: string;
}) {
  const dispatch = useAppDispatch();
  const avatar = useAppSelector((state) => state.user.profile.avatar[0]);
  const badges = useAppSelector((state) => state.user.profile.badges);
  const streamMessages = useAppSelector((state) => state.chat.streamMessages);
  const [post, setPost] = useState<Post | null>(null);
  const commentRef = useRef<HTMLDivElement | null>(null);
  const [showComments, setShowComments] = useState(true);
  const msgOptionsRef = useRef<MsgOptionsRef>(null);
  const longPressAttrs = useLongPress(
    (e) => msgOptionsRef.current?.show?.(e as any),
    { threshold: 500 }
  );
  const [isInputFocused, setIsInputFocused] = useState(false);

  const myAvatar = useMemo(
    () =>
      getAssetUrl({
        media: avatar,
        defaultType: "avatar",
        variation: "thumb",
      }),
    [avatar]
  );
  const myAvatarFileName = useMemo(() => myAvatar.split("/").pop(), [avatar]);

  const scrollIntoView = () => {
    // @ts-expect-error to be handled later
    commentRef.current?.scrollIntoViewIfNeeded(false);
  };

  useEffect(() => {
    scrollIntoView();
  }, [streamMessages]);

  useEffect(() => {
    (async () => {
      const _post: Post = (await GetSinglePost(props.postId)).data[0];
      setPost(_post);

      // attach to the socket channel
      // TODO: channel should not reconnect on refresh
      await socketChannelInstance.updateChannel(
        _post.event.event_converse_channel_id
      );

      const res = await socketChannelInstance.getNextBucketMessages();

      if (Array.isArray(res?.msgs?.read)) {
        dispatch(chatActions.clearStreamChat());
        dispatch(
          chatActions.setPrevStreamChatMsgs([
            ...(res?.msgs.read || []),
            ...(res?.msgs.unread || []),
          ])
        );
      }
    })();

    return () => socketChannelInstance.closeChannel();
  }, []);

  const addStreamComment = (e: any) => {
    socketChannelInstance.channel?.sendMessage({
      message: e.target!.value,
      meta: { avatar: avatar && myAvatarFileName, badges, type: "stream" },
    });
    e.target!.value = "";
  };

  return (
    <div
      className={classNames(styles["stream-chat"], props.className)}
      style={props.style}
    >
      <div
        className={classNames(
          styles["stream-comments"],
          "d-flex flex-column px-3",
          { invisible: !showComments }
        )}
      >
        {streamMessages?.map((streamMessage, index: number) => (
          <div
            key={index}
            className={classNames(
              styles["comment"],
              "w-100 d-flex align-items-start mb-2 gap-2 position-relative"
            )}
          >
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={
                streamMessage?.meta?.avatar
                  ? process.env.public_asset +
                    "/users/" +
                    streamMessage?.meta?.avatar
                  : "/images/common/default.svg"
              }
              className="img-fluid rounded-circle user-select-none"
              width={35}
              height={35}
              alt="profile pic"
            />
            <div
              className="w-100 d-flex flex-column rounded text-white text-break user-select-none"
              style={{ fontSize: "0.9em" }}
            >
              <div className="d-flex align-items-center">
                <span className="fw-semibold">{streamMessage.name}</span>
                <Badges array={streamMessage.meta.badges} cls="ms-1" />
                {(streamMessage.sid || streamMessage.sender_id) ===
                  post?.author._id && (
                  <span className="color-primary ms-2">Owner</span>
                )}
              </div>
              <span>{streamMessage.message}</span>
            </div>
            {props.iAmOwner && (
              <div
                className={classNames(
                  "position-absolute top-0 start-0 h-100 w-100 user-select-none",
                  {
                    "d-none":
                      (streamMessage.sid || streamMessage.sender_id) ===
                      post?.author._id,
                  }
                )}
                data-user-id={streamMessage.sid || streamMessage.sender_id}
                data-user-name={streamMessage.name}
                {...longPressAttrs}
              ></div>
            )}
          </div>
        ))}
        <div className="scroll-ref" ref={commentRef}></div>
      </div>

      <div className="d-flex justify-content-center align-items-center gap-2 px-3 py-2 ">
        <div
          className="flex-grow-1 d-flex border border-1 border-secondary rounded-pill dropdown dropup"
          data-bs-theme="dark"
        >
          <input
            type="text"
            placeholder="Add a comment..."
            className={classNames(
              styles["placeholder-white"],
              "form-control border-0 bg-black rounded-pill"
            )}
            onKeyDown={(e: any) =>
              e.key === "Enter" ? addStreamComment(e) : null
            }
            onFocus={() => setIsInputFocused(true)}
            onBlur={() => setIsInputFocused(false)}
          />
          <button
            className="btn text-white fs-9 px-3"
            style={{ minWidth: "auto" }}
            data-bs-toggle="dropdown"
            data-bs-auto-close="true"
          >
            •••
          </button>
          <ul className="dropdown-menu w-100 mb-2">
            <li>
              <a
                className="dropdown-item bg-transparent text-white fs-7 fw-normal"
                href="#"
                onClick={() => setShowComments((v) => !v)}
              >
                {showComments ? "Hide" : "Show"} Comments
              </a>
            </li>
          </ul>
        </div>

        {!props.iAmOwner && post && !isInputFocused && (
          <ServicesBtn
            classes="rounded d-flex justify-content-center align-items-center pointer"
            target={{
              target_user_display_name: post.author.display_name,
              target_user_id: post.author._id,
            }}
            is_text_required={false}
          />
        )}

        {!props.iAmOwner && post && !isInputFocused && (
          <PostActionSendTip
            postId={post.event._id}
            author={post.author}
            type="event"
            onlyIcon
            cls={styles["send-tip-btn"]}
          />
        )}

        {!isInputFocused && (
          <PostActionShare
            postId={props.postId}
            shareText={`Checkout the knky event by ${post?.author.display_name}\n`}
            cls={styles["share-btn"]}
            style={
              props.iAmOwner
                ? { marginRight: "-0.5em" }
                : { marginLeft: "-0.25em", marginRight: "-0.25em" }
            }
            iconWhite
          />
        )}

        {!props.iAmOwner && !isInputFocused && <LiveReactionsBtn onlyIcon />}
      </div>

      <MsgOptions ref={msgOptionsRef} isMobile={true} />
    </div>
  );
}
